/**
 * 🔍 CHECK DATABASE TABLES
 * Investigate the database structure and session management
 */

const mysql = require('mysql2/promise');

async function checkDatabaseTables() {
  console.log('🔍 DATABASE STRUCTURE INVESTIGATION');
  console.log('=' .repeat(60));
  
  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 1. Show all tables
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📊 Total tables in database: ${tables.length}`);
    console.log('\n📋 Available tables:');
    tables.forEach((table, index) => {
      const tableName = Object.values(table)[0];
      console.log(`  ${index + 1}. ${tableName}`);
    });
    
    // 2. Check if there's a users table and look for session-related columns
    console.log('\n🔍 Checking users table structure...');
    try {
      const [userColumns] = await connection.execute('DESCRIBE users');
      console.log('👥 Users table columns:');
      userColumns.forEach((col, index) => {
        console.log(`  ${index + 1}. ${col.Field} (${col.Type}) - ${col.Null === 'YES' ? 'Nullable' : 'Not Null'}`);
      });
      
      // Check for any session-related columns
      const sessionColumns = userColumns.filter(col => 
        col.Field.toLowerCase().includes('session') || 
        col.Field.toLowerCase().includes('token') ||
        col.Field.toLowerCase().includes('login')
      );
      
      if (sessionColumns.length > 0) {
        console.log('\n🔐 Session-related columns found:');
        sessionColumns.forEach(col => {
          console.log(`  - ${col.Field} (${col.Type})`);
        });
      } else {
        console.log('\n❌ No session-related columns found in users table');
      }
      
    } catch (error) {
      console.log('❌ Users table not found or error:', error.message);
    }
    
    // 3. Look for any table that might contain session data
    console.log('\n🔍 Searching for session-related tables...');
    const sessionTables = tables.filter(table => {
      const tableName = Object.values(table)[0].toLowerCase();
      return tableName.includes('session') || 
             tableName.includes('auth') || 
             tableName.includes('login') ||
             tableName.includes('token');
    });
    
    if (sessionTables.length > 0) {
      console.log('🔐 Session-related tables found:');
      sessionTables.forEach(table => {
        console.log(`  - ${Object.values(table)[0]}`);
      });
    } else {
      console.log('❌ No session-related tables found');
    }
    
    // 4. Check current user count
    try {
      const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
      console.log(`\n👥 Total users in system: ${userCount[0].count}`);
      
      // Show sample users
      const [sampleUsers] = await connection.execute('SELECT id, name, department FROM users LIMIT 5');
      console.log('\n📋 Sample users:');
      sampleUsers.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.name} (${user.department}) - ID: ${user.id}`);
      });
      
    } catch (error) {
      console.log('❌ Error checking users:', error.message);
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('✅ DATABASE INVESTIGATION COMPLETE');
    console.log('💡 Session management appears to be handled differently');
    console.log('🔄 The 401 errors may be due to client-side session issues');
    console.log('=' .repeat(60));
    
  } catch (error) {
    console.error('❌ Error investigating database:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the investigation
checkDatabaseTables().catch(console.error);
