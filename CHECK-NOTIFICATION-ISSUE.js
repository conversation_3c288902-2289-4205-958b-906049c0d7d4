/**
 * 🔍 NOTIFICATION ISSUE INVESTIGATION
 * Check why NEW VOUCHER batch notifications are not appearing in real-time
 */

const mysql = require('mysql2/promise');

async function checkNotificationIssue() {
  console.log('🔍 NOTIFICATION ISSUE INVESTIGATION');
  console.log('=' .repeat(60));
  
  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 1. Check voucher_batches table structure
    console.log('\n📋 Checking voucher_batches table structure...');
    const [batchColumns] = await connection.execute('DESCRIBE voucher_batches');
    console.log('📊 voucher_batches columns:');
    batchColumns.forEach((col, index) => {
      console.log(`  ${index + 1}. ${col.Field} (${col.Type})`);
    });
    
    // 2. Check recent vouchers
    console.log('\n🎫 Checking recent vouchers...');
    const [vouchers] = await connection.execute(`
      SELECT voucher_id, department, status, sent_to_audit, received_by_audit 
      FROM vouchers 
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    console.log(`📊 Recent vouchers: ${vouchers.length}`);
    vouchers.forEach((voucher, index) => {
      console.log(`  ${index + 1}. ${voucher.voucher_id} (${voucher.department})`);
      console.log(`     Status: ${voucher.status}`);
      console.log(`     Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`     Received by Audit: ${voucher.received_by_audit ? 'YES' : 'NO'}`);
      console.log('');
    });
    
    // 3. Check voucher batches (using correct column names)
    console.log('\n📦 Checking voucher batches...');
    const [batches] = await connection.execute(`
      SELECT * FROM voucher_batches 
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    console.log(`📊 Recent batches: ${batches.length}`);
    batches.forEach((batch, index) => {
      console.log(`  ${index + 1}. Batch ID: ${batch.id}`);
      console.log(`     Department: ${batch.department}`);
      console.log(`     Received: ${batch.received ? 'YES' : 'NO'}`);
      console.log(`     From Audit: ${batch.from_audit ? 'YES' : 'NO'}`);
      console.log('');
    });
    
    // 4. Check notifications table structure
    console.log('\n📋 Checking notifications table structure...');
    const [notificationColumns] = await connection.execute('DESCRIBE notifications');
    console.log('📊 notifications columns:');
    notificationColumns.forEach((col, index) => {
      console.log(`  ${index + 1}. ${col.Field} (${col.Type})`);
    });
    
    // 5. Check recent notifications
    console.log('\n🔔 Checking recent notifications...');
    const [notifications] = await connection.execute(`
      SELECT * FROM notifications 
      ORDER BY id DESC 
      LIMIT 10
    `);
    
    console.log(`📊 Recent notifications: ${notifications.length}`);
    notifications.forEach((notification, index) => {
      console.log(`  ${index + 1}. ${notification.type}: ${notification.message}`);
      console.log(`     User ID: ${notification.user_id}`);
      console.log(`     Read: ${notification.is_read ? 'YES' : 'NO'}`);
      console.log('');
    });
    
    // 6. Check for Audit users
    console.log('\n👥 Checking Audit users...');
    const [auditUsers] = await connection.execute(`
      SELECT id, name, department FROM users WHERE department = 'AUDIT'
    `);
    
    console.log(`📊 Audit users: ${auditUsers.length}`);
    auditUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.name} (ID: ${user.id})`);
    });
    
    // 7. Check for NEW_BATCH notifications for Audit users
    console.log('\n🎯 Checking NEW_BATCH notifications for Audit users...');
    if (auditUsers.length > 0) {
      const auditUserIds = auditUsers.map(u => `'${u.id}'`).join(',');
      const [auditNotifications] = await connection.execute(`
        SELECT * FROM notifications 
        WHERE type = 'NEW_BATCH' 
          AND user_id IN (${auditUserIds})
        ORDER BY id DESC
        LIMIT 5
      `);
      
      console.log(`📊 NEW_BATCH notifications for Audit: ${auditNotifications.length}`);
      auditNotifications.forEach((notification, index) => {
        console.log(`  ${index + 1}. ${notification.message}`);
        console.log(`     Read: ${notification.is_read ? 'YES' : 'NO'}`);
        console.log('');
      });
    }
    
    // 8. Analysis
    console.log('\n' + '=' .repeat(60));
    console.log('📋 ANALYSIS');
    console.log('=' .repeat(60));
    
    const vouchersToAudit = vouchers.filter(v => v.sent_to_audit);
    const pendingBatches = batches.filter(b => !b.received && !b.from_audit);
    
    console.log(`🎫 Vouchers sent to Audit: ${vouchersToAudit.length}`);
    console.log(`📦 Pending batches for Audit: ${pendingBatches.length}`);
    console.log(`👥 Audit users in system: ${auditUsers.length}`);
    
    if (vouchersToAudit.length > 0 && pendingBatches.length === 0) {
      console.log('\n⚠️ ISSUE: Vouchers sent to Audit but no corresponding batches found');
    }
    
    if (pendingBatches.length > 0 && notifications.filter(n => n.type === 'NEW_BATCH').length === 0) {
      console.log('\n⚠️ ISSUE: Pending batches exist but no NEW_BATCH notifications found');
    }
    
    console.log('\n✅ INVESTIGATION COMPLETE');
    
  } catch (error) {
    console.error('❌ Error investigating notifications:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the investigation
checkNotificationIssue().catch(console.error);
