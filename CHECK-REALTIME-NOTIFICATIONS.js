/**
 * 🔍 REAL-TIME NOTIFICATION INVESTIGATION
 * Check why NEW VOUCHER batch notifications are not appearing in real-time
 */

const mysql = require('mysql2/promise');

async function checkRealtimeNotifications() {
  console.log('🔍 REAL-TIME NOTIFICATION INVESTIGATION');
  console.log('=' .repeat(60));
  
  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 1. Check recent vouchers
    console.log('\n🎫 Checking recent vouchers...');
    const [vouchers] = await connection.execute(`
      SELECT voucher_id, department, status, created_at, sent_to_audit, received_by_audit 
      FROM vouchers 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    console.log(`📊 Recent vouchers found: ${vouchers.length}`);
    vouchers.forEach((voucher, index) => {
      console.log(`  ${index + 1}. ${voucher.voucher_id} (${voucher.department})`);
      console.log(`     Status: ${voucher.status}`);
      console.log(`     Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`     Received by Audit: ${voucher.received_by_audit ? 'YES' : 'NO'}`);
      console.log(`     Created: ${voucher.created_at}`);
      console.log('');
    });
    
    // 2. Check voucher batches
    console.log('\n📦 Checking voucher batches...');
    const [batches] = await connection.execute(`
      SELECT id, department, created_at, received, from_audit, voucher_count 
      FROM voucher_batches 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    console.log(`📊 Recent batches found: ${batches.length}`);
    batches.forEach((batch, index) => {
      console.log(`  ${index + 1}. Batch ID: ${batch.id}`);
      console.log(`     Department: ${batch.department}`);
      console.log(`     Voucher Count: ${batch.voucher_count}`);
      console.log(`     Received: ${batch.received ? 'YES' : 'NO'}`);
      console.log(`     From Audit: ${batch.from_audit ? 'YES' : 'NO'}`);
      console.log(`     Created: ${batch.created_at}`);
      console.log('');
    });
    
    // 3. Check notifications
    console.log('\n🔔 Checking notifications...');
    const [notifications] = await connection.execute(`
      SELECT id, type, message, user_id, is_read, from_audit, created_at 
      FROM notifications 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log(`📊 Recent notifications found: ${notifications.length}`);
    notifications.forEach((notification, index) => {
      console.log(`  ${index + 1}. ${notification.type}: ${notification.message}`);
      console.log(`     User ID: ${notification.user_id}`);
      console.log(`     Read: ${notification.is_read ? 'YES' : 'NO'}`);
      console.log(`     From Audit: ${notification.from_audit ? 'YES' : 'NO'}`);
      console.log(`     Created: ${notification.created_at}`);
      console.log('');
    });
    
    // 4. Check for unread NEW_BATCH notifications for Audit users
    console.log('\n🎯 Checking unread NEW_BATCH notifications for Audit...');
    const [auditNotifications] = await connection.execute(`
      SELECT n.*, u.name, u.department 
      FROM notifications n
      JOIN users u ON n.user_id = u.id
      WHERE n.type = 'NEW_BATCH' 
        AND u.department = 'AUDIT' 
        AND n.is_read = FALSE
      ORDER BY n.created_at DESC
    `);
    
    console.log(`📊 Unread Audit notifications: ${auditNotifications.length}`);
    auditNotifications.forEach((notification, index) => {
      console.log(`  ${index + 1}. ${notification.message}`);
      console.log(`     User: ${notification.name} (${notification.department})`);
      console.log(`     Created: ${notification.created_at}`);
      console.log('');
    });
    
    // 5. Check active sessions to see who's connected
    console.log('\n👥 Checking active sessions...');
    const [activeSessions] = await connection.execute(`
      SELECT s.*, u.name, u.department 
      FROM active_sessions s
      JOIN users u ON s.user_id = u.id
      ORDER BY s.created_at DESC
    `);
    
    console.log(`📊 Active sessions: ${activeSessions.length}`);
    activeSessions.forEach((session, index) => {
      console.log(`  ${index + 1}. ${session.name} (${session.department})`);
      console.log(`     Session ID: ${session.session_id}`);
      console.log(`     Created: ${session.created_at}`);
      console.log('');
    });
    
    // 6. Analysis and recommendations
    console.log('\n' + '=' .repeat(60));
    console.log('📋 ANALYSIS & RECOMMENDATIONS');
    console.log('=' .repeat(60));
    
    const auditVouchers = vouchers.filter(v => v.sent_to_audit && !v.received_by_audit);
    const pendingBatches = batches.filter(b => !b.received && !b.from_audit);
    const auditSessions = activeSessions.filter(s => s.department === 'AUDIT');
    
    console.log(`🎫 Vouchers sent to Audit (not received): ${auditVouchers.length}`);
    console.log(`📦 Pending batches for Audit: ${pendingBatches.length}`);
    console.log(`🔔 Unread Audit notifications: ${auditNotifications.length}`);
    console.log(`👤 Active Audit sessions: ${auditSessions.length}`);
    
    if (auditVouchers.length > 0 && auditNotifications.length === 0) {
      console.log('\n⚠️ ISSUE DETECTED: Vouchers sent to Audit but no notifications created');
      console.log('💡 Possible causes:');
      console.log('   - Notification creation failed during voucher dispatch');
      console.log('   - WebSocket broadcast not working');
      console.log('   - Client not listening for real-time events');
    }
    
    if (auditSessions.length === 0) {
      console.log('\n⚠️ NO AUDIT USERS CURRENTLY LOGGED IN');
      console.log('💡 Real-time notifications require active WebSocket connections');
    }
    
    console.log('\n✅ INVESTIGATION COMPLETE');
    
  } catch (error) {
    console.error('❌ Error investigating notifications:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the investigation
checkRealtimeNotifications().catch(console.error);
