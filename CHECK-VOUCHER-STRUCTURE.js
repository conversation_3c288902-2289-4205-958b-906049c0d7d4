/**
 * 🔍 CHECK VOUCHER TABLE STRUCTURE
 */

const mysql = require('mysql2/promise');

async function checkVoucherStructure() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('📋 VOUCHER TABLE STRUCTURE:');
    console.log('=' .repeat(50));
    
    const [columns] = await connection.execute('DESCRIBE vouchers');
    columns.forEach((col, index) => {
      console.log(`${index + 1}. ${col.Field} - ${col.Type} - ${col.Null} - ${col.Key} - ${col.Default}`);
    });
    
    console.log('\n📊 SAMPLE VOUCHERS:');
    const [vouchers] = await connection.execute(`
      SELECT voucher_id, amount, currency, department, status 
      FROM vouchers 
      LIMIT 3
    `);
    
    vouchers.forEach(v => {
      console.log(`${v.voucher_id}: ${v.amount} ${v.currency} (${v.department}) - ${v.status}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) await connection.end();
  }
}

checkVoucherStructure();
