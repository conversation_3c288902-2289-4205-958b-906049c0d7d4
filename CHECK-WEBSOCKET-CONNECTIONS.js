const io = require('socket.io-client');

async function checkWebSocketConnections() {
  console.log('🔍 CHECKING WEBSOCKET CONNECTIONS...\n');
  
  try {
    // Connect as Audit user
    console.log('1️⃣  Connecting as Audit user...');
    const auditSocket = io('http://10.25.41.232:8080', {
      transports: ['websocket'],
      timeout: 5000
    });
    
    let auditConnected = false;
    let auditRoomJoined = false;
    
    auditSocket.on('connect', () => {
      console.log('   ✅ Audit WebSocket connected - Socket ID:', auditSocket.id);
      auditConnected = true;
      
      // Join <PERSON>t department room
      auditSocket.emit('join_department', { 
        department: 'AUDIT',
        userId: 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98',
        userName: 'EMMANUEL AMOAKOH'
      });
    });
    
    auditSocket.on('joined_department', (data) => {
      console.log('   ✅ Audit user joined department room:', data);
      auditRoomJoined = true;
    });
    
    auditSocket.on('connect_error', (error) => {
      console.log('   ❌ Audit WebSocket connection error:', error.message);
    });
    
    auditSocket.on('disconnect', (reason) => {
      console.log('   🔌 Audit WebSocket disconnected:', reason);
    });
    
    // Wait for connection
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log(`\n📊 AUDIT CONNECTION STATUS:`);
    console.log(`   Connected: ${auditConnected}`);
    console.log(`   Room Joined: ${auditRoomJoined}`);
    
    // Connect as Finance user
    console.log('\n2️⃣  Connecting as Finance user...');
    const financeSocket = io('http://10.25.41.232:8080', {
      transports: ['websocket'],
      timeout: 5000
    });
    
    let financeConnected = false;
    let financeRoomJoined = false;
    
    financeSocket.on('connect', () => {
      console.log('   ✅ Finance WebSocket connected - Socket ID:', financeSocket.id);
      financeConnected = true;
      
      // Join Finance department room
      financeSocket.emit('join_department', { 
        department: 'FINANCE',
        userId: '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84',
        userName: 'MR. FELIX AYISI'
      });
    });
    
    financeSocket.on('joined_department', (data) => {
      console.log('   ✅ Finance user joined department room:', data);
      financeRoomJoined = true;
    });
    
    financeSocket.on('connect_error', (error) => {
      console.log('   ❌ Finance WebSocket connection error:', error.message);
    });
    
    financeSocket.on('disconnect', (reason) => {
      console.log('   🔌 Finance WebSocket disconnected:', reason);
    });
    
    // Wait for connection
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log(`\n📊 FINANCE CONNECTION STATUS:`);
    console.log(`   Connected: ${financeConnected}`);
    console.log(`   Room Joined: ${financeRoomJoined}`);
    
    // Test notification
    if (auditConnected && auditRoomJoined) {
      console.log('\n3️⃣  Testing notification delivery...');
      
      let notificationReceived = false;
      
      auditSocket.on('new_batch_notification', (data) => {
        console.log('   🔔 NOTIFICATION RECEIVED!');
        console.log('   📄 Data:', JSON.stringify(data, null, 2));
        notificationReceived = true;
      });
      
      // Simulate a batch notification broadcast
      console.log('   📡 Simulating batch notification...');
      
      // Wait a bit to see if notification comes through
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log(`   📊 Notification received: ${notificationReceived}`);
    }
    
    // Cleanup
    auditSocket.disconnect();
    financeSocket.disconnect();
    
    console.log('\n🎯 SUMMARY:');
    console.log(`   Audit WebSocket: ${auditConnected ? '✅' : '❌'}`);
    console.log(`   Audit Room Join: ${auditRoomJoined ? '✅' : '❌'}`);
    console.log(`   Finance WebSocket: ${financeConnected ? '✅' : '❌'}`);
    console.log(`   Finance Room Join: ${financeRoomJoined ? '✅' : '❌'}`);
    
  } catch (error) {
    console.error('❌ Error checking WebSocket connections:', error);
  }
}

checkWebSocketConnections();
