/**
 * 🔧 PRODUCTION FIX: Clear Invalid Sessions
 * Fix the 401 authentication errors by clearing invalid session data
 */

const mysql = require('mysql2/promise');

async function clearInvalidSessions() {
  console.log('🔧 PRODUCTION FIX: Clearing Invalid Sessions');
  console.log('=' .repeat(60));
  
  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 1. Check active_sessions table
    console.log('\n🔍 Checking active_sessions table...');
    try {
      const [activeSessions] = await connection.execute('SELECT * FROM active_sessions');
      console.log(`📊 Active sessions found: ${activeSessions.length}`);
      
      if (activeSessions.length > 0) {
        console.log('📋 Active sessions:');
        activeSessions.forEach((session, index) => {
          console.log(`  ${index + 1}. Session ID: ${session.session_id || session.id}`);
          console.log(`     User ID: ${session.user_id}`);
          console.log(`     Created: ${session.created_at || session.timestamp}`);
          console.log('');
        });
        
        // Clear active sessions
        const [deleteActive] = await connection.execute('DELETE FROM active_sessions');
        console.log(`✅ Cleared ${deleteActive.affectedRows} active sessions`);
      }
    } catch (error) {
      console.log('❌ Error with active_sessions:', error.message);
    }
    
    // 2. Check lan_user_sessions table
    console.log('\n🔍 Checking lan_user_sessions table...');
    try {
      const [lanSessions] = await connection.execute('SELECT * FROM lan_user_sessions');
      console.log(`📊 LAN user sessions found: ${lanSessions.length}`);
      
      if (lanSessions.length > 0) {
        console.log('📋 LAN user sessions:');
        lanSessions.forEach((session, index) => {
          console.log(`  ${index + 1}. Session ID: ${session.session_id || session.id}`);
          console.log(`     User ID: ${session.user_id}`);
          console.log(`     IP Address: ${session.ip_address || 'N/A'}`);
          console.log(`     Created: ${session.created_at || session.timestamp}`);
          console.log('');
        });
        
        // Clear LAN user sessions
        const [deleteLan] = await connection.execute('DELETE FROM lan_user_sessions');
        console.log(`✅ Cleared ${deleteLan.affectedRows} LAN user sessions`);
      }
    } catch (error) {
      console.log('❌ Error with lan_user_sessions:', error.message);
    }
    
    // 3. Verify all sessions are cleared
    console.log('\n🔍 Verifying session cleanup...');
    try {
      const [remainingActive] = await connection.execute('SELECT COUNT(*) as count FROM active_sessions');
      const [remainingLan] = await connection.execute('SELECT COUNT(*) as count FROM lan_user_sessions');
      
      console.log(`📊 Remaining active sessions: ${remainingActive[0].count}`);
      console.log(`📊 Remaining LAN sessions: ${remainingLan[0].count}`);
      
      if (remainingActive[0].count === 0 && remainingLan[0].count === 0) {
        console.log('✅ All sessions successfully cleared');
      } else {
        console.log('⚠️ Some sessions may still remain');
      }
    } catch (error) {
      console.log('❌ Error verifying cleanup:', error.message);
    }
    
    // 4. Show available users for fresh login
    console.log('\n👥 Available users for fresh login:');
    try {
      const [users] = await connection.execute('SELECT name, department FROM users WHERE department != "SYSTEM ADMIN" ORDER BY department, name');
      users.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.name} (${user.department})`);
      });
    } catch (error) {
      console.log('❌ Error fetching users:', error.message);
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('✅ SESSION CLEANUP COMPLETE');
    console.log('🔄 All users need to login again with fresh credentials');
    console.log('🔐 Invalid session IDs have been cleared from database');
    console.log('💻 Browser cache may also need to be cleared');
    console.log('=' .repeat(60));
    
  } catch (error) {
    console.error('❌ Error clearing sessions:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the session cleanup
clearInvalidSessions().catch(console.error);
