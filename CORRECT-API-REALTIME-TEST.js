/**
 * 🧪 CORRECT API REAL-TIME TEST
 * Test real-time notifications using the correct API endpoint
 */

const mysql = require('mysql2/promise');
const axios = require('axios');
const io = require('socket.io-client');

async function correctApiRealtimeTest() {
  console.log('🧪 CORRECT API REAL-TIME NOTIFICATION TEST');
  console.log('=' .repeat(60));
  
  let connection;
  let auditSocket;
  
  try {
    // 1. Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 2. Get users
    const [financeUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'FINANCE' LIMIT 1
    `);
    
    const [auditUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'AUDIT' LIMIT 1
    `);
    
    if (financeUsers.length === 0 || auditUsers.length === 0) {
      console.log('❌ Need both Finance and Audit users');
      return;
    }
    
    const financeUser = financeUsers[0];
    const auditUser = auditUsers[0];
    
    console.log(`👤 Finance user: ${financeUser.name} (ID: ${financeUser.id})`);
    console.log(`👤 Audit user: ${auditUser.name} (ID: ${auditUser.id})`);
    
    // 3. Find an existing voucher that can be sent to Audit
    console.log('\n🔍 Finding existing voucher to send to Audit...');
    
    const [existingVouchers] = await connection.execute(`
      SELECT id, voucher_id, department, status, sent_to_audit 
      FROM vouchers 
      WHERE department = 'FINANCE' 
        AND sent_to_audit = FALSE 
        AND status IN ('FINANCE_PENDING', 'PENDING') 
      LIMIT 1
    `);
    
    let voucherId, voucherDbId;
    
    if (existingVouchers.length > 0) {
      const voucher = existingVouchers[0];
      voucherId = voucher.voucher_id;
      voucherDbId = voucher.id;
      console.log(`✅ Found existing voucher: ${voucherId} (ID: ${voucherDbId})`);
    } else {
      console.log('📝 No suitable existing voucher found, creating new one...');
      
      // Create a new voucher with minimal required fields
      const newVoucherId = `REALTIME${Date.now()}`;
      
      const [insertResult] = await connection.execute(`
        INSERT INTO vouchers (voucher_id, amount, currency, department, created_by, status, created_at)
        VALUES (?, 1000.00, 'GHS', 'FINANCE', ?, 'FINANCE_PENDING', NOW())
      `, [newVoucherId, financeUser.name]);
      
      voucherId = newVoucherId;
      voucherDbId = insertResult.insertId;
      console.log(`✅ Created new voucher: ${voucherId} (ID: ${voucherDbId})`);
    }
    
    // 4. Set up Audit WebSocket BEFORE sending voucher
    console.log('\n🔌 Setting up Audit WebSocket listener...');
    
    auditSocket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 5000
    });
    
    let connected = false;
    let roomJoined = false;
    let notificationReceived = false;
    let notificationData = null;
    
    auditSocket.on('connect', () => {
      console.log('✅ Audit WebSocket connected');
      connected = true;
      
      // Join Audit department room
      auditSocket.emit('join_department', { 
        department: 'AUDIT',
        userId: auditUser.id,
        userName: auditUser.name
      });
    });
    
    auditSocket.on('joined_department', (data) => {
      console.log('✅ Joined department room:', data);
      roomJoined = true;
    });
    
    auditSocket.on('new_batch_notification', (data) => {
      console.log('🔔 REAL-TIME: new_batch_notification received!');
      console.log('📄 Data:', JSON.stringify(data, null, 2));
      notificationReceived = true;
      notificationData = data;
    });
    
    auditSocket.on('batch_update', (data) => {
      console.log('📦 REAL-TIME: batch_update received:', data);
    });
    
    auditSocket.on('voucher_update', (data) => {
      console.log('🎫 REAL-TIME: voucher_update received:', data);
    });
    
    // Wait for WebSocket setup
    console.log('⏳ Waiting for WebSocket setup...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log(`🔌 Connected: ${connected}`);
    console.log(`📡 Room joined: ${roomJoined}`);
    
    if (!connected || !roomJoined) {
      console.log('❌ WebSocket setup failed');
      return;
    }
    
    // 5. Login as Finance user
    console.log('\n🔐 Logging in as Finance user...');
    
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      userId: financeUser.id
    });
    
    const sessionCookie = loginResponse.headers['set-cookie']?.[0];
    console.log('✅ Logged in as Finance user');
    
    // 6. Send voucher to Audit using CORRECT API endpoint
    console.log('\n📤 Sending voucher to Audit using correct API...');
    
    try {
      const sendResponse = await axios.post(
        `http://localhost:8080/api/vouchers/${voucherDbId}/send-to-audit`,
        {}, // Empty body for this endpoint
        {
          headers: {
            'Cookie': sessionCookie,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('✅ Voucher sent to Audit successfully!');
      console.log('📊 API Response:', sendResponse.data);
      
    } catch (apiError) {
      console.log('❌ API Error:', apiError.response?.data || apiError.message);
      console.log('📊 Status:', apiError.response?.status);
      
      if (apiError.response?.status === 404) {
        console.log('💡 Voucher might not exist or endpoint might be different');
      }
      return;
    }
    
    // 7. Wait for real-time notification
    console.log('\n⏳ Waiting for real-time notification (10 seconds)...');
    
    const startTime = Date.now();
    while (Date.now() - startTime < 10000) {
      if (notificationReceived) {
        console.log('🎉 NOTIFICATION RECEIVED DURING WAIT!');
        break;
      }
      await new Promise(resolve => setTimeout(resolve, 500));
      process.stdout.write('.');
    }
    
    console.log('\n');
    
    // 8. Check final results
    console.log('\n' + '=' .repeat(60));
    console.log('📋 CORRECT API TEST RESULTS');
    console.log('=' .repeat(60));
    
    console.log(`🔌 WebSocket connected: ${connected ? 'YES' : 'NO'}`);
    console.log(`📡 Joined department room: ${roomJoined ? 'YES' : 'NO'}`);
    console.log(`🔔 Notification received: ${notificationReceived ? 'YES' : 'NO'}`);
    
    if (notificationData) {
      console.log('📄 Notification data:');
      console.log(JSON.stringify(notificationData, null, 2));
    }
    
    // Check database state
    const [finalVoucher] = await connection.execute(`
      SELECT voucher_id, status, sent_to_audit, received_by_audit 
      FROM vouchers WHERE id = ?
    `, [voucherDbId]);
    
    if (finalVoucher.length > 0) {
      const voucher = finalVoucher[0];
      console.log(`🎫 Final voucher: ${voucher.voucher_id} - Status: ${voucher.status}`);
      console.log(`📤 Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`📥 Received by Audit: ${voucher.received_by_audit ? 'YES' : 'NO'}`);
    }
    
    // Check for new batches
    const [recentBatches] = await connection.execute(`
      SELECT id, department, received, created_at 
      FROM voucher_batches 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
      ORDER BY created_at DESC 
      LIMIT 3
    `);
    
    console.log(`📦 Recent batches created: ${recentBatches.length}`);
    recentBatches.forEach(batch => {
      console.log(`   ${batch.id} (${batch.department}) - Received: ${batch.received ? 'YES' : 'NO'}`);
    });
    
    if (notificationReceived) {
      console.log('\n🎉 SUCCESS: Real-time notifications are working!');
      console.log('✅ The issue has been resolved');
      console.log('🔧 The problem was using the wrong API endpoint');
    } else {
      console.log('\n⚠️ ISSUE PERSISTS: No real-time notification received');
      console.log('🔧 Need to check server logs for broadcast debugging');
    }
    
    console.log('\n✅ CORRECT API REAL-TIME TEST COMPLETE');
    
  } catch (error) {
    console.error('❌ Error in correct API test:', error);
  } finally {
    if (auditSocket) {
      auditSocket.disconnect();
    }
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
correctApiRealtimeTest().catch(console.error);
