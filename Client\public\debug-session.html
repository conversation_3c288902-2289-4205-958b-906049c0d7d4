<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMS Session Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        input, select { padding: 8px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 VMS Session Authentication Debug</h1>
    
    <div class="debug-section">
        <h3>1. Check Current Session</h3>
        <button onclick="checkSession()">Check Session ID</button>
        <div id="sessionStatus"></div>
    </div>

    <div class="debug-section">
        <h3>2. Test Login</h3>
        <select id="department">
            <option value="SYSTEM ADMIN">SYSTEM ADMIN</option>
            <option value="AUDIT">AUDIT</option>
            <option value="FINANCE">FINANCE</option>
        </select>
        <input type="text" id="username" placeholder="Username" value="SYSTEM ADMINISTRATOR">
        <input type="password" id="password" placeholder="Password" value="enter123">
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult"></div>
    </div>

    <div class="debug-section">
        <h3>3. Test API Call</h3>
        <button onclick="testApiCall()">Test /api/users</button>
        <div id="apiResult"></div>
    </div>

    <div class="debug-section">
        <h3>4. Debug Logs</h3>
        <div id="debugLogs"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('debugLogs');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logs.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(`[${timestamp}] ${message}`);
        }

        function checkSession() {
            const sessionId = localStorage.getItem('session_id');
            const status = document.getElementById('sessionStatus');
            
            if (sessionId) {
                status.innerHTML = `<div class="success">✅ Session ID found: ${sessionId.substring(0, 8)}...</div>`;
                log(`Session ID found: ${sessionId}`, 'success');
            } else {
                status.innerHTML = `<div class="error">❌ No session ID found</div>`;
                log('No session ID found in localStorage', 'error');
            }
        }

        async function testLogin() {
            const department = document.getElementById('department').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const result = document.getElementById('loginResult');

            log(`Attempting login: ${username} from ${department}`);

            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ department, username, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    result.innerHTML = `<div class="success">✅ Login successful!</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    log(`Login successful for ${data.user?.name}`, 'success');
                    
                    if (data.sessionId) {
                        localStorage.setItem('session_id', data.sessionId);
                        log(`Session ID stored: ${data.sessionId}`, 'success');
                    }
                } else {
                    result.innerHTML = `<div class="error">❌ Login failed</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    log(`Login failed: ${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
                log(`Network error: ${error.message}`, 'error');
            }
        }

        async function testApiCall() {
            const sessionId = localStorage.getItem('session_id');
            const result = document.getElementById('apiResult');

            log(`Testing API call with session ID: ${sessionId ? sessionId.substring(0, 8) + '...' : 'None'}`);

            try {
                const headers = {
                    'Content-Type': 'application/json',
                };

                if (sessionId) {
                    headers['X-Session-ID'] = sessionId;
                    log(`Adding X-Session-ID header: ${sessionId.substring(0, 8)}...`);
                }

                // Use relative URL to go through Vite proxy
                const response = await fetch('/api/users', {
                    method: 'GET',
                    headers: headers
                });

                const data = await response.json();
                
                if (response.ok) {
                    result.innerHTML = `<div class="success">✅ API call successful!</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    log(`API call successful, got ${Array.isArray(data) ? data.length : 'unknown'} users`, 'success');
                } else {
                    result.innerHTML = `<div class="error">❌ API call failed (${response.status})</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    log(`API call failed: ${response.status} - ${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
                log(`Network error: ${error.message}`, 'error');
            }
        }

        // Check session on page load
        window.onload = function() {
            checkSession();
            log('Debug page loaded');
        };
    </script>
</body>
</html>
