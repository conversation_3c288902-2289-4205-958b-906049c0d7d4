import React, { useEffect, useState } from 'react';
import { useAppStore } from '@/lib/store';
import { toast } from 'sonner';

// Simple session management for LAN deployment
const SessionManager: React.FC = () => {
  const { currentUser, logout } = useAppStore();
  const [isOnline, setIsOnline] = useState(true);
  const [sessionWarning, setSessionWarning] = useState(false);
  const [showNetworkError, setShowNetworkError] = useState(false);

  // Session timeout configuration (8 hours for work day)
  const SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // 8 hours
  const WARNING_THRESHOLD = 30 * 60 * 1000; // 30 minutes warning

  // Check network connectivity to server
  useEffect(() => {
    if (!currentUser) return;

    const checkConnection = async () => {
      try {
        const response = await fetch('/health', {
          method: 'HEAD',
          timeout: 3000  // Shorter timeout for LAN
        });
        
        if (response.ok) {
          if (!isOnline) {
            // Connection restored
            toast.success('✅ Connection restored');
            setShowNetworkError(false);
          }
          setIsOnline(true);
        } else {
          if (isOnline) {
            // Connection lost
            toast.error('⚠️ Cannot connect to server');
            setShowNetworkError(true);
          }
          setIsOnline(false);
        }
      } catch (error) {
        if (isOnline) {
          // Connection lost
          toast.error('⚠️ Cannot connect to server');
          setShowNetworkError(true);
        }
        setIsOnline(false);
      }
    };

    // Check connection every 2 minutes (LAN is more stable)
    const connectionInterval = setInterval(checkConnection, 120000);
    
    // Initial check
    checkConnection();

    return () => clearInterval(connectionInterval);
  }, [currentUser]);

  // Simple session timeout management
  useEffect(() => {
    if (!currentUser?.loginTime) return;

    const checkSessionTimeout = () => {
      const now = Date.now();
      const sessionAge = now - currentUser.loginTime;
      const timeLeft = SESSION_TIMEOUT - sessionAge;

      if (timeLeft <= 0) {
        // Session expired - redirect to login with message
        localStorage.setItem('auth_error', 'Your session has expired. Please log in again.');
        logout();
        return;
      }

      if (timeLeft <= WARNING_THRESHOLD && !sessionWarning) {
        // Show warning
        const minutesLeft = Math.floor(timeLeft / (60 * 1000));
        setSessionWarning(true);
        
        toast.warning(
          `Your session will expire in ${minutesLeft} minutes. Save your work!`,
          {
            duration: 10000,
            action: {
              label: 'Extend Session',
              onClick: () => {
                // Simple session extension - just update login time
                useAppStore.setState({
                  currentUser: {
                    ...currentUser,
                    loginTime: Date.now()
                  }
                });
                setSessionWarning(false);
                toast.success('Session extended for 8 more hours');
              }
            }
          }
        );
      }
    };

    // Check session every minute
    const sessionInterval = setInterval(checkSessionTimeout, 60000);
    
    return () => clearInterval(sessionInterval);
  }, [currentUser, sessionWarning, logout]);

  // PRODUCTION FIX: Extended work hours for 24/7 operation
  useEffect(() => {
    if (!currentUser) return;

    const checkWorkHours = () => {
      const now = new Date();
      const hour = now.getHours();

      // PRODUCTION: Allow 24/7 access - only logout during maintenance window
      // Maintenance window: 2 AM to 4 AM (when system is typically idle)
      if (hour >= 2 && hour < 4) {
        toast.info('System maintenance window: Please save your work and log back in after 4 AM');
        // Give users 5 minutes warning before logout
        setTimeout(() => {
          logout();
        }, 5 * 60 * 1000); // 5 minutes delay
        return;
      }

      // For production VMS, allow access during all business hours
      // No automatic logout based on time - only session timeout and inactivity
    };

    // Check maintenance window every hour
    const workHoursInterval = setInterval(checkWorkHours, 60 * 60 * 1000);

    return () => clearInterval(workHoursInterval);
  }, [currentUser, logout]);

  // Auto-dismiss network error notification
  useEffect(() => {
    if (showNetworkError) {
      const timer = setTimeout(() => {
        setShowNetworkError(false);
      }, 5000); // Auto-dismiss after 5 seconds

      return () => clearTimeout(timer);
    }
  }, [showNetworkError]);

  // Show temporary network error notification (auto-dismissing)
  if (currentUser && showNetworkError) {
    return (
      <div className="fixed bottom-4 left-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-40 max-w-sm animate-in slide-in-from-left duration-300">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">
            ⚠️ Network connection lost
          </span>
          <button
            onClick={() => setShowNetworkError(false)}
            className="ml-2 text-white hover:text-gray-200 text-lg leading-none"
            aria-label="Dismiss"
          >
            ×
          </button>
        </div>
      </div>
    );
  }

  return null;
};

export default SessionManager;
