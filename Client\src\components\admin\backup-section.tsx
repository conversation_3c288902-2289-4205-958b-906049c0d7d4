import { useState, useRef, useEffect } from 'react';
import { useAppStore } from '@/lib/store/hooks';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Download, Upload, Trash, Clock, AlertTriangle, Settings } from 'lucide-react';
import { formatStandardDate, formatFileSize } from '@/lib/store/utils';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export function BackupSection() {
  const store = useAppStore();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { lastBackupDate, isPerformingBackup, isRestoringBackup, restoreError } = store;
  const backupError = store.backupError;

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);

  // Backup scheduling state
  const [scheduleEnabled, setScheduleEnabled] = useState(false);
  const [scheduledTime, setScheduledTime] = useState('02:00');
  const [nextBackup, setNextBackup] = useState<string>('');
  const [isLoadingSchedule, setIsLoadingSchedule] = useState(false);

  // Load backup schedule on component mount
  useEffect(() => {
    loadBackupSchedule();
  }, []);

  const loadBackupSchedule = async () => {
    try {
      const response = await fetch('/api/admin/backup-schedule');
      if (response.ok) {
        const data = await response.json();
        setScheduleEnabled(data.isScheduled);
        setScheduledTime(data.scheduledTime || '02:00');
        setNextBackup(data.nextBackup || '');
      }
    } catch (error) {
      console.error('Failed to load backup schedule:', error);
    }
  };

  const handleScheduleChange = async () => {
    setIsLoadingSchedule(true);
    try {
      const response = await fetch('/api/admin/backup-schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          enabled: scheduleEnabled,
          time: scheduledTime
        })
      });

      if (response.ok) {
        const data = await response.json();
        setNextBackup(data.nextBackup || '');
        toast.success(scheduleEnabled ?
          `Backup scheduled for ${scheduledTime} daily` :
          'Backup scheduling disabled'
        );
      } else {
        const error = await response.json();
        toast.error('Failed to update backup schedule', {
          description: error.details || error.error
        });
      }
    } catch (error) {
      toast.error('Failed to update backup schedule');
    } finally {
      setIsLoadingSchedule(false);
    }
  };

  const handleManualBackup = async () => {
    setIsBackingUp(true);
    try {
      const response = await fetch('/api/admin/backup-manual', {
        method: 'POST'
      });

      if (response.ok) {
        toast.success('Manual backup completed successfully');
        await loadBackupSchedule(); // Refresh schedule info
      } else {
        const error = await response.json();
        toast.error('Manual backup failed', {
          description: error.details || error.error
        });
      }
    } catch (error) {
      toast.error('Manual backup failed');
    } finally {
      setIsBackingUp(false);
    }
  };

  const handleBackup = () => {
    setIsBackingUp(true);
    try {
      const success = store.backupSystemData();
      if (success) {
        toast.success('Backup created successfully');
        setIsBackingUp(false);
      } else {
        toast.error('Backup failed', {
          description: backupError || 'An unexpected error occurred'
        });
        setIsBackingUp(false);
      }
    } catch (error) {
      toast.error('Backup failed', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
      setIsBackingUp(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const [isRestoring, setIsRestoring] = useState(false);

  const handleRestore = () => {
    if (!selectedFile) {
      toast.error('Please select a backup file first');
      return;
    }

    // Validate file type
    if (!selectedFile.name.endsWith('.json')) {
      toast.error('Invalid file type', {
        description: 'Please select a JSON backup file'
      });
      return;
    }

    setIsRestoring(true);
    try {
      store.restoreSystemData(selectedFile);
      toast.success('System restore initiated', {
        description: 'The application will reload when the restore is complete'
      });

      // We don't set isRestoring to false here because the page will reload
      // If there's an error, it will be caught and handled below
    } catch (error) {
      toast.error('Restore failed', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
      setIsRestoring(false);
    }
  };

  const [isResetting, setIsResetting] = useState(false);

  const handleReset = () => {
    setIsResetting(true);
    try {
      const success = store.clearLocalStorage();
      if (success) {
        toast.success('System reset initiated');
        setIsResetDialogOpen(false);
        // The page will reload automatically from the clearLocalStorage function
      } else {
        toast.error('System reset failed');
        setIsResetting(false);
        setIsResetDialogOpen(false);
      }
    } catch (error) {
      toast.error('System reset failed', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
      setIsResetting(false);
      setIsResetDialogOpen(false);
    }
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="p-6">
      <div className="mb-4">
        <h2 className="text-2xl font-bold">Backup & Restore</h2>
        <p className="text-muted-foreground">Manage system backups and restore data when needed</p>
      </div>

      <div className="grid gap-6 grid-cols-1 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Download className="mr-2 h-5 w-5" />
              Backup Data
            </CardTitle>
            <CardDescription>Create a backup of all system data</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-muted p-4 rounded-md">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span className="text-sm">Last backup:</span>
                </div>
                <span className="font-medium">
                  {lastBackupDate ? formatStandardDate(new Date(lastBackupDate)) : 'Never'}
                </span>
              </div>
            </div>

            {backupError && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Backup Error</AlertTitle>
                <AlertDescription>
                  {backupError}
                </AlertDescription>
              </Alert>
            )}

            {/* Backup Scheduling Section */}
            <div className="border rounded-md p-4 space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Settings className="h-4 w-4" />
                  <Label htmlFor="schedule-enabled" className="font-medium">
                    Automated Daily Backup
                  </Label>
                </div>
                <Switch
                  id="schedule-enabled"
                  checked={scheduleEnabled}
                  onCheckedChange={(checked) => {
                    setScheduleEnabled(checked);
                    // Auto-save when toggling
                    setTimeout(handleScheduleChange, 100);
                  }}
                  disabled={isLoadingSchedule}
                />
              </div>

              {scheduleEnabled && (
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="backup-time" className="text-sm">
                      Daily backup time:
                    </Label>
                    <Input
                      id="backup-time"
                      type="time"
                      value={scheduledTime}
                      onChange={(e) => setScheduledTime(e.target.value)}
                      onBlur={handleScheduleChange}
                      className="w-32"
                      disabled={isLoadingSchedule}
                    />
                  </div>

                  {nextBackup && nextBackup !== 'Not scheduled' && (
                    <div className="text-sm text-muted-foreground">
                      <Clock className="h-3 w-3 inline mr-1" />
                      Next backup: {nextBackup}
                    </div>
                  )}
                </div>
              )}
            </div>

            <p className="text-sm text-muted-foreground">
              Backups include all system data including vouchers, users, settings, and more.
              Store your backup files in a secure location.
            </p>
          </CardContent>
          <CardFooter className="flex gap-2">
            <Button
              onClick={handleBackup}
              disabled={isBackingUp || isPerformingBackup}
              className="flex-1"
              variant="outline"
            >
              {isBackingUp || isPerformingBackup ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Download Backup
                </>
              )}
            </Button>

            <Button
              onClick={handleManualBackup}
              disabled={isBackingUp || isPerformingBackup}
              className="flex-1"
            >
              {isBackingUp || isPerformingBackup ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                  Backing up...
                </>
              ) : (
                <>
                  <Clock className="mr-2 h-4 w-4" />
                  Manual Backup
                </>
              )}
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="mr-2 h-5 w-5" />
              Restore Data
            </CardTitle>
            <CardDescription>Restore system from a previous backup</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="border-2 border-dashed rounded-md p-4 text-center cursor-pointer hover:bg-muted/50 transition-colors" onClick={triggerFileInput}>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept=".json"
                className="hidden"
              />
              <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm font-medium">
                {selectedFile ? selectedFile.name : 'Click to select backup file'}
              </p>
              {selectedFile && (
                <p className="text-xs text-muted-foreground mt-1">
                  {formatFileSize(selectedFile.size)} • Modified {formatStandardDate(new Date(selectedFile.lastModified))}
                </p>
              )}
            </div>

            {restoreError && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Restore Error</AlertTitle>
                <AlertDescription>
                  {restoreError}
                </AlertDescription>
              </Alert>
            )}

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                Restoring from backup will replace all current data and cannot be undone.
                The system will reload after restoration is complete.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter className="flex flex-col gap-2">
            <Button
              onClick={handleRestore}
              disabled={!selectedFile || isRestoring || isRestoringBackup}
              className="w-full"
            >
              {isRestoring || isRestoringBackup ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                  Restoring Backup...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Restore From Backup
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>

      <div className="mt-8">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive flex items-center">
              <Trash className="mr-2 h-5 w-5" />
              Reset System
            </CardTitle>
            <CardDescription>Clear all data and reset the system to default</CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Danger Zone</AlertTitle>
              <AlertDescription>
                This will permanently delete all vouchers, user accounts, settings, and other data.
                This action cannot be undone.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Dialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive" className="w-full">
                  <Trash className="mr-2 h-4 w-4" />
                  Reset System
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle className="text-destructive">Reset System Confirmation</DialogTitle>
                  <DialogDescription>
                    This action will permanently delete all data in the system. This cannot be undone.
                  </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <p className="text-sm font-medium">Are you absolutely sure you want to:</p>
                  <ul className="list-disc pl-5 mt-2 text-sm text-muted-foreground space-y-1">
                    <li>Delete all vouchers and records</li>
                    <li>Remove all user accounts</li>
                    <li>Reset all system settings</li>
                    <li>Clear all audit logs and history</li>
                  </ul>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsResetDialogOpen(false)} disabled={isResetting}>Cancel</Button>
                  <Button variant="destructive" onClick={handleReset} disabled={isResetting}>
                    {isResetting ? (
                      <>
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                        Resetting...
                      </>
                    ) : (
                      'Yes, Reset Everything'
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}