import { useState, useEffect } from 'react';
import { Clock, Globe, Server } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

export function LiveSystemTime() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [serverTime, setServerTime] = useState<Date | null>(null);

  // Update local time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Fetch server time every 30 seconds to stay in sync
  useEffect(() => {
    const fetchServerTime = async () => {
      try {
        const response = await fetch('/api/health', { credentials: 'include' });
        if (response.ok) {
          const data = await response.json();
          setServerTime(new Date(data.timestamp));
        }
      } catch (error) {
        console.error('Failed to fetch server time:', error);
      }
    };

    // Initial fetch
    fetchServerTime();

    // Update every 30 seconds
    const serverTimer = setInterval(fetchServerTime, 30000);

    return () => clearInterval(serverTimer);
  }, []);

  const formatDateTime = (date: Date, timezone: string = 'Africa/Accra') => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

    // Convert to Ghana timezone
    const ghanaDate = new Date(date.toLocaleString("en-US", {timeZone: timezone}));

    const dayName = days[ghanaDate.getDay()];
    const day = ghanaDate.getDate().toString().padStart(2, '0');
    const month = months[ghanaDate.getMonth()];
    const year = ghanaDate.getFullYear();

    let hours = ghanaDate.getHours();
    const minutes = ghanaDate.getMinutes().toString().padStart(2, '0');
    const seconds = ghanaDate.getSeconds().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12; // 0 should be 12
    const formattedHours = hours.toString().padStart(2, '0');

    return `${dayName}, ${day}-${month}-${year} ${formattedHours}:${minutes}:${seconds} ${ampm}`;
  };

  const formatUTC = (date: Date) => {
    return date.toISOString().replace('T', ' ').substring(0, 19) + ' UTC';
  };

  return (
    <Card className="bg-gradient-to-r from-slate-800 to-slate-900 border-slate-700 text-white">
      <CardContent className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Ghana Time */}
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-600 rounded-full">
              <Clock className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="text-xs font-medium text-green-400 uppercase tracking-wide">Ghana Time</p>
              <p className="text-sm font-mono font-semibold text-white">
                {formatDateTime(currentTime, 'Africa/Accra')}
              </p>
            </div>
          </div>

          {/* Server Time */}
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-600 rounded-full">
              <Server className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="text-xs font-medium text-blue-400 uppercase tracking-wide">Server Time</p>
              <p className="text-sm font-mono font-semibold text-white">
                {serverTime ? formatDateTime(serverTime, 'Africa/Accra') : 'Loading...'}
              </p>
            </div>
          </div>

          {/* UTC Time */}
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-600 rounded-full">
              <Globe className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="text-xs font-medium text-purple-400 uppercase tracking-wide">UTC Time</p>
              <p className="text-sm font-mono font-semibold text-white">
                {formatUTC(currentTime)}
              </p>
            </div>
          </div>
        </div>

        {/* Time Zone Info */}
        <div className="mt-3 pt-3 border-t border-slate-600">
          <div className="flex items-center justify-between text-xs text-slate-300">
            <span>🇬🇭 Ghana Standard Time (GMT+0)</span>
            <span className="flex items-center">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
              Live Updates
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
