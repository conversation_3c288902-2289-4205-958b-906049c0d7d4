import { useState, useEffect } from 'react';
import { useAppStore } from '@/lib/store/hooks';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { createManagedInterval, clearManagedInterval } from '@/lib/interval-manager';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, CheckCircle, XCircle, Clock, Lock } from 'lucide-react';
import { PendingRegistration, PasswordChangeRequest } from '@/lib/store/types';
import { formatStandardDate } from '@/lib/store/utils';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export function PendingRegistrationsSection() {
  const store = useAppStore();
  const { pendingRegistrations, passwordChangeRequests, fetchPendingRegistrations, fetchPasswordChangeRequests } = store;

  // Refresh data when component mounts and periodically
  useEffect(() => {
    // Fetch immediately
    fetchPendingRegistrations();
    fetchPasswordChangeRequests();

    // Set up interval to refresh every 5 seconds
    const intervalId = createManagedInterval(() => {
      fetchPendingRegistrations();
      fetchPasswordChangeRequests();
    }, 5000);

    // Clean up interval on unmount
    return () => clearManagedInterval(intervalId);
  }, [fetchPendingRegistrations, fetchPasswordChangeRequests]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRegistration, setSelectedRegistration] = useState<PendingRegistration | null>(null);
  const [selectedPasswordRequest, setSelectedPasswordRequest] = useState<PasswordChangeRequest | null>(null);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'registrations' | 'passwords'>('registrations');
  const [isProcessing, setIsProcessing] = useState(false);

  // Filtered registrations based on search term (with safety check)
  const filteredRegistrations = (pendingRegistrations || []).filter(reg =>
    reg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    reg.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filtered password requests based on search term (with safety check)
  const filteredPasswordRequests = (passwordChangeRequests || []).filter(req =>
    req.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    req.user_department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleApproveRegistration = (registration: PendingRegistration) => {
    setSelectedRegistration(registration);
    setIsApproveDialogOpen(true);
  };

  const handleRejectRegistration = (registration: PendingRegistration) => {
    setSelectedRegistration(registration);
    setSelectedPasswordRequest(null);
    setIsRejectDialogOpen(true);
  };

  const handleApprovePasswordRequest = (request: PasswordChangeRequest) => {
    setSelectedPasswordRequest(request);
    setSelectedRegistration(null);
    setIsApproveDialogOpen(true);
  };

  const handleRejectPasswordRequest = (request: PasswordChangeRequest) => {
    setSelectedPasswordRequest(request);
    setSelectedRegistration(null);
    setIsRejectDialogOpen(true);
  };

  const confirmApproval = async () => {
    setIsProcessing(true);
    try {
      if (selectedRegistration) {
        const success = await store.approvePendingUser(selectedRegistration.id);
        if (success) {
          toast.success(`Registration for ${selectedRegistration.name} approved successfully`);
          setSelectedRegistration(null);
          // Data will be updated automatically by the store
        } else {
          throw new Error('Failed to approve registration');
        }
      } else if (selectedPasswordRequest) {
        const success = await store.approvePasswordChangeRequest(selectedPasswordRequest.id);
        if (success) {
          toast.success(`Password change for ${selectedPasswordRequest.user_name} approved successfully`);
          setSelectedPasswordRequest(null);
          // Data will be updated automatically by the store
        } else {
          throw new Error('Failed to approve password change');
        }
      }

      setIsApproveDialogOpen(false);
    } catch (error) {
      toast.error('Failed to approve request', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const confirmRejection = async () => {
    setIsProcessing(true);
    try {
      if (selectedRegistration) {
        const success = await store.rejectPendingUser(selectedRegistration.id);
        if (success) {
          toast.success(`Registration for ${selectedRegistration.name} rejected`);
          setSelectedRegistration(null);
          // Data will be updated automatically by the store
        } else {
          throw new Error('Failed to reject registration');
        }
      } else if (selectedPasswordRequest) {
        const success = await store.rejectPasswordChangeRequest(selectedPasswordRequest.id, 'Rejected by administrator');
        if (success) {
          toast.success(`Password change for ${selectedPasswordRequest.user_name} rejected`);
          setSelectedPasswordRequest(null);
          // Data will be updated automatically by the store
        } else {
          throw new Error('Failed to reject password change');
        }
      }

      setIsRejectDialogOpen(false);
    } catch (error) {
      toast.error('Failed to reject request', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-4">
        <h2 className="text-2xl font-bold">Pending Requests</h2>
        <p className="text-muted-foreground">Approve or reject user registrations and password change requests</p>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'registrations' | 'passwords')} className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList className="grid w-auto grid-cols-2">
            <TabsTrigger value="registrations" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Registrations ({filteredRegistrations.length})
            </TabsTrigger>
            <TabsTrigger value="passwords" className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              Password Changes ({filteredPasswordRequests.length})
            </TabsTrigger>
          </TabsList>

          <div className="relative w-72">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={activeTab === 'registrations' ? "Search registrations..." : "Search password requests..."}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        <TabsContent value="registrations">
          <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>

                <TableHead>Department</TableHead>
                <TableHead>Date Requested</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRegistrations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-6 text-muted-foreground">
                    {searchTerm ? "No registrations matching your search" : "No pending registrations"}
                  </TableCell>
                </TableRow>
              ) : (
                filteredRegistrations.map((registration) => (
                  <TableRow key={registration.id}>
                    <TableCell className="font-medium">{registration.name}</TableCell>

                    <TableCell>{registration.department}</TableCell>
                    <TableCell className="text-muted-foreground">
                      <div className="flex items-center">
                        <Clock className="mr-1 h-3 w-3" />
                        <span>
                          {registration.dateRequested
                            ? formatStandardDate(new Date(registration.dateRequested))
                            : 'Date not available'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center text-green-600"
                          onClick={() => handleApproveRegistration(registration)}
                        >
                          <CheckCircle className="mr-1 h-4 w-4" />
                          Approve
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center text-red-600"
                          onClick={() => handleRejectRegistration(registration)}
                        >
                          <XCircle className="mr-1 h-4 w-4" />
                          Reject
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
        </TabsContent>

        <TabsContent value="passwords">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Date Requested</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPasswordRequests.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-6 text-muted-foreground">
                        {searchTerm ? "No password requests matching your search" : "No pending password change requests"}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredPasswordRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell className="font-medium">{request.user_name}</TableCell>
                        <TableCell>{request.user_department}</TableCell>
                        <TableCell className="text-muted-foreground">
                          <div className="flex items-center">
                            <Clock className="mr-1 h-3 w-3" />
                            <span>
                              {formatStandardDate(new Date(request.requested_at))}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center text-green-600"
                              onClick={() => handleApprovePasswordRequest(request)}
                            >
                              <CheckCircle className="mr-1 h-4 w-4" />
                              Approve
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center text-red-600"
                              onClick={() => handleRejectPasswordRequest(request)}
                            >
                              <XCircle className="mr-1 h-4 w-4" />
                              Reject
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Approve Dialog */}
      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {selectedRegistration ? 'Approve Registration' : 'Approve Password Change'}
            </DialogTitle>
            <DialogDescription>
              {selectedRegistration
                ? `Are you sure you want to approve the registration for ${selectedRegistration.name}?`
                : selectedPasswordRequest
                ? `Are you sure you want to approve the password change for ${selectedPasswordRequest.user_name}?`
                : 'Approve request'
              }
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedRegistration && (
              <div className="space-y-2">
                <div>
                  <span className="font-semibold">Name:</span> {selectedRegistration.name}
                </div>
                <div>
                  <span className="font-semibold">Department:</span> {selectedRegistration.department}
                </div>
              </div>
            )}
            {selectedPasswordRequest && (
              <div className="space-y-2">
                <div>
                  <span className="font-semibold">User:</span> {selectedPasswordRequest.user_name}
                </div>
                <div>
                  <span className="font-semibold">Department:</span> {selectedPasswordRequest.user_department}
                </div>

                <div>
                  <span className="font-semibold">Requested:</span> {formatStandardDate(new Date(selectedPasswordRequest.requested_at))}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsApproveDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmApproval}
              className="bg-green-600 hover:bg-green-700"
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Approve'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {selectedRegistration ? 'Reject Registration' : 'Reject Password Change'}
            </DialogTitle>
            <DialogDescription>
              {selectedRegistration
                ? `Are you sure you want to reject the registration for ${selectedRegistration.name}?`
                : selectedPasswordRequest
                ? `Are you sure you want to reject the password change for ${selectedPasswordRequest.user_name}?`
                : 'Reject request'
              }
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedRegistration && (
              <div className="space-y-2">
                <div>
                  <span className="font-semibold">Name:</span> {selectedRegistration.name}
                </div>

                <div>
                  <span className="font-semibold">Department:</span> {selectedRegistration.department}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRejectDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmRejection}
              variant="destructive"
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Reject'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
