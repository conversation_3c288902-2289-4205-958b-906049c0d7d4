import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Download } from 'lucide-react';
import { formatNumberWithCommas } from '@/utils/formatUtils';

interface MonthlySavingsDataPoint {
  month: string;
  savings: number;
  percentage: number;
  originalAmount: number;
  certifiedAmount: number;
  currency: string;
}

interface DepartmentSavingsDataPoint {
  name: string;
  savings: number;
  percentage: number;
  originalAmount: number;
  certifiedAmount: number;
  currency: string;
  monthlySavings: MonthlySavingsDataPoint[];
  yearlySavings: {
    savings: number;
    percentage: number;
    originalAmount: number;
    certifiedAmount: number;
    currency: string;
  };
  currencySummary?: {
    [currency: string]: {
      savings: number;
      originalAmount: number;
      certifiedAmount: number;
    };
  };
}

interface SavingsData {
  departmental: DepartmentSavingsDataPoint[];
  totalSavings: number;
  savingsPercentage: number;
  defaultCurrency: string;
  currencySummary: {
    [currency: string]: {
      totalSavings: number;
      totalOriginalAmount: number;
      totalCertifiedAmount: number;
    };
  };
}

interface PreAuditSavingsTableProps {
  data: SavingsData;
}

export function PreAuditSavingsTable({ data }: PreAuditSavingsTableProps) {
  const [selectedDepartment, setSelectedDepartment] = useState<string | null>(null);
  const [selectedView, setSelectedView] = useState<'monthly' | 'yearly'>('yearly');

  // Get the selected department data
  const selectedDepartmentData = selectedDepartment
    ? data.departmental.find(dept => dept.name === selectedDepartment)
    : null;

  // Function to export data to Excel
  const handleExport = () => {
    // Determine what data to export based on current view
    let exportData;
    let filename;

    if (selectedDepartment) {
      if (selectedView === 'monthly' && selectedDepartmentData) {
        // Export monthly data for the selected department
        exportData = selectedDepartmentData.monthlySavings;
        filename = `${selectedDepartment.toLowerCase()}-monthly-savings-${new Date().toISOString().split('T')[0]}`;

        // Create CSV content
        const headers = ['Month', 'Original Amount', 'Certified Amount', 'Savings', 'Currency', 'Savings %'];
        const rows = exportData.map(item => [
          item.month,
          item.originalAmount.toString(),
          item.certifiedAmount.toString(),
          item.savings.toString(),
          item.currency,
          (item.percentage || 0).toFixed(2)
        ]);

        const csvContent = [
          headers.join(','),
          ...rows.map(row => row.join(','))
        ].join('\n');

        // Create and trigger download
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `${filename}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else if (selectedDepartmentData) {
        // Export yearly data for the selected department
        const yearlyData = [{
          name: selectedDepartment,
          ...selectedDepartmentData.yearlySavings
        }];

        filename = `${selectedDepartment.toLowerCase()}-yearly-savings-${new Date().toISOString().split('T')[0]}`;

        // Create CSV content
        const headers = ['Department', 'Original Amount', 'Certified Amount', 'Savings', 'Currency', 'Savings %'];
        const rows = yearlyData.map(item => [
          item.name,
          item.originalAmount.toString(),
          item.certifiedAmount.toString(),
          item.savings.toString(),
          item.currency,
          (item.percentage || 0).toFixed(2)
        ]);

        const csvContent = [
          headers.join(','),
          ...rows.map(row => row.join(','))
        ].join('\n');

        // Create and trigger download
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', `${filename}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } else {
      // Export all departments data
      exportData = data.departmental;
      filename = `all-departments-savings-${new Date().toISOString().split('T')[0]}`;

      // Create CSV content
      const headers = ['Department', 'Original Amount', 'Certified Amount', 'Savings', 'Currency', 'Savings %'];
      const rows = exportData.map(item => [
        item.name,
        item.originalAmount.toString(),
        item.certifiedAmount.toString(),
        item.savings.toString(),
        item.currency,
        (item.percentage || 0).toFixed(2)
      ]);

      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n');

      // Create and trigger download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Render department list
  const renderDepartmentList = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <div className="text-2xl font-bold">
            {data.defaultCurrency} {formatNumberWithCommas(data.totalSavings)}
          </div>
          <p className="text-sm text-muted-foreground">
            Total savings ({(data.savingsPercentage || 0).toFixed(1)}% of original amounts)
          </p>
          {Object.entries(data.currencySummary).length > 1 && (
            <div className="text-xs text-muted-foreground mt-1">
              {Object.entries(data.currencySummary).map(([currency, summary]) => (
                <div key={currency}>
                  {currency}: {formatNumberWithCommas(summary.totalSavings)} (from {formatNumberWithCommas(summary.totalOriginalAmount)})
                </div>
              ))}
            </div>
          )}
        </div>

        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          onClick={handleExport}
        >
          <Download className="h-4 w-4" />
          Export All
        </Button>
      </div>

      <div className="border rounded-md overflow-x-auto">
        <Table className="w-full">
          <TableHeader className="sticky top-0 bg-background z-10">
            <TableRow>
              <TableHead className="w-[20%]">Department</TableHead>
              <TableHead className="w-[20%] text-right">Original Amount ({data.defaultCurrency})</TableHead>
              <TableHead className="w-[20%] text-right">Certified Amount ({data.defaultCurrency})</TableHead>
              <TableHead className="w-[15%] text-right">Savings ({data.defaultCurrency})</TableHead>
              <TableHead className="w-[10%] text-right">Currency</TableHead>
              <TableHead className="w-[15%] text-right">Savings %</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.departmental.map((dept) => (
              <TableRow
                key={dept.name}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => setSelectedDepartment(dept.name)}
              >
                <TableCell className="font-medium">{dept.name}</TableCell>
                <TableCell className="text-right">{formatNumberWithCommas(dept.originalAmount || dept.totalOriginalAmount || 0)}</TableCell>
                <TableCell className="text-right">{formatNumberWithCommas(dept.certifiedAmount || dept.totalCertifiedAmount || 0)}</TableCell>
                <TableCell className="text-right font-medium text-green-600">{formatNumberWithCommas(dept.savings || dept.totalSavings || 0)}</TableCell>
                <TableCell className="text-right">{dept.currency || 'GHS'}</TableCell>
                <TableCell className="text-right">{(dept.percentage || dept.savingsPercentage || 0).toFixed(1)}%</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );

  // Render department detail
  const renderDepartmentDetail = () => {
    if (!selectedDepartmentData) return null;

    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedDepartment(null)}
            >
              Back to Departments
            </Button>
            <h3 className="text-lg font-medium">{selectedDepartment} Pre-audit Savings</h3>
          </div>

          <div className="flex items-center gap-4">
            <Tabs value={selectedView} onValueChange={(v) => setSelectedView(v as any)} className="w-[250px]">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="yearly">Yearly</TabsTrigger>
                <TabsTrigger value="monthly">Monthly</TabsTrigger>
              </TabsList>
            </Tabs>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              onClick={handleExport}
            >
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        {selectedView === 'yearly' ? (
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 border rounded-md">
              <div className="text-sm text-muted-foreground">Original Amount</div>
              <div className="text-2xl font-bold">
                {selectedDepartmentData.currency || 'GHS'} {formatNumberWithCommas(selectedDepartmentData.originalAmount || selectedDepartmentData.totalOriginalAmount || 0)}
              </div>
            </div>
            <div className="p-4 border rounded-md">
              <div className="text-sm text-muted-foreground">Certified Amount</div>
              <div className="text-2xl font-bold">
                {selectedDepartmentData.currency || 'GHS'} {formatNumberWithCommas(selectedDepartmentData.certifiedAmount || 0)}
              </div>
            </div>
            <div className="p-4 border rounded-md">
              <div className="text-sm text-muted-foreground">Total Savings</div>
              <div className="text-2xl font-bold text-green-600">
                {selectedDepartmentData.currency || 'GHS'} {formatNumberWithCommas(selectedDepartmentData.totalSavings || selectedDepartmentData.savings || 0)}
              </div>
            </div>
            <div className="p-4 border rounded-md">
              <div className="text-sm text-muted-foreground">Savings Percentage</div>
              <div className="text-2xl font-bold text-green-600">
                {(selectedDepartmentData.percentage || selectedDepartmentData.savingsPercentage || 0).toFixed(1)}%
              </div>
            </div>
          </div>
        ) : (
          <div className="border rounded-md overflow-x-auto">
            <Table className="w-full">
              <TableHeader className="sticky top-0 bg-background z-10">
                <TableRow>
                  <TableHead className="w-[15%]">Month</TableHead>
                  <TableHead className="w-[20%] text-right">Original Amount</TableHead>
                  <TableHead className="w-[20%] text-right">Certified Amount</TableHead>
                  <TableHead className="w-[15%] text-right">Savings</TableHead>
                  <TableHead className="w-[10%] text-right">Currency</TableHead>
                  <TableHead className="w-[15%] text-right">Savings %</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(selectedDepartmentData.monthlySavings || []).map((month) => (
                  <TableRow key={month.month}>
                    <TableCell className="font-medium">{month.month}</TableCell>
                    <TableCell className="text-right">{formatNumberWithCommas(month.originalAmount || 0)}</TableCell>
                    <TableCell className="text-right">{formatNumberWithCommas(month.certifiedAmount || 0)}</TableCell>
                    <TableCell className="text-right font-medium text-green-600">{formatNumberWithCommas(month.savings || 0)}</TableCell>
                    <TableCell className="text-right">{month.currency || 'GHS'}</TableCell>
                    <TableCell className="text-right">{(month.percentage || 0).toFixed(1)}%</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {selectedDepartment ? renderDepartmentDetail() : renderDepartmentList()}
    </div>
  );
}
