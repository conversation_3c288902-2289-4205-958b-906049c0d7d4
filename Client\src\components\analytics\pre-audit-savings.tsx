import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatNumberWithCommas } from '@/utils/formatUtils';

interface SavingsDataPoint {
  name: string;
  savings: number;
  percentage: number;
  originalAmount: number;
  certifiedAmount: number;
}

interface SavingsData {
  monthly: SavingsDataPoint[];
  quarterly: SavingsDataPoint[];
  departmental: SavingsDataPoint[];
  totalSavings: number;
  savingsPercentage: number;
}

interface PreAuditSavingsProps {
  data: SavingsData;
}

export function PreAuditSavings({ data }: PreAuditSavingsProps) {
  const [view, setView] = useState<'monthly' | 'quarterly' | 'departmental'>('monthly');
  
  const viewData = {
    monthly: data.monthly,
    quarterly: data.quarterly,
    departmental: data.departmental
  };
  
  const currentData = viewData[view];
  
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Card className="bg-background border-border p-2">
          <CardContent className="p-2">
            <p className="font-bold">{label}</p>
            <p className="text-sm">
              Original Amount: GHS {formatNumberWithCommas(payload[0].payload.originalAmount)}
            </p>
            <p className="text-sm">
              Certified Amount: GHS {formatNumberWithCommas(payload[0].payload.certifiedAmount)}
            </p>
            <p className="text-sm text-green-500 font-semibold">
              Savings: GHS {formatNumberWithCommas(payload[0].payload.savings)} ({payload[0].payload.percentage.toFixed(1)}%)
            </p>
          </CardContent>
        </Card>
      );
    }
    return null;
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <div className="text-2xl font-bold">
            GHS {formatNumberWithCommas(data.totalSavings)}
          </div>
          <p className="text-sm text-muted-foreground">
            Total savings ({data.savingsPercentage.toFixed(1)}% of original amounts)
          </p>
        </div>
        
        <Tabs value={view} onValueChange={(v) => setView(v as any)} className="w-[400px]">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="quarterly">Quarterly</TabsTrigger>
            <TabsTrigger value="departmental">By Department</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={currentData}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis 
              yAxisId="left"
              orientation="left"
              tickFormatter={(value) => `GHS ${formatNumberWithCommas(value)}`}
            />
            <YAxis 
              yAxisId="right"
              orientation="right"
              tickFormatter={(value) => `${value}%`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar 
              yAxisId="left"
              dataKey="savings" 
              name="Savings Amount" 
              fill="#10b981" 
              radius={[4, 4, 0, 0]}
            />
            <Bar 
              yAxisId="right"
              dataKey="percentage" 
              name="Savings Percentage" 
              fill="#6366f1" 
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
