import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowDownIcon, ArrowRightIcon, ArrowUpIcon, ClockIcon, CheckCircleIcon, XCircleIcon, BanknoteIcon } from 'lucide-react';
import { formatNumberWithCommas } from '@/utils/formatUtils';

interface SummaryCardProps {
  title: string;
  value: string | number;
  description: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  icon: React.ReactNode;
}

function SummaryCard({ title, value, description, trend, trendValue, icon }: SummaryCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-4 w-4 text-muted-foreground">{icon}</div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {trend && trendValue && (
          <div className="flex items-center pt-1">
            {trend === 'up' ? (
              <ArrowUpIcon className="h-3 w-3 text-green-500 mr-1" />
            ) : trend === 'down' ? (
              <ArrowDownIcon className="h-3 w-3 text-red-500 mr-1" />
            ) : (
              <ArrowRightIcon className="h-3 w-3 text-yellow-500 mr-1" />
            )}
            <span className={`text-xs ${
              trend === 'up' ? 'text-green-500' : 
              trend === 'down' ? 'text-red-500' : 
              'text-yellow-500'
            }`}>
              {trendValue}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface SummaryData {
  totalVouchers: number;
  vouchersTrend: 'up' | 'down' | 'neutral';
  vouchersTrendValue: string;
  
  avgProcessingTime: number;
  processingTimeTrend: 'up' | 'down' | 'neutral';
  processingTimeTrendValue: string;
  
  certificationRate: number;
  certificationRateTrend: 'up' | 'down' | 'neutral';
  certificationRateTrendValue: string;
  
  totalValue: number;
  totalValueTrend: 'up' | 'down' | 'neutral';
  totalValueTrendValue: string;
  
  pendingVouchers: number;
  pendingVouchersTrend: 'up' | 'down' | 'neutral';
  pendingVouchersTrendValue: string;
}

interface SummaryCardsProps {
  data: SummaryData;
}

export function SummaryCards({ data }: SummaryCardsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
      <SummaryCard
        title="Total Vouchers"
        value={data.totalVouchers}
        description="Vouchers processed this period"
        trend={data.vouchersTrend}
        trendValue={data.vouchersTrendValue}
        icon={<CheckCircleIcon className="h-4 w-4" />}
      />
      
      <SummaryCard
        title="Avg. Processing Time"
        value={`${data.avgProcessingTime} hrs`}
        description="From receipt to certification"
        trend={data.processingTimeTrend}
        trendValue={data.processingTimeTrendValue}
        icon={<ClockIcon className="h-4 w-4" />}
      />
      
      <SummaryCard
        title="Certification Rate"
        value={`${data.certificationRate}%`}
        description="Vouchers certified vs. rejected"
        trend={data.certificationRateTrend}
        trendValue={data.certificationRateTrendValue}
        icon={<CheckCircleIcon className="h-4 w-4" />}
      />
      
      <SummaryCard
        title="Total Value"
        value={`GHS ${formatNumberWithCommas(data.totalValue)}`}
        description="Value of processed vouchers"
        trend={data.totalValueTrend}
        trendValue={data.totalValueTrendValue}
        icon={<BanknoteIcon className="h-4 w-4" />}
      />
      
      <SummaryCard
        title="Pending Vouchers"
        value={data.pendingVouchers}
        description="Vouchers awaiting processing"
        trend={data.pendingVouchersTrend}
        trendValue={data.pendingVouchersTrendValue}
        icon={<ClockIcon className="h-4 w-4" />}
      />
    </div>
  );
}
