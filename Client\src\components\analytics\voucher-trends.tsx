import { useState } from 'react';
import { Area, AreaChart, CartesianGrid, Legend, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { formatNumberWithCommas } from '@/utils/formatUtils';

interface TrendDataPoint {
  date: string;
  value: number;
  [key: string]: any;
}

interface VoucherTrendsData {
  volume: TrendDataPoint[];
  efficiency: TrendDataPoint[];
  financialFlow: TrendDataPoint[];
  certificationRates: TrendDataPoint[];
}

interface VoucherTrendsProps {
  data: VoucherTrendsData;
}

export function VoucherTrends({ data }: VoucherTrendsProps) {
  const [trend, setTrend] = useState<'volume' | 'efficiency' | 'financialFlow' | 'certificationRates'>('volume');
  
  const trendConfig = {
    volume: {
      data: data.volume,
      title: 'Voucher Volume',
      yAxisLabel: 'Count',
      valueFormatter: (value: number) => value.toString(),
      chartType: 'line' as const
    },
    efficiency: {
      data: data.efficiency,
      title: 'Processing Efficiency',
      yAxisLabel: 'Hours',
      valueFormatter: (value: number) => `${value} hrs`,
      chartType: 'line' as const
    },
    financialFlow: {
      data: data.financialFlow,
      title: 'Financial Flow',
      yAxisLabel: 'Amount (GHS)',
      valueFormatter: (value: number) => `GHS ${formatNumberWithCommas(value)}`,
      chartType: 'area' as const
    },
    certificationRates: {
      data: data.certificationRates,
      title: 'Certification vs. Rejection Rates',
      yAxisLabel: 'Percentage',
      valueFormatter: (value: number) => `${value}%`,
      chartType: 'area' as const,
      isStacked: true
    }
  };
  
  const currentTrend = trendConfig[trend];
  
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Card className="bg-background border-border p-2">
          <CardContent className="p-2">
            <p className="font-bold">{label}</p>
            {payload.map((entry: any, index: number) => (
              <p key={`item-${index}`} className="text-sm" style={{ color: entry.color }}>
                {entry.name}: {
                  trend === 'financialFlow' 
                    ? `GHS ${formatNumberWithCommas(entry.value)}`
                    : trend === 'efficiency'
                      ? `${entry.value} hrs`
                      : trend === 'certificationRates'
                        ? `${entry.value}%`
                        : entry.value
                }
              </p>
            ))}
          </CardContent>
        </Card>
      );
    }
    return null;
  };

  return (
    <div className="space-y-4">
      <Tabs value={trend} onValueChange={(v) => setTrend(v as any)} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="volume">Volume</TabsTrigger>
          <TabsTrigger value="efficiency">Efficiency</TabsTrigger>
          <TabsTrigger value="financialFlow">Financial</TabsTrigger>
          <TabsTrigger value="certificationRates">Certification</TabsTrigger>
        </TabsList>
      </Tabs>
      
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          {currentTrend.chartType === 'line' ? (
            <LineChart
              data={currentTrend.data}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis 
                label={{ value: currentTrend.yAxisLabel, angle: -90, position: 'insideLeft' }}
                tickFormatter={
                  trend === 'financialFlow' 
                    ? (value) => `${formatNumberWithCommas(value)}`
                    : undefined
                }
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="value" 
                name={currentTrend.title} 
                stroke="#6366f1" 
                activeDot={{ r: 8 }} 
              />
            </LineChart>
          ) : (
            <AreaChart
              data={currentTrend.data}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis 
                label={{ value: currentTrend.yAxisLabel, angle: -90, position: 'insideLeft' }}
                tickFormatter={
                  trend === 'financialFlow' 
                    ? (value) => `${formatNumberWithCommas(value)}`
                    : undefined
                }
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              {currentTrend.isStacked ? (
                <>
                  <Area 
                    type="monotone" 
                    dataKey="certified" 
                    name="Certified" 
                    stackId="1"
                    stroke="#10b981" 
                    fill="#10b981" 
                  />
                  <Area 
                    type="monotone" 
                    dataKey="rejected" 
                    name="Rejected" 
                    stackId="1"
                    stroke="#ef4444" 
                    fill="#ef4444" 
                  />
                </>
              ) : (
                <Area 
                  type="monotone" 
                  dataKey="value" 
                  name={currentTrend.title} 
                  stroke="#6366f1" 
                  fill="#6366f1" 
                />
              )}
            </AreaChart>
          )}
        </ResponsiveContainer>
      </div>
    </div>
  );
}
