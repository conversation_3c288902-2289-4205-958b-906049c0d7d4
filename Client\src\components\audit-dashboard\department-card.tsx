import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileCheck, Lock, Unlock } from 'lucide-react';
import { DepartmentCardProps } from './types';
import { useAppStore } from '@/lib/store';
import { useResourceLock } from '@/hooks/use-resource-lock';
import { useState } from 'react';
import { toast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';

export function DepartmentCard({ dept, onSelectDepartment, onShowProvisionalCash }: DepartmentCardProps) {
  const vouchers = useAppStore((state) => state.vouchers);
  const currentUser = useAppStore((state) => state.currentUser);

  // CRITICAL FIX: Count vouchers that originally came from this department and are now being processed by audit
  const pendingVouchers = vouchers.filter(v =>
    v.originalDepartment === dept &&
    v.department === "AUDIT" &&
    v.status === "AUDIT: PROCESSING" &&
    v.receivedBy &&
    v.receivedByAudit === true &&
    // Only count vouchers that haven't been worked on yet (NEW VOUCHERS)
    !v.preAuditedAmount &&
    !v.preAuditedBy &&
    !v.certifiedBy &&
    !v.dispatched &&
    !v.pendingReturn &&
    !v.isReturned &&
    !v.workStarted
  ).length;

  // Use the resource lock hook for this department hub
  const {
    isLocked,
    isLockOwner,
    lockOwnerName,
    acquireLock,
    releaseLock,
    isViewing
  } = useResourceLock(
    'voucher-hub',
    dept,
    {
      autoRelease: true,
      onLockAcquired: () => {
        toast({
          title: "Editor Rights Acquired",
          description: `You are now editing ${dept} vouchers.`,
          variant: "default",
        });
      },
      onLockReleased: () => {
        toast({
          title: "Editor Rights Released",
          description: `You are no longer editing ${dept} vouchers.`,
          variant: "default",
        });
      },
      onLockFailed: () => {
        toast({
          title: "Failed to Acquire Editor Rights",
          description: "Another user may already have editor rights.",
          variant: "destructive",
        });
      }
    },
    // Pass the target department since this is an Audit user
    dept
  );

  // Track if the user has clicked the release button
  const [isReleasing, setIsReleasing] = useState(false);

  // Function to handle acquiring editor rights
  const handleAcquireEditorRights = async () => {
    const success = await acquireLock();
    return success;
  };

  // Function to handle releasing editor rights
  const handleReleaseEditorRights = async () => {
    setIsReleasing(true);
    await releaseLock();
    setIsReleasing(false);
  };

  // Function to handle clicking VIEW VOUCHERS
  const handleViewVouchers = async () => {
    // Always navigate first to ensure UI responsiveness
    onSelectDepartment(dept);

    // If not locked, try to acquire lock
    if (!isLocked) {
      console.log('Resource not locked, attempting to acquire lock');
      const success = await handleAcquireEditorRights();
      if (!success) {
        console.log('Failed to acquire lock, entering read-only mode');
        toast({
          title: "Read-only Mode",
          description: "Unable to acquire edit rights. You can view in read-only mode.",
          variant: "default",
        });
      }
    } else if (!isLockOwner) {
      console.log('Resource locked by someone else, entering read-only mode');
      toast({
        title: "Read-only Mode",
        description: `${lockOwnerName} is currently editing. You can view in read-only mode.`,
        variant: "default",
      });
    } else {
      console.log('We own the lock, continuing with edit mode');
    }
  };

  // Function to handle clicking PROVISIONAL CASH
  const handleProvisionalCash = async () => {
    // Always navigate first
    onShowProvisionalCash(dept);

    // If not locked, try to acquire lock
    if (!isLocked) {
      const success = await handleAcquireEditorRights();
      if (!success) {
        toast({
          title: "Read-only Mode",
          description: "Unable to acquire edit rights. You can view in read-only mode.",
          variant: "default",
        });
      }
    } else if (!isLockOwner) {
      toast({
        title: "Read-only Mode",
        description: `${lockOwnerName} is currently editing. You can view in read-only mode.`,
        variant: "default",
      });
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium uppercase flex justify-between items-center">
          <span>{dept} VOUCHER HUB</span>
          <div className="flex items-center gap-2">
            {/* Lock status badge */}
            <Badge
              variant={isLocked ? (isLockOwner ? "default" : "outline") : "outline"}
              className={
                isLocked
                  ? (isLockOwner ? "bg-green-600" : "border-yellow-600 text-yellow-600")
                  : "border-green-600 text-green-600"
              }
            >
              {isLocked ? (
                <>
                  <Lock className="h-3 w-3 mr-1" />
                  {isLockOwner ? "EDITING" : `LOCKED BY ${lockOwnerName}`}
                </>
              ) : (
                <>
                  <Unlock className="h-3 w-3 mr-1" />
                  OPENED
                </>
              )}
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent>
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <FileCheck className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">{pendingVouchers} Pending</span>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="border-primary hover:bg-primary/10"
              onClick={handleProvisionalCash}
            >
              PROVISIONAL CASH
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleViewVouchers}
            >
              VIEW VOUCHERS
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
