
import { Department } from '@/lib/types';
import { DepartmentCard } from './department-card';

interface DepartmentVoucherHubsProps {
  onSelectDepartment: (department: Department) => void;
  onShowProvisionalCash: (department: Department) => void;
}

export function DepartmentVoucherHubs({ 
  onSelectDepartment, 
  onShowProvisionalCash 
}: DepartmentVoucherHubsProps) {
  const departmentsToShow = ["FINANCE", "MINISTRIES", "PENSIONS", "PENTMEDIA", "MISSIONS", "PENTSOS"] as Department[];

  return (
    <div className="mt-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold uppercase">DEPARTMENT VOUCHER HUBS</h2>
      </div>
      
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {departmentsToShow.map((dept) => (
          <DepartmentCard 
            key={dept} 
            dept={dept} 
            onSelectDepartment={onSelectDepartment} 
            onShowProvisionalCash={onShowProvisionalCash} 
          />
        ))}
      </div>
    </div>
  );
}
