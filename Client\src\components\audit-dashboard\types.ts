
import { Department, Voucher } from "@/lib/types";

export interface BatchReceivingProps {
  batchId: string;
  open: boolean;
  onClose: () => void;
  isEditable?: boolean;
}

export interface NewlyArrivedVouchersProps {
  pendingBatches: any[];
  onOpenBatch: (batchId: string) => void;
  isEditable?: boolean;
}

export interface DepartmentCardProps {
  dept: Department;
  onSelectDepartment: (department: Department) => void;
  onShowProvisionalCash: (department: Department) => void;
  isEditable?: boolean;
}

export interface DispatchControlsProps {
  selectedDispatchVouchers: string[];
  selectedDepartment: Department | null;
  dispatchPerson: string;
  setDispatchPerson: (person: string) => void;
  handleDispatchVouchers: () => void;
  isEditable?: boolean;
}

// REMOVED: AuditVoucherBatchNotificationProps - legacy notification system removed
