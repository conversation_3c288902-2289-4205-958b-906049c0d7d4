import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, File, X, AlertCircle, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { usersApi } from '@/lib/api';

interface AttachmentUploadProps {
  voucherId: string;
  claimant: string;
  description: string;
  onUploadSuccess: (shouldCloseModal?: boolean) => void;
  disabled?: boolean;
}

export function AttachmentUpload({
  voucherId,
  claimant,
  description,
  onUploadSuccess,
  disabled = false
}: AttachmentUploadProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [uploadedCount, setUploadedCount] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach(file => {
      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg'];
      if (!allowedTypes.includes(file.type)) {
        errors.push(`${file.name}: Only PDF and JPG files are allowed`);
        return;
      }

      // Validate file size (10MB max)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        errors.push(`${file.name}: File size exceeds 10MB limit`);
        return;
      }

      validFiles.push(file);
    });

    if (errors.length > 0) {
      errors.forEach(error => toast.error(error));
    }

    setSelectedFiles(prev => [...prev, ...validFiles]);
    
    // Clear the input so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      toast.error('Please select files to upload');
      return;
    }

    setUploading(true);
    try {
      // Create FileList-like object
      const fileList = {
        length: selectedFiles.length,
        item: (index: number) => selectedFiles[index],
        [Symbol.iterator]: function* () {
          for (let i = 0; i < this.length; i++) {
            yield this.item(i);
          }
        }
      } as FileList;

      // Add files to FileList prototype
      selectedFiles.forEach((file, index) => {
        (fileList as any)[index] = file;
      });

      const result = await usersApi.uploadVoucherAttachments(voucherId, fileList);

      toast.success(`Successfully uploaded ${result.attachments.length} file(s)`);
      setSelectedFiles([]);
      setUploadSuccess(true);
      setUploadedCount(result.attachments.length);
      onUploadSuccess();
      
    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error(error.response?.data?.error || 'Failed to upload files');
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file: File) => {
    if (file.type === 'application/pdf') {
      return '📄';
    } else if (file.type === 'image/jpeg') {
      return '🖼️';
    }
    return '📎';
  };

  // Show success state
  if (uploadSuccess) {
    return (
      <Card className="w-full">
        <CardContent className="p-4">
          <div className="space-y-4 text-center">
            <div className="flex items-center justify-center gap-2 text-green-600">
              <CheckCircle className="h-8 w-8" />
              <h3 className="text-lg font-semibold">Upload Successful!</h3>
            </div>

            <p className="text-gray-600">
              Successfully uploaded {uploadedCount} file{uploadedCount !== 1 ? 's' : ''} to voucher {voucherId}
            </p>

            <div className="flex gap-2 justify-center">
              <Button
                onClick={() => {
                  setUploadSuccess(false);
                  setUploadedCount(0);
                }}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Upload More Files
              </Button>

              <Button
                onClick={() => {
                  setUploadSuccess(false);
                  setUploadedCount(0);
                  // This will close the modal via parent component
                  onUploadSuccess(true);
                }}
                variant="outline"
              >
                Done
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardContent className="p-4">
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Upload className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium">Attach Documents</h3>
          </div>

          <div className="text-sm text-gray-600">
            <p><strong>Voucher:</strong> {claimant} - {description}</p>
            <p className="text-xs mt-1">Supported formats: PDF, JPG (Max 10MB each)</p>
          </div>

          {/* File Selection */}
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || uploading}
              className="flex items-center gap-2"
            >
              <File className="h-4 w-4" />
              Select Files
            </Button>
            
            {selectedFiles.length > 0 && (
              <Button
                type="button"
                onClick={handleUpload}
                disabled={disabled || uploading}
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                {uploading ? 'Uploading...' : `Upload ${selectedFiles.length} file(s)`}
              </Button>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.jpg,.jpeg"
            onChange={handleFileSelect}
            className="hidden"
          />

          {/* Selected Files List */}
          {selectedFiles.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Selected Files:</h4>
              {selectedFiles.map((file, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{getFileIcon(file)}</span>
                    <div>
                      <p className="text-sm font-medium">{file.name}</p>
                      <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(index)}
                    disabled={uploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Upload Progress */}
          {uploading && (
            <div className="flex items-center gap-2 text-sm text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              Uploading files...
            </div>
          )}

          {/* Help Text */}
          <div className="flex items-start gap-2 p-3 bg-blue-50 rounded text-sm">
            <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-blue-800">
              <p className="font-medium">How to attach scanned documents:</p>
              <ol className="list-decimal list-inside mt-1 space-y-1 text-xs">
                <li>Scan your document using the office scanner</li>
                <li>Save the scan to the designated audit folder</li>
                <li>Click "Select Files" and choose your scanned document</li>
                <li>Click "Upload" to attach it to this voucher</li>
              </ol>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
