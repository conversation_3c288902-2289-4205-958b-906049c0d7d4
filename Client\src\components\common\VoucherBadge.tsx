/**
 * Voucher Badge Component
 * Displays workflow state badges (RE-SUBMITTED, RETURNED, REJECTED)
 */

import React from 'react';
import { BadgeType, VoucherWorkflowStateMachine } from '../../lib/workflow/VoucherWorkflowStateMachine';

interface VoucherBadgeProps {
  badgeType: BadgeType;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const VoucherBadge: React.FC<VoucherBadgeProps> = ({
  badgeType,
  className = '',
  size = 'md'
}) => {
  // Don't render if no badge
  if (badgeType === BadgeType.NONE) {
    return null;
  }

  const displayName = VoucherWorkflowStateMachine.getBadgeDisplayName(badgeType);
  const badgeClasses = VoucherWorkflowStateMachine.getBadgeClasses(badgeType);
  
  // Size variations
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm'
  };

  const finalClasses = `
    inline-flex items-center rounded-full font-medium
    ${sizeClasses[size]}
    ${badgeClasses}
    ${className}
  `.trim();

  return (
    <span className={finalClasses}>
      {displayName}
    </span>
  );
};

// Voucher Badge with Icon
interface VoucherBadgeWithIconProps extends VoucherBadgeProps {
  showIcon?: boolean;
}

export const VoucherBadgeWithIcon: React.FC<VoucherBadgeWithIconProps> = ({
  badgeType,
  showIcon = true,
  ...props
}) => {
  if (badgeType === BadgeType.NONE) {
    return null;
  }

  const getIcon = (type: BadgeType) => {
    switch (type) {
      case BadgeType.RE_SUBMITTED:
        return (
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
          </svg>
        );
      case BadgeType.RETURNED:
        return (
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
        );
      case BadgeType.REJECTED:
        return (
          <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  const displayName = VoucherWorkflowStateMachine.getBadgeDisplayName(badgeType);
  const badgeClasses = VoucherWorkflowStateMachine.getBadgeClasses(badgeType);
  
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-0.5 text-xs',
    lg: 'px-3 py-1 text-sm'
  };

  const finalClasses = `
    inline-flex items-center rounded-full font-medium
    ${sizeClasses[props.size || 'md']}
    ${badgeClasses}
    ${props.className || ''}
  `.trim();

  return (
    <span className={finalClasses}>
      {showIcon && getIcon(badgeType)}
      {displayName}
    </span>
  );
};

// Multiple Badges Container
interface VoucherBadgesProps {
  voucher: {
    badge_type: BadgeType;
    workflow_state: string;
    is_copy?: boolean;
    is_resubmitted?: boolean;
    isResubmitted?: boolean;
    rejectedBy?: string;
    status?: string;
  };
  showWorkflowState?: boolean;
  className?: string;
}

export const VoucherBadges: React.FC<VoucherBadgesProps> = ({
  voucher,
  showWorkflowState = false,
  className = ''
}) => {
  const badges = [];

  // REMOVED: Resubmission badge logic moved to UnifiedVoucherBadges system
  // This prevents duplicate badges when both VoucherBadges and ResubmissionBadge are used

  // Main badge
  if (voucher.badge_type !== BadgeType.NONE) {
    badges.push(
      <VoucherBadgeWithIcon
        key="main-badge"
        badgeType={voucher.badge_type}
        size="sm"
      />
    );
  }

  // Copy indicator
  if (voucher.is_copy) {
    badges.push(
      <span
        key="copy-badge"
        className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
      >
        COPY
      </span>
    );
  }

  // Workflow state (for debugging)
  if (showWorkflowState) {
    badges.push(
      <span
        key="workflow-state"
        className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-mono bg-purple-100 text-purple-800"
      >
        {voucher.workflow_state}
      </span>
    );
  }

  if (badges.length === 0) {
    return null;
  }

  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {badges}
    </div>
  );
};

// Badge Legend Component (for help/documentation)
export const VoucherBadgeLegend: React.FC = () => {
  const badges = [
    { type: BadgeType.RE_SUBMITTED, description: 'Voucher has been resubmitted after rejection' },
    { type: BadgeType.RETURNED, description: 'Voucher was returned for corrections' },
    { type: BadgeType.REJECTED, description: 'Voucher was rejected by audit' }
  ];

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200">
      <h3 className="text-sm font-medium text-gray-900 mb-3">Badge Legend</h3>
      <div className="space-y-2">
        {badges.map(({ type, description }) => (
          <div key={type} className="flex items-center space-x-3">
            <VoucherBadgeWithIcon badgeType={type} size="sm" />
            <span className="text-sm text-gray-600">{description}</span>
          </div>
        ))}
        <div className="flex items-center space-x-3">
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            COPY
          </span>
          <span className="text-sm text-gray-600">Permanent audit record copy</span>
        </div>
      </div>
    </div>
  );
};

export default VoucherBadge;
