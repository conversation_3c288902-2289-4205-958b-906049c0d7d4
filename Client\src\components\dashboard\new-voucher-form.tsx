
import { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { VoucherForm } from '@/components/voucher-form';
import { Department } from '@/lib/types';
import { ChevronUp, ChevronDown, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface NewVoucherFormProps {
  department: Department;
  isDisabled?: boolean;
  onDisabledClick?: () => void;
  hidden?: boolean;
}

export function NewVoucherForm({
  department,
  isDisabled = false,
  onDisabledClick,
  hidden = false
}: NewVoucherFormProps) {
  const [isOpen, setIsOpen] = useState(false); // Changed to false to hide by default
  const [isFormActive, setIsFormActive] = useState(false);
  const formRef = useRef<HTMLDivElement>(null);

  const handleFormClick = () => {
    if (isDisabled && onDisabledClick) {
      onDisabledClick();
    }
  };

  // Enhanced responsiveness: Smart hide/show logic
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        // Close the form when clicking outside, but with a small delay to allow for quick interactions
        setTimeout(() => {
          if (!isFormActive) {
            setIsOpen(false);
          }
        }, 150);
      }
    };

    // Add event listener when form is open
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, isFormActive]);

  // Enhanced keyboard handling for better UX
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
        setIsFormActive(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);

  // REMOVED: Focus/blur handlers that were causing input issues

  // Enhanced click behavior: Don't interfere with collapsible controls
  const handleCardClick = (e: React.MouseEvent) => {
    // Don't interfere with collapsible triggers (buttons and interactive elements)
    if ((e.target as HTMLElement).closest('button') ||
        (e.target as HTMLElement).closest('[data-radix-collection-item]')) {
      return;
    }

    e.stopPropagation();
    if (isDisabled) {
      handleFormClick();
    } else {
      setIsOpen(true);
      setIsFormActive(true);
    }
  };

  // Handle form submission completion
  const handleFormComplete = () => {
    setIsOpen(false);
    setIsFormActive(false);
  };



  if (hidden) return null;

  return (
    <Card
      ref={formRef}
      className={`bg-[#0a0a0a] border-gray-800 mb-4 ${isDisabled ? 'opacity-70' : ''} cursor-pointer transition-all duration-200 hover:border-gray-600`}
      onClick={handleCardClick}
    >
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CardHeader className="pb-0 pt-3 flex flex-row items-center justify-between">
          <CollapsibleTrigger asChild>
            <div className="flex items-center cursor-pointer" title="Click to toggle form visibility">
              <CardTitle className="text-lg">CREATE NEW VOUCHER</CardTitle>
              {isDisabled && (
                <Lock className="ml-2 h-4 w-4 text-amber-500" />
              )}
            </div>
          </CollapsibleTrigger>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 flex items-center justify-center opacity-50 hover:opacity-100"
              disabled={isDisabled}
            >
              {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
        </CardHeader>
        <CollapsibleContent>
          <CardContent className="p-0 pt-2">
            <VoucherForm
              department={department}
              isDisabled={isDisabled}
              onComplete={handleFormComplete}
              infoMessage={isDisabled ? "You must receive pending vouchers from Audit before creating new vouchers." : undefined}
            />
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
