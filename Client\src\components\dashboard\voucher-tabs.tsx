import { useState } from 'react';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { VoucherTable } from '@/components/voucher-table';
import { Department, Voucher } from '@/lib/types';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Lock } from 'lucide-react';
import { CertifiedVouchersTab } from '@/components/voucher-hub/certified-vouchers-tab';

interface VoucherTabsProps {
  voucherView: string;
  onVoucherViewChange: (value: string) => void;
  pendingSubmissionVouchers: Voucher[];
  processingVouchers: Voucher[];
  certifiedVouchers: Voucher[];
  rejectedVouchers: Voucher[];
  returnedVouchers: Voucher[];
  department: Department;
  onSelectionChange: (selectedIds: string[]) => void;
  onViewVoucher: (voucher: Voucher) => void;
  isDisabled?: boolean;
}

export function VoucherTabs({
  voucherView,
  onVoucherViewChange,
  pendingSubmissionVouchers,
  processingVouchers,
  certifiedVouchers,
  rejectedVouchers,
  returnedVouchers,
  department,
  onSelectionChange,
  onViewVoucher,
  isDisabled = false
}: VoucherTabsProps) {

  // State for sorting certified vouchers
  const [certifiedSortColumn, setCertifiedSortColumn] = useState<string | null>(null);
  const [certifiedSortDirection, setCertifiedSortDirection] = useState<'asc' | 'desc'>('asc');

  // Handle sorting for certified vouchers
  const handleCertifiedSort = (column: string) => {
    if (certifiedSortColumn === column) {
      // Toggle direction if same column
      setCertifiedSortDirection(certifiedSortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new column and default to ascending
      setCertifiedSortColumn(column);
      setCertifiedSortDirection('asc');
    }
  };
  const handleValueChange = (value: string) => {
    // Processing, Certified, Rejected and Returned tabs are always enabled, even when isDisabled is true
    const alwaysEnabledTabs = ['processing', 'certified', 'rejected', 'returned'];

    if (!isDisabled || alwaysEnabledTabs.includes(value)) {
      onVoucherViewChange(value);
    }
  };

  // Determine which tabs should be locked - ALL ASPECTS when batch is received
  const shouldLockTab = (tabName: string) => {
    if (!isDisabled) return false;

    // Lock all tabs that allow user interaction when batch is received
    return tabName === 'pending-submission' || tabName === 'rejected' || tabName === 'returned';
  };



  return (
    <>

      <Tabs
        defaultValue="pending-submission"
        value={voucherView}
        onValueChange={handleValueChange}
        className="flex flex-col flex-1 overflow-hidden"
      >
        <TabsList className="grid w-full grid-cols-5 bg-[#0f0f0f]">
          {['pending-submission', 'processing', 'certified', 'rejected', 'returned'].map((tab) => {
            const isLocked = shouldLockTab(tab);

            return (
              <TabsTrigger
                key={tab}
                value={tab}
                className={`uppercase data-[state=active]:bg-blue-700 data-[state=active]:text-white flex items-center justify-center ${isLocked ? 'cursor-not-allowed opacity-70' : ''}`}
                disabled={isLocked}
              >
                {tab === 'pending-submission' ? 'Pending' : tab === 'processing' ? 'Processing' : tab}
                {' '}({tab === 'pending-submission'
                  ? pendingSubmissionVouchers.length
                  : tab === 'processing'
                    ? processingVouchers.length
                    : tab === 'certified'
                      ? certifiedVouchers.length
                      : tab === 'rejected'
                        ? rejectedVouchers.length
                        : returnedVouchers.length})
                {isLocked && <Lock className="ml-1 h-3 w-3 text-amber-500" />}
              </TabsTrigger>
            );
          })}
        </TabsList>

        <div className={`rounded-md border border-gray-800 bg-[#0f0f0f] flex-1 overflow-hidden mt-2`}>
          <TabsContent value="pending-submission" className="h-full p-0 m-0 data-[state=active]:flex-1 data-[state=active]:flex data-[state=active]:flex-col">
            <div className="h-[calc(100vh-220px)] overflow-auto">
              <VoucherTable
                vouchers={pendingSubmissionVouchers}
                department={department}
                selectable={!shouldLockTab('pending-submission')}
                onSelectionChange={onSelectionChange}
                view="pending-submission"
                onViewVoucher={onViewVoucher}
                showDelete={!shouldLockTab('pending-submission')}
                showScrollBars={true}
              />
            </div>
          </TabsContent>

          <TabsContent value="processing" className="h-full p-0 m-0 data-[state=active]:flex-1 data-[state=active]:flex data-[state=active]:flex-col">
            <div className="h-[calc(100vh-220px)] overflow-auto">
              <VoucherTable
                vouchers={processingVouchers}
                department={department}
                selectable={false}
                view="processing"
                onViewVoucher={onViewVoucher}
                showScrollBars={true}
              />
            </div>
          </TabsContent>

          <TabsContent value="certified" className="h-full p-0 m-0 data-[state=active]:flex-1 data-[state=active]:flex data-[state=active]:flex-col">
            <div className="h-[calc(100vh-220px)] overflow-auto">
              <CertifiedVouchersTab
                filteredVouchers={certifiedVouchers}
                sortColumn={certifiedSortColumn}
                sortDirection={certifiedSortDirection}
                handleSort={handleCertifiedSort}
                onViewVoucher={onViewVoucher}
              />
            </div>
          </TabsContent>

          <TabsContent value="rejected" className="h-full p-0 m-0 data-[state=active]:flex-1 data-[state=active]:flex data-[state=active]:flex-col">
            <div className="h-[calc(100vh-220px)] overflow-auto">
              <VoucherTable
                vouchers={rejectedVouchers}
                department={department}
                selectable={false}
                view="rejected"
                onViewVoucher={onViewVoucher}
                showDelete={!shouldLockTab('rejected')}
                showAddBack={!shouldLockTab('rejected')}
                showScrollBars={true}
              />
            </div>
          </TabsContent>

          <TabsContent value="returned" className="h-full p-0 m-0 data-[state=active]:flex-1 data-[state=active]:flex data-[state=active]:flex-col">
            <div className="h-[calc(100vh-220px)] overflow-auto">
              <VoucherTable
                vouchers={returnedVouchers}
                department={department}
                selectable={false}
                view="returned"
                onViewVoucher={onViewVoucher}
                showAddBack={!shouldLockTab('returned')}
                showDelete={!shouldLockTab('returned')}
                showScrollBars={true}
              />
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </>
  );
}
