import { useNavigate } from 'react-router-dom';
import { useAppStore } from '@/lib/store';
import { useDashboardState } from '@/hooks/use-dashboard-state';
import { useNetworkStatus } from '@/hooks/use-network-status';
import { useSmartBatchLocking } from '@/hooks/use-smart-batch-locking';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { NewVoucherForm } from '@/components/dashboard/new-voucher-form';
import { DashboardContent } from '@/components/dashboard/dashboard-content';
import { DashboardModals } from '@/components/dashboard/dashboard-modals';
import { DashboardFooter } from '@/components/dashboard/dashboard-footer';
import { Department } from '@/lib/types';
import { useDepartmentData } from '@/hooks/use-department-data';
import { useEffect } from 'react';
import { getSocket } from '@/lib/socket';
import { toast } from '@/hooks/use-toast';

interface GenericDepartmentDashboardProps {
  department: Department;
}

export function GenericDepartmentDashboard({ department }: GenericDepartmentDashboardProps) {
  const navigate = useNavigate();
  const fetchBatches = useAppStore((state) => state.fetchBatches);
  const fetchNotifications = useAppStore((state) => state.fetchNotifications);

  // FEATURE PARITY FIX: Add network monitoring for all departments
  useNetworkStatus();

  // FEATURE PARITY FIX: Add smart batch locking for all departments
  const { executeWithBatchLock, isOperationInProgress } = useSmartBatchLocking(department);

  const {
    currentUser,
    selectedVouchers,
    setSelectedVouchers,
    showVoucherReceiving,
    setShowVoucherReceiving,
    receivingVoucherIds,
    setReceivingVoucherIds,
    dispatchedBy,
    setDispatchedBy,
    customDispatchName,
    setCustomDispatchName,
    viewingVoucher,
    setViewingVoucher,
    showBatchReceiving,
    setShowBatchReceiving,
    selectedBatchId,
    setSelectedBatchId,
    voucherView,
    setVoucherView,
    isNotificationBlinking,
    refreshTrigger,
    refreshData,
    handleDisabledFormClick
  } = useDashboardState();

  // CRITICAL FIX: Fetch batches when department dashboard loads
  useEffect(() => {
    if (currentUser && currentUser.department === department) {
      console.log(`🔄 ${department}: Fetching batches for department dashboard`);
      fetchBatches().catch((error) => {
        console.error(`❌ ${department}: Failed to fetch batches:`, error);
      });
    }
  }, [currentUser?.id, department, fetchBatches]);

  // BIDIRECTIONAL REAL-TIME FIX: Listen for new batch notifications from Audit
  useEffect(() => {
    if (currentUser?.department === department && department !== 'AUDIT') {
      const socket = getSocket();
      if (!socket) return;

      const handleNewBatchNotification = (data: any) => {
        console.log(`🔔 REAL-TIME: ${department} received batch notification:`, data);

        // Show toast notification
        toast({
          title: "New Voucher Batch Arrived",
          description: `${data.message} from ${data.department}`,
          variant: "default",
        });

        // Refresh batches to show new notification
        fetchBatches().catch(console.error);
        fetchNotifications().catch(console.error);
      };

      // Listen for real-time batch notifications
      socket.on('new_batch_notification', handleNewBatchNotification);

      return () => {
        socket.off('new_batch_notification', handleNewBatchNotification);
      };
    }
  }, [currentUser?.department, department, fetchBatches, fetchNotifications]);

  const { batchesArray } = useDepartmentData(department, refreshTrigger);

  // FEATURE PARITY FIX: Use Finance's simple hasVouchersToReceive logic
  const hasVouchersToReceive = batchesArray && batchesArray.length > 0 &&
    batchesArray.some(batch =>
      batch.vouchers && batch.vouchers.some(v => v.certifiedBy || v.status === "VOUCHER REJECTED")
    );

  // Redirect if user is not from this department
  useEffect(() => {
    if (!currentUser) {
      navigate('/');
      return;
    }

    if (currentUser.department !== department) {
      if (currentUser.department === 'AUDIT') {
        navigate('/audit-dashboard');
      } else if (currentUser.department === 'ADMINISTRATOR') {
        navigate('/admin-dashboard');
      } else {
        navigate('/dashboard');
      }
    }
  }, [currentUser, department, navigate]);

  if (!currentUser) {
    return null;
  }

  return (
    <div className="flex flex-col h-screen bg-black text-white">
      <DashboardHeader />

      <div className="px-6 py-2 bg-black">
        <NewVoucherForm
          department={department}
          isDisabled={false}
          onDisabledClick={handleDisabledFormClick}
          hidden={false}
        />
      </div>

      <DashboardContent
        department={department}
        refreshTrigger={refreshTrigger}
        onRefresh={refreshData}
        onReceiveVouchers={(voucherIdsOrBatchId, isBatchId = false) => {
          console.log("🔄 DASHBOARD: Receive vouchers called with:", voucherIdsOrBatchId, "isBatchId:", isBatchId);

          if (isBatchId) {
            // PRODUCTION FIX: Use proper batch receiving for batches from Audit
            console.log("✅ DASHBOARD: Opening batch receiving for batch ID:", voucherIdsOrBatchId);
            setSelectedBatchId(voucherIdsOrBatchId);
            setShowBatchReceiving(true);
          } else {
            // Fallback to old voucher receiving for individual vouchers
            console.log("⚠️ DASHBOARD: Using fallback voucher receiving for voucher IDs:", voucherIdsOrBatchId);
            setReceivingVoucherIds(voucherIdsOrBatchId);
            setShowVoucherReceiving(true);
          }
        }}
        selectedVouchers={selectedVouchers}
        dispatchedBy={dispatchedBy}
        customDispatchName={customDispatchName}
        onDispatcherChange={setDispatchedBy}
        onCustomDispatchNameChange={setCustomDispatchName}
        onSendToAudit={async () => {
          const sendVouchersToAudit = useAppStore.getState().sendVouchersToAudit;

          if (selectedVouchers.length > 0 && (dispatchedBy || customDispatchName)) {
            const finalDispatchedBy = dispatchedBy || customDispatchName.toUpperCase();

            // ORIGINAL: Send vouchers from user's department
            const result = await executeWithBatchLock(
              'batch-dispatch',
              async () => {
                return await sendVouchersToAudit(department, selectedVouchers, finalDispatchedBy);
              },
              selectedVouchers
            );

            // Only update UI if operation was successful (not blocked by lock)
            if (result !== null) {
              setSelectedVouchers([]);
              setDispatchedBy('');
              setCustomDispatchName('');
              setVoucherView('processing');
            }
          }
        }}
        onSelectionChange={setSelectedVouchers}
        onViewVoucher={setViewingVoucher}
        voucherView={voucherView}
        onVoucherViewChange={setVoucherView}
        isNotificationBlinking={isNotificationBlinking}
      />

      <DashboardModals
        department={department}
        showVoucherReceiving={showVoucherReceiving}
        setShowVoucherReceiving={setShowVoucherReceiving}
        receivingVoucherIds={receivingVoucherIds}
        viewingVoucher={viewingVoucher}
        setViewingVoucher={setViewingVoucher}
        showBatchReceiving={showBatchReceiving}
        setShowBatchReceiving={setShowBatchReceiving}
        selectedBatchId={selectedBatchId}
        onRefresh={refreshData}
      />

      <DashboardFooter />
    </div>
  );
}
