import { Button } from '@/components/ui/button';
import { ArrowLeft, Lock } from 'lucide-react';
import { useVoucherHub } from './hooks/use-voucher-hub';
import { Department } from '@/lib/types';
import { HubHeader } from './hub-header';
import { SearchBar } from './search-bar';
import { DepartmentVoucherTabs } from './voucher-tabs';
import { TabContent } from './tab-content';
import { SendDialog } from './send-dialog';
import { ViewVoucherModal } from './view-voucher-modal';
import { useDepartmentLock } from '@/hooks/use-resource-lock';
import { useAppStore } from '@/lib/store';
import { Badge } from '@/components/ui/badge';
import { useState, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertTriangle, Shield } from 'lucide-react';
import { vouchersApi } from '@/lib/api';

interface DepartmentVoucherHubProps {
  department: Department;
  auditUsers: string[];
  onBackToHubs: () => void;
}

export function DepartmentVoucherHub({
  department,
  auditUsers,
  onBackToHubs
}: DepartmentVoucherHubProps) {


  const currentUser = useAppStore((state) => state.currentUser);
  const isAudit = currentUser?.department === 'AUDIT';
  const navigate = useNavigate();
  const location = useLocation();

  // REDESIGNED: Use new department lock with exclusive access control
  const {
    isLocked,
    isLockOwner,
    lockOwner,
    canAccess,
    isBlocked,
    blockedBy,
    acquireLock,
    releaseLock,
    releaseOnNavigation,
    isLoading: isLockLoading
  } = useDepartmentLock(
    department,
    {
      onLockAcquired: () => {
        console.log(`🔒 EXCLUSIVE ACCESS: Acquired lock for ${department} voucher hub`);
      },
      onLockFailed: () => {
        console.log(`❌ EXCLUSIVE ACCESS: Failed to acquire lock for ${department} voucher hub`);
      },
      onLockReleased: () => {
        console.log(`🔓 EXCLUSIVE ACCESS: Released lock for ${department} voucher hub`);
      },
      onAccessBlocked: (blockedBy) => {
        console.log(`🚫 EXCLUSIVE ACCESS: Blocked from ${department} by ${blockedBy?.name}`);
      }
    }
  );

  // AUTO-RELEASE LOCK: Automatically release lock when navigating away or component unmounts
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isLockOwner) {
        console.log('🔓 AUTO-RELEASE: Releasing lock due to page unload');
        releaseOnNavigation();
      }
    };

    const handleVisibilityChange = () => {
      if (document.hidden && isLockOwner) {
        console.log('🔓 AUTO-RELEASE: Releasing lock due to tab becoming hidden');
        releaseOnNavigation();
      }
    };

    // Add event listeners for automatic lock release
    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup function to release lock when component unmounts
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);

      if (isLockOwner) {
        console.log('🔓 AUTO-RELEASE: Releasing lock due to component unmount');
        releaseOnNavigation();
      }
    };
  }, [isLockOwner, releaseOnNavigation]);

  // AUTO-RELEASE LOCK: Release lock when location changes (navigation)
  useEffect(() => {
    return () => {
      if (isLockOwner) {
        console.log('🔓 AUTO-RELEASE: Releasing lock due to navigation');
        releaseOnNavigation();
      }
    };
  }, [location.pathname, isLockOwner, releaseOnNavigation]);

  // AUTO-RELEASE LOCK: Wrapper function to release lock before going back
  const handleBackToHubs = () => {
    if (isLockOwner) {
      console.log('🔓 AUTO-RELEASE: Releasing lock before going back to hubs');
      releaseOnNavigation();
    }
    onBackToHubs();
  };

  // EXCLUSIVE ACCESS: Render blocked access UI
  const renderBlockedAccess = () => (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header with back button */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBackToHubs}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Hubs
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">
            {department} Voucher Hub
          </h1>
        </div>

        {/* Exclusive Access Blocked Message */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-8 text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-red-100 p-3 rounded-full">
              <Shield className="h-8 w-8 text-red-600" />
            </div>
          </div>

          <h2 className="text-xl font-semibold text-red-900 mb-2">
            Access Currently Restricted
          </h2>

          <p className="text-red-700 mb-4">
            <strong>{blockedBy?.name}</strong> is currently accessing the {department} voucher hub.
            <br />
            Only one audit user can access a department hub at a time for security reasons.
          </p>

          <Button
            variant="outline"
            onClick={handleBackToHubs}
            className="border-red-300 text-red-700 hover:bg-red-50"
          >
            Return to Department Selection
          </Button>
        </div>
      </div>
    </div>
  );

  const {
    // Workflow data
    workflowVouchers,
    isLoading,
    error,

    searchTerm,
    setSearchTerm,
    sortColumn,
    sortDirection,
    activeTab,
    setActiveTab,
    dispatchedBy,
    setDispatchedBy,
    customDispatchName,
    setCustomDispatchName,
    showSendDialog,
    setShowSendDialog,
    isSending,
    filteredVouchers,
    pendingDispatchVouchers,
    newVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers,
    viewingVoucher,
    setViewingVoucher,
    voucherEdits,
    selectedVouchers,
    setSelectedVouchers,
    handleSort,
    handleVoucherEdit,
    handleSaveVoucherEdits,
    handleSendToDepartment,
    handleConfirmSend,
    handleViewVoucher,
    handleReturnToNew,
    handleDeleteVoucher,
    handleSelectVoucher,
    handleSelectAllVouchers,

    // NEW: Workflow helpers
    tabCounts,
    getVouchersForTab,
    getTabDisplayName,
    getAvailableTabs,
    isVoucherEditable,
    canDispatchVoucher,
    canResubmitVoucher,
    getAvailableActions
  } = useVoucherHub(department);

  // EXCLUSIVE ACCESS: Return blocked UI if access is restricted
  if (isAudit && !canAccess && isBlocked) {
    return renderBlockedAccess();
  }

  // FEATURE 1: Handle voucher editing in PENDING tab
  const handleEditPendingVoucher = async (voucherId: string, updates: { claimant: string; description: string; amount: number }) => {
    try {
      const response = await vouchersApi.editPendingVoucher(voucherId, updates);

      // Update the voucher in the store
      const updateVoucher = useAppStore.getState().updateVoucher;
      updateVoucher(voucherId, response.voucher);

      toast({
        title: "Success",
        description: "Voucher updated successfully",
        variant: "default"
      });
    } catch (error: any) {
      console.error('Error editing voucher:', error);
      toast({
        title: "Error",
        description: error.response?.data?.error || "Failed to update voucher",
        variant: "destructive"
      });
      throw error;
    }
  };

  // FEATURE 2: Handle voucher hold toggle in NEW VOUCHER tab
  const handleToggleVoucherHold = async (voucherId: string, holdComment: string, isOnHold: boolean) => {
    try {
      const response = await vouchersApi.toggleVoucherHold(voucherId, { holdComment, isOnHold });

      // HOLD FEATURE FIX: Update local store directly without triggering another API call
      // This prevents the original_department from being overwritten
      const { updateVoucherFromWebSocket } = useAppStore.getState();
      updateVoucherFromWebSocket(voucherId, response.voucher);

      toast({
        title: "Success",
        description: `Voucher ${isOnHold ? 'put on hold' : 'removed from hold'} successfully`,
        variant: "default"
      });
    } catch (error: any) {
      console.error('Error toggling voucher hold:', error);
      toast({
        title: "Error",
        description: error.response?.data?.error || "Failed to toggle voucher hold status",
        variant: "destructive"
      });
      throw error;
    }
  };

  // EXCLUSIVE ACCESS: Only allow editing when user has exclusive access
  const finalIsEditable = isLockOwner;

  return (
    <>
      <style>{`
        @keyframes rapid-color-blink {
          0% { background: #dc2626; }
          50% { background: #16a34a; }
          100% { background: #dc2626; }
        }
      `}</style>
      <div className="space-y-6">

      <div className="flex items-center gap-2">
        <Button
          onClick={handleBackToHubs}
          variant="outline"
          className="border-primary hover:bg-primary/10 h-10 w-10 p-0"
        >
          <ArrowLeft className="h-4 w-4 text-primary" />
        </Button>
        <div className="flex-1">
          <HubHeader department={department} />
        </div>

        {/* EXCLUSIVE ACCESS: Lock status and controls */}
        <div className="flex items-center gap-2">
          {isAudit && (
            <>
              {isLockOwner ? (
                <div className="flex items-center gap-2">
                  <Badge variant="default" className="bg-green-600">
                    <Shield className="h-3 w-3 mr-1" />
                    EXCLUSIVE ACCESS
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={releaseLock}
                    disabled={isLockLoading}
                    className="border-green-600 text-green-600 hover:bg-green-50"
                  >
                    Release Access
                  </Button>
                </div>
              ) : isLocked ? (
                <Badge variant="outline" className="border-red-600 text-red-600">
                  <Lock className="h-3 w-3 mr-1" />
                  LOCKED BY {lockOwner?.name}
                </Badge>
              ) : (
                <Button
                  variant="default"
                  size="sm"
                  onClick={acquireLock}
                  disabled={isLockLoading}
                  className={`bg-blue-600 hover:bg-blue-700 ${!isLockOwner && isAudit ? 'animate-pulse' : ''}`}
                  style={!isLockOwner && isAudit ? {
                    animation: 'rapid-blink 0.5s infinite alternate',
                    background: 'linear-gradient(45deg, #dc2626, #16a34a)',
                    backgroundSize: '200% 200%',
                    animationName: 'rapid-color-blink'
                  } : {}}
                >
                  {isLockLoading ? 'Acquiring...' : 'Get Exclusive Access'}
                </Button>
              )}
            </>
          )}

          {/* For non-audit users, show read-only status if audit user has access */}
          {!isAudit && isLocked && (
            <Badge variant="outline" className="border-yellow-600 text-yellow-600">
              <Lock className="h-3 w-3 mr-1" />
              AUDIT ACCESS ACTIVE
            </Badge>
          )}
        </div>
      </div>

      <SearchBar
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
      />

      {/* Loading state */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading vouchers...</p>
          </div>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-red-600 mb-2">Error: {error}</p>
            <Button size="sm" onClick={() => window.location.reload()}>
              Retry
            </Button>
          </div>
        </div>
      )}

      {/* EXCLUSIVE ACCESS: Blur interface when audit user doesn't have access */}
      <div className={(isAudit && !isLockOwner) ? 'blur-sm pointer-events-none opacity-60' : ''}>

        <DepartmentVoucherTabs
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          department={department}
          newVouchers={newVouchers || []}
          pendingDispatchVouchers={pendingDispatchVouchers || []}
          dispatchedVouchers={dispatchedVouchers || []}
          returnedVouchers={returnedVouchers || []}
          rejectedVouchers={rejectedVouchers || []}
          tabCounts={tabCounts}
          getAvailableTabs={getAvailableTabs}
          getTabDisplayName={getTabDisplayName}
        />

        <TabContent
          activeTab={activeTab}
          filteredVouchers={filteredVouchers}
          auditUsers={auditUsers}
          sortColumn={sortColumn}
          sortDirection={sortDirection}
          department={department}
          voucherEdits={voucherEdits}
          selectedVouchers={selectedVouchers}
          onSort={handleSort}
          onVoucherEdit={handleVoucherEdit}
          onSaveVoucherEdits={handleSaveVoucherEdits}
          onReturnToNew={handleReturnToNew}
          onViewVoucher={handleViewVoucher}
          onDeleteVoucher={handleDeleteVoucher}
          onSelectVoucher={handleSelectVoucher}
          onSelectAll={handleSelectAllVouchers}
          setSelectedVouchers={setSelectedVouchers}
          dispatchedBy={dispatchedBy}
          customDispatchName={customDispatchName}
          setDispatchedBy={setDispatchedBy}
          setCustomDispatchName={setCustomDispatchName}
          handleSendToDepartment={handleSendToDepartment}
          isEditable={finalIsEditable}
          // FEATURE 1: Edit voucher in PENDING tab
          onEditPendingVoucher={handleEditPendingVoucher}
          // FEATURE 2: Toggle voucher hold in NEW VOUCHER tab
          onToggleVoucherHold={handleToggleVoucherHold}
        />
      </div>

      {showSendDialog && pendingDispatchVouchers && (
        <SendDialog
          open={showSendDialog}
          voucherCount={selectedVouchers.length > 0 ? selectedVouchers.length : pendingDispatchVouchers.length}
          department={department}
          isLoading={isSending}
          onConfirm={handleConfirmSend}
          onCancel={() => setShowSendDialog(false)}
        />
      )}

      {viewingVoucher && (
        <ViewVoucherModal
          viewingVoucher={viewingVoucher}
          isOpen={!!viewingVoucher}
          onClose={() => setViewingVoucher(null)}
          department={department}
          onDelete={handleDeleteVoucher}
        />
      )}
    </div>
    </>
  );
}
