import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogHeader,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Department } from '@/lib/types';

interface SendDialogProps {
  open: boolean;
  voucherCount: number;
  department: Department;
  isLoading: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export function SendDialog({
  open,
  voucherCount,
  department,
  isLoading,
  onConfirm,
  onCancel,
}: SendDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="uppercase">
            CONFIRM SEND VOUCHERS
          </DialogTitle>
          <DialogDescription className="uppercase">
            YOU ARE ABOUT TO SEND {voucherCount} VOUCHER{voucherCount !== 1 ? 'S' : ''} TO THE {department} DEPARTMENT
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={onCancel} 
            disabled={isLoading} 
            className="uppercase bg-secondary hover:bg-secondary/90"
          >
              CANCEL
            </Button>
          <Button onClick={onConfirm} disabled={isLoading} variant="success" className="uppercase">
            {isLoading ? 'SENDING...' : 'CONFIRM SEND'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
