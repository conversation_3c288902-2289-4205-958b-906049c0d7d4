import { useState } from 'react';
import { Wifi, WifiOff, Refresh<PERSON><PERSON>, Clock, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/lib/store';
import { useNetworkStatus } from '@/hooks/use-network-status';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

export function OfflineStatus() {
  const { isOnline, triggerSync } = useNetworkStatus();
  const offlineOperations = useAppStore(state => state.offlineOperations);
  const isSyncing = useAppStore(state => state.isSyncing);
  const lastSyncTime = useAppStore(state => state.lastSyncTime);
  const clearSyncedOperations = useAppStore(state => state.clearSyncedOperations);
  const clearFailedOperations = useAppStore(state => state.clearFailedOperations);
  const clearAllOperations = useAppStore(state => state.clearAllOperations);
  
  const [isOpen, setIsOpen] = useState(false);

  // Count operations by status
  const pendingOps = offlineOperations.filter(op => op.status === 'pending');
  const syncingOps = offlineOperations.filter(op => op.status === 'syncing');
  const failedOps = offlineOperations.filter(op => op.status === 'failed');
  const syncedOps = offlineOperations.filter(op => op.status === 'synced');

  const totalPending = pendingOps.length + syncingOps.length;
  const hasOperations = offlineOperations.length > 0;

  // Format last sync time
  const formatLastSync = (timestamp: number | null) => {
    if (!timestamp) return 'Never';
    const date = new Date(timestamp);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  // Get status color and icon
  const getStatusDisplay = () => {
    if (!isOnline) {
      return {
        icon: WifiOff,
        color: 'bg-red-500',
        text: 'Offline',
        description: 'No connection - operations will be queued'
      };
    }
    
    if (isSyncing) {
      return {
        icon: RefreshCw,
        color: 'bg-blue-500',
        text: 'Syncing',
        description: 'Syncing offline operations...'
      };
    }
    
    if (totalPending > 0) {
      return {
        icon: Clock,
        color: 'bg-amber-500',
        text: 'Pending',
        description: `${totalPending} operations waiting to sync`
      };
    }
    
    if (failedOps.length > 0) {
      return {
        icon: AlertCircle,
        color: 'bg-red-500',
        text: 'Failed',
        description: `${failedOps.length} operations failed to sync`
      };
    }
    
    return {
      icon: Wifi,
      color: 'bg-green-500',
      text: 'Online',
      description: 'Connected and synchronized'
    };
  };

  const status = getStatusDisplay();
  const StatusIcon = status.icon;

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="relative h-8 w-8 p-0"
          title={status.description}
        >
          <StatusIcon 
            className={`h-4 w-4 ${isSyncing ? 'animate-spin' : ''}`}
            color={isOnline ? (totalPending > 0 || failedOps.length > 0 ? '#f59e0b' : '#10b981') : '#ef4444'}
          />
          
          {/* Badge for pending operations */}
          {totalPending > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs flex items-center justify-center"
            >
              {totalPending > 9 ? '9+' : totalPending}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${status.color}`} />
              <span className="font-medium">{status.text}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={triggerSync}
              disabled={!isOnline || isSyncing}
              className="h-7"
            >
              <RefreshCw className={`h-3 w-3 mr-1 ${isSyncing ? 'animate-spin' : ''}`} />
              Sync
            </Button>
          </div>

          {/* Status description */}
          <p className="text-sm text-muted-foreground">
            {status.description}
          </p>

          {/* Last sync time */}
          <div className="text-xs text-muted-foreground">
            Last sync: {formatLastSync(lastSyncTime)}
          </div>

          {/* Operations summary */}
          {hasOperations && (
            <div className="space-y-2">
              <div className="text-sm font-medium">Operations Queue</div>
              
              {pendingOps.length > 0 && (
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-1">
                    <Clock className="h-3 w-3 text-amber-500" />
                    Pending
                  </span>
                  <Badge variant="secondary">{pendingOps.length}</Badge>
                </div>
              )}
              
              {syncingOps.length > 0 && (
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-1">
                    <RefreshCw className="h-3 w-3 text-blue-500 animate-spin" />
                    Syncing
                  </span>
                  <Badge variant="secondary">{syncingOps.length}</Badge>
                </div>
              )}
              
              {failedOps.length > 0 && (
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-1">
                    <AlertCircle className="h-3 w-3 text-red-500" />
                    Failed
                  </span>
                  <div className="flex items-center gap-2">
                    <Badge variant="destructive">{failedOps.length}</Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFailedOperations}
                      className="h-6 px-2 text-xs"
                    >
                      Clear
                    </Button>
                  </div>
                </div>
              )}
              
              {syncedOps.length > 0 && (
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    Synced
                  </span>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{syncedOps.length}</Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearSyncedOperations}
                      className="h-6 px-2 text-xs"
                    >
                      Clear
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Operation details */}
          {hasOperations && (
            <div className="space-y-1 max-h-32 overflow-y-auto">
              <div className="text-xs font-medium text-muted-foreground">Recent Operations</div>
              {offlineOperations.slice(-5).reverse().map((op) => (
                <div key={op.id} className="flex items-center justify-between text-xs p-1 rounded bg-muted/50">
                  <span className="truncate">
                    {op.type.replace('_', ' ').toLowerCase()}
                  </span>
                  <div className="flex items-center gap-1">
                    {op.status === 'pending' && <Clock className="h-3 w-3 text-amber-500" />}
                    {op.status === 'syncing' && <RefreshCw className="h-3 w-3 text-blue-500 animate-spin" />}
                    {op.status === 'synced' && <CheckCircle className="h-3 w-3 text-green-500" />}
                    {op.status === 'failed' && <AlertCircle className="h-3 w-3 text-red-500" />}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
