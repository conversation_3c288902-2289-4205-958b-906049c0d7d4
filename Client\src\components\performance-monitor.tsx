/**
 * PRODUCTION-LEVEL: Performance Monitoring Component
 * 
 * This component provides real-time monitoring of WebSocket performance
 * and displays key metrics for production validation.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { connectionInitializer } from '@/lib/connection-initializer';
import { performanceTesting } from '@/lib/performance-test';
import { getSocket } from '@/lib/socket';

interface PerformanceMetrics {
  connectionStatus: string;
  preWarmDuration: number | null;
  socketConnected: boolean;
  socketId: string | null;
  lastUpdate: number;
}

export const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    connectionStatus: 'unknown',
    preWarmDuration: null,
    socketConnected: false,
    socketId: null,
    lastUpdate: Date.now()
  });
  
  const [testResults, setTestResults] = useState<any>(null);
  const [isTestRunning, setIsTestRunning] = useState(false);

  // Update metrics every second
  useEffect(() => {
    const updateMetrics = () => {
      const initStatus = connectionInitializer.getStatus();
      const socket = getSocket();
      
      setMetrics({
        connectionStatus: initStatus.status,
        preWarmDuration: initStatus.preWarmDuration,
        socketConnected: socket?.connected || false,
        socketId: socket?.id || null,
        lastUpdate: Date.now()
      });
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 1000);
    
    return () => clearInterval(interval);
  }, []);

  const runPerformanceTest = async () => {
    setIsTestRunning(true);
    try {
      const results = await performanceTesting.runFullSuite();
      setTestResults(results);
    } catch (error) {
      console.error('Performance test failed:', error);
    } finally {
      setIsTestRunning(false);
    }
  };

  const runQuickCheck = async () => {
    const result = await performanceTesting.quickCheck();
    console.log('Quick performance check result:', result);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ready':
        return <Badge variant="default" className="bg-green-500">Ready</Badge>;
      case 'pre-warming':
        return <Badge variant="secondary">Pre-warming</Badge>;
      case 'not-initialized':
        return <Badge variant="destructive">Not Initialized</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getPerformanceBadge = (duration: number | null, target: number) => {
    if (duration === null) return <Badge variant="outline">N/A</Badge>;
    
    if (duration < target) {
      return <Badge variant="default" className="bg-green-500">Excellent</Badge>;
    } else if (duration < target * 1.5) {
      return <Badge variant="secondary">Good</Badge>;
    } else {
      return <Badge variant="destructive">Needs Improvement</Badge>;
    }
  };

  return (
    <div className="space-y-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🚀 WebSocket Performance Monitor
            <Badge variant="outline" className="text-xs">
              Production Level
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Connection Status */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Connection Status</label>
              <div className="flex items-center gap-2 mt-1">
                {getStatusBadge(metrics.connectionStatus)}
                <span className="text-xs text-muted-foreground">
                  {metrics.connectionStatus}
                </span>
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium">Socket Connected</label>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={metrics.socketConnected ? "default" : "destructive"}>
                  {metrics.socketConnected ? "Connected" : "Disconnected"}
                </Badge>
                {metrics.socketId && (
                  <span className="text-xs text-muted-foreground">
                    ID: {metrics.socketId.substring(0, 8)}...
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Pre-warm Performance</label>
              <div className="flex items-center gap-2 mt-1">
                {getPerformanceBadge(metrics.preWarmDuration, 200)}
                <span className="text-xs text-muted-foreground">
                  {metrics.preWarmDuration ? `${metrics.preWarmDuration}ms` : 'N/A'}
                  <span className="text-green-600"> (target: &lt;200ms)</span>
                </span>
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium">Last Update</label>
              <div className="text-xs text-muted-foreground mt-1">
                {new Date(metrics.lastUpdate).toLocaleTimeString()}
              </div>
            </div>
          </div>

          {/* Test Controls */}
          <div className="flex gap-2 pt-4 border-t">
            <Button 
              onClick={runPerformanceTest}
              disabled={isTestRunning}
              size="sm"
            >
              {isTestRunning ? 'Running Tests...' : 'Run Full Performance Test'}
            </Button>
            
            <Button 
              onClick={runQuickCheck}
              variant="outline"
              size="sm"
            >
              Quick Check
            </Button>
          </div>

          {/* Test Results */}
          {testResults && (
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Test Results</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Tests Passed:</span>
                  <span className="font-medium">
                    {testResults.passedTests}/{testResults.totalTests}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Pass Rate:</span>
                  <span className="font-medium">
                    {((testResults.passedTests / testResults.totalTests) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Total Duration:</span>
                  <span className="font-medium">
                    {testResults.endTime - testResults.startTime}ms
                  </span>
                </div>
              </div>
              
              {/* Individual Test Results */}
              <div className="mt-3 space-y-1">
                {testResults.results.map((result: any, index: number) => (
                  <div key={index} className="flex justify-between text-xs">
                    <span>{result.testName}:</span>
                    <span className={result.passed ? 'text-green-600' : 'text-red-600'}>
                      {result.duration}ms {result.passed ? '✅' : '❌'}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
