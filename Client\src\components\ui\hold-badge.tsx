import { Badge } from '@/components/ui/badge';
import { Pause } from 'lucide-react';
import { cn } from '@/lib/utils';

interface HoldBadgeProps {
  isOnHold: boolean;
  holdComment?: string;
  className?: string;
}

export function HoldBadge({ isOnHold, holdComment, className }: HoldBadgeProps) {
  if (!isOnHold) return null;

  return (
    <Badge 
      variant="destructive" 
      className={cn(
        "animate-pulse bg-red-600 hover:bg-red-700 text-white font-bold text-xs px-2 py-1 flex items-center gap-1",
        className
      )}
      title={holdComment ? `ON HOLD: ${holdComment}` : 'ON HOLD'}
    >
      <Pause className="h-3 w-3" />
      ON HOLD
    </Badge>
  );
}
