import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Pause } from 'lucide-react';
import { cn } from '@/lib/utils';

interface HoldBadgeProps {
  isOnHold: boolean;
  holdComment?: string;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
}

export function HoldBadge({ isOnHold, holdComment, className, size = 'sm' }: HoldBadgeProps) {
  if (!isOnHold) return null;

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    default: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  };

  return (
    <Badge 
      variant="destructive" 
      className={cn(
        "animate-pulse bg-red-600 hover:bg-red-700 text-white font-bold flex items-center gap-1 cursor-help",
        sizeClasses[size],
        className
      )}
      title={holdComment ? `ON HOLD: ${holdComment}` : 'ON HOLD'}
    >
      <Pause className="h-3 w-3" />
      ON HOLD
    </Badge>
  );
}
