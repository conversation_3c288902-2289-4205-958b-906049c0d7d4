import { Button } from '@/components/ui/button';
import { Pause, Play } from 'lucide-react';
import { cn } from '@/lib/utils';

interface HoldControlsProps {
  isOnHold: boolean;
  onToggleHold: () => void;
  disabled?: boolean;
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

export function HoldControls({ 
  isOnHold, 
  onToggleHold, 
  disabled = false, 
  size = 'sm',
  className 
}: HoldControlsProps) {
  return (
    <Button
      variant={isOnHold ? "default" : "destructive"}
      size={size}
      onClick={onToggleHold}
      disabled={disabled}
      className={cn(
        "flex items-center gap-1 min-w-[80px] justify-center",
        isOnHold 
          ? "bg-green-600 hover:bg-green-700 text-white" 
          : "bg-red-600 hover:bg-red-700 text-white",
        className
      )}
      title={isOnHold ? "Resume voucher processing" : "Put voucher on hold"}
    >
      {isOnHold ? (
        <>
          <Play className="h-3 w-3" />
          RESUME
        </>
      ) : (
        <>
          <Pause className="h-3 w-3" />
          HOLD
        </>
      )}
    </Button>
  );
}
