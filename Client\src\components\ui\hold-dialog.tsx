import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Pause, Play, AlertCircle } from 'lucide-react';
import { Voucher } from '@/lib/types';

interface HoldDialogProps {
  open: boolean;
  onClose: () => void;
  voucher: Voucher | null;
  onToggleHold: (voucherId: string, holdComment: string, isOnHold: boolean) => Promise<void>;
}

export function HoldDialog({ open, onClose, voucher, onToggleHold }: HoldDialogProps) {
  const [holdComment, setHoldComment] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  if (!voucher) return null;

  const isCurrentlyOnHold = voucher.isOnHold === true;

  const handleToggleHold = async () => {
    // If putting on hold, require a comment
    if (!isCurrentlyOnHold && !holdComment.trim()) {
      return;
    }

    setIsProcessing(true);
    try {
      await onToggleHold(voucher.id, holdComment.trim(), !isCurrentlyOnHold);
      onClose();
      setHoldComment('');
    } catch (error) {
      console.error('Error toggling voucher hold:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    if (isProcessing) return;
    setHoldComment('');
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isCurrentlyOnHold ? (
              <>
                <Play className="h-5 w-5 text-green-600" />
                Resume Voucher
              </>
            ) : (
              <>
                <Pause className="h-5 w-5 text-red-600" />
                Put Voucher on Hold
              </>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-gray-50 p-3 rounded-md">
            <p className="text-sm font-medium">Voucher: {voucher.voucherId}</p>
            <p className="text-sm text-gray-600">{voucher.description}</p>
            <p className="text-sm text-gray-600">Amount: {voucher.amount}</p>
          </div>

          {isCurrentlyOnHold ? (
            <div className="bg-red-50 border border-red-200 p-3 rounded-md">
              <div className="flex items-center gap-2 text-red-800">
                <AlertCircle className="h-4 w-4" />
                <span className="font-medium">Currently on hold</span>
              </div>
              {voucher.holdComment && (
                <p className="text-sm text-red-700 mt-1">
                  Reason: {voucher.holdComment}
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="holdComment">
                Reason for putting on hold <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="holdComment"
                placeholder="Enter reason for putting this voucher on hold..."
                value={holdComment}
                onChange={(e) => setHoldComment(e.target.value)}
                className="min-h-[80px]"
                disabled={isProcessing}
              />
              {!holdComment.trim() && (
                <p className="text-sm text-red-600">Please provide a reason for putting this voucher on hold.</p>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isProcessing}
          >
            Cancel
          </Button>
          <Button
            variant={isCurrentlyOnHold ? "default" : "destructive"}
            onClick={handleToggleHold}
            disabled={isProcessing || (!isCurrentlyOnHold && !holdComment.trim())}
            className={isCurrentlyOnHold 
              ? "bg-green-600 hover:bg-green-700" 
              : "bg-red-600 hover:bg-red-700"
            }
          >
            {isProcessing ? (
              "Processing..."
            ) : isCurrentlyOnHold ? (
              <>
                <Play className="h-4 w-4 mr-2" />
                Resume Voucher
              </>
            ) : (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Put on Hold
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
