import { useState, useEffect } from 'react';
import { Check, X, CornerDownLeft } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { useAppStore } from '@/lib/store';
import { useSmartBatchLocking } from '@/hooks/use-smart-batch-locking';
import { Voucher } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { ResubmissionBadge, ReturnBadge } from '@/components/common/UnifiedVoucherBadges';
import { ScrollArea } from '@/components/ui/scroll-area';
import { VoucherRejectionDialog } from './voucher-rejection-dialog';
import { batchesApi } from '@/lib/api';
import { ResubmissionBadge } from '@/components/common/UnifiedVoucherBadges';

interface VoucherBatchReceivingProps {
  batchId: string;
  open: boolean;
  onClose: () => void;
  isEditable?: boolean;
}

export function VoucherBatchReceiving({ batchId, open, onClose, isEditable = true }: VoucherBatchReceivingProps) {
  const voucherBatches = useAppStore((state) => state.voucherBatches);
  const receiveVoucherBatch = useAppStore((state) => state.receiveVoucherBatch);
  const currentUser = useAppStore((state) => state.currentUser);

  // CRITICAL FIX: Use isolated batch receiving locks to prevent interference with department voucher hub locks
  // This ensures batch receiving doesn't affect AUDIT user's exclusive access to department hubs
  const batchReceivingDepartment = `${currentUser?.department || 'AUDIT'}-BATCH-RECEIVING`;
  const { executeWithBatchLock, isOperationInProgress } = useSmartBatchLocking(
    batchReceivingDepartment
  );

  // State for batch data and vouchers
  const [batch, setBatch] = useState<any>(null);
  const [batchVouchers, setBatchVouchers] = useState<Voucher[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const [acceptedVouchers, setAcceptedVouchers] = useState<string[]>([]);
  const [rejectedVouchers, setRejectedVouchers] = useState<string[]>([]);
  const [rejectionComments, setRejectionComments] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // For rejection dialog
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [currentVoucherId, setCurrentVoucherId] = useState<string | null>(null);

  // Fetch batch details when component opens
  useEffect(() => {
    if (open && batchId) {
      fetchBatchDetails();
    }
  }, [open, batchId]);

  const fetchBatchDetails = async () => {
    setIsLoading(true);
    try {
      console.log(`🔍 BATCH RECEIVING: Fetching batch details for batch ${batchId}`);

      // PRODUCTION FIX: Validate batch ID format first
      if (!batchId || batchId.trim() === '') {
        throw new Error('Invalid batch ID provided');
      }

      // PRODUCTION FIX: Try API first with timeout protection
      try {
        console.log('🌐 BATCH RECEIVING: Fetching batch data from API...');

        // Add timeout protection
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('API request timeout after 10 seconds')), 10000);
        });

        const apiPromise = batchesApi.getBatchById(batchId);
        const batchData = await Promise.race([apiPromise, timeoutPromise]);

        console.log('✅ BATCH RECEIVING: Received batch data from API:', batchData);

        if (!batchData) {
          throw new Error(`Batch ${batchId} not found in API - it may have been processed or doesn't exist`);
        }

        // PRODUCTION FIX: Ensure vouchers array exists and has data
        const batchVouchers = batchData.vouchers || [];
        console.log(`📊 BATCH RECEIVING: Batch contains ${batchVouchers.length} vouchers:`,
          batchVouchers.map(v => `${v.voucherId || v.voucher_id} (${v.id})`));

        if (batchVouchers.length === 0) {
          console.warn(`⚠️ BATCH RECEIVING: Batch ${batchId} has no vouchers, trying local store...`);
          throw new Error(`Batch ${batchId} has no vouchers in API - it may have been processed already`);
        }

        // CRITICAL: Convert server voucher format to client format for proper display
        const clientVouchers = batchVouchers.map(voucher => ({
          ...voucher,
          // Ensure all required client fields are present
          voucherId: voucher.voucher_id || voucher.voucherId,
          dispatchedBy: voucher.dispatched_by || voucher.dispatchedBy || '',
          createdBy: voucher.created_by || voucher.createdBy || '',
          sentToAudit: voucher.sent_to_audit !== undefined ? Boolean(voucher.sent_to_audit) : voucher.sentToAudit,
          isReturned: voucher.is_returned !== undefined ? Boolean(voucher.is_returned) : voucher.isReturned,
          pendingReturn: voucher.pending_return !== undefined ? Boolean(voucher.pending_return) : voucher.pendingReturn,
          amount: typeof voucher.amount === 'string' ? parseFloat(voucher.amount) : voucher.amount,
          // REQUIREMENT 4: Include pre-audited amount for certified vouchers
          preAuditedAmount: typeof voucher.preAuditedAmount === 'string' ? parseFloat(voucher.preAuditedAmount) : voucher.preAuditedAmount,
          pre_audited_amount: typeof voucher.pre_audited_amount === 'string' ? parseFloat(voucher.pre_audited_amount) : voucher.pre_audited_amount,
          taxAmount: typeof voucher.taxAmount === 'string' ? parseFloat(voucher.taxAmount) : voucher.taxAmount,
          // CERTIFIED-RESUBMISSION FIX: Ensure certified_by field is properly mapped
          certifiedBy: voucher.certified_by || voucher.certifiedBy,
          // REJECTION FIX: Ensure rejection fields are properly mapped
          rejectedBy: voucher.rejected_by || voucher.rejectedBy,
          rejectionTime: voucher.rejection_time || voucher.rejectionTime,
          comment: voucher.comment, // Rejection reason
          isResubmitted: voucher.is_resubmitted !== undefined ? Boolean(voucher.is_resubmitted) : voucher.isResubmitted,
          workflowState: voucher.workflow_state || voucher.workflowState
        }));

        setBatch(batchData);
        setBatchVouchers(clientVouchers);

        // Reset state when new batch is loaded
        setAcceptedVouchers([]);
        setRejectedVouchers([]);
        setRejectionComments({});

        console.log(`✅ BATCH RECEIVING: Successfully loaded batch with ${clientVouchers.length} vouchers for processing`);
        return;
      } catch (apiError) {
        console.error('❌ BATCH RECEIVING: API batch fetch failed:', apiError);
        console.error('❌ BATCH RECEIVING: Error details:', {
          message: apiError instanceof Error ? apiError.message : 'Unknown error',
          stack: apiError instanceof Error ? apiError.stack : undefined,
          batchId: batchId
        });

        // PRODUCTION FIX: Check if it's a 404 error (batch not found)
        if (apiError instanceof Error && (apiError.message.includes('404') || apiError.message.includes('not found'))) {
          console.log('🔍 BATCH RECEIVING: Batch not found in API, checking if it was already processed...');

          // Check if there are any vouchers that might need receiving
          const { vouchers, fetchVouchers } = useAppStore.getState();
          await fetchVouchers(); // CRITICAL FIX: Fetch ALL vouchers

          const updatedState = useAppStore.getState();
          const currentUser = updatedState.currentUser;

          // Look for vouchers that might be waiting for this batch
          const waitingVouchers = updatedState.vouchers.filter(v =>
            v.status === 'PENDING RECEIPT' ||
            v.status === 'VOUCHER PROCESSING'
          );

          if (waitingVouchers.length === 0) {
            throw new Error(`Batch ${batchId} not found and no vouchers are waiting to be received. The batch may have been processed already.`);
          }

          console.log(`📋 BATCH RECEIVING: Found ${waitingVouchers.length} vouchers that might be related to this batch`);
        }

        // FALLBACK: Try to get batch from local store
        console.log('🔄 BATCH RECEIVING: Trying local store as fallback...');
        const { voucherBatches, vouchers, fetchVouchers } = useAppStore.getState();

        // Force refresh vouchers for all departments
        console.log('🔄 BATCH RECEIVING: Refreshing voucher data...');
        await fetchVouchers(); // CRITICAL FIX: Fetch ALL vouchers

        const updatedState = useAppStore.getState();
        const localBatch = updatedState.voucherBatches.find(b => b.id === batchId);

        if (localBatch && localBatch.voucherIds && localBatch.voucherIds.length > 0) {
          console.log('📦 BATCH RECEIVING: Found batch in local store:', localBatch);

          // Get vouchers for this batch from updated local store
          const batchVouchers = updatedState.vouchers.filter(v =>
            localBatch.voucherIds.includes(v.id)
          );

          console.log(`📊 BATCH RECEIVING: Local batch contains ${batchVouchers.length} vouchers:`,
            batchVouchers.map(v => `${v.voucherId} (${v.id})`));

          const batchData = {
            ...localBatch,
            vouchers: batchVouchers,
            sent_by: localBatch.sentBy,
            sent_time: localBatch.sentTime,
            from_audit: localBatch.fromAudit
          };

          setBatch(batchData);
          setBatchVouchers(batchVouchers);

          // Reset state when new batch is loaded
          // PRODUCTION FIX: Always start with empty selections to prevent hanging
          setAcceptedVouchers([]);
          setRejectedVouchers([]);
          setRejectionComments({});

          console.log(`✅ BATCH RECEIVING: Batch loaded, user must manually select vouchers to prevent UI hanging`);

          console.log(`✅ BATCH RECEIVING: Successfully loaded batch from local store with ${batchVouchers.length} vouchers`);
          return;
        }

        // FINAL FALLBACK: Create a virtual batch from vouchers that need to be received
        console.log('🔄 BATCH RECEIVING: No batch found, creating virtual batch from receivable vouchers...');
        const currentUser = useAppStore.getState().currentUser;
        const allVouchers = updatedState.vouchers;

        // Find vouchers that should be receivable for this department
        const receivableVouchers = allVouchers.filter(v => {
          const isForThisDepartment = v.department === currentUser?.department;
          const isReceivable = (
            v.status === "VOUCHER PROCESSING" ||
            v.certifiedBy ||
            v.status === "VOUCHER REJECTED" ||
            v.status === "VOUCHER RETURNED" ||
            v.isReturned ||
            v.pendingReturn ||
            (v.dispatched && v.auditDispatchedBy)
          );
          const notYetReceived = !v.departmentReceiptTime;

          return isForThisDepartment && isReceivable && notYetReceived;
        });

        if (receivableVouchers.length > 0) {
          console.log(`📦 BATCH RECEIVING: Created virtual batch with ${receivableVouchers.length} receivable vouchers`);

          const virtualBatch = {
            id: batchId,
            department: currentUser?.department || 'Unknown',
            vouchers: receivableVouchers,
            sentBy: 'Audit Department',
            sentTime: new Date().toISOString(),
            fromAudit: true
          };

          setBatch(virtualBatch);
          setBatchVouchers(receivableVouchers);

          // Reset state when new batch is loaded
          // PRODUCTION FIX: Always start with empty selections to prevent hanging
          setAcceptedVouchers([]);
          console.log(`✅ VIRTUAL BATCH: Created virtual batch, user must manually select vouchers`);
          setRejectedVouchers([]);
          setRejectionComments({});

          console.log(`✅ BATCH RECEIVING: Successfully created virtual batch with ${receivableVouchers.length} vouchers`);
          return;
        }

        throw new Error(`No receivable vouchers found for batch ${batchId} - all vouchers may have been processed already`);
      }
    } catch (error) {
      console.error('❌ BATCH RECEIVING: Error fetching batch details:', error);

      // PRODUCTION FIX: Provide more specific error messages
      let errorMessage = 'Failed to load batch details';
      if (error instanceof Error) {
        if (error.message.includes('404') || error.message.includes('not found')) {
          errorMessage = `Batch ${batchId} not found. It may have been processed already or doesn't exist.`;
        } else if (error.message.includes('no vouchers')) {
          errorMessage = `Batch ${batchId} has no vouchers to process. It may have been completed already.`;
        } else {
          errorMessage = `Error loading batch: ${error.message}`;
        }
      }

      toast.error(errorMessage, {
        duration: 5000,
        action: {
          label: 'Close',
          onClick: () => onClose(),
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!batch && !isLoading) return null;

  const handleAcceptVoucher = (voucherId: string) => {
    setAcceptedVouchers(prev => {
      if (prev.includes(voucherId)) {
        return prev.filter(id => id !== voucherId);
      } else {
        // Remove from rejected if it's there
        setRejectedVouchers(prev => prev.filter(id => id !== voucherId));
        return [...prev, voucherId];
      }
    });
  };

  const handleStartRejectVoucher = (voucherId: string) => {
    setCurrentVoucherId(voucherId);
    setRejectionDialogOpen(true);
  };

  const handleConfirmReject = (comment: string) => {
    if (currentVoucherId) {
      // Add to rejected vouchers
      setRejectedVouchers(prev => {
        if (!prev.includes(currentVoucherId)) {
          // Remove from accepted if it's there
          setAcceptedVouchers(prev => prev.filter(id => id !== currentVoucherId));
          return [...prev, currentVoucherId];
        }
        return prev;
      });

      // Save the rejection comment
      setRejectionComments(prev => ({
        ...prev,
        [currentVoucherId]: comment
      }));

      // Close the dialog
      setRejectionDialogOpen(false);
      setCurrentVoucherId(null);
    }
  };

  const handleCompleteBatchReceiving = async () => {
    const unprocessedVouchers = batchVouchers.filter(
      v => !acceptedVouchers.includes(v.id) && !rejectedVouchers.includes(v.id)
    );

    if (unprocessedVouchers.length > 0) {
      toast.error('Please accept or reject all vouchers in the batch', {
        duration: 3000,
      });
      return;
    }

    // Verify that all rejected vouchers have comments
    const missingComments = rejectedVouchers.filter(id => !rejectionComments[id] || rejectionComments[id].trim() === '');
    if (missingComments.length > 0) {
      toast.error('Please provide rejection comments for all rejected vouchers', {
        duration: 3000,
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // PRODUCTION FIX: Add timeout protection to prevent hanging
      console.log(`🔄 BATCH RECEIVING: Starting batch processing with timeout protection...`);

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Batch processing timeout after 20 seconds')), 20000);
      });

      // SMART BATCH LOCKING: Execute batch receive with automatic locking and timeout
      const batchProcessPromise = executeWithBatchLock(
        'batch-receive',
        async () => {
          // Before calling receiveVoucherBatch, add the comments to each rejected voucher
          const finalRejectedVouchers = rejectedVouchers.map(id => {
            const comment = rejectionComments[id];
            return { id, comment };
          });

          // Update the receiveVoucherBatch function to process these comments
          await receiveVoucherBatch(
            batchId,
            acceptedVouchers,
            finalRejectedVouchers.map(item => item.id),
            rejectionComments
          );

          return { acceptedCount: acceptedVouchers.length, rejectedCount: rejectedVouchers.length };
        },
        [...acceptedVouchers, ...rejectedVouchers]
      );

      const result = await Promise.race([batchProcessPromise, timeoutPromise]);

      // Only show success and close if operation was successful (not blocked by lock)
      if (result !== null) {
        toast.success(`Vouchers processed: ${result.acceptedCount} accepted, ${result.rejectedCount} rejected`, {
          duration: 3000,
        });
        onClose();
      }
    } catch (error) {
      console.error('❌ BATCH RECEIVING: Error during batch processing:', error);

      let errorMessage = 'Failed to process batch';
      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          errorMessage = 'Batch processing timed out. Please try again.';
        } else {
          errorMessage = `Batch processing failed: ${error.message}`;
        }
      }

      toast.error(errorMessage, {
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="bg-black text-white border-white/10 max-w-4xl">
          <DialogHeader className="pb-2 shrink-0">
            <DialogTitle className="text-base text-white">Receive Voucher Batch</DialogTitle>
            <DialogDescription className="text-xs text-gray-400">
              {isLoading ? 'Loading batch details...' : `Review and process vouchers from ${batch?.department || 'Unknown'} Department`}
            </DialogDescription>
          </DialogHeader>

          {!isLoading && batch && (
            <div className="flex justify-between items-center py-1 px-1 shrink-0">
              <div className="text-xs text-gray-400">
                Sent by: {batch.sentBy} on {batch.sentTime}
              </div>
              <div className="flex gap-2">
                <Badge variant="success" className="flex gap-1 items-center text-xs py-0.5">
                  <Check className="h-3 w-3" /> {acceptedVouchers.length}
                </Badge>
                <Badge variant="destructive" className="flex gap-1 items-center text-xs py-0.5">
                  <X className="h-3 w-3" /> {rejectedVouchers.length}
                </Badge>
                <Badge variant="outline" className="flex gap-1 items-center text-xs py-0.5">
                  {batchVouchers.length - acceptedVouchers.length - rejectedVouchers.length}
                </Badge>
              </div>
            </div>
          )}

          <div className="overflow-y-auto flex-1 min-h-0">
            <div className="space-y-2 p-1">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-400">Loading voucher details...</div>
                </div>
              ) : batchVouchers.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-400">No vouchers found in this batch</div>
                </div>
              ) : (
                batchVouchers.map((voucher) => {
                  const isAccepted = acceptedVouchers.includes(voucher.id);
                  const isRejected = rejectedVouchers.includes(voucher.id);
                  const isReturning = voucher.pendingReturn || voucher.isReturned;
                  // DUAL-TAB WORKFLOW: Enhanced rejection detection for new workflow
                  const isRejectedVoucher = voucher.isRejectedVoucher ||
                                          voucher.status === "VOUCHER REJECTED" ||
                                          (voucher.rejectedBy && voucher.rejectedBy.trim() !== '') ||
                                          (voucher.rejected_by && voucher.rejected_by.trim() !== '') ||
                                          voucher.isRejectionCopy === true || voucher.isRejectionCopy === 1 ||
                                          voucher.rejectionType === 'DISPATCHABLE_COPY' ||
                                          voucher.rejectionWorkflowStage === 'DISPATCHED';

                return (
                  <div
                    key={voucher.id}
                    className={`py-3 px-4 mb-2 rounded-lg border border-white/10 ${
                      isAccepted ? 'bg-green-950/20' :
                      isRejected ? 'bg-red-950/20' :
                      isReturning ? 'bg-yellow-950/20' : 'bg-[#0f0f0f]'
                    }`}
                  >
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="text-base font-semibold text-white">{voucher.voucherId}</h3>
                          {isReturning && (
                            <Badge variant="outline" className="bg-yellow-900/30 text-yellow-300 border-yellow-600">
                              <CornerDownLeft className="h-3 w-3 mr-1" /> RETURNING
                            </Badge>
                          )}
                          {/* UNIFIED RESUBMISSION BADGE - No duplicates */}
                          <ResubmissionBadge
                            voucher={voucher}
                            size="sm"
                            className="bg-purple-900/30 text-purple-300 border-purple-600"
                          />
                          {/* RETURNED VOUCHER TRACKING: Return badge display */}
                          <ReturnBadge
                            voucher={voucher}
                            size="sm"
                            className="bg-orange-900/30 text-orange-300 border-orange-600"
                          />
                          {isRejectedVoucher &&
                           !(voucher.isResubmitted ||
                             voucher.status === 'RE-SUBMISSION' ||
                             voucher.workflowState?.includes('RESUBMISSION') ||
                             voucher.workflow_state?.includes('RESUBMISSION')) && (
                            <Badge variant="outline" className="bg-red-900/30 text-red-300 border-red-600">
                              <X className="h-3 w-3 mr-1" />
                              {(voucher.isRejectionCopy === true || voucher.isRejectionCopy === 1) ? 'REJECTED BY AUDIT' : 'REJECTED VOUCHER'}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-white/70">Claimant: {voucher.claimant}</p>
                        <p className="text-sm text-white/70">Date: {voucher.date}</p>

                        {/* PHASE 1 RETURN REASON: Show return reason for all returned vouchers */}
                        {(voucher.is_returned_voucher === 1 || voucher.return_count > 0 || isReturning) && (
                          <div className="mt-2 p-2 bg-yellow-900/20 border border-yellow-800/30 rounded-md">
                            <p className="text-xs text-yellow-300 font-medium">RETURN REASON:</p>
                            <p className="text-sm text-white/90">
                              {voucher.original_return_reason ||
                               voucher.originalReturnReason ||
                               voucher.returnComment ||
                               voucher.return_comment ||
                               voucher.comment ||
                               'No return reason recorded'}
                            </p>
                            {(voucher.returnedBy || voucher.returned_by || voucher.original_returned_by) && (
                              <p className="text-xs text-yellow-400 mt-1">
                                Returned by: {voucher.returnedBy || voucher.returned_by || voucher.original_returned_by}
                              </p>
                            )}
                          </div>
                        )}

                        {/* CRITICAL FIX: Show rejection reason for rejected vouchers being dispatched back to finance */}
                        {isRejectedVoucher &&
                         !(voucher.isResubmitted || voucher.status === 'RE-SUBMISSION') &&
                         (voucher.comment || voucher.rejectionReason || voucher.rejection_reason) && (
                          <div className="mt-2 p-2 bg-red-900/20 border border-red-900/30 rounded-md">
                            <p className="text-xs text-red-300 font-medium">REJECTION REASON:</p>
                            <p className="text-sm text-white/90">
                              {voucher.comment || voucher.rejectionReason || voucher.rejection_reason || 'No rejection reason recorded'}
                            </p>
                            {(voucher.rejectedBy || voucher.rejected_by) && (
                              <p className="text-xs text-red-400 mt-1">
                                Rejected by: {voucher.rejectedBy || voucher.rejected_by}
                              </p>
                            )}
                            {voucher.rejectionTime && (
                              <p className="text-xs text-red-400 mt-1">
                                Rejected on: {new Date(voucher.rejectionTime).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                        )}

                        {/* REQUIREMENT 3: Show original rejection reason for resubmitted vouchers */}
                        {(voucher.isResubmitted || voucher.status === 'RE-SUBMISSION') && (
                          <div className="mt-2 p-2 bg-purple-900/20 border border-purple-900/30 rounded-md">
                            <p className="text-xs text-purple-300 font-medium">ORIGINAL REJECTION REASON:</p>
                            <p className="text-sm text-white/90">
                              {(() => {
                                // PRODUCTION FIX: Comprehensive comment resolution logic
                                const originalReason = voucher.original_rejection_reason || voucher.originalRejectionReason;

                                // If original_rejection_reason exists and is not "NO COMMENT PROVIDED", use it
                                if (originalReason && originalReason !== 'NO COMMENT PROVIDED' && originalReason !== 'null' && originalReason !== 'undefined') {
                                  return originalReason;
                                }

                                // If comment exists and is not a generic resubmission message, use it
                                if (voucher.comment &&
                                    !voucher.comment.includes('Re-added from rejection') &&
                                    !voucher.comment.includes('Re-added from returned') &&
                                    voucher.comment !== 'NO COMMENT PROVIDED' &&
                                    voucher.comment !== 'null' &&
                                    voucher.comment !== 'undefined') {
                                  return voucher.comment;
                                }

                                // Check other possible comment fields
                                if (voucher.rejectionReason && voucher.rejectionReason !== 'NO COMMENT PROVIDED') {
                                  return voucher.rejectionReason;
                                }

                                if (voucher.rejection_reason && voucher.rejection_reason !== 'NO COMMENT PROVIDED') {
                                  return voucher.rejection_reason;
                                }

                                // Last resort: show that no reason was provided
                                return 'No rejection reason recorded';
                              })()}
                            </p>
                            {(voucher.original_rejected_by || voucher.originalRejectedBy || voucher.rejectedBy) && (
                              <p className="text-xs text-purple-400 mt-1">
                                Originally rejected by: {voucher.original_rejected_by || voucher.originalRejectedBy || voucher.rejectedBy}
                              </p>
                            )}
                            {(voucher.original_rejection_date || voucher.originalRejectionDate) && (
                              <p className="text-xs text-purple-400 mt-1">
                                Originally rejected on: {new Date(voucher.original_rejection_date || voucher.originalRejectionDate).toLocaleDateString()}
                              </p>
                            )}
                            {voucher.lastResubmissionDate && (
                              <p className="text-xs text-purple-400 mt-1">Resubmitted on: {new Date(voucher.lastResubmissionDate).toLocaleDateString()}</p>
                            )}
                          </div>
                        )}

                        {/* CRITICAL FIX: Show original return reason for resubmitted returned vouchers */}
                        {(voucher.is_returned_voucher === 1 || voucher.return_count > 0) &&
                         (voucher.isResubmitted || voucher.is_resubmitted === 1) && (
                          <div className="mt-2 p-2 bg-orange-900/20 border border-orange-900/30 rounded-md">
                            <p className="text-xs text-orange-300 font-medium">ORIGINAL RETURN REASON (PERSISTENT):</p>
                            <p className="text-sm text-white/90">
                              {voucher.original_return_reason ||
                               voucher.originalReturnReason ||
                               voucher.returnComment ||
                               voucher.return_comment ||
                               // Fallback to comment if it doesn't contain generic messages
                               (voucher.comment && !voucher.comment.includes('Re-added from') ? voucher.comment : null) ||
                               'No return reason recorded'}
                            </p>
                            {(voucher.original_returned_by || voucher.originalReturnedBy || voucher.returned_by) && (
                              <p className="text-xs text-orange-400 mt-1">
                                Originally returned by: {voucher.original_returned_by || voucher.originalReturnedBy || voucher.returned_by}
                              </p>
                            )}
                            {(voucher.last_return_date || voucher.lastReturnDate) && (
                              <p className="text-xs text-orange-400 mt-1">
                                Originally returned on: {new Date(voucher.last_return_date || voucher.lastReturnDate).toLocaleDateString()}
                              </p>
                            )}
                            <p className="text-xs text-orange-500 mt-1 italic">
                              ℹ️ This reason persists through all resubmission cycles for context
                            </p>
                          </div>
                        )}


                      </div>
                      <div>
                        <p className="text-sm mb-1"><span className="font-medium">Description:</span> {voucher.description}</p>
                        {/* REQUIREMENT 4: Display appropriate amount based on voucher state */}
                        {(() => {
                          const isCertified = voucher.certifiedBy || voucher.status === 'VOUCHER CERTIFIED';
                          const displayAmount = isCertified && voucher.preAuditedAmount
                            ? voucher.preAuditedAmount
                            : voucher.amount;
                          const amountLabel = isCertified && voucher.preAuditedAmount
                            ? 'Certified Amount'
                            : 'Amount';

                          return (
                            <div>
                              <p className="text-sm font-semibold">
                                {amountLabel}: {displayAmount.toFixed(2)} {voucher.currency}
                              </p>
                              {isCertified && voucher.preAuditedAmount && voucher.preAuditedAmount !== voucher.amount && (
                                <p className="text-xs text-white/70">
                                  Original Amount: {voucher.amount.toFixed(2)} {voucher.currency}
                                </p>
                              )}
                              {voucher.taxAmount && voucher.taxAmount > 0 && (
                                <p className="text-xs text-white/70">
                                  Tax Amount: {voucher.taxAmount.toFixed(2)} {voucher.currency}
                                </p>
                              )}
                            </div>
                          );
                        })()}



                        <div className="flex justify-end mt-3 gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className={`${isRejected ? 'bg-red-500 text-white hover:bg-red-600' : ''} px-4`}
                            onClick={() => isRejected ? handleAcceptVoucher(voucher.id) : handleStartRejectVoucher(voucher.id)}
                            disabled={!isEditable || isRejectedVoucher} // Disable reject button for already rejected vouchers
                            title={isRejectedVoucher ? "This voucher was already rejected by Audit and cannot be rejected again" : ""}
                          >
                            <X className="h-4 w-4 mr-2" />
                            {isRejected ? 'Undo Reject' : 'Reject'}
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            className={`${isAccepted ? 'bg-green-500 text-white hover:bg-green-600' : ''} px-4`}
                            onClick={() => handleAcceptVoucher(voucher.id)}
                            disabled={!isEditable}
                          >
                            <Check className="h-4 w-4 mr-2" />
                            {isAccepted ? 'Undo Accept' : 'Accept'}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                );
                })
              )}
            </div>
          </div>

          <DialogFooter className="pt-4 mt-2 border-t border-white/10">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting || isLoading}
              className="h-9"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCompleteBatchReceiving}
              disabled={
                isSubmitting ||
                isLoading ||
                isOperationInProgress ||
                acceptedVouchers.length + rejectedVouchers.length !== batchVouchers.length ||
                !isEditable ||
                batchVouchers.length === 0
              }
              className="h-9 bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? 'Loading...' : isOperationInProgress ? 'Processing...' : 'Complete Processing'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection dialog */}
      <VoucherRejectionDialog
        isOpen={rejectionDialogOpen}
        onClose={() => {
          setRejectionDialogOpen(false);
          setCurrentVoucherId(null);
        }}
        onConfirm={handleConfirmReject}
      />
    </>
  );
}
