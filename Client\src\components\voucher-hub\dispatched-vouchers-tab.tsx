
import React, { useState, useRef, useMemo, useEffect } from 'react';
import { formatNumberWithCommas, formatVMSDateTime } from '@/utils/formatUtils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Voucher, TaxType, Department } from '@/lib/types';
import { SortableColumnHeader } from './sortable-column-header';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogHeader } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { ArrowUpDown, Download, Filter, Edit3, Save, X, Check, Paperclip } from 'lucide-react';
import { exportVouchersToExcel, groupVouchersByMonth } from '@/utils/exportUtils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { UnifiedVoucherBadges } from '@/components/common/UnifiedVoucherBadges';
import { PostTransactionEditModal } from './post-transaction-edit-modal';
import { useVoucherStore } from '@/lib/store/hooks';
import { useAppStore } from '@/lib/store';
import { toast } from 'sonner';
import { AttachmentViewer } from '@/components/audit/attachment-viewer';
import { usersApi } from '@/lib/api';

interface DispatchedVouchersTabProps {
  filteredVouchers: Voucher[];
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  onViewVoucher?: (voucher: Voucher) => void;
  isEditable?: boolean;
}

// REMOVED: Legacy badge logic replaced by unified system

export function DispatchedVouchersTab({
  filteredVouchers,
  sortColumn,
  sortDirection,
  handleSort,
  onViewVoucher,
  isEditable = true,
}: DispatchedVouchersTabProps) {
  const [selectedVoucher, setSelectedVoucher] = useState<Voucher | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<string>('All');
  const [selectedVoucherForAttachments, setSelectedVoucherForAttachments] = useState<string | null>(null);
  const [showAttachmentModal, setShowAttachmentModal] = useState(false);
  const [attachmentCounts, setAttachmentCounts] = useState<Record<string, number>>({});
  const [attachmentCountsLoaded, setAttachmentCountsLoaded] = useState(false);
  const currentUser = useAppStore((state) => state.currentUser);



  const handleCloseAttachmentModal = () => {
    setShowAttachmentModal(false);
    setSelectedVoucherForAttachments(null);
  };

  // Load actual attachment count for a voucher
  const loadAttachmentCount = async (voucherId: string) => {
    if (currentUser?.department !== 'AUDIT') return;

    try {
      const attachments = await usersApi.getVoucherAttachments(voucherId);
      setAttachmentCounts(prev => ({
        ...prev,
        [voucherId]: attachments.length
      }));
    } catch (error) {
      // If error, set count to 0
      setAttachmentCounts(prev => ({
        ...prev,
        [voucherId]: 0
      }));
    }
  };
  // Load attachment counts for all vouchers once when component mounts
  useEffect(() => {
    if (currentUser?.department === 'AUDIT' && !attachmentCountsLoaded && filteredVouchers.length > 0) {
      const loadAllAttachmentCounts = async () => {
        console.log('Loading attachment counts for', filteredVouchers.length, 'vouchers');
        setAttachmentCountsLoaded(true); // Set flag immediately to prevent re-runs

        const counts: Record<string, number> = {};

        // Load counts for all vouchers in parallel
        const promises = filteredVouchers.map(async (voucher) => {
          try {
            const attachments = await usersApi.getVoucherAttachments(voucher.id);
            counts[voucher.id] = attachments.length;
          } catch (error) {
            counts[voucher.id] = 0; // Default to 0 on error
          }
        });

        await Promise.all(promises);
        setAttachmentCounts(counts);
        console.log('Loaded attachment counts:', counts);
      };

      loadAllAttachmentCounts();
    }
  }, [currentUser?.department, attachmentCountsLoaded, filteredVouchers.length]); // Only depend on length, not the array itself





  // Handle attachment modal
  const handleOpenAttachmentModal = (voucherId: string) => {
    setSelectedVoucherForAttachments(voucherId);
    setShowAttachmentModal(true);
    // Load actual attachment count when opening modal
    loadAttachmentCount(voucherId);
  };



  const handleUploadSuccess = (shouldCloseModal = false) => {
    // Reload actual attachment count after upload
    if (selectedVoucherForAttachments) {
      loadAttachmentCount(selectedVoucherForAttachments);
    }

    // Close modal if requested (when "Done" is clicked)
    if (shouldCloseModal) {
      handleCloseAttachmentModal();
    }
  };

  // POST-TRANSACTION EDIT STATE
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingVoucher, setEditingVoucher] = useState<Voucher | null>(null);
  const [isSaving, setIsSaving] = useState(false);



  // Refs for synchronized scrolling
  const headerRef = useRef<HTMLDivElement>(null);
  const bodyRef = useRef<HTMLDivElement>(null);

  // Filter vouchers by selected month
  const vouchersByMonth = groupVouchersByMonth(filteredVouchers);

  // Get all months that have vouchers
  const availableMonths = Object.entries(vouchersByMonth)
    .filter(([_, vouchers]) => vouchers.length > 0)
    .map(([month]) => month);

  // Get the vouchers for the selected month or all vouchers if 'All' is selected
  const displayedVouchers = selectedMonth === 'All'
    ? filteredVouchers
    : vouchersByMonth[selectedMonth] || [];

  // Set up synchronized scrolling between header and body
  useEffect(() => {
    const headerElement = headerRef.current;
    const bodyElement = bodyRef.current;

    if (!headerElement || !bodyElement) return;

    const handleHeaderScroll = () => {
      if (bodyElement) {
        bodyElement.scrollLeft = headerElement.scrollLeft;
      }
    };

    const handleBodyScroll = () => {
      if (headerElement) {
        headerElement.scrollLeft = bodyElement.scrollLeft;
      }
    };

    headerElement.addEventListener('scroll', handleHeaderScroll);
    bodyElement.addEventListener('scroll', handleBodyScroll);

    return () => {
      headerElement.removeEventListener('scroll', handleHeaderScroll);
      bodyElement.removeEventListener('scroll', handleBodyScroll);
    };
  }, []);

  const handleViewVoucher = (voucher: Voucher) => {
    if (onViewVoucher) {
      onViewVoucher(voucher);
    } else {
      setSelectedVoucher(voucher);
    }
  };

  const handleCloseDialog = () => {
    setSelectedVoucher(null);
  };

  // Handle export to Excel
  const handleExport = () => {
    exportVouchersToExcel(
      displayedVouchers,
      `dispatched-vouchers-${selectedMonth.toLowerCase() !== 'all' ? selectedMonth.toLowerCase() + '-' : ''}${new Date().toISOString().split('T')[0]}`
    );
  };

  // POST-TRANSACTION EDIT FUNCTIONS
  const canEdit = currentUser?.department === 'AUDIT' && isEditable;

  const startEditing = (voucher: Voucher) => {
    if (!canEdit) return;

    setEditingVoucher(voucher);
    setEditModalOpen(true);
  };

  const closeEditModal = () => {
    setEditModalOpen(false);
    setEditingVoucher(null);
  };

  const handleSaveEdit = async (voucherId: string, editData: any, auditReason: string) => {
    if (!canEdit || isSaving) return;

    setIsSaving(true);
    try {
      const response = await fetch(`/api/vouchers/${voucherId}/post-transaction-edit`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          ...editData,
          auditReason: auditReason
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save changes');
      }

      const result = await response.json();

      // Reset editing state
      setEditingVoucher(null);
      setEditModalOpen(false);

      // Show success message
      toast.success("Voucher updated successfully");

      // Trigger a data refresh without full page reload
      // This will cause the parent component to refetch the vouchers
      window.dispatchEvent(new CustomEvent('voucherUpdated', {
        detail: { voucherId: editingVoucher.id, updatedData: result }
      }));

    } catch (error) {
      console.error('Error saving edit:', error);
      throw error; // Re-throw to let modal handle the error display
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-2">
      {/* Controls for filtering and export */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4" />
          <span className="text-sm font-medium">Filter by Month:</span>
          <Select value={selectedMonth} onValueChange={setSelectedMonth} disabled={!isEditable}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Month" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All Months</SelectItem>
              {availableMonths.map(month => (
                <SelectItem key={month} value={month}>{month}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button
          variant="outline"
          size="sm"
          className="flex items-center space-x-2"
          onClick={handleExport}
          disabled={!isEditable}
        >
          <Download className="h-4 w-4" />
          <span>Export to Excel</span>
        </Button>
      </div>

      <div className="flex flex-col rounded-md border">
        {/* Fixed header */}
        <div className="sticky top-0 z-10 bg-background overflow-hidden">
          <div ref={headerRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
              <thead>
                <tr className="bg-background">
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('voucherId')}
                    >
                      <span>VOUCHER ID</span>
                      {sortColumn === 'voucherId' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[20%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('date')}
                    >
                      <span>DATE</span>
                      {sortColumn === 'date' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[20%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('claimant')}
                    >
                      <span>CLAIMANT</span>
                      {sortColumn === 'claimant' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[25%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('description')}
                    >
                      <span>DESCRIPTION</span>
                      {sortColumn === 'description' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[10%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('amount')}
                    >
                      <span>AMOUNT</span>
                      {sortColumn === 'amount' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('preAuditedAmount')}
                    >
                      <span>CERTIFIED AMT</span>
                      {sortColumn === 'preAuditedAmount' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('taxType')}
                    >
                      <span>TAX DETAILS</span>
                      {sortColumn === 'taxType' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[12%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('preAuditedBy')}
                    >
                      <span>PRE-AUDITED BY</span>
                      {sortColumn === 'preAuditedBy' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[12%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('certifiedBy')}
                    >
                      <span>CERTIFIED BY</span>
                      {sortColumn === 'certifiedBy' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('auditDispatchedBy')}
                    >
                      <span>DISPATCHED BY</span>
                      {sortColumn === 'auditDispatchedBy' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  <th className="w-[15%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                    <button
                      className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
                      onClick={() => handleSort('auditDispatchTime')}
                    >
                      <span>DISPATCH TIME</span>
                      {sortColumn === 'auditDispatchTime' && (
                        <ArrowUpDown className="h-3 w-3 ml-1" />
                      )}
                    </button>
                  </th>
                  {canEdit && (
                    <th className="w-[10%] p-4 text-center font-medium uppercase whitespace-nowrap bg-background">
                      ACTIONS
                    </th>
                  )}
                </tr>
              </thead>
            </table>
          </div>
        </div>

        {/* Scrollable body */}
        <div className="overflow-auto h-[60vh] scrollbar-visible" style={{ scrollbarWidth: 'thin', overflowY: 'scroll' }}>
          <div ref={bodyRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
              <tbody>
              {displayedVouchers.length === 0 ? (
                <tr className="border-b h-14">
                  <td colSpan={canEdit ? 12 : 11} className="p-4 text-center uppercase font-medium">
                    {selectedMonth !== 'All'
                      ? `NO DISPATCHED VOUCHERS FOUND FOR ${selectedMonth.toUpperCase()}.`
                      : 'NO DISPATCHED VOUCHERS FOUND.'}
                  </td>
                </tr>
              ) : (
                displayedVouchers.map((voucher) => (
                  <React.Fragment key={voucher.id}>
                    <tr
                      className="border-b cursor-pointer hover:bg-muted/50 h-14"
                      onClick={() => handleViewVoucher(voucher)}
                    >
                    <td className="font-medium uppercase p-4 align-middle w-[15%] text-center">
                      <div className="flex flex-col items-center gap-1">
                        <span>{voucher.voucherId}</span>
                        {/* PRODUCTION FIX: Unified badge system for dispatched tab */}
                        <UnifiedVoucherBadges
                          voucher={voucher}
                          tabName="dispatched"
                          size="sm"
                          className="flex-wrap justify-center"
                        />
                      </div>
                    </td>
                    <td className="uppercase p-4 align-middle w-[20%] text-center">{voucher.date}</td>
                    <td className="uppercase p-4 align-middle w-[20%] text-center">
                      {voucher.claimant}
                    </td>
                    <td className="max-w-xs truncate uppercase p-4 align-middle w-[25%] text-center">
                      {voucher.description}
                    </td>
                    <td className="uppercase whitespace-nowrap p-4 align-middle w-[10%] text-center text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {formatNumberWithCommas(voucher.amount)} {voucher.currency}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{formatNumberWithCommas(voucher.amount)} {voucher.currency}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase whitespace-nowrap p-4 align-middle w-[15%] text-center text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase whitespace-nowrap p-4 align-middle w-[15%] text-center text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.taxType && voucher.taxAmount
                                ? `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}`
                                : (voucher.taxType || '-')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {voucher.taxType && voucher.taxAmount
                                ? `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}`
                                : (voucher.taxType || '-')}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[12%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.preAuditedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Pre-Audited By</p>
                              <p>{voucher.preAuditedBy || 'Not pre-audited'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[12%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.certifiedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Certified By</p>
                              <p>{voucher.certifiedBy || 'Not certified'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.auditDispatchedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Dispatched By</p>
                              <p>{voucher.auditDispatchedBy || 'Not dispatched'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.auditDispatchTime ? formatVMSDateTime(voucher.auditDispatchTime) : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Dispatch Time</p>
                              <p>{voucher.auditDispatchTime ? formatVMSDateTime(voucher.auditDispatchTime) : 'Not dispatched'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    {canEdit && (
                      <td className="p-2 align-middle w-[10%] text-center">
                        <div className="flex items-center justify-center gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              startEditing(voucher);
                            }}
                            className="h-8 w-8 p-0 hover:bg-green-50 border-green-200"
                            title="Edit Voucher"
                          >
                            <Edit3 className="h-4 w-4 text-green-600" />
                          </Button>

                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleOpenAttachmentModal(voucher.id);
                            }}

                            className="h-8 w-8 p-0 relative hover:bg-purple-50 border-purple-200"
                            title="View/Upload attachments for this voucher"
                          >
                            <Paperclip className={`h-4 w-4 ${
                              attachmentCounts[voucher.id] > 0 ? 'text-green-600' : 'text-purple-600'
                            }`} />
                            {attachmentCounts[voucher.id] > 0 && (
                              <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                                {attachmentCounts[voucher.id]}
                              </span>
                            )}
                          </Button>
                        </div>
                      </td>
                    )}
                  </tr>
                  </React.Fragment>
                ))
              )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Attachment Upload Modal - Only for Audit Department */}
      {currentUser?.department === 'AUDIT' && showAttachmentModal && selectedVoucherForAttachments && (
        <Dialog open={showAttachmentModal} onOpenChange={setShowAttachmentModal}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Voucher Attachments</DialogTitle>
              <DialogDescription>
                Upload and manage attachments for this voucher
              </DialogDescription>
            </DialogHeader>

            <AttachmentViewer
              voucherId={selectedVoucherForAttachments}
              onAttachmentsChange={() => {
                // Reload actual attachment count when attachments change
                if (selectedVoucherForAttachments) {
                  loadAttachmentCount(selectedVoucherForAttachments);
                }
              }}
              readOnly={false}
            />

            {/* OK Button to close modal */}
            <div className="flex justify-end pt-4 border-t">
              <Button
                onClick={handleCloseAttachmentModal}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6"
              >
                OK
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Voucher Details Dialog - shown only when onViewVoucher prop is not provided */}
      {selectedVoucher && !onViewVoucher && (
        <Dialog open={!!selectedVoucher} onOpenChange={() => handleCloseDialog()}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="uppercase">Voucher Details: {selectedVoucher.voucherId}</DialogTitle>
              <DialogDescription>
                Complete information about this voucher
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Voucher ID:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.voucherId}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Date:</div>
                <div className="col-span-3">{formatVMSDate(selectedVoucher.date)}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Claimant:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.claimant}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Description:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.description}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Amount:</div>
                <div className="col-span-3">
                  {formatNumberWithCommas(selectedVoucher.amount)} {selectedVoucher.currency}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Pre-Audited Amount:</div>
                <div className="col-span-3">
                  {selectedVoucher.preAuditedAmount
                    ? `${formatNumberWithCommas(selectedVoucher.preAuditedAmount)} ${selectedVoucher.currency}`
                    : '-'}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Tax Details:</div>
                <div className="col-span-3 uppercase">
                  {selectedVoucher.taxType && selectedVoucher.taxAmount
                    ? `${selectedVoucher.taxType}: ${formatNumberWithCommas(selectedVoucher.taxAmount)} ${selectedVoucher.currency}`
                    : (selectedVoucher.taxType || '-')}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Department:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.department}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Pre-Audited By:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.preAuditedBy || '-'}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Certified By:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.certifiedBy || '-'}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Dispatched By:</div>
                <div className="col-span-3 uppercase">{selectedVoucher.auditDispatchedBy || '-'}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Dispatch Time:</div>
                <div className="col-span-3">
                  {selectedVoucher.auditDispatchTime ? formatVMSDateTime(selectedVoucher.auditDispatchTime) : '-'}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-semibold text-right">Department Receipt:</div>
                <div className="col-span-3">
                  {selectedVoucher.departmentReceiptTime
                    ? `Received by ${selectedVoucher.departmentReceivedBy} at ${selectedVoucher.departmentReceiptTime}`
                    : 'Not yet received by department'}
                </div>
              </div>
              {selectedVoucher.comment && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="font-semibold text-right">Comment:</div>
                  <div className="col-span-3 uppercase">{selectedVoucher.comment}</div>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Post-Transaction Edit Modal */}
      <PostTransactionEditModal
        isOpen={editModalOpen}
        onClose={closeEditModal}
        voucher={editingVoucher}
        onSave={handleSaveEdit}
        isSaving={isSaving}
      />
    </div>
  );
}
