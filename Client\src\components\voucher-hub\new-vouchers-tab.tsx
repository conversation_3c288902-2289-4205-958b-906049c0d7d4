import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { formatNumberWithCommas, formatVMSDateTime, formatVMSDate } from '@/utils/formatUtils';
import { Pencil, Check, X, RotateCcw, Paperclip } from 'lucide-react';
import { VoucherReturnDialog } from '@/components/voucher-return-dialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { HoldBadge } from '@/components/ui/hold-badge';
import { HoldControls } from '@/components/ui/hold-controls';
import { HoldDialog } from '@/components/ui/hold-dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Voucher, TaxType } from '@/lib/types';
import { SortableColumnHeader } from './sortable-column-header';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogTitle, DialogDescription, DialogHeader } from '@/components/ui/dialog';
import { UnifiedVoucherBadges } from '@/components/common/UnifiedVoucherBadges';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  PopoverClose,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAppStore } from '@/lib/store';
import { toast } from 'sonner';
import { AttachmentViewer } from '@/components/audit/attachment-viewer';
import { usersApi } from '@/lib/api';
import { formatCurrentDate } from '@/lib/store/utils';



interface NewVouchersTabProps {
  filteredVouchers: Voucher[];
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  voucherEdits: Record<string, any>;
  handleVoucherEdit: (voucherId: string, field: string, value: any) => void;
  handleSaveVoucherEdits: (voucherId: string) => void;
  auditUsers: string[];
  setActiveTab?: (tab: string) => void;
  isEditable?: boolean;
  department?: string; // CRITICAL FIX: Add department prop for dynamic return dialog text
}

export function NewVouchersTab({
  filteredVouchers,
  sortColumn,
  sortDirection,
  handleSort,
  voucherEdits,
  handleVoucherEdit,
  handleSaveVoucherEdits,
  auditUsers,
  setActiveTab,
  isEditable = true,
  department = 'FINANCE', // CRITICAL FIX: Default to FINANCE for backward compatibility
}: NewVouchersTabProps) {
  const [editingVoucher, setEditingVoucher] = useState<string | null>(null);
  const [returnCommentMap, setReturnCommentMap] = useState<Record<string, string>>({});
  const [showReturnCommentMap, setShowReturnCommentMap] = useState<Record<string, boolean>>({});

  // HOLD FEATURE: State management
  const [holdDialogOpen, setHoldDialogOpen] = useState(false);
  const [selectedVoucherForHold, setSelectedVoucherForHold] = useState<any>(null);
  const [showReturnDialog, setShowReturnDialog] = useState(false);
  const [currentReturnVoucherId, setCurrentReturnVoucherId] = useState<string | null>(null);

  // Store hooks
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const currentUser = useAppStore((state) => state.currentUser);
  const [selectedVoucherForAttachment, setSelectedVoucherForAttachment] = useState<string | null>(null);
  const [attachmentRefreshTrigger, setAttachmentRefreshTrigger] = useState(0);
  const [showAttachmentModal, setShowAttachmentModal] = useState(false);
  const [attachmentCounts, setAttachmentCounts] = useState<Record<string, number>>({});
  // Memoize selected voucher data to prevent infinite re-renders
  const selectedVoucherData = useMemo(() => {
    if (!selectedVoucherForAttachment) return null;
    return filteredVouchers.find(v => v.id === selectedVoucherForAttachment);
  }, [selectedVoucherForAttachment, filteredVouchers]);

  // Handle closing attachment modal (MOVED UP to fix circular dependency)
  const handleCloseAttachmentModal = useCallback(() => {
    setShowAttachmentModal(false);
    setSelectedVoucherForAttachment(null);
  }, []);

  // Load actual attachment count for a voucher
  const loadAttachmentCount = useCallback(async (voucherId: string) => {
    if (currentUser?.department !== 'AUDIT') return;

    try {
      const attachments = await usersApi.getVoucherAttachments(voucherId);
      setAttachmentCounts(prev => ({
        ...prev,
        [voucherId]: attachments.length
      }));
    } catch (error) {
      // If error, set count to 0
      setAttachmentCounts(prev => ({
        ...prev,
        [voucherId]: 0
      }));
    }
  }, [currentUser?.department]);

  // Handle opening attachment modal
  const handleOpenAttachmentModal = useCallback((voucherId: string) => {
    setSelectedVoucherForAttachment(voucherId);
    setShowAttachmentModal(true);
    // Load actual attachment count when opening modal
    loadAttachmentCount(voucherId);
  }, [loadAttachmentCount]);

  // Simple upload success callback without complex API calls
  const handleUploadSuccess = useCallback((shouldCloseModal = false) => {
    setAttachmentRefreshTrigger(prev => prev + 1);

    // Reload actual attachment count after upload
    if (selectedVoucherForAttachment) {
      loadAttachmentCount(selectedVoucherForAttachment);
    }

    // Close modal if requested (when "Done" is clicked)
    if (shouldCloseModal) {
      handleCloseAttachmentModal();
    }
  }, [selectedVoucherForAttachment, handleCloseAttachmentModal, loadAttachmentCount]);

  // Refs for synchronized scrolling
  const headerRef = useRef<HTMLDivElement>(null);
  const bodyRef = useRef<HTMLDivElement>(null);

  // Set up synchronized scrolling between header and body
  useEffect(() => {
    const headerElement = headerRef.current;
    const bodyElement = bodyRef.current;

    if (!headerElement || !bodyElement) return;

    const handleHeaderScroll = () => {
      if (bodyElement) {
        bodyElement.scrollLeft = headerElement.scrollLeft;
      }
    };

    const handleBodyScroll = () => {
      if (headerElement) {
        headerElement.scrollLeft = bodyElement.scrollLeft;
      }
    };

    headerElement.addEventListener('scroll', handleHeaderScroll);
    bodyElement.addEventListener('scroll', handleBodyScroll);

    return () => {
      headerElement.removeEventListener('scroll', handleHeaderScroll);
      bodyElement.removeEventListener('scroll', handleBodyScroll);
    };
  }, []);
  const [openTaxPopover, setOpenTaxPopover] = useState<string | null>(null);
  const [openOptionsPopover, setOpenOptionsPopover] = useState<string | null>(null);

  const handleStartEditing = (voucherId: string) => {
    setEditingVoucher(voucherId);

    if (!voucherEdits[voucherId]) {
      const voucher = filteredVouchers.find(v => v.id === voucherId);
      if (voucher) {
        handleVoucherEdit(voucherId, 'preAuditedAmount', voucher.amount);
      }
    }
  };

  const handleCancelEditing = (voucherId: string) => {
    setEditingVoucher(null);
    setOpenTaxPopover(null);
    setOpenOptionsPopover(null);

    if (voucherEdits[voucherId]) {
      handleVoucherEdit(voucherId, null, null);
    }
  };

  const handleSave = (voucherId: string) => {
    handleSaveVoucherEdits(voucherId);
    setEditingVoucher(null);
    setOpenTaxPopover(null);
    setOpenOptionsPopover(null);
  };

  const handleReturnVoucherToggle = (voucherId: string, isChecked: boolean) => {
    if (isChecked) {
      setShowReturnCommentMap(prev => ({...prev, [voucherId]: true}));
    } else {
      handleVoucherEdit(voucherId, 'isReturned', false);
      setShowReturnCommentMap(prev => ({ ...prev, [voucherId]: false }));
    }
  };

  const handleReturnCommentChange = (voucherId: string, comment: string) => {
    setReturnCommentMap(prev => ({ ...prev, [voucherId]: comment }));
  };



  const handleConfirmReturn = (voucherId: string) => {
    const comment = returnCommentMap[voucherId] || 'NO COMMENT PROVIDED';

    console.log(`Confirming return for voucher ${voucherId} with comment: "${comment}"`);

    handleVoucherEdit(voucherId, 'isReturned', true);
    handleVoucherEdit(voucherId, 'returnComment', comment);
    handleVoucherEdit(voucherId, 'comment', comment);

    // RETURNED VOUCHER TRACKING: Set return voucher flag for badge system
    handleVoucherEdit(voucherId, 'isReturnedVoucher', true);
    handleVoucherEdit(voucherId, 'is_returned_voucher', 1);

    handleSaveVoucherEdits(voucherId);

    setShowReturnCommentMap(prev => ({ ...prev, [voucherId]: false }));
    setReturnCommentMap(prev => {
      const newMap = { ...prev };
      delete newMap[voucherId];
      return newMap;
    });
  };

  // NEW RETURN WORKFLOW: Handle return voucher action
  const handleReturnVoucher = (voucherId: string) => {
    setCurrentReturnVoucherId(voucherId);
    setShowReturnDialog(true);
  };

  const handleConfirmReturnVoucher = async (voucherId: string, returnReason: string, dispatcher: string) => {
    try {
      console.log(`Processing return for voucher ${voucherId} with reason: "${returnReason}" and dispatcher: "${dispatcher}"`);

      // Call the return voucher API
      const response = await fetch(`/api/vouchers/${voucherId}/return`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          returnReason,
          dispatcher
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to return voucher');
      }

      const result = await response.json();

      toast.success(`Voucher ${voucherId} returned to ${department} successfully`, {
        duration: 3000,
      });

      // REAL-TIME TAB MOVEMENT FIX: Force immediate UI update for return
      setTimeout(() => {
        // Dispatch custom event to trigger immediate tab refresh
        window.dispatchEvent(new CustomEvent('voucherUpdated', {
          detail: {
            voucherId,
            type: 'voucher_returned',
            status: 'VOUCHER RETURNED',
            returnReason: returnReason
          }
        }));
        console.log(`🔄 REAL-TIME: Dispatched voucherUpdated event for immediate RETURNED tab movement`);
      }, 10);

      // Close dialog
      setShowReturnDialog(false);
      setCurrentReturnVoucherId(null);

    } catch (error) {
      console.error('Error returning voucher:', error);
      toast.error('Failed to return voucher. Please try again.', {
        duration: 3000,
      });
    }
  };

  const handleCloseReturnDialog = () => {
    setShowReturnDialog(false);
    setCurrentReturnVoucherId(null);
  };

  const handleCancelReturn = (voucherId: string) => {
    setShowReturnCommentMap(prev => ({ ...prev, [voucherId]: false }));
    setReturnCommentMap(prev => {
      const newMap = { ...prev };
      delete newMap[voucherId];
      return newMap;
    });
  };

  // HOLD FEATURE: Handle hold voucher actions
  const handleHoldVoucher = (voucher: any) => {
    setSelectedVoucherForHold(voucher);
    setHoldDialogOpen(true);
  };

  const handleToggleVoucherHold = async (voucherId: string, isOnHold: boolean, holdComment: string) => {
    try {
      const response = await fetch(`/api/vouchers/${voucherId}/toggle-hold`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isOnHold,
          holdComment
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle voucher hold status');
      }

      const result = await response.json();
      console.log(`✅ HOLD FEATURE: Voucher ${voucherId} ${isOnHold ? 'put on hold' : 'resumed'}`);

      // HOLD FEATURE FIX: No page reload - let the voucher stay visible
      // The hold status will be updated in the next natural refresh
      console.log('🎯 Hold action completed - voucher should remain visible');

    } catch (error: any) {
      console.error('Error toggling voucher hold:', error);
      alert(`Error: ${error.message}`);
    }
  };

  const getTaxOptions = (): TaxType[] => [
    "NONE",
    "GOODS 3%",
    "SERVICE 7.5%",
    "WORKS 5%",
    "RENT 8%",
    "PCC 12.5%",
    "RISK 5%",
    "VEH.MAINT 10%",
    "OTHER"
  ];

  const getReturnOptions = () => [
    { value: "NO", label: "NO" },
    { value: "YES", label: "YES" },
  ];

  const getProvisionalCashOptions = () => [
    { value: "NO", label: "NO" },
    { value: "YES", label: "YES" },
  ];

  return (
    <div className="space-y-2">
      <div className="flex flex-col rounded-md border">
        {/* Fixed header */}
        <div className="sticky top-0 z-10 bg-background overflow-hidden">
          <div ref={headerRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
            <thead>
              <tr className="bg-background">
                <SortableColumnHeader
                  title="VOUCHER ID"
                  sortKey="voucherId"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap w-[8%] px-4 text-center"
                />
                <SortableColumnHeader
                  title="DATE RECEIVED"
                  sortKey="date"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap w-[12%] px-4 text-center"
                />
                <SortableColumnHeader
                  title="CLAIMANT"
                  sortKey="claimant"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap w-[12%] px-4 text-center"
                />
                <SortableColumnHeader
                  title="DESCRIPTION"
                  sortKey="description"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap w-[15%] px-4 text-center"
                />
                <SortableColumnHeader
                  title="AMOUNT"
                  sortKey="amount"
                  currentSortColumn={sortColumn}
                  currentSortDirection={sortDirection}
                  onSort={handleSort}
                  className="whitespace-nowrap w-[8%] px-4 text-center"
                />
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[12%] px-4 text-center">CERTIFIED AMT</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[10%] px-4 text-center">TAX</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[12%] px-4 text-center">PRE-AUDITED BY</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[12%] px-4 text-center">CERTIFIED BY</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[10%] px-4 text-center">OTHER ACTIONS</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[7%] px-4 text-center">HOLD</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[7%] px-4 text-center">RETURN</TableHead>
                <TableHead className="uppercase whitespace-nowrap sticky top-0 bg-background z-10 w-[7%] px-4 text-center">SAVE</TableHead>
              </tr>
            </thead>
            </table>
          </div>
        </div>

        {/* Scrollable body */}
        <div className="overflow-auto h-[60vh] scrollbar-visible" style={{ scrollbarWidth: 'thin', overflowY: 'scroll' }}>
          <div ref={bodyRef} className="overflow-x-auto scrollbar-visible" style={{ scrollbarWidth: 'thin' }}>
            <table className="w-full table-fixed" style={{ tableLayout: 'fixed', minWidth: '1500px' }}>
              <tbody>
              {filteredVouchers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={12} className="h-24 text-center uppercase">
                    NO NEW VOUCHERS FOUND.
                  </TableCell>
                </TableRow>
              ) : (
                filteredVouchers.map((voucher) => {
                  const isEditing = editingVoucher === voucher.id;
                  const edits = voucherEdits[voucher.id] || {};
                  const showReturnComment = showReturnCommentMap[voucher.id] || false;

                  return (
                    <React.Fragment key={voucher.id}>
                      <TableRow
                        className={`hover:bg-muted/50 ${isEditable ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'}`}
                        data-voucher-id={voucher.id}
                        onClick={() => {
                          if (!isEditing && isEditable) {
                            setEditingVoucher(voucher.id);
                            // Initialize voucher edits using the proper handler
                            handleVoucherEdit(voucher.id, 'claimant', voucher.claimant);
                            handleVoucherEdit(voucher.id, 'description', voucher.description);
                            handleVoucherEdit(voucher.id, 'amount', voucher.amount);
                            handleVoucherEdit(voucher.id, 'date', voucher.date);
                          }
                        }}
                      >
                        <TableCell className="font-medium uppercase w-[8%] px-4 text-center">
                          <div className="flex flex-col items-center gap-1">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="block truncate">{voucher.voucherId}</span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{voucher.voucherId}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            {/* PRODUCTION FIX: Unified badge system for new vouchers tab */}
                            <UnifiedVoucherBadges
                              voucher={voucher}
                              tabName="new-vouchers"
                              size="sm"
                              className="flex-wrap justify-center"
                            />
                            {/* HOLD FEATURE: Hold badge overlay */}
                            <HoldBadge
                              isOnHold={voucher.isOnHold === true || voucher.is_on_hold === true}
                              holdComment={voucher.holdComment || voucher.hold_comment}
                              size="sm"
                            />
                          </div>
                        </TableCell>
                        <TableCell className="uppercase whitespace-nowrap overflow-hidden text-ellipsis w-[12%] px-4 text-center">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">
                                  {voucher.receiptTime ? formatVMSDateTime(voucher.receiptTime) : 'Not Received'}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-center">
                                  <p className="font-semibold">Date Received by Audit</p>
                                  <p>{voucher.receiptTime ? formatVMSDateTime(voucher.receiptTime) : 'Not yet received'}</p>
                                  {voucher.receivedBy && (
                                    <>
                                      <p className="font-semibold mt-2">Received By</p>
                                      <p>{voucher.receivedBy}</p>
                                    </>
                                  )}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase whitespace-nowrap overflow-hidden text-ellipsis w-[12%] px-4 text-center">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{voucher.claimant}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{voucher.claimant}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase overflow-hidden text-ellipsis w-[15%] px-4 text-center">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{voucher.description}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{voucher.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase whitespace-nowrap text-xs w-[8%] px-4 text-center">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="block truncate">{typeof voucher.amount === 'number' ? voucher.amount.toFixed(2) : parseFloat(voucher.amount || '0').toFixed(2)} {voucher.currency}</span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{typeof voucher.amount === 'number' ? voucher.amount.toFixed(2) : parseFloat(voucher.amount || '0').toFixed(2)} {voucher.currency}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="uppercase text-xs w-[12%] px-4 text-center">
                          {isEditing ? (
                            <Input
                              type="number"
                              value={edits.preAuditedAmount || ''}
                              onChange={(e) => {
                                const value = e.target.value;
                                handleVoucherEdit(voucher.id, 'preAuditedAmount', value ? parseFloat(value) : undefined);
                              }}
                              placeholder="e.g., 200.012"
                              min="0"
                              step="0.001"
                              className="w-full uppercase"
                              disabled={!isEditable}
                            />
                          ) : (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="block truncate">
                                    {voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </TableCell>
                        <TableCell className="uppercase w-[10%] px-4 text-center">
                          {isEditing ? (
                            <Popover
                              open={openTaxPopover === voucher.id}
                              onOpenChange={(open) => {
                                setOpenTaxPopover(open ? voucher.id : null);
                              }}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="w-[100px] uppercase bg-transparent border-white/10"
                                >
                                  {edits.taxType && edits.taxType !== 'NONE' ? edits.taxType : 'TAX'}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-[400px] p-0 bg-background border-white/10" align="start">
                                <div className="p-4 space-y-4">
                                  <div className="flex justify-between items-center">
                                    <h4 className="font-medium text-lg uppercase">TAX DETAILS</h4>
                                    <PopoverClose asChild>
                                      <Button
                                        variant="ghost"
                                        className="text-xs"
                                        onClick={() => {
                                          setOpenTaxPopover(null);
                                        }}
                                      >
                                        OK
                                      </Button>
                                    </PopoverClose>
                                  </div>

                                  <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                      <label className="text-sm uppercase">Tax Type</label>
                                      <Select
                                        value={edits.taxType || 'NONE'}
                                        onValueChange={(value) => handleVoucherEdit(voucher.id, 'taxType', value === 'NONE' ? '' : value)}
                                        disabled={!isEditable}
                                      >
                                        <SelectTrigger className="w-full bg-transparent border-white/10 uppercase">
                                          <SelectValue placeholder="SELECT TYPE" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="" className="uppercase">SELECT TAX TYPE</SelectItem>
                                          {getTaxOptions().map((tax) => (
                                            <SelectItem key={tax} value={tax}>
                                              {tax}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>

                                    <div className="space-y-2">
                                      <label className="text-sm uppercase">Tax Amount</label>
                                      <Input
                                        type="number"
                                        value={edits.taxAmount || ''}
                                        onChange={(e) => {
                                          const value = e.target.value;
                                          handleVoucherEdit(voucher.id, 'taxAmount', value ? parseFloat(value) : undefined);
                                        }}
                                        className="w-full bg-transparent border-white/10"
                                        placeholder="e.g., 15.125"
                                        min="0"
                                        step="0.001"
                                        disabled={!isEditable || !edits.taxType || edits.taxType === 'NONE'}
                                      />
                                    </div>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          ) : (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="block truncate">
                                    {voucher.taxType ?
                                      (voucher.taxAmount ?
                                        `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}` :
                                        voucher.taxType) :
                                      '-'}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    {voucher.taxType ?
                                      (voucher.taxAmount ?
                                        `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}` :
                                        voucher.taxType) :
                                      '-'}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </TableCell>
                        <TableCell className="uppercase w-[12%] px-4 text-center">
                          {isEditing ? (
                            <Select
                              value={edits.preAuditedBy || ''}
                              onValueChange={(value) => handleVoucherEdit(voucher.id, 'preAuditedBy', value)}
                              disabled={!isEditable}
                            >
                              <SelectTrigger className="uppercase w-full bg-transparent border-white/10">
                                <SelectValue placeholder={edits.preAuditedBy || 'SELECT'} />
                              </SelectTrigger>
                              <SelectContent className="uppercase">
                                <SelectItem value="" className="uppercase">SELECT PERSON</SelectItem>
                                {auditUsers.map((user) => (
                                  <SelectItem key={user} value={user}>
                                    {user}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="block truncate">
                                    {voucher.preAuditedBy || '-'}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{voucher.preAuditedBy || '-'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </TableCell>

                        <TableCell className="uppercase w-[12%] px-4 text-center">
                          {isEditing ? (
                            <Select
                              value={edits.certifiedBy || ''}
                              onValueChange={(value) => handleVoucherEdit(voucher.id, 'certifiedBy', value)}
                              disabled={!isEditable}
                            >
                              <SelectTrigger className="w-full uppercase bg-transparent border-white/10">
                                <SelectValue placeholder={edits.certifiedBy || 'SELECT'} />
                              </SelectTrigger>
                              <SelectContent className="uppercase">
                                <SelectItem value="" className="uppercase">SELECT PERSON</SelectItem>
                                {auditUsers.map((user) => (
                                  <SelectItem key={user} value={user}>
                                    {user}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="block truncate">
                                    {voucher.certifiedBy || '-'}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{voucher.certifiedBy || '-'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </TableCell>
                        <TableCell className="uppercase w-[10%] px-4 text-center">
                          {isEditing ? (
                            <Popover
                              open={openOptionsPopover === voucher.id}
                              onOpenChange={(open) => {
                                setOpenOptionsPopover(open ? voucher.id : null);
                              }}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="uppercase bg-transparent border-white/10"
                                >
                                  OPTIONS
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-[400px] p-0 bg-background border-white/10" align="start">
                                <div className="p-4 space-y-4">
                                  <div className="flex justify-between items-center">
                                    <h4 className="font-medium text-lg uppercase">OTHER OPTIONS</h4>
                                    <PopoverClose asChild>
                                      <Button
                                        variant="ghost"
                                        className="text-xs"
                                        onClick={async () => {
                                          // Only handle provisional cash creation if selected
                                          const edits = voucherEdits[voucher.id] || {};
                                          if (edits.postProvisionalCash) {
                                            try {
                                              const addProvisionalCashRecord = useAppStore.getState().addProvisionalCashRecord;

                                              await addProvisionalCashRecord({
                                                voucherId: voucher.id,
                                                voucherRef: voucher.voucherId,
                                                claimant: voucher.claimant,
                                                description: voucher.description,
                                                mainAmount: voucher.preAuditedAmount || voucher.amount,
                                                currency: voucher.currency,
                                                date: formatCurrentDate()
                                              });

                                              toast.success('✅ Provisional cash record created successfully', {
                                                description: `Record created for ${voucher.claimant} - ${voucher.voucherId}`,
                                                duration: 4000,
                                              });
                                            } catch (error) {
                                              console.error('❌ Failed to create provisional cash record:', error);
                                              toast.error('Failed to create provisional cash record');
                                            }
                                          }
                                          setOpenOptionsPopover(null);
                                        }}
                                      >
                                        OK
                                      </Button>
                                    </PopoverClose>
                                  </div>

                                  <div className="space-y-4">
                                    <div className="space-y-2">
                                      <label className="text-sm uppercase">Post Provisional Cash</label>
                                      <Select
                                        value={edits.postProvisionalCash ? "YES" : "NO"}
                                        onValueChange={(value) => handleVoucherEdit(voucher.id, 'postProvisionalCash', value === "YES")}
                                        disabled={!isEditable}
                                      >
                                        <SelectTrigger className="w-full bg-transparent border-white/10">
                                          <SelectValue placeholder="Select" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="" className="uppercase">SELECT OPTION</SelectItem>
                                          {getProvisionalCashOptions().map(option => (
                                            <SelectItem key={option.value} value={option.value}>
                                              {option.label}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          ) : (
                            <div className="flex gap-1 justify-center">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleStartEditing(voucher.id);
                                }}
                                className="bg-blue-600 hover:bg-blue-700 text-white"
                                disabled={!isEditable}
                              >
                                <Pencil className="h-4 w-4" />
                                <span className="ml-2">EDIT</span>
                              </Button>

                              {/* Attachment button - Only for Audit Department */}
                              {currentUser?.department === 'AUDIT' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleOpenAttachmentModal(voucher.id);
                                  }}
                                  className={`${
                                    attachmentCounts[voucher.id] > 0
                                      ? 'bg-green-600 hover:bg-green-700'
                                      : 'bg-purple-600 hover:bg-purple-700'
                                  } text-white relative`}
                                  title={
                                    attachmentCounts[voucher.id] > 0
                                      ? `${attachmentCounts[voucher.id]} attachment(s) - Click to manage`
                                      : 'Attach documents to this voucher'
                                  }
                                  disabled={!isEditable}
                                >
                                  <Paperclip className="h-4 w-4" />
                                  {attachmentCounts[voucher.id] > 0 && (
                                    <span className="absolute -top-2 -right-2 bg-white text-green-600 text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold border border-green-600">
                                      {attachmentCounts[voucher.id]}
                                    </span>
                                  )}
                                </Button>
                              )}
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="uppercase w-[7%] px-4 text-center">
                          {!isEditing && (
                            <HoldControls
                              isOnHold={voucher.isOnHold === true || voucher.is_on_hold === true}
                              onToggleHold={() => handleHoldVoucher(voucher)}
                              disabled={!isEditable}
                              size="sm"
                            />
                          )}
                        </TableCell>
                        <TableCell className="uppercase w-[7%] px-4 text-center">
                          {!isEditing && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleReturnVoucher(voucher.id);
                              }}
                              className="bg-orange-600 hover:bg-orange-700 text-white"
                              disabled={!isEditable}
                            >
                              <RotateCcw className="h-4 w-4" />
                              <span className="ml-2">RETURN</span>
                            </Button>
                          )}
                        </TableCell>
                        <TableCell className="uppercase w-[7%] px-4 text-center">
                          {isEditing && (
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCancelEditing(voucher.id)}
                                className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                disabled={!isEditable}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => handleSave(voucher.id)}
                                className="bg-green-600 hover:bg-green-700"
                                disabled={!isEditable}
                              >
                                SAVE
                              </Button>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>

                      {showReturnComment && (
                        <TableRow>
                          <TableCell colSpan={11} className="bg-muted/30">
                            <Card>
                              <CardContent className="pt-4">
                                <div className="space-y-4">
                                  <div>
                                    <Label htmlFor={`return-comment-${voucher.id}`} className="text-sm font-medium uppercase">
                                      Return Comment
                                    </Label>
                                    <Textarea
                                      id={`return-comment-${voucher.id}`}
                                      placeholder="Enter reason for returning this voucher"
                                      value={returnCommentMap[voucher.id] || ''}
                                      onChange={(e) => handleReturnCommentChange(voucher.id, e.target.value)}
                                      className="mt-1"
                                      rows={3}
                                      disabled={!isEditable}
                                    />
                                  </div>
                                  <div className="flex justify-end space-x-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleCancelReturn(voucher.id)}
                                      disabled={!isEditable}
                                    >
                                      CANCEL
                                    </Button>
                                    <Button
                                      size="sm"
                                      onClick={() => handleConfirmReturn(voucher.id)}
                                      className="bg-red-500 hover:bg-red-600"
                                      disabled={!returnCommentMap[voucher.id] || !isEditable}
                                    >
                                      CONFIRM RETURN
                                    </Button>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  );
                })
              )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Attachment Upload Modal - Only for Audit Department */}
      {currentUser?.department === 'AUDIT' && (
        <Dialog open={showAttachmentModal} onOpenChange={setShowAttachmentModal}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Paperclip className="h-5 w-5" />
                Attach Documents to Voucher
              </DialogTitle>
              <DialogDescription>
                {selectedVoucherData && (
                  <div className="space-y-1 text-sm">
                    <p><strong>Voucher ID:</strong> {selectedVoucherData.voucherId}</p>
                    <p><strong>Claimant:</strong> {selectedVoucherData.claimant}</p>
                    <p><strong>Description:</strong> {selectedVoucherData.description}</p>
                  </div>
                )}
              </DialogDescription>
            </DialogHeader>

            {selectedVoucherForAttachment && (
              <div className="space-y-4">
                <AttachmentViewer
                  voucherId={selectedVoucherForAttachment}
                  onAttachmentsChange={() => {
                    // Reload actual attachment count when attachments change
                    if (selectedVoucherForAttachment) {
                      loadAttachmentCount(selectedVoucherForAttachment);
                    }
                  }}
                  readOnly={false}
                />

                {/* OK Button to close modal */}
                <div className="flex justify-end pt-4 border-t">
                  <Button
                    onClick={handleCloseAttachmentModal}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6"
                    disabled={!isEditable}
                  >
                    OK
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      )}

      {/* Return Voucher Dialog */}
      {showReturnDialog && currentReturnVoucherId && (
        <VoucherReturnDialog
          isOpen={showReturnDialog}
          voucherId={currentReturnVoucherId}
          onClose={handleCloseReturnDialog}
          onConfirm={handleConfirmReturnVoucher}
          auditUsers={auditUsers}
          department={department} // CRITICAL FIX: Pass department for dynamic dialog text
        />
      )}

      {/* HOLD FEATURE: Hold Dialog */}
      <HoldDialog
        open={holdDialogOpen}
        onClose={() => {
          setHoldDialogOpen(false);
          setSelectedVoucherForHold(null);
        }}
        voucher={selectedVoucherForHold}
        onToggleHold={handleToggleVoucherHold}
      />
    </div>
  );
}
