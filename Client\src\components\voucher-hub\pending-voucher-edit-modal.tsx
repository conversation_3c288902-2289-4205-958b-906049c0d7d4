/**
 * Pending Voucher Edit Modal
 * Allows editing of CLAIMANT, DESCRIPTION, and AMOUNT for rejected/returned vouchers in PENDING tab
 */

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Voucher } from '@/lib/types';
import { formatNumberWithCommas } from '@/utils/formatUtils';
import { toast } from 'sonner';
import { Edit3, Save, X, AlertCircle } from 'lucide-react';

interface PendingVoucherEditModalProps {
  isOpen: boolean;
  voucher: Voucher | null;
  onClose: () => void;
  onSave: (voucherId: string, updates: { claimant: string; description: string; amount: number }) => Promise<void>;
}

export function PendingVoucherEditModal({
  isOpen,
  voucher,
  onClose,
  onSave
}: PendingVoucherEditModalProps) {
  const [editData, setEditData] = useState({
    claimant: '',
    description: '',
    amount: 0
  });
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when voucher changes
  useEffect(() => {
    if (voucher) {
      setEditData({
        claimant: voucher.claimant || '',
        description: voucher.description || '',
        amount: voucher.amount || 0
      });
      setErrors({});
    }
  }, [voucher]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!editData.claimant.trim()) {
      newErrors.claimant = 'Claimant is required';
    }

    if (!editData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!editData.amount || editData.amount <= 0) {
      newErrors.amount = 'Amount must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!voucher || !validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      await onSave(voucher.id, editData);
      toast.success('Voucher updated successfully');
      onClose();
    } catch (error) {
      console.error('Error updating voucher:', error);
      toast.error('Failed to update voucher');
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    if (isSaving) return;
    onClose();
  };

  const isResubmittedVoucher = voucher && (
    voucher.status === 'VOUCHER REJECTED' || 
    voucher.isReturned || 
    voucher.returnComment ||
    voucher.comment?.includes('REJECTED') ||
    voucher.comment?.includes('RETURNED')
  );

  if (!voucher) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5 text-blue-600" />
            Edit Voucher - {voucher.voucherId}
          </DialogTitle>
          <DialogDescription>
            Edit the claimant, description, and amount for this {isResubmittedVoucher ? 'resubmitted' : ''} voucher.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Voucher Status Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900">Voucher Information</span>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Status:</span> {voucher.status}
              </div>
              <div>
                <span className="font-medium">Department:</span> {voucher.department}
              </div>
              {voucher.comment && (
                <div className="col-span-2">
                  <span className="font-medium">Comment:</span> {voucher.comment}
                </div>
              )}
            </div>
          </div>

          {/* Editable Fields */}
          <div className="space-y-4">
            {/* Claimant */}
            <div className="space-y-2">
              <Label htmlFor="claimant" className="text-sm font-medium">
                Claimant <span className="text-red-500">*</span>
              </Label>
              <Input
                id="claimant"
                value={editData.claimant}
                onChange={(e) => setEditData(prev => ({ ...prev, claimant: e.target.value }))}
                placeholder="Enter claimant name"
                className={errors.claimant ? 'border-red-500' : ''}
                disabled={isSaving}
              />
              {errors.claimant && (
                <p className="text-sm text-red-500">{errors.claimant}</p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">
                Description <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="description"
                value={editData.description}
                onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter voucher description"
                className={errors.description ? 'border-red-500' : ''}
                rows={3}
                disabled={isSaving}
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description}</p>
              )}
            </div>

            {/* Amount */}
            <div className="space-y-2">
              <Label htmlFor="amount" className="text-sm font-medium">
                Amount (GHS) <span className="text-red-500">*</span>
              </Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                value={editData.amount}
                onChange={(e) => setEditData(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                placeholder="Enter amount"
                className={errors.amount ? 'border-red-500' : ''}
                disabled={isSaving}
              />
              {errors.amount && (
                <p className="text-sm text-red-500">{errors.amount}</p>
              )}
              <p className="text-sm text-gray-600">
                Formatted: GHS {formatNumberWithCommas(editData.amount)}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSaving}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
