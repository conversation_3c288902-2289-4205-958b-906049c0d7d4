import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';

interface SendDialogProps {
  open: boolean;
  voucherCount: number;
  department: string;
  comment: string;
  isLoading: boolean;
  onCommentChange: (comment: string) => void;
  onConfirm: () => void;
  onCancel: () => void;
}

export function SendDialog({
  open,
  voucherCount,
  department,
  comment,
  isLoading,
  onCommentChange,
  onConfirm,
  onCancel,
}: SendDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="uppercase">CONFIRM SEND VOUCHERS</DialogTitle>
          <DialogDescription className="uppercase">
            YOU ARE ABOUT TO SEND {voucherCount} VOUCHER(S) TO THE {department} DEPARTMENT
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="dispatch-comment" className="uppercase">
              ADD A COMMENT (OPTIONAL)
            </Label>
            <Textarea
              id="dispatch-comment"
              value={comment}
              onChange={(e) => onCommentChange(e.target.value.toUpperCase())}
              placeholder="ENTER COMMENT"
              className="resize-none uppercase"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onCancel} className="uppercase">
            CANCEL
          </Button>
          <Button onClick={onConfirm} disabled={isLoading} variant="success" className="uppercase">
            {isLoading ? 'SENDING...' : 'CONFIRM SEND'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
