
import React from 'react';
import { ArrowUpDown } from 'lucide-react';
import { TableHead } from '@/components/ui/table';

interface SortableColumnHeaderProps {
  title: string;
  sortKey: string;
  currentSortColumn: string | null;
  currentSortDirection: 'asc' | 'desc';
  onSort: (column: string) => void;
  className?: string;
}

export function SortableColumnHeader({
  title,
  sortKey,
  currentSortColumn,
  currentSortDirection,
  onSort,
  className
}: SortableColumnHeaderProps) {
  return (
    <th className={`p-4 text-center font-medium whitespace-nowrap sticky top-0 bg-background z-10 ${className || ''}`}>
      <button
        className="flex items-center justify-center text-xs font-medium w-full uppercase whitespace-nowrap"
        onClick={() => onSort(sortKey)}
      >
        <span>{title}</span>
        {currentSortColumn === sortKey && (
          <ArrowUpDown className="h-3 w-3 ml-1" />
        )}
      </button>
    </th>
  );
}
