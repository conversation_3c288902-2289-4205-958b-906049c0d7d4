/**
 * Voucher Hold Dialog
 * Allows audit users to put vouchers ON-HOLD with comments in NEW VOUCHER tab
 */

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Voucher } from '@/lib/types';
import { toast } from 'sonner';
import { Pause, Play, MessageSquare, AlertTriangle } from 'lucide-react';

interface VoucherHoldDialogProps {
  isOpen: boolean;
  voucher: Voucher | null;
  onClose: () => void;
  onToggleHold: (voucherId: string, holdComment: string, isOnHold: boolean) => Promise<void>;
}

export function VoucherHoldDialog({
  isOpen,
  voucher,
  onClose,
  onToggleHold
}: VoucherHoldDialogProps) {
  const [holdComment, setHoldComment] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Initialize form data when voucher changes
  useEffect(() => {
    if (voucher) {
      setHoldComment(voucher.holdComment || '');
    }
  }, [voucher]);

  const isCurrentlyOnHold = voucher?.isOnHold === true;

  const handleToggleHold = async () => {
    if (!voucher) return;

    // If putting on hold, require a comment
    if (!isCurrentlyOnHold && !holdComment.trim()) {
      toast.error('Please provide a reason for putting this voucher on hold');
      return;
    }

    setIsProcessing(true);
    try {
      await onToggleHold(voucher.id, holdComment.trim(), !isCurrentlyOnHold);
      
      if (isCurrentlyOnHold) {
        toast.success('Voucher removed from hold');
      } else {
        toast.success('Voucher put on hold');
      }
      
      onClose();
    } catch (error) {
      console.error('Error toggling voucher hold:', error);
      toast.error('Failed to update voucher hold status');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    if (isProcessing) return;
    onClose();
  };

  if (!voucher) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isCurrentlyOnHold ? (
              <>
                <Play className="h-5 w-5 text-green-600" />
                Remove Hold - {voucher.voucherId}
              </>
            ) : (
              <>
                <Pause className="h-5 w-5 text-red-600" />
                Put On Hold - {voucher.voucherId}
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {isCurrentlyOnHold 
              ? 'Remove this voucher from hold status and continue processing.'
              : 'Put this voucher on hold with a comment explaining the reason.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Status */}
          <div className={`p-4 rounded-lg ${isCurrentlyOnHold ? 'bg-red-50' : 'bg-blue-50'}`}>
            <div className="flex items-center gap-2 mb-2">
              {isCurrentlyOnHold ? (
                <>
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="font-medium text-red-900">Currently On Hold</span>
                  <Badge className="bg-red-600 text-white animate-pulse">ON-HOLD</Badge>
                </>
              ) : (
                <>
                  <MessageSquare className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-900">Active Processing</span>
                  <Badge className="bg-green-600 text-white">ACTIVE</Badge>
                </>
              )}
            </div>
            <div className="text-sm">
              <div><span className="font-medium">Voucher:</span> {voucher.voucherId}</div>
              <div><span className="font-medium">Claimant:</span> {voucher.claimant}</div>
              <div><span className="font-medium">Amount:</span> GHS {voucher.amount?.toLocaleString()}</div>
            </div>
          </div>

          {/* Hold Comment */}
          <div className="space-y-2">
            <Label htmlFor="holdComment" className="text-sm font-medium">
              {isCurrentlyOnHold ? 'Current Hold Reason' : 'Hold Reason'} 
              {!isCurrentlyOnHold && <span className="text-red-500"> *</span>}
            </Label>
            <Textarea
              id="holdComment"
              value={holdComment}
              onChange={(e) => setHoldComment(e.target.value)}
              placeholder={isCurrentlyOnHold 
                ? 'Current hold reason...' 
                : 'Enter reason for putting this voucher on hold...'
              }
              rows={4}
              disabled={isProcessing}
              className={isCurrentlyOnHold ? 'bg-gray-50' : ''}
            />
            <p className="text-xs text-gray-600">
              {isCurrentlyOnHold 
                ? 'This is the current reason why the voucher is on hold.'
                : 'This comment will be visible to other audit users and will help track why the voucher is on hold.'
              }
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={handleToggleHold}
              disabled={isProcessing}
              className={isCurrentlyOnHold 
                ? 'bg-green-600 hover:bg-green-700' 
                : 'bg-red-600 hover:bg-red-700'
              }
            >
              {isCurrentlyOnHold ? (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  {isProcessing ? 'Removing Hold...' : 'Remove Hold'}
                </>
              ) : (
                <>
                  <Pause className="h-4 w-4 mr-2" />
                  {isProcessing ? 'Putting On Hold...' : 'Put On Hold'}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
