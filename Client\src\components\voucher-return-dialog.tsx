import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, <PERSON>alogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';

interface VoucherReturnDialogProps {
  isOpen: boolean;
  voucherId: string;
  onClose: () => void;
  onConfirm: (voucherId: string, returnReason: string, dispatcher: string) => void;
  auditUsers: string[];
  department?: string; // CRITICAL FIX: Add department prop for dynamic text
}

export function VoucherReturnDialog({
  isOpen,
  voucherId,
  onClose,
  onConfirm,
  auditUsers,
  department = 'FINANCE' // CRITICAL FIX: Default to FINANCE for backward compatibility
}: VoucherReturnDialogProps) {
  const [returnReason, setReturnReason] = useState('');
  const [selectedDispatcher, setSelectedDispatcher] = useState('');

  const handleConfirm = () => {
    if (!returnReason.trim()) {
      toast.error("A return reason is required", {
        duration: 3000,
      });
      return;
    }

    if (!selectedDispatcher) {
      toast.error("Please select a dispatcher", {
        duration: 3000,
      });
      return;
    }

    // Pass the return reason and dispatcher to the parent
    const reasonText = String(returnReason.trim());
    console.log(`Confirming return with reason: "${reasonText}" and dispatcher: "${selectedDispatcher}"`);
    onConfirm(voucherId, reasonText, selectedDispatcher);
    
    // Reset form
    setReturnReason('');
    setSelectedDispatcher('');
  };

  const handleClose = () => {
    setReturnReason('');
    setSelectedDispatcher('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Return Voucher to {department}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Return Reason *</label>
            <Textarea
              placeholder={`Enter reason for returning this voucher to ${department}...`}
              value={returnReason}
              onChange={(e) => setReturnReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Select Dispatcher *</label>
            <Select value={selectedDispatcher} onValueChange={setSelectedDispatcher}>
              <SelectTrigger>
                <SelectValue placeholder="Choose who will dispatch this voucher" />
              </SelectTrigger>
              <SelectContent>
                {auditUsers.map((user) => (
                  <SelectItem key={user} value={user}>
                    {user}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            className="bg-orange-600 hover:bg-orange-700 text-white"
          >
            Send to {department}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
