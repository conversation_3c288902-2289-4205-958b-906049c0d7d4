
import { useState } from 'react';
import { NavigateFunction } from 'react-router-dom';
import { Voucher, Department } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { toast } from '@/hooks/use-toast';

interface UseVoucherTableProps {
  vouchers: Voucher[];
  searchTerm: string;
  department: Department;
  selectable: boolean;
  onSelectionChange?: (selectedIds: string[]) => void;
  onViewVoucher?: (voucher: Voucher) => void;
  navigate: NavigateFunction;
}

export function useVoucherTable({
  vouchers,
  searchTerm,
  department,
  selectable,
  onSelectionChange,
  onViewVoucher,
  navigate
}: UseVoucherTableProps) {
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedVouchers, setSelectedVouchers] = useState<string[]>([]);

  const addVoucher = useAppStore(state => state.addVoucher);
  const deleteVoucher = useAppStore(state => state.deleteVoucher);

  const isVoucherSelectable = (voucher: Voucher): boolean => {
    return (voucher.status === "PENDING" || voucher.status === "PENDING SUBMISSION" || voucher.status === "PENDING RECEIPT") && !voucher.sentToAudit;
  };

  const sortAndFilterVouchers = () => {
    let result = [...vouchers];

    if (searchTerm) {
      result = result.filter((voucher) => {
        return (
          voucher.claimant.toLowerCase().includes(searchTerm.toLowerCase()) ||
          voucher.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          voucher.voucherId.toLowerCase().includes(searchTerm.toLowerCase())
        );
      });
    }

    if (sortColumn) {
      result.sort((a, b) => {
        let valueA = a[sortColumn as keyof Voucher];
        let valueB = b[sortColumn as keyof Voucher];

        if (sortColumn === 'amount' || sortColumn === 'preAuditedAmount') {
          valueA = Number(valueA) || 0;
          valueB = Number(valueB) || 0;
        } else if (typeof valueA === 'string' && typeof valueB === 'string') {
          valueA = valueA.toLowerCase();
          valueB = valueB.toLowerCase();
        }

        if (valueA < valueB) return sortDirection === 'asc' ? -1 : 1;
        if (valueA > valueB) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return result;
  };

  const filteredVouchers = sortAndFilterVouchers();
  const selectableFilteredVouchers = filteredVouchers.filter(isVoucherSelectable);

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const handleSelectVoucher = (voucherId: string) => {
    const voucher = vouchers.find(v => v.id === voucherId);
    if (!voucher || !isVoucherSelectable(voucher)) return;

    let updatedSelection;
    if (selectedVouchers.includes(voucherId)) {
      updatedSelection = selectedVouchers.filter(id => id !== voucherId);
    } else {
      updatedSelection = [...selectedVouchers, voucherId];
    }
    setSelectedVouchers(updatedSelection);

    if (onSelectionChange) {
      onSelectionChange(updatedSelection);
    }
  };

  const handleSelectAll = () => {
    if (selectable && selectableFilteredVouchers.length > 0) {
      if (selectedVouchers.length === selectableFilteredVouchers.length) {
        setSelectedVouchers([]);
        if (onSelectionChange) {
          onSelectionChange([]);
        }
      } else {
        const allSelectableIds = selectableFilteredVouchers.map(v => v.id);
        setSelectedVouchers(allSelectableIds);
        if (onSelectionChange) {
          onSelectionChange(allSelectableIds);
        }
      }
    }
  };

  const formatCurrentDate = () => {
    const now = new Date();
    const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    const month = monthNames[now.getMonth()];
    const day = now.getDate();
    const year = now.getFullYear();

    const hours = now.getHours();
    const minutes = now.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const formattedHours = hours % 12 || 12;

    return `${month} ${day}, ${year} AT ${formattedHours}:${minutes.toString().padStart(2, '0')}${ampm}`;
  };

  const handleAddBack = (voucher: Voucher) => {
    try {
      // APPROACH 1: Preserve the original voucher ID when adding rejected vouchers back to PENDING
      // This maintains traceability and history of the voucher throughout its lifecycle
      const updateVoucher = useAppStore.getState().updateVoucher;

      console.log(`Adding voucher ${voucher.id} (${voucher.voucherId}) back to pending submission`);

      // Update the voucher to move it back to pending submission while preserving its ID
      // Determine the appropriate comment based on the voucher's current status
      const currentDate = new Date().toLocaleDateString();
      const commentText = voucher.status === "VOUCHER REJECTED"
        ? `Re-added from rejection on ${currentDate}`
        : voucher.isReturned || voucher.status === "VOUCHER RETURNED"
          ? `Re-added from returned on ${currentDate}`
          : `Re-added to pending on ${currentDate}`;

      // CRITICAL FIX: Preserve original rejection reason before updating
      const originalRejectionReason = voucher.comment || voucher.returnComment || voucher.original_rejection_reason || voucher.originalRejectionReason;
      const originalRejectedBy = voucher.rejectedBy || voucher.rejected_by || voucher.originalRejectedBy;

      updateVoucher(voucher.id, {
        status: "PENDING SUBMISSION",
        sentToAudit: false,
        isReturned: false,
        returnTime: undefined,
        returnComment: undefined,
        rejectionTime: undefined,
        comment: commentText,
        // CRITICAL FIX: Preserve original rejection reason in dedicated fields
        original_rejection_reason: originalRejectionReason,
        originalRejectionReason: originalRejectionReason, // camelCase for frontend
        original_rejected_by: originalRejectedBy,
        originalRejectedBy: originalRejectedBy, // camelCase for frontend
        deleted: false // Make sure it's not marked as deleted
      });

      // Create a toast message that reflects the source of the re-addition
      const toastMessage = voucher.status === "VOUCHER REJECTED"
        ? `Voucher ${voucher.voucherId} re-added from rejection to Pending Submission`
        : voucher.isReturned || voucher.status === "VOUCHER RETURNED"
          ? `Voucher ${voucher.voucherId} re-added from returned to Pending Submission`
          : `Voucher ${voucher.voucherId} moved back to Pending Submission`;

      toast({
        title: "Success",
        description: toastMessage,
        variant: "default",
        duration: 3000,
      });
    } catch (error) {
      console.error("Error adding voucher back to pending:", error);
      toast({
        title: "Error",
        description: "Failed to add voucher back",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleDeleteVoucher = (voucherId: string) => {
    try {
      // Get the voucher before deletion
      const voucher = vouchers.find(v => v.id === voucherId);
      if (!voucher) return;

      // Now delete it
      deleteVoucher(voucherId);

      // Remove from selection if it was selected
      if (selectedVouchers.includes(voucherId)) {
        const updatedSelection = selectedVouchers.filter(id => id !== voucherId);
        setSelectedVouchers(updatedSelection);
        if (onSelectionChange) {
          onSelectionChange(updatedSelection);
        }
      }

      toast({
        title: "Success",
        description: "Voucher deleted successfully",
        variant: "default",
        duration: 3000,
      });
    } catch (error) {
      console.error("Error deleting voucher:", error);
      toast({
        title: "Error",
        description: "Failed to delete voucher",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  const handleViewVoucher = (voucher: Voucher) => {
    console.log('View voucher clicked:', voucher);

    // Make sure we have a valid voucher object
    if (!voucher || !voucher.id) {
      console.error('Invalid voucher object:', voucher);
      toast({
        title: "Error",
        description: "Cannot view voucher details: Invalid voucher data",
        variant: "destructive",
      });
      return;
    }

    // Use the callback if provided
    if (onViewVoucher) {
      console.log('Using provided onViewVoucher callback');
      onViewVoucher(voucher);
    } else {
      // Otherwise navigate to the voucher detail page
      console.log('Navigating to voucher detail page');
      if (department === 'AUDIT') {
        navigate(`/audit-dashboard/voucher/${voucher.id}`);
      } else {
        navigate(`/dashboard/voucher/${voucher.id}`);
      }
    }
  };

  return {
    sortColumn,
    sortDirection,
    selectedVouchers,
    setSelectedVouchers,
    filteredVouchers,
    selectableFilteredVouchers,
    handleSort,
    handleSelectVoucher,
    handleSelectAll,
    handleViewVoucher,
    handleAddBack,
    handleDeleteVoucher,
    formatCurrentDate
  };
}
