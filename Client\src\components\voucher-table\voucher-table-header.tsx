import { ArrowUpDown } from 'lucide-react';
import {
  Table,
  TableHeader,
  TableRow,
  TableHead
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Voucher } from '@/lib/types';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface SortableColumnHeaderProps {
  column: string;
  label: string;
  sortColumn: string | null;
  handleSort: (column: string) => void;
  tooltip?: string;
  className?: string;
}

function SortableColumnHeader({ column, label, sortColumn, handleSort, tooltip, className }: SortableColumnHeaderProps) {
  const headerContent = (
    <button
      className="flex items-center justify-center font-medium w-full uppercase"
      onClick={() => handleSort(column)}
    >
      <span>{label}</span>
      {sortColumn === column && (
        <ArrowUpDown className="h-4 w-4 ml-1" />
      )}
    </button>
  );

  return (
    <th className={`p-4 text-center font-medium ${className || ''}`}>
      {tooltip ? (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {headerContent}
            </TooltipTrigger>
            <TooltipContent>
              <p className="font-semibold">{label}</p>
              <p className="text-sm">{tooltip}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : (
        headerContent
      )}
    </th>
  );
}

interface VoucherTableHeaderProps {
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  selectable: boolean;
  handleSelectAll: () => void;
  selectedVouchers: string[];
  filteredVouchers: Voucher[];
  view: string;
  isAudit: boolean;
  showPreAuditedBy: boolean;
}

export function VoucherTableHeader({
  sortColumn,
  handleSort,
  selectable,
  handleSelectAll,
  selectedVouchers,
  filteredVouchers,
  view,
  isAudit,
  showPreAuditedBy
}: VoucherTableHeaderProps) {
  // Only consider selectable vouchers for the "select all" checkbox state
  const selectableVouchers = filteredVouchers.filter(
    v => (v.status === "PENDING" || v.status === "PENDING SUBMISSION" || v.status === "PENDING RECEIPT") && !v.sentToAudit
  );

  return (
    <div className="w-full bg-background">
      <table className="w-full table-fixed" style={{ tableLayout: 'fixed' }}>
        <thead>
          <tr className="bg-background">
          {selectable && (
            <th className="w-[5%] sticky left-0 bg-background z-20 p-4 text-center">
              <Checkbox
                checked={selectedVouchers.length === selectableVouchers.length && selectableVouchers.length > 0}
                onCheckedChange={handleSelectAll}
                aria-label="Select all"
                disabled={selectableVouchers.length === 0}
              />
            </th>
          )}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <th className="sticky left-0 bg-background z-20 uppercase w-[15%] p-4 text-center font-medium">VOUCHER ID</th>
              </TooltipTrigger>
              <TooltipContent>
                <p className="font-semibold">VOUCHER ID</p>
                <p className="text-sm">Unique identifier for each voucher. Format: VCH-YYYYMMDD-XXXX where XXXX is sequential number.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <SortableColumnHeader
            column="date"
            label="DATE"
            sortColumn={sortColumn}
            handleSort={handleSort}
            className="w-[20%]"
            tooltip="Voucher date in DD-MMM-YYYY format. Click to sort by date. Hover over dates in table for creation time details."
          />
          <SortableColumnHeader
            column="claimant"
            label="CLAIMANT"
            sortColumn={sortColumn}
            handleSort={handleSort}
            className="w-[20%]"
            tooltip="Person or entity claiming the voucher amount. Click to sort alphabetically by claimant name."
          />
          <SortableColumnHeader
            column="description"
            label="DESCRIPTION"
            sortColumn={sortColumn}
            handleSort={handleSort}
            className="w-[25%]"
            tooltip="Brief description of the voucher purpose or transaction. Click to sort alphabetically by description."
          />
          <SortableColumnHeader
            column="amount"
            label="AMOUNT"
            sortColumn={sortColumn}
            handleSort={handleSort}
            className="w-[10%]"
            tooltip="Original voucher amount in GHS. Click to sort by amount value from lowest to highest."
          />

          {/* Show CERTIFIED AMT column only for certified tab */}
          {!isAudit && view === 'certified' && (
            <SortableColumnHeader
              column="preAuditedAmount"
              label="CERTIFIED AMT"
              sortColumn={sortColumn}
              handleSort={handleSort}
              className="w-[15%]"
              tooltip="Amount certified by audit department after processing. This may differ from original amount due to audit adjustments."
            />
          )}

          {isAudit && showPreAuditedBy && view !== 'rejected' && (
            <>
              <SortableColumnHeader
                column="preAuditedAmount"
                label="CERTIFIED AMT"
                sortColumn={sortColumn}
                handleSort={handleSort}
                className="w-[15%]"
                tooltip="Amount certified by audit department. Click to sort by certified amount value."
              />
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <th className="uppercase w-[15%] p-4 text-center font-medium">CERTIFIED BY</th>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-semibold">CERTIFIED BY</p>
                    <p className="text-sm">Audit staff member who certified this voucher amount and processed it for dispatch.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </>
          )}

          {view === 'dispatched' && (
            <>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <th className="uppercase w-[15%] p-4 text-center font-medium">CERTIFIED BY</th>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-semibold">CERTIFIED BY</p>
                    <p className="text-sm">Audit staff member who certified and processed this voucher for dispatch back to department.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <th className="uppercase w-[15%] p-4 text-center font-medium">TAX</th>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-semibold">TAX AMOUNT</p>
                    <p className="text-sm">Tax amount calculated and applied by audit department during voucher processing.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <th className="uppercase w-[15%] p-4 text-center font-medium">DISPATCHED BY</th>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-semibold">DISPATCHED BY</p>
                    <p className="text-sm">Audit staff member who dispatched the processed voucher back to the originating department.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <th className="uppercase w-[15%] p-4 text-center font-medium">DISPATCHED ON</th>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-semibold">DISPATCH TIME</p>
                    <p className="text-sm">Date and time when audit dispatched the voucher back to department in DD-MMM-YYYY HH:MM:SS AM/PM format.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <th className="uppercase w-[15%] p-4 text-center font-medium">RECEIVED BY</th>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-semibold">RECEIVED BY</p>
                    <p className="text-sm">Department staff member who received the voucher batch from audit and the time of receipt.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </>
          )}

          {view === 'returned' && (
            <>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <th className="uppercase w-[15%] p-4 text-center font-medium">DATE RETURNED</th>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-semibold">RETURN DATE</p>
                    <p className="text-sm">Date and time when the voucher was returned by audit in DD-MMM-YYYY HH:MM:SS AM/PM format.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <th className="uppercase w-[15%] p-4 text-center font-medium">STATUS</th>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-semibold">RETURN STATUS</p>
                    <p className="text-sm">Current status of the returned voucher - indicates if it needs corrections or resubmission.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <th className="uppercase w-[25%] p-4 text-center font-medium">COMMENT</th>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-semibold">RETURN COMMENT</p>
                    <p className="text-sm">Audit's explanation for why the voucher was returned and what corrections are needed.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </>
          )}

          {view === 'rejected' && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <th className="uppercase w-[15%] p-4 text-center font-medium">REJECTION TIME</th>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="font-semibold">REJECTION TIME</p>
                  <p className="text-sm">Date and time when audit rejected the voucher in DD-MMM-YYYY HH:MM:SS AM/PM format.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {isAudit && view === 'rejected' && (
            <>
              <th className="uppercase w-[15%] p-4 text-center font-medium">CREATED BY</th>
              <th className="uppercase w-[15%] p-4 text-center font-medium">DISPATCHED BY</th>
              <th className="uppercase w-[15%] p-4 text-center font-medium">REJECTED BY</th>
            </>
          )}

          {view !== 'rejected' && view !== 'returned' && (
            <th className="uppercase w-[15%] p-4 text-center font-medium">STATUS</th>
          )}

          <th className="text-center uppercase sticky right-0 bg-background z-20 w-[10%] p-4 font-medium">ACTIONS</th>
          </tr>
        </thead>
      </table>
    </div>
  );
}
