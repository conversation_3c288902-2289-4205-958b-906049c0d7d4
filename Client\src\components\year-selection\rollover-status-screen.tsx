import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Database, 
  Calendar, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  RefreshCw,
  Shield,
  FileText,
  Users,
  Settings
} from 'lucide-react';

interface RolloverStatus {
  isRolloverInProgress: boolean;
  currentStep: string;
  progress: number;
  oldYear: number;
  newYear: number;
  estimatedTimeRemaining: number;
  steps: RolloverStep[];
  error?: string;
}

interface RolloverStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'error';
  startTime?: string;
  endTime?: string;
}

interface RolloverStatusScreenProps {
  onRolloverComplete: () => void;
}

export function RolloverStatusScreen({ onRolloverComplete }: RolloverStatusScreenProps) {
  const [rolloverStatus, setRolloverStatus] = useState<RolloverStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [retryCount, setRetryCount] = useState(0);

  // Poll rollover status
  useEffect(() => {
    const pollStatus = async () => {
      try {
        const response = await fetch('/api/years/rollover/status', {
          credentials: 'include'
        });

        if (response.ok) {
          const status = await response.json();
          setRolloverStatus(status);
          
          // If rollover is complete, notify parent
          if (!status.isRolloverInProgress && status.progress === 100) {
            setTimeout(() => {
              onRolloverComplete();
            }, 2000); // Show completion for 2 seconds
          }
        }
      } catch (error) {
        console.error('Error polling rollover status:', error);
        setRetryCount(prev => prev + 1);
      } finally {
        setIsLoading(false);
      }
    };

    // Poll every 2 seconds during rollover
    const interval = setInterval(pollStatus, 2000);
    
    // Initial poll
    pollStatus();

    return () => clearInterval(interval);
  }, [onRolloverComplete]);

  const handleRetry = () => {
    setIsLoading(true);
    setRetryCount(0);
  };

  const formatTimeRemaining = (seconds: number) => {
    if (seconds < 60) return `${seconds} seconds`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getStepIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'in-progress':
        return <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <Card className="w-96">
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
              <p className="text-lg font-medium">Checking System Status...</p>
              <p className="text-sm text-muted-foreground mt-2">Please wait</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!rolloverStatus) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-100">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-xl font-bold mb-2">System Status Unavailable</h2>
            <p className="text-muted-foreground mb-4">
              Unable to check system status. Please try again.
            </p>
            <Button onClick={handleRetry} className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry ({retryCount} attempts)
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!rolloverStatus.isRolloverInProgress && rolloverStatus.progress === 100) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100">
        <Card className="w-96">
          <CardContent className="p-8 text-center">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">Year Rollover Complete!</h2>
            <p className="text-muted-foreground mb-4">
              Successfully updated to {rolloverStatus.newYear}
            </p>
            <Badge variant="default" className="bg-green-600">
              System Ready
            </Badge>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Database className="h-12 w-12 text-blue-600 mr-3" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                SYSTEM UPDATING
              </h1>
              <p className="text-lg text-gray-600">
                Preparing for {rolloverStatus.newYear} Operations
              </p>
            </div>
          </div>
          
          <div className="flex items-center justify-center space-x-4 mb-4">
            <Badge variant="outline" className="text-lg px-4 py-2">
              <Calendar className="h-4 w-4 mr-2" />
              {rolloverStatus.oldYear} → {rolloverStatus.newYear}
            </Badge>
            <Badge variant="default" className="text-lg px-4 py-2 bg-blue-600">
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              In Progress
            </Badge>
          </div>
        </div>

        {/* Progress Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Overall Progress
            </CardTitle>
            <CardDescription>
              {rolloverStatus.currentStep}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Progress value={rolloverStatus.progress} className="h-3" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{rolloverStatus.progress}% Complete</span>
                <span>
                  {rolloverStatus.estimatedTimeRemaining > 0 && (
                    <>ETA: {formatTimeRemaining(rolloverStatus.estimatedTimeRemaining)}</>
                  )}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Rollover Steps
            </CardTitle>
            <CardDescription>
              Detailed progress of year transition process
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {rolloverStatus.steps.map((step, index) => (
                <div key={step.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50">
                  <div className="flex-shrink-0 mt-1">
                    {getStepIcon(step.status)}
                  </div>
                  <div className="flex-grow">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{step.name}</h4>
                      <Badge 
                        variant={step.status === 'completed' ? 'default' : 'outline'}
                        className={
                          step.status === 'completed' ? 'bg-green-600' :
                          step.status === 'in-progress' ? 'bg-blue-600' :
                          step.status === 'error' ? 'bg-red-600' : ''
                        }
                      >
                        {step.status.replace('-', ' ').toUpperCase()}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {step.description}
                    </p>
                    {step.startTime && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Started: {new Date(step.startTime).toLocaleTimeString()}
                        {step.endTime && (
                          <> • Completed: {new Date(step.endTime).toLocaleTimeString()}</>
                        )}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Safety Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-green-700">
              <Shield className="h-5 w-5 mr-2" />
              Data Safety Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>All historical data preserved</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Automatic backup created</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>User accounts maintained</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>Please do not close this window or refresh the page</p>
          <p>The system will automatically redirect when complete</p>
        </div>
      </div>
    </div>
  );
}
