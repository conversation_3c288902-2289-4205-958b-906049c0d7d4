import { useState } from 'react';
import { toast } from '@/hooks/use-toast';
import { useAppStore } from '@/lib/store';

export function useVoucherHold() {
  const [isProcessing, setIsProcessing] = useState(false);
  const { refreshVouchers } = useAppStore();

  const toggleVoucherHold = async (
    voucherId: string, 
    holdComment: string, 
    isOnHold: boolean
  ): Promise<void> => {
    setIsProcessing(true);
    
    try {
      const response = await fetch(`/api/vouchers/${voucherId}/toggle-hold`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          holdComment,
          isOnHold
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle voucher hold status');
      }

      const result = await response.json();
      
      // Show success message
      if (isOnHold) {
        toast({
          title: "Voucher Put on Hold",
          description: `Voucher has been put on hold: ${holdComment}`,
          variant: "default",
        });
      } else {
        toast({
          title: "Voucher Resumed",
          description: "Voucher has been removed from hold and can be processed normally.",
          variant: "default",
        });
      }

      // Refresh vouchers to get updated state
      await refreshVouchers();
      
    } catch (error: any) {
      console.error('Error toggling voucher hold:', error);
      toast({
        title: "Error",
        description: error.message || 'Failed to update voucher hold status',
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    toggleVoucherHold,
    isProcessing
  };
}
