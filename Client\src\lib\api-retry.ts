/**
 * SIMPLE: API retry utility for handling network failures
 * Provides basic retry logic without complex queuing or state management
 */

interface RetryOptions {
  maxRetries?: number;
  delayMs?: number;
  backoffMultiplier?: number;
  retryCondition?: (error: any) => boolean;
}

const DEFAULT_OPTIONS: Required<RetryOptions> = {
  maxRetries: 3,
  delayMs: 1000,
  backoffMultiplier: 1.5,
  retryCondition: (error: any) => {
    // Retry on network errors, timeouts, and 5xx server errors
    if (!error.response) return true; // Network error
    const status = error.response?.status || error.status;
    return status >= 500 || status === 408; // Server errors or timeout
  }
};

/**
 * SIMPLE: Retry a function with exponential backoff
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  let lastError: any;

  for (let attempt = 0; attempt <= opts.maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Don't retry if this is the last attempt
      if (attempt === opts.maxRetries) {
        break;
      }

      // Don't retry if the error doesn't meet retry conditions
      if (!opts.retryCondition(error)) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = opts.delayMs * Math.pow(opts.backoffMultiplier, attempt);
      
      console.warn(`API call failed (attempt ${attempt + 1}/${opts.maxRetries + 1}), retrying in ${delay}ms:`, error.message);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

/**
 * SIMPLE: Enhanced fetch with automatic retry
 */
export async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  retryOptions: RetryOptions = {}
): Promise<Response> {
  return withRetry(async () => {
    const response = await fetch(url, options);
    
    // Throw error for non-ok responses to trigger retry logic
    if (!response.ok) {
      const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
      (error as any).response = response;
      (error as any).status = response.status;
      throw error;
    }
    
    return response;
  }, retryOptions);
}

/**
 * SIMPLE: Axios-style API client with retry
 */
export class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private defaultRetryOptions: RetryOptions;

  constructor(
    baseURL: string = '/api',
    defaultHeaders: Record<string, string> = {},
    defaultRetryOptions: RetryOptions = {}
  ) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders
    };
    this.defaultRetryOptions = defaultRetryOptions;
  }

  private async request<T>(
    method: string,
    endpoint: string,
    data?: any,
    options: RequestInit = {},
    retryOptions?: RetryOptions
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const requestOptions: RequestInit = {
      method,
      headers: {
        ...this.defaultHeaders,
        ...options.headers
      },
      ...options
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      requestOptions.body = JSON.stringify(data);
    }

    const response = await fetchWithRetry(
      url,
      requestOptions,
      { ...this.defaultRetryOptions, ...retryOptions }
    );

    // Parse JSON response
    const responseText = await response.text();
    try {
      return responseText ? JSON.parse(responseText) : null;
    } catch (error) {
      console.warn('Failed to parse JSON response:', responseText);
      return responseText as any;
    }
  }

  async get<T>(endpoint: string, options?: RequestInit, retryOptions?: RetryOptions): Promise<T> {
    return this.request<T>('GET', endpoint, undefined, options, retryOptions);
  }

  async post<T>(endpoint: string, data?: any, options?: RequestInit, retryOptions?: RetryOptions): Promise<T> {
    return this.request<T>('POST', endpoint, data, options, retryOptions);
  }

  async put<T>(endpoint: string, data?: any, options?: RequestInit, retryOptions?: RetryOptions): Promise<T> {
    return this.request<T>('PUT', endpoint, data, options, retryOptions);
  }

  async patch<T>(endpoint: string, data?: any, options?: RequestInit, retryOptions?: RetryOptions): Promise<T> {
    return this.request<T>('PATCH', endpoint, data, options, retryOptions);
  }

  async delete<T>(endpoint: string, options?: RequestInit, retryOptions?: RetryOptions): Promise<T> {
    return this.request<T>('DELETE', endpoint, undefined, options, retryOptions);
  }
}

/**
 * SIMPLE: Default API client instance
 */
export const apiClient = new ApiClient('/api', {}, {
  maxRetries: 2, // Conservative retry count
  delayMs: 1000,
  backoffMultiplier: 1.5
});

/**
 * SIMPLE: Utility to check if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  return DEFAULT_OPTIONS.retryCondition(error);
}

/**
 * SIMPLE: Utility to create a retry wrapper for any async function
 */
export function createRetryWrapper<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  retryOptions: RetryOptions = {}
): T {
  return ((...args: any[]) => {
    return withRetry(() => fn(...args), retryOptions);
  }) as T;
}
