/**
 * Legacy Voucher Status Constants
 * Temporary compatibility layer for existing frontend code
 * TODO: Migrate all usage to the new workflow state machine
 */

// Legacy status constants for backward compatibility
export const VOUCHER_STATUSES = {
  PENDING: 'PENDING SUBMISSION' as const,
  PENDING_RECEIPT: 'PENDING RECEIPT' as const,
  VOUCHER_PROCESSING: 'VOUCHER PROCESSING' as const,
  AUDIT_PROCESSING: 'AUDIT: PROCESSING' as const,
  VOUCHER_CERTIFIED: 'VOUCHER CERTIFIED' as const,
  VOUCHER_REJECTED: 'VOUCHER REJECTED' as const,
  VOUCHER_RETURNED: 'VOUCHER RETURNED' as const,
  PENDING_DISPATCH: 'PENDING DISPATCH' as const,
  DISPATCHED: 'DISPATCHED' as const
};

// Legacy status arrays for filtering
export const FINANCE_STATUSES = [
  VOUCHER_STATUSES.PENDING,
  VOUCHER_STATUSES.PENDING_RECEIPT,
  VOUCHER_STATUSES.VOUCHER_CERTIFIED,
  VOUCHER_STATUSES.VOUCHER_REJECTED,
  VOUCHER_STATUSES.VOUCHER_RETURNED
];

export const AUDIT_STATUSES = [
  VOUCHER_STATUSES.VOUCHER_PROCESSING,
  VOUCHER_STATUSES.AUDIT_PROCESSING,
  VOUCHER_STATUSES.PENDING_DISPATCH,
  VOUCHER_STATUSES.DISPATCHED
];

// Status display names
export const STATUS_DISPLAY_NAMES = {
  [VOUCHER_STATUSES.PENDING]: 'Pending Submission',
  [VOUCHER_STATUSES.PENDING_RECEIPT]: 'Pending Receipt',
  [VOUCHER_STATUSES.VOUCHER_PROCESSING]: 'Processing',
  [VOUCHER_STATUSES.AUDIT_PROCESSING]: 'Audit Processing',
  [VOUCHER_STATUSES.VOUCHER_CERTIFIED]: 'Certified',
  [VOUCHER_STATUSES.VOUCHER_REJECTED]: 'Rejected',
  [VOUCHER_STATUSES.VOUCHER_RETURNED]: 'Returned',
  [VOUCHER_STATUSES.PENDING_DISPATCH]: 'Pending Dispatch',
  [VOUCHER_STATUSES.DISPATCHED]: 'Dispatched'
};

// Status colors for UI
export const STATUS_COLORS = {
  [VOUCHER_STATUSES.PENDING]: 'bg-gray-100 text-gray-800',
  [VOUCHER_STATUSES.PENDING_RECEIPT]: 'bg-blue-100 text-blue-800',
  [VOUCHER_STATUSES.VOUCHER_PROCESSING]: 'bg-yellow-100 text-yellow-800',
  [VOUCHER_STATUSES.AUDIT_PROCESSING]: 'bg-orange-100 text-orange-800',
  [VOUCHER_STATUSES.VOUCHER_CERTIFIED]: 'bg-green-100 text-green-800',
  [VOUCHER_STATUSES.VOUCHER_REJECTED]: 'bg-red-100 text-red-800',
  [VOUCHER_STATUSES.VOUCHER_RETURNED]: 'bg-purple-100 text-purple-800',
  [VOUCHER_STATUSES.PENDING_DISPATCH]: 'bg-indigo-100 text-indigo-800',
  [VOUCHER_STATUSES.DISPATCHED]: 'bg-teal-100 text-teal-800'
};

// Helper functions
export function getStatusDisplayName(status: string): string {
  return STATUS_DISPLAY_NAMES[status as keyof typeof STATUS_DISPLAY_NAMES] || status;
}

export function getStatusColor(status: string): string {
  return STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'bg-gray-100 text-gray-800';
}

export function isFinanceStatus(status: string): boolean {
  return FINANCE_STATUSES.includes(status as any);
}

export function isAuditStatus(status: string): boolean {
  return AUDIT_STATUSES.includes(status as any);
}

// Legacy compatibility layer - warnings removed for production
// TODO: Gradually migrate all usage to the new workflow state machine
