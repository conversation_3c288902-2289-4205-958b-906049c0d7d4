/**
 * Global interval manager to track and clear all active intervals
 * This prevents intervals from continuing after logout
 */

// Registry to track all active intervals
const activeIntervals = new Set<number>();

/**
 * Create a managed interval that can be cleared on logout
 */
export const createManagedInterval = (callback: () => void, delay: number): number => {
  const intervalId = setInterval(callback, delay);
  activeIntervals.add(intervalId);
  
  console.log(`📅 Created managed interval ${intervalId} with delay ${delay}ms`);
  return intervalId;
};

/**
 * Clear a specific managed interval
 */
export const clearManagedInterval = (intervalId: number): void => {
  clearInterval(intervalId);
  activeIntervals.delete(intervalId);
  console.log(`🗑️ Cleared managed interval ${intervalId}`);
};

/**
 * Clear all managed intervals (called on logout)
 */
export const clearAllManagedIntervals = (): void => {
  console.log(`🛑 Clearing ${activeIntervals.size} managed intervals...`);
  
  activeIntervals.forEach(intervalId => {
    clearInterval(intervalId);
    console.log(`🗑️ Cleared interval ${intervalId}`);
  });
  
  activeIntervals.clear();
  console.log('✅ All managed intervals cleared');
};

/**
 * Get count of active intervals
 */
export const getActiveIntervalCount = (): number => {
  return activeIntervals.size;
};

/**
 * List all active interval IDs (for debugging)
 */
export const getActiveIntervals = (): number[] => {
  return Array.from(activeIntervals);
};
