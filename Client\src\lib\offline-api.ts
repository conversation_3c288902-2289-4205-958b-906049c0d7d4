import { useAppStore } from '@/lib/store';
import { toast } from 'sonner';

/**
 * Offline-Aware API Wrapper
 * 
 * Automatically queues operations when offline and executes when online
 * Provides seamless offline/online experience for users
 */

interface ApiOptions {
  showOfflineToast?: boolean;
  maxRetries?: number;
  priority?: 'low' | 'normal' | 'high';
}

class OfflineAPI {
  private getStore() {
    return useAppStore.getState();
  }

  /**
   * Create voucher with offline support
   */
  async createVoucher(voucherData: any, options: ApiOptions = {}) {
    const { isOnline, addOfflineOperation } = this.getStore();
    
    if (isOnline) {
      try {
        const response = await fetch('/api/vouchers', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(voucherData)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        toast.success('Voucher created successfully');
        return result;
        
      } catch (error) {
        console.error('Failed to create voucher online:', error);
        
        // If online but API failed, queue for retry
        addOfflineOperation({
          type: 'CREATE_VOUCHER',
          data: voucherData,
          maxRetries: options.maxRetries || 3
        });
        
        toast.error('Failed to create voucher - queued for retry');
        throw error;
      }
    } else {
      // Offline - queue operation
      addOfflineOperation({
        type: 'CREATE_VOUCHER',
        data: voucherData,
        maxRetries: options.maxRetries || 3
      });

      if (options.showOfflineToast !== false) {
        toast.info('Voucher saved offline - will sync when connection restored');
      }

      // Return a temporary result for UI feedback
      return {
        id: `offline_${Date.now()}`,
        ...voucherData,
        status: 'PENDING_SYNC',
        isOffline: true
      };
    }
  }

  /**
   * Update voucher with offline support
   */
  async updateVoucher(voucherId: string, updateData: any, options: ApiOptions = {}) {
    const { isOnline, addOfflineOperation } = this.getStore();
    
    if (isOnline) {
      try {
        const response = await fetch(`/api/vouchers/${voucherId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(updateData)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        toast.success('Voucher updated successfully');
        return result;
        
      } catch (error) {
        console.error('Failed to update voucher online:', error);
        
        // Queue for retry
        addOfflineOperation({
          type: 'UPDATE_VOUCHER',
          data: { id: voucherId, ...updateData },
          maxRetries: options.maxRetries || 3
        });
        
        toast.error('Failed to update voucher - queued for retry');
        throw error;
      }
    } else {
      // Offline - queue operation
      addOfflineOperation({
        type: 'UPDATE_VOUCHER',
        data: { id: voucherId, ...updateData },
        maxRetries: options.maxRetries || 3
      });

      if (options.showOfflineToast !== false) {
        toast.info('Voucher update saved offline - will sync when connection restored');
      }

      return { success: true, isOffline: true };
    }
  }

  /**
   * Send vouchers to audit with offline support
   */
  async sendToAudit(voucherIds: string[], dispatchedBy: string, options: ApiOptions = {}) {
    const { isOnline, addOfflineOperation, currentUser } = this.getStore();

    const batchData = {
      department: currentUser?.department || 'FINANCE', // ORIGINAL: Use user's department
      voucherIds,
      dispatchedBy,
      fromAudit: false
    };
    
    if (isOnline) {
      try {
        const response = await fetch('/api/batches', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(batchData)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        toast.success(`Batch sent to audit: ${voucherIds.length} vouchers`);
        return result;
        
      } catch (error) {
        console.error('Failed to send batch to audit:', error);
        
        // Queue for retry
        addOfflineOperation({
          type: 'SEND_TO_AUDIT',
          data: batchData,
          maxRetries: options.maxRetries || 3
        });
        
        toast.error('Failed to send to audit - queued for retry');
        throw error;
      }
    } else {
      // Offline - queue operation
      addOfflineOperation({
        type: 'SEND_TO_AUDIT',
        data: batchData,
        maxRetries: options.maxRetries || 3
      });

      if (options.showOfflineToast !== false) {
        toast.info(`Batch queued offline: ${voucherIds.length} vouchers - will send when connection restored`);
      }

      return { success: true, isOffline: true, batchId: `offline_batch_${Date.now()}` };
    }
  }

  /**
   * Receive batch with offline support
   */
  async receiveBatch(batchId: string, receivedVoucherIds: string[], rejectedVoucherIds: string[], rejectionComments: any, options: ApiOptions = {}) {
    const { isOnline, addOfflineOperation } = this.getStore();
    
    const receiveData = {
      batchId,
      receivedVoucherIds,
      rejectedVoucherIds,
      rejectionComments
    };
    
    if (isOnline) {
      try {
        const response = await fetch(`/api/batches/${batchId}/receive`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            receivedVoucherIds,
            rejectedVoucherIds,
            rejectionComments
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        toast.success(`Batch processed: ${receivedVoucherIds.length} accepted, ${rejectedVoucherIds.length} rejected`);
        return result;
        
      } catch (error) {
        console.error('Failed to receive batch:', error);
        
        // Queue for retry
        addOfflineOperation({
          type: 'RECEIVE_BATCH',
          data: receiveData,
          maxRetries: options.maxRetries || 3
        });
        
        toast.error('Failed to process batch - queued for retry');
        throw error;
      }
    } else {
      // Offline - queue operation
      addOfflineOperation({
        type: 'RECEIVE_BATCH',
        data: receiveData,
        maxRetries: options.maxRetries || 3
      });

      if (options.showOfflineToast !== false) {
        toast.info('Batch processing saved offline - will sync when connection restored');
      }

      return { success: true, isOffline: true };
    }
  }

  /**
   * Update voucher status with offline support
   */
  async updateVoucherStatus(voucherId: string, status: string, options: ApiOptions = {}) {
    const { isOnline, addOfflineOperation } = this.getStore();
    
    if (isOnline) {
      try {
        const response = await fetch(`/api/vouchers/${voucherId}/status`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ status })
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        toast.success('Voucher status updated');
        return result;
        
      } catch (error) {
        console.error('Failed to update voucher status:', error);
        
        // Queue for retry
        addOfflineOperation({
          type: 'UPDATE_STATUS',
          data: { id: voucherId, status },
          maxRetries: options.maxRetries || 3
        });
        
        toast.error('Status update queued for retry');
        throw error;
      }
    } else {
      // Offline - queue operation
      addOfflineOperation({
        type: 'UPDATE_STATUS',
        data: { id: voucherId, status },
        maxRetries: options.maxRetries || 3
      });

      if (options.showOfflineToast !== false) {
        toast.info('Status update saved offline - will sync when connection restored');
      }

      return { success: true, isOffline: true };
    }
  }
}

// Export singleton instance
export const offlineAPI = new OfflineAPI();
