/**
 * PRODUCTION-LEVEL: WebSocket Performance Testing Suite
 * 
 * This module provides comprehensive testing for the optimized WebSocket
 * connection system to validate performance improvements and ensure
 * production-level SLA compliance.
 * 
 * Performance Targets:
 * - Connection initialization: <200ms
 * - First notification delivery: <500ms
 * - Pre-warmed connection activation: 0ms delay
 * - Room joining: <100ms total
 * - Notification broadcast: <50ms delivery
 */

import { getSocket, preWarmConnection } from './socket';
import { connectionInitializer } from './connection-initializer';

// Performance test results interface
interface PerformanceTestResult {
  testName: string;
  startTime: number;
  endTime: number;
  duration: number;
  target: number;
  passed: boolean;
  details: any;
}

// Test suite state
interface TestSuiteState {
  isRunning: boolean;
  results: PerformanceTestResult[];
  startTime: number;
  endTime: number;
  totalTests: number;
  passedTests: number;
  failedTests: number;
}

const testState: TestSuiteState = {
  isRunning: false,
  results: [],
  startTime: 0,
  endTime: 0,
  totalTests: 0,
  passedTests: 0,
  failedTests: 0
};

/**
 * PRODUCTION-LEVEL: Test connection pre-warming performance
 */
const testConnectionPreWarming = async (): Promise<PerformanceTestResult> => {
  console.log('🧪 PERFORMANCE TEST: Connection Pre-warming');
  
  const startTime = Date.now();
  
  try {
    await preWarmConnection();
    const endTime = Date.now();
    const duration = endTime - startTime;
    const target = 200; // 200ms target
    
    const result: PerformanceTestResult = {
      testName: 'Connection Pre-warming',
      startTime,
      endTime,
      duration,
      target,
      passed: duration < target,
      details: {
        connectionReady: true,
        preWarmingSuccessful: true
      }
    };
    
    console.log(`🧪 Pre-warming completed in ${duration}ms (target: <${target}ms) - ${result.passed ? 'PASSED' : 'FAILED'}`);
    return result;
    
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const result: PerformanceTestResult = {
      testName: 'Connection Pre-warming',
      startTime,
      endTime,
      duration,
      target: 200,
      passed: false,
      details: {
        error: error.message,
        connectionReady: false,
        preWarmingSuccessful: false
      }
    };
    
    console.error(`🧪 Pre-warming failed after ${duration}ms:`, error);
    return result;
  }
};

/**
 * PRODUCTION-LEVEL: Test socket connection activation speed
 */
const testSocketActivation = async (): Promise<PerformanceTestResult> => {
  console.log('🧪 PERFORMANCE TEST: Socket Activation');
  
  const startTime = Date.now();
  
  try {
    const socket = getSocket();
    const endTime = Date.now();
    const duration = endTime - startTime;
    const target = 50; // 50ms target for activation
    
    const result: PerformanceTestResult = {
      testName: 'Socket Activation',
      startTime,
      endTime,
      duration,
      target,
      passed: duration < target && socket !== null,
      details: {
        socketAvailable: socket !== null,
        socketConnected: socket?.connected || false,
        socketId: socket?.id || null
      }
    };
    
    console.log(`🧪 Socket activation completed in ${duration}ms (target: <${target}ms) - ${result.passed ? 'PASSED' : 'FAILED'}`);
    return result;
    
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const result: PerformanceTestResult = {
      testName: 'Socket Activation',
      startTime,
      endTime,
      duration,
      target: 50,
      passed: false,
      details: {
        error: error.message,
        socketAvailable: false
      }
    };
    
    console.error(`🧪 Socket activation failed after ${duration}ms:`, error);
    return result;
  }
};

/**
 * PRODUCTION-LEVEL: Test notification delivery simulation
 */
const testNotificationDelivery = async (): Promise<PerformanceTestResult> => {
  console.log('🧪 PERFORMANCE TEST: Notification Delivery Simulation');
  
  const startTime = Date.now();
  
  return new Promise<PerformanceTestResult>((resolve) => {
    try {
      const socket = getSocket();
      
      if (!socket || !socket.connected) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        resolve({
          testName: 'Notification Delivery',
          startTime,
          endTime,
          duration,
          target: 500,
          passed: false,
          details: {
            error: 'Socket not available or not connected',
            socketAvailable: socket !== null,
            socketConnected: socket?.connected || false
          }
        });
        return;
      }
      
      // Simulate notification delivery
      const testEventName = 'performance_test_notification';
      const testData = { timestamp: startTime, testId: Math.random() };
      
      // Set up listener for test notification
      const timeoutId = setTimeout(() => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        resolve({
          testName: 'Notification Delivery',
          startTime,
          endTime,
          duration,
          target: 500,
          passed: false,
          details: {
            error: 'Notification delivery timeout',
            timeout: true
          }
        });
      }, 1000); // 1 second timeout
      
      socket.once(testEventName, (receivedData) => {
        clearTimeout(timeoutId);
        const endTime = Date.now();
        const duration = endTime - startTime;
        const target = 500; // 500ms target
        
        resolve({
          testName: 'Notification Delivery',
          startTime,
          endTime,
          duration,
          target,
          passed: duration < target,
          details: {
            notificationReceived: true,
            dataMatches: receivedData.testId === testData.testId,
            receivedData
          }
        });
      });
      
      // Emit test notification (simulating server broadcast)
      socket.emit('test_notification_broadcast', testData);
      
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      resolve({
        testName: 'Notification Delivery',
        startTime,
        endTime,
        duration,
        target: 500,
        passed: false,
        details: {
          error: error.message,
          testFailed: true
        }
      });
    }
  });
};

/**
 * PRODUCTION-LEVEL: Run complete performance test suite
 */
export const runPerformanceTestSuite = async (): Promise<TestSuiteState> => {
  console.log('🧪 STARTING: WebSocket Performance Test Suite');
  
  testState.isRunning = true;
  testState.results = [];
  testState.startTime = Date.now();
  testState.totalTests = 0;
  testState.passedTests = 0;
  testState.failedTests = 0;
  
  const tests = [
    testConnectionPreWarming,
    testSocketActivation,
    testNotificationDelivery
  ];
  
  for (const test of tests) {
    try {
      const result = await test();
      testState.results.push(result);
      testState.totalTests++;
      
      if (result.passed) {
        testState.passedTests++;
      } else {
        testState.failedTests++;
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      console.error('🧪 Test execution error:', error);
      testState.failedTests++;
      testState.totalTests++;
    }
  }
  
  testState.endTime = Date.now();
  testState.isRunning = false;
  
  // Generate test report
  const totalDuration = testState.endTime - testState.startTime;
  const passRate = (testState.passedTests / testState.totalTests) * 100;
  
  console.log('🧪 PERFORMANCE TEST SUITE COMPLETED');
  console.log(`📊 Results: ${testState.passedTests}/${testState.totalTests} tests passed (${passRate.toFixed(1)}%)`);
  console.log(`⏱️ Total duration: ${totalDuration}ms`);
  
  // Log individual test results
  testState.results.forEach(result => {
    const status = result.passed ? '✅ PASSED' : '❌ FAILED';
    console.log(`${status} ${result.testName}: ${result.duration}ms (target: <${result.target}ms)`);
  });
  
  return { ...testState };
};

/**
 * PRODUCTION-LEVEL: Get current test state
 */
export const getTestState = (): TestSuiteState => {
  return { ...testState };
};

/**
 * PRODUCTION-LEVEL: Quick performance validation
 */
export const quickPerformanceCheck = async (): Promise<boolean> => {
  console.log('🧪 QUICK CHECK: WebSocket Performance Validation');
  
  try {
    // Check connection initializer status
    const initStatus = connectionInitializer.getStatus();
    console.log('🧪 Connection system status:', initStatus.status);
    
    // Check socket availability
    const socket = getSocket();
    const socketReady = socket && socket.connected;
    console.log('🧪 Socket ready:', socketReady);
    
    // Check pre-warming performance
    const preWarmOk = initStatus.preWarmDuration ? initStatus.preWarmDuration < 200 : false;
    console.log('🧪 Pre-warming performance:', preWarmOk ? 'GOOD' : 'NEEDS IMPROVEMENT');
    
    const allChecksPass = initStatus.isInitialized && socketReady && preWarmOk;
    console.log('🧪 Overall performance:', allChecksPass ? '✅ EXCELLENT' : '⚠️ NEEDS ATTENTION');
    
    return allChecksPass;
    
  } catch (error) {
    console.error('🧪 Quick performance check failed:', error);
    return false;
  }
};

// Export for debugging and monitoring
export const performanceTesting = {
  runFullSuite: runPerformanceTestSuite,
  quickCheck: quickPerformanceCheck,
  getState: getTestState
};
