
import { StateCreator } from 'zustand';
import { AppState } from '../types';

export interface BlacklistedVoucherIdsSlice {
  blacklistedVoucherIds: AppState['blacklistedVoucherIds'];
  addBlacklistedVoucherId: AppState['addBlacklistedVoucherId'];
}

export const createBlacklistedVoucherIdsSlice: StateCreator<AppState, [], [], BlacklistedVoucherIdsSlice> = (set, get, api) => ({
  blacklistedVoucherIds: [],
  addBlacklistedVoucherId: (voucherId) => set((state) => ({ 
    blacklistedVoucherIds: [
      ...state.blacklistedVoucherIds, 
      { id: `bl${Date.now()}`, voucherId }
    ] 
  })),
});
