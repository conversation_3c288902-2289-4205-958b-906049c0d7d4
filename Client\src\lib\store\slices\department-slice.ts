
import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export interface DepartmentSlice {
  getVouchersForDepartment: AppState['getVouchersForDepartment'];
  getVoucherBatchesForDepartment: AppState['getVoucherBatchesForDepartment'];
  getPendingVouchersForDepartment: AppState['getPendingVouchersForDepartment'];
  getNotificationsForUser: AppState['getNotificationsForUser'];
}

export const createDepartmentSlice: StateCreator<AppState, [], [], DepartmentSlice> = (set, get) => ({
  getVouchersForDepartment: (department) => {
    const { vouchers } = get();
    if (department === "AUDIT" || department === "ADMINISTRATOR") {
      return vouchers; // Audit and Admin see all vouchers
    } else {
      // HOLD FEATURE FIX: Enhanced department matching for vouchers with incorrect original_department
      return vouchers.filter(voucher =>
        voucher.department === department ||
        voucher.originalDepartment === department ||
        (voucher.voucherId && voucher.voucherId.toUpperCase().startsWith(department.substring(0, 3).toUpperCase()))
      );
    }
  },
  getVoucherBatchesForDepartment: (department) => {
    const { voucherBatches } = get();
    if (department === "AUDIT" || department === "ADMINISTRATOR") {
      // CRITICAL FIX: Audit should only see batches FROM departments TO audit
      // Batches FROM audit TO departments should appear in department dashboards
      return voucherBatches.filter(batch => !batch.fromAudit);
    } else {
      // CRITICAL FIX: Departments should see batches that belong to them
      // This includes both: batches FROM audit TO this department AND batches FROM this department TO audit
      return voucherBatches.filter(batch => batch.department === department);
    }
  },
  getPendingVouchersForDepartment: (department) => {
    const { vouchers } = get();
    return vouchers.filter(
      voucher => voucher.department === department &&
               (voucher.status === VOUCHER_STATUSES.PENDING ||
                voucher.status === VOUCHER_STATUSES.PENDING_SUBMISSION ||
                voucher.status === VOUCHER_STATUSES.PENDING_RECEIPT) &&
               !voucher.sentToAudit
    );
  },
  getNotificationsForUser: (userId) => {
    const { notifications } = get();
    return notifications.filter(notification => notification.userId === userId);
  },
});
