import { users as defaultUsers, initialVouchers, initialNotifications } from '@/lib/data';
import { useAppStore } from '@/lib/store';

// Function to initialize default data in the store
export const initializeDefaultData = () => {
  const store = useAppStore.getState();
  const currentUsers = store.users;

  // Only initialize if the users array is empty
  if (currentUsers.length === 0) {
    console.log('Initializing default users...');
    
    // Add each default user to the store
    defaultUsers.forEach(user => {
      store.addUser(user);
    });
    
    console.log(`Initialized ${defaultUsers.length} default users`);
  } else {
    console.log(`Users already initialized (${currentUsers.length} users found)`);
  }

  // Initialize other data as needed
  // This can be expanded to initialize vouchers, notifications, etc.
};
