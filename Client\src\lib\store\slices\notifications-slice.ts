
import { StateCreator } from 'zustand';
import { AppState } from '../types';
import { initialNotifications } from '../../data';
import { formatCurrentDate } from '../utils';

export interface NotificationsSlice {
  notifications: AppState['notifications'];
  addNotification: AppState['addNotification'];
  markNotificationAsRead: AppState['markNotificationAsRead'];
  deleteNotification: AppState['deleteNotification'];
  fetchNotifications: () => Promise<void>;
}

export const createNotificationsSlice: StateCreator<AppState, [], [], NotificationsSlice> = (set) => ({
  notifications: initialNotifications,
  addNotification: (notification) => {
    const newNotification = {
      ...notification,
      id: `n${Date.now()}`,
      timestamp: formatCurrentDate(),
      isRead: false,
      fromAudit: notification.fromAudit || false
    };
    
    set((state) => ({ 
      notifications: [...state.notifications, newNotification] 
    }));
    
    return newNotification;
  },
  markNotificationAsRead: (notificationId) => set((state) => ({
    notifications: state.notifications.map(notification => 
      notification.id === notificationId ? { ...notification, isRead: true } : notification
    )
  })),
  deleteNotification: (notificationId) => set((state) => ({
    notifications: state.notifications.filter(notification => notification.id !== notificationId)
  })),

  // Fetch notifications from API
  fetchNotifications: async () => {
    try {
      console.log('🔄 Fetching notifications from API...');

      // Import the API function
      const { notificationsApi } = await import('../../api');

      // Fetch notifications from the API
      const notifications = await notificationsApi.getAllNotifications();

      console.log(`✅ Fetched ${notifications.length} notifications`);

      // Update the store with fetched notifications
      set({ notifications });
    } catch (error) {
      console.error('❌ Failed to fetch notifications:', error);
    }
  },
});
