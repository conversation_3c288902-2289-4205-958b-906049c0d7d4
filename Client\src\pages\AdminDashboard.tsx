import { AdminDashboard } from '@/components/admin';
import { useAppStore } from '@/lib/store/hooks';
import { ErrorTest } from '@/components/error-test';

// Main layout for Admin Dashboard page - NO AUTHENTICATION
const AdminDashboardPage = () => {
  const store = useAppStore();
  // Create a mock admin user if no user is logged in
  if (!store.currentUser) {
    const mockAdminUser = {
      id: "admin-temp",
      name: "ADMIN",
      department: "SYSTEM ADMIN",
      role: "admin",
      email: "<EMAIL>",
      dateCreated: new Date().toISOString(),
      isActive: true
    };
    store.setMockAdminUser(mockAdminUser);
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
        <div className="container flex h-14 items-center">
          <div className="mr-4 flex">
            <span className="font-bold">VMS Admin Dashboard</span>
          </div>
          <div className="ml-auto flex items-center space-x-4">
            <div className="flex items-center">
              <span className="text-sm text-muted-foreground mr-2">
                {store.currentUser?.name || 'ADMIN'}
              </span>
              <div className="h-8 w-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-semibold text-sm">
                {store.currentUser?.name?.charAt(0).toUpperCase() || 'A'}
              </div>
            </div>
            <a
              href="/"
              className="px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm font-medium transition-colors"
              onClick={(e) => {
                e.preventDefault();
                store.logout();
                window.location.href = '/';
              }}
            >
              Exit
            </a>
          </div>
        </div>
      </header>
      <main className="bg-muted/40 min-h-[calc(100vh-3.5rem)]">
        <AdminDashboard />
      </main>
    </div>
  );
};

export default AdminDashboardPage;
