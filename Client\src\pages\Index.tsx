
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAppStore } from '@/lib/store';

const Index = () => {
  const navigate = useNavigate();
  const currentUser = useAppStore((state) => state.currentUser);

  // Redirect based on user role if already logged in
  useEffect(() => {
    if (currentUser) {
      if (currentUser.department === 'AUDIT') {
        navigate('/audit-dashboard');
      } else if (currentUser.department === 'SYSTEM ADMIN') {
        navigate('/admin-dashboard');
      } else {
        navigate('/dashboard');
      }
    } else {
      // If not logged in, redirect to login page
      navigate('/');
    }
  }, [currentUser, navigate]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-gray-900 to-black text-white">
      <div className="text-center max-w-3xl px-4">
        <h1 className="text-5xl font-bold mb-6">Voucher Guardian System</h1>
        <p className="text-xl text-gray-300 mb-10">
          A comprehensive solution for managing, tracking, and auditing vouchers across departments
        </p>

        <div className="grid gap-4 md:grid-cols-2 max-w-md mx-auto">
          <Button
            size="lg"
            className="bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => navigate('/')}
          >
            Log In
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="border-gray-500 hover:bg-gray-800"
            onClick={() => navigate('/dashboard')}
          >
            View Demo
          </Button>
        </div>
      </div>

      <div className="mt-16 grid gap-8 md:grid-cols-3 max-w-5xl px-4">
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-bold mb-3">Department Management</h3>
          <p className="text-gray-300">
            Create, dispatch, and track vouchers through their entire lifecycle from any department.
          </p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-bold mb-3">Audit Controls</h3>
          <p className="text-gray-300">
            Comprehensive audit features for certifying, rejecting, and managing vouchers.
          </p>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-bold mb-3">Administrative Overview</h3>
          <p className="text-gray-300">
            System-wide reporting and administrative controls for complete oversight.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;
