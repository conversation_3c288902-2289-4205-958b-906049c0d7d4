import { useNavigate } from 'react-router-dom';
import { YearSelector } from '@/components/year-selection/year-selector';
import { useAppStore } from '@/lib/store';

export default function YearSelection() {
  const navigate = useNavigate();
  const currentUser = useAppStore((state) => state.currentUser);

  const handleYearSelected = async (year: number) => {
    try {
      // Send year selection to backend
      const response = await fetch('/api/years/select', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ year })
      });

      if (response.ok) {
        // Store year in app state
        useAppStore.setState({ selectedYear: year });
        
        // Navigate back to appropriate dashboard
        const userDepartment = currentUser?.department;
        if (userDepartment) {
          switch (userDepartment) {
            case 'FINANCE':
              navigate('/dashboard');
              break;
            case 'AUDIT':
              navigate('/audit-dashboard');
              break;
            case 'MINISTRIES':
              navigate('/ministries-dashboard');
              break;
            case 'PENSIONS':
              navigate('/pensions-dashboard');
              break;
            case 'PENTMEDIA':
              navigate('/pentmedia-dashboard');
              break;
            case 'MISSIONS':
              navigate('/missions-dashboard');
              break;
            case 'PENTSOS':
              navigate('/pentsos-dashboard');
              break;
            default:
              navigate('/dashboard');
          }
        } else {
          navigate('/dashboard');
        }
      } else {
        console.error('Failed to select year');
      }
    } catch (error) {
      console.error('Error selecting year:', error);
    }
  };

  const handleReturn = () => {
    // Navigate back to appropriate dashboard without changing year
    const userDepartment = currentUser?.department;
    if (userDepartment) {
      switch (userDepartment) {
        case 'FINANCE':
          navigate('/dashboard');
          break;
        case 'AUDIT':
          navigate('/audit-dashboard');
          break;
        case 'MINISTRIES':
          navigate('/ministries-dashboard');
          break;
        case 'PENSIONS':
          navigate('/pensions-dashboard');
          break;
        case 'PENTMEDIA':
          navigate('/pentmedia-dashboard');
          break;
        case 'MISSIONS':
          navigate('/missions-dashboard');
          break;
        case 'PENTSOS':
          navigate('/pentsos-dashboard');
          break;
        default:
          navigate('/dashboard');
      }
    } else {
      navigate('/dashboard');
    }
  };

  return (
    <YearSelector 
      onYearSelected={handleYearSelected} 
      onReturn={handleReturn}
    />
  );
}
