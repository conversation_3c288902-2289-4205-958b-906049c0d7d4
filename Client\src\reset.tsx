import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';

export default function Reset() {
  const [isResetting, setIsResetting] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  const handleReset = () => {
    setIsResetting(true);

    try {
      // Clear all local storage
      localStorage.clear();

      // Create a proper admin user and store it directly
      const adminUser = {
        id: "admin1",
        name: "ADMIN",
        department: "SYSTEM ADMIN",
        role: "ADMIN",
        password: "ADMIN123",
        dateCreated: new Date().toISOString(),
        isActive: true
      };

      // Create initial state with admin user
      const initialState = {
        currentUser: null,
        users: [adminUser],
        // Add other necessary initial state
        vouchers: [],
        voucherBatches: [],
        blacklistedVoucherIds: [],
        provisionalCashRecords: [],
        notifications: [],
        systemSettings: {
          fiscalYearStart: 'JAN',
          fiscalYearEnd: 'DEC',
          currentFiscalYear: new Date().getFullYear(),
          systemTime: new Date().toISOString(),
          sessionTimeout: 30,
          autoBackupEnabled: true
        },
        lastBackupDate: null
      };

      // Store the initial state
      localStorage.setItem('voucher-management-system', JSON.stringify({
        state: initialState,
        version: 0
      }));

      setIsComplete(true);
      toast.success('Application reset successfully!');
    } catch (error) {
      console.error('Reset error:', error);
      toast.error('Reset failed. Please try again.');
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-50 dark:bg-slate-900">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Reset Application</CardTitle>
          <CardDescription>
            This will reset the application state and create a fresh admin user.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            After resetting, you can log in with:
            <br />
            <strong>Department:</strong> SYSTEM ADMIN
            <br />
            <strong>Username:</strong> ADMIN
            <br />
            <strong>Password:</strong> ADMIN123
          </p>
          {isComplete && (
            <div className="bg-green-100 dark:bg-green-900 p-4 rounded-md mb-4">
              <p className="text-green-800 dark:text-green-200">
                Reset complete! You can now go to the <a href="/" className="underline">login page</a>.
              </p>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleReset}
            disabled={isResetting || isComplete}
            className="w-full"
            variant="destructive"
          >
            {isResetting ? 'Resetting...' : isComplete ? 'Reset Complete' : 'Reset Application'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
