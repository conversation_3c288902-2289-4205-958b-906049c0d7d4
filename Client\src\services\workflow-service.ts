/**
 * Frontend Workflow Service
 * Handles API calls for voucher workflow operations
 */

import { WorkflowEvent, type Voucher } from '../lib/workflow/VoucherWorkflowStateMachine';

const API_BASE = '/api/workflow';

export interface WorkflowTransitionRequest {
  voucherId: string;
  event: WorkflowEvent;
  metadata?: Record<string, any>;
}

export interface WorkflowTransitionResponse {
  success: boolean;
  data?: {
    voucher: Voucher;
    copy?: Voucher;
    previousState: string;
    newState: string;
    affectedTabs: string[];
  };
  error?: string;
}

export interface VoucherActionsResponse {
  success: boolean;
  data?: {
    voucher: {
      id: string;
      voucher_id: string;
      workflow_state: string;
      badge_type: string;
    };
    actions: Array<{
      event: WorkflowEvent;
      name: string;
      requiresConfirmation: boolean;
      icon: string;
    }>;
    currentState: string;
    isEditable: boolean;
  };
  error?: string;
}

export interface WorkflowVouchersResponse {
  success: boolean;
  data?: {
    vouchers: Voucher[];
    tabCounts: Record<string, number>;
    totalCount: number;
    filteredCount: number;
  };
  error?: string;
}

export interface WorkflowStatsResponse {
  success: boolean;
  data?: {
    period: string;
    statistics: Array<{
      workflow_state: string;
      badge_type: string;
      count: number;
      avg_processing_hours: number;
    }>;
  };
  error?: string;
}

export class WorkflowService {
  private static instance: WorkflowService;

  static getInstance(): WorkflowService {
    if (!WorkflowService.instance) {
      WorkflowService.instance = new WorkflowService();
    }
    return WorkflowService.instance;
  }

  /**
   * Get vouchers for department with workflow filtering
   */
  async getVouchersForDepartment(
    department: string,
    options: {
      tab?: string;
      search?: string;
      dateRange?: { start: string; end: string };
    } = {}
  ): Promise<WorkflowVouchersResponse> {
    try {
      const params = new URLSearchParams();
      if (options.tab) params.append('tab', options.tab);
      if (options.search) params.append('search', options.search);
      if (options.dateRange) {
        params.append('dateStart', options.dateRange.start);
        params.append('dateEnd', options.dateRange.end);
      }

      const url = `${API_BASE}/vouchers/${department}${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching workflow vouchers:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch vouchers'
      };
    }
  }

  /**
   * Execute workflow transition
   */
  async transitionVoucher(request: WorkflowTransitionRequest): Promise<WorkflowTransitionResponse> {
    try {
      const response = await fetch(`${API_BASE}/transition`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error executing workflow transition:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to execute workflow transition'
      };
    }
  }

  /**
   * Get available actions for voucher
   */
  async getVoucherActions(voucherId: string): Promise<VoucherActionsResponse> {
    try {
      const response = await fetch(`${API_BASE}/voucher/${voucherId}/actions`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching voucher actions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch voucher actions'
      };
    }
  }

  /**
   * Get workflow audit log for voucher
   */
  async getWorkflowAuditLog(voucherId: string): Promise<any> {
    try {
      const response = await fetch(`${API_BASE}/audit-log/${voucherId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching workflow audit log:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch audit log'
      };
    }
  }

  /**
   * Get workflow statistics for department
   */
  async getWorkflowStats(department: string, period: string = '30'): Promise<WorkflowStatsResponse> {
    try {
      const response = await fetch(`${API_BASE}/stats/${department}?period=${period}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching workflow stats:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch workflow statistics'
      };
    }
  }

  /**
   * Batch workflow operations
   */
  async batchTransition(
    voucherIds: string[],
    event: WorkflowEvent,
    metadata?: Record<string, any>
  ): Promise<WorkflowTransitionResponse[]> {
    const results: WorkflowTransitionResponse[] = [];

    // Execute transitions sequentially to avoid race conditions
    for (const voucherId of voucherIds) {
      const result = await this.transitionVoucher({
        voucherId,
        event,
        metadata
      });
      results.push(result);

      // If one fails, continue with others but log the error
      if (!result.success) {
        console.error(`Batch transition failed for voucher ${voucherId}:`, result.error);
      }
    }

    return results;
  }

  /**
   * Real-time workflow updates subscription
   */
  subscribeToWorkflowUpdates(
    department: string,
    callback: (update: any) => void
  ): () => void {
    // This would integrate with WebSocket or Server-Sent Events
    // For now, return a no-op unsubscribe function
    console.log(`Subscribing to workflow updates for ${department}`);
    
    // TODO: Implement real-time updates
    // const eventSource = new EventSource(`${API_BASE}/subscribe/${department}`);
    // eventSource.onmessage = (event) => {
    //   const update = JSON.parse(event.data);
    //   callback(update);
    // };
    
    // Return unsubscribe function
    return () => {
      console.log(`Unsubscribing from workflow updates for ${department}`);
      // eventSource.close();
    };
  }

  /**
   * Validate workflow transition before execution
   */
  async validateTransition(
    voucherId: string,
    event: WorkflowEvent
  ): Promise<{ valid: boolean; reason?: string }> {
    try {
      // Get current voucher actions to check if transition is valid
      const actionsResponse = await this.getVoucherActions(voucherId);
      
      if (!actionsResponse.success || !actionsResponse.data) {
        return { valid: false, reason: 'Could not fetch voucher actions' };
      }

      const validEvents = actionsResponse.data.actions.map(action => action.event);
      const isValid = validEvents.includes(event);

      return {
        valid: isValid,
        reason: isValid ? undefined : `Transition ${event} not allowed in current state`
      };
    } catch (error) {
      return {
        valid: false,
        reason: error instanceof Error ? error.message : 'Validation failed'
      };
    }
  }
}

// Export singleton instance
export const workflowService = WorkflowService.getInstance();
