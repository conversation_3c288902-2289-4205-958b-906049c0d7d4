{"root": ["./src/app.tsx", "./src/main.tsx", "./src/reset.tsx", "./src/vite-env.d.ts", "./src/components/errorboundary.tsx", "./src/components/serverdiscovery.tsx", "./src/components/sessionmanager.tsx", "./src/components/change-password-dialog.tsx", "./src/components/custom-dialog.tsx", "./src/components/department-provisional-cash.tsx", "./src/components/department-user-count.tsx", "./src/components/department-voucher-hub.tsx", "./src/components/department-voucher-receiving.tsx", "./src/components/exit-button.tsx", "./src/components/logo-signature.tsx", "./src/components/mode-toggle.tsx", "./src/components/notifications.tsx", "./src/components/offline-status.tsx", "./src/components/pending-batches.tsx", "./src/components/performance-monitor.tsx", "./src/components/theme-provider.tsx", "./src/components/user-nav.tsx", "./src/components/viewer-badge.tsx", "./src/components/voucher-batch-receiving.tsx", "./src/components/voucher-details-modal.tsx", "./src/components/voucher-form.tsx", "./src/components/voucher-rejection-dialog.tsx", "./src/components/voucher-return-dialog.tsx", "./src/components/voucher-table.tsx", "./src/components/admin/admin-dashboard.tsx", "./src/components/admin/backup-section.tsx", "./src/components/admin/index.ts", "./src/components/admin/live-system-time.tsx", "./src/components/admin/pending-registrations-section.tsx", "./src/components/admin/pendrive-backup-section.tsx", "./src/components/admin/system-settings-section.tsx", "./src/components/admin/user-management-section.tsx", "./src/components/analytics/analytics-dashboard.tsx", "./src/components/analytics/departmental-performance.tsx", "./src/components/analytics/pre-audit-savings-table.tsx", "./src/components/analytics/pre-audit-savings.tsx", "./src/components/analytics/provisional-cash-analysis.tsx", "./src/components/analytics/summary-cards.tsx", "./src/components/analytics/user-activity-metrics.tsx", "./src/components/analytics/voucher-trends.tsx", "./src/components/audit/attachment-upload.tsx", "./src/components/audit/attachment-viewer.tsx", "./src/components/audit-dashboard/audit-dashboard-content-new.tsx", "./src/components/audit-dashboard/audit-navigation.tsx", "./src/components/audit-dashboard/audit-voucher-batch-notification-new.tsx", "./src/components/audit-dashboard/department-card.tsx", "./src/components/audit-dashboard/department-voucher-hubs.tsx", "./src/components/audit-dashboard/dispatch-controls.tsx", "./src/components/audit-dashboard/newly-arrived-vouchers.tsx", "./src/components/audit-dashboard/simple-voucher-details.tsx", "./src/components/audit-dashboard/types.ts", "./src/components/audit-dashboard/voucher-details-dialog.tsx", "./src/components/common/unifiedvoucherbadges.tsx", "./src/components/common/voucherbadge.tsx", "./src/components/common/workflowactionbutton.tsx", "./src/components/dashboard/active-users-display.tsx", "./src/components/dashboard/dashboard-content.tsx", "./src/components/dashboard/dashboard-footer.tsx", "./src/components/dashboard/dashboard-header.tsx", "./src/components/dashboard/dashboard-modals.tsx", "./src/components/dashboard/department-newly-arrived-vouchers.tsx", "./src/components/dashboard/new-voucher-form.tsx", "./src/components/dashboard/voucher-list-controls.tsx", "./src/components/dashboard/voucher-tabs.tsx", "./src/components/department-dashboard/generic-department-dashboard.tsx", "./src/components/department-voucher-hub/dispatch-controls.tsx", "./src/components/department-voucher-hub/hub-header.tsx", "./src/components/department-voucher-hub/index.tsx", "./src/components/department-voucher-hub/search-bar.tsx", "./src/components/department-voucher-hub/send-dialog.tsx", "./src/components/department-voucher-hub/tab-content.tsx", "./src/components/department-voucher-hub/view-voucher-modal.tsx", "./src/components/department-voucher-hub/voucher-tabs.tsx", "./src/components/department-voucher-hub/hooks/use-department-voucher-tabs.ts", "./src/components/department-voucher-hub/hooks/use-voucher-deletion.ts", "./src/components/department-voucher-hub/hooks/use-voucher-dispatch.ts", "./src/components/department-voucher-hub/hooks/use-voucher-editing.ts", "./src/components/department-voucher-hub/hooks/use-voucher-filters.ts", "./src/components/department-voucher-hub/hooks/use-voucher-hub.tsx", "./src/components/department-voucher-hub/hooks/use-voucher-selection.ts", "./src/components/department-voucher-hub/hooks/use-voucher-tabs.ts", "./src/components/department-voucher-hub/hooks/use-voucher-viewing.ts", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/use-toast.ts", "./src/components/voucher-hub/certified-vouchers-tab.tsx", "./src/components/voucher-hub/dispatch-controls.tsx", "./src/components/voucher-hub/dispatched-vouchers-tab.tsx", "./src/components/voucher-hub/new-vouchers-tab.tsx", "./src/components/voucher-hub/pending-dispatch-tab.tsx", "./src/components/voucher-hub/post-transaction-edit-modal.tsx", "./src/components/voucher-hub/rejected-vouchers-tab.tsx", "./src/components/voucher-hub/returned-vouchers-tab.tsx", "./src/components/voucher-hub/send-dialog.tsx", "./src/components/voucher-hub/sortable-column-header.tsx", "./src/components/voucher-receiving/batch-information.tsx", "./src/components/voucher-receiving/rejection-comment-dialog.tsx", "./src/components/voucher-receiving/voucher-card.tsx", "./src/components/voucher-receiving/voucher-tabs-content.tsx", "./src/components/voucher-table/index.tsx", "./src/components/voucher-table/use-voucher-table.ts", "./src/components/voucher-table/voucher-table-body.tsx", "./src/components/voucher-table/voucher-table-header.tsx", "./src/components/voucher-table/voucher-table.tsx", "./src/components/year-selection/rollover-status-screen.tsx", "./src/components/year-selection/year-aware-app.tsx", "./src/components/year-selection/year-selector.tsx", "./src/hooks/use-analytics-data.ts", "./src/hooks/use-dashboard-state.ts", "./src/hooks/use-department-data.ts", "./src/hooks/use-department-users.ts", "./src/hooks/use-forced-reception.ts", "./src/hooks/use-lock.ts", "./src/hooks/use-mobile.tsx", "./src/hooks/use-network-status.ts", "./src/hooks/use-resource-lock.ts", "./src/hooks/use-smart-batch-locking.ts", "./src/hooks/use-toast.ts", "./src/hooks/use-voucher-processing.ts", "./src/lib/api-retry.ts", "./src/lib/api.ts", "./src/lib/connection-initializer.ts", "./src/lib/data.ts", "./src/lib/interval-manager.ts", "./src/lib/offline-api.ts", "./src/lib/performance-test.ts", "./src/lib/socket.ts", "./src/lib/store.ts", "./src/lib/toast-global.ts", "./src/lib/types.ts", "./src/lib/utils.ts", "./src/lib/constants/voucher-statuses.ts", "./src/lib/store/hooks.ts", "./src/lib/store/index.ts", "./src/lib/store/store.ts", "./src/lib/store/types.ts", "./src/lib/store/utils.ts", "./src/lib/store/slices/admin-slice.ts", "./src/lib/store/slices/audit-slice.ts", "./src/lib/store/slices/auth-slice.ts", "./src/lib/store/slices/blacklisted-voucher-ids-slice.ts", "./src/lib/store/slices/cleanup-slice.ts", "./src/lib/store/slices/department-slice.ts", "./src/lib/store/slices/initialize-data.ts", "./src/lib/store/slices/notifications-slice.ts", "./src/lib/store/slices/offline-slice.ts", "./src/lib/store/slices/provisional-cash-records-slice.ts", "./src/lib/store/slices/resource-locks-slice.ts", "./src/lib/store/slices/resource-viewers-slice.ts", "./src/lib/store/slices/users-slice.ts", "./src/lib/store/slices/voucher-batches-slice.ts", "./src/lib/store/slices/vouchers-slice.ts", "./src/lib/workflow/voucherworkflowstatemachine.ts", "./src/pages/admindashboard.tsx", "./src/pages/auditanalytics.tsx", "./src/pages/auditanalyticsdashboard.tsx", "./src/pages/auditdashboard.tsx", "./src/pages/auditvoucherview.tsx", "./src/pages/dashboard.tsx", "./src/pages/index.tsx", "./src/pages/login.tsx", "./src/pages/ministriesdashboard.tsx", "./src/pages/missionsdashboard.tsx", "./src/pages/notfound.tsx", "./src/pages/pensionsdashboard.tsx", "./src/pages/pentmediadashboard.tsx", "./src/pages/pentsosdashboard.tsx", "./src/pages/provisionalcashrecord.tsx", "./src/pages/register.tsx", "./src/pages/voucherdetails.tsx", "./src/pages/yearselection.tsx", "./src/services/workflow-service.ts", "./src/utils/exportutils.ts", "./src/utils/formatutils.ts", "./src/utils/server-discovery.ts", "./src/utils/voucherbadgeutils.ts", "./src/utils/voucherutils.ts"], "errors": true, "version": "5.8.3"}