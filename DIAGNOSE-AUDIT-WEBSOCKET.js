const io = require('socket.io-client');

async function diagnoseAuditWebSocket() {
  console.log('🔍 DIAGNOSING AUDIT WEBSOCKET CONNECTION...\n');
  
  try {
    // Connect as Audit user (same as browser would)
    console.log('1️⃣  Connecting to WebSocket as Audit user...');
    const auditSocket = io('http://10.25.41.232:8080', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true
    });
    
    let connected = false;
    let roomJoined = false;
    let joinResponse = null;
    
    // Set up all event listeners
    auditSocket.on('connect', () => {
      console.log('   ✅ WebSocket connected - Socket ID:', auditSocket.id);
      connected = true;
      
      // Join <PERSON>t department room (exactly like browser does)
      console.log('   📡 Joining AUDIT department room...');
      auditSocket.emit('join_department', { 
        department: 'AUDIT',
        userId: 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98',
        userName: 'EMMANUEL AMOAKOH'
      });
    });
    
    auditSocket.on('joined_department', (data) => {
      console.log('   ✅ Successfully joined department room:', data);
      roomJoined = true;
      joinResponse = data;
    });
    
    auditSocket.on('connect_error', (error) => {
      console.log('   ❌ Connection error:', error.message);
    });
    
    auditSocket.on('disconnect', (reason) => {
      console.log('   🔌 Disconnected:', reason);
    });
    
    // Listen for the notification event
    let notificationReceived = false;
    let notificationData = null;
    
    auditSocket.on('new_batch_notification', (data) => {
      console.log('   🔔 NOTIFICATION RECEIVED!');
      console.log('   📄 Data:', JSON.stringify(data, null, 2));
      notificationReceived = true;
      notificationData = data;
    });
    
    // Wait for connection setup
    console.log('   ⏳ Waiting for connection setup...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('\n📊 CONNECTION STATUS:');
    console.log(`   Connected: ${connected ? '✅' : '❌'}`);
    console.log(`   Room Joined: ${roomJoined ? '✅' : '❌'}`);
    
    if (joinResponse) {
      console.log(`   Join Response:`, joinResponse);
    }
    
    if (!connected) {
      console.log('\n❌ WEBSOCKET CONNECTION FAILED');
      console.log('💡 This explains why the audit user is not receiving notifications');
      console.log('🔧 Possible causes:');
      console.log('   - Server WebSocket not running on port 8080');
      console.log('   - Firewall blocking WebSocket connections');
      console.log('   - Network connectivity issues');
      auditSocket.disconnect();
      return;
    }
    
    if (!roomJoined) {
      console.log('\n❌ DEPARTMENT ROOM JOIN FAILED');
      console.log('💡 This explains why notifications are not reaching the audit user');
      console.log('🔧 Possible causes:');
      console.log('   - Server-side join_department handler not working');
      console.log('   - Authentication issues preventing room joining');
      auditSocket.disconnect();
      return;
    }
    
    console.log('\n✅ WEBSOCKET CONNECTION IS WORKING CORRECTLY');
    console.log('🎯 The audit user should be able to receive real-time notifications');
    
    // Test if we can receive a notification by sending a voucher
    console.log('\n2️⃣  Testing notification by sending a voucher...');
    
    // Wait a bit more to see if any notifications come through
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    if (notificationReceived) {
      console.log('✅ NOTIFICATION SYSTEM IS WORKING!');
      console.log('📄 Received notification:', notificationData);
    } else {
      console.log('⚠️  No notifications received during test period');
      console.log('💡 This could mean:');
      console.log('   - No vouchers were sent during the test');
      console.log('   - Server is not broadcasting notifications');
      console.log('   - Browser WebSocket connection is different from this test');
    }
    
    // Cleanup
    auditSocket.disconnect();
    
  } catch (error) {
    console.error('❌ Error during diagnosis:', error);
  }
}

// Run the diagnosis
diagnoseAuditWebSocket();
