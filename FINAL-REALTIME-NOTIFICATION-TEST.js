const io = require('socket.io-client');
const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');

let connection = null;
let auditSocket = null;

async function finalRealtimeNotificationTest() {
  console.log('🎯 FINAL REAL-TIME NOTIFICATION TEST\n');
  
  try {
    // 1. Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 2. Get users
    const [financeUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'FINANCE' LIMIT 1
    `);
    
    const [auditUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'AUDIT' LIMIT 1
    `);
    
    if (financeUsers.length === 0 || auditUsers.length === 0) {
      console.log('❌ Missing required users');
      return;
    }
    
    const financeUser = financeUsers[0];
    const auditUser = auditUsers[0];
    
    console.log(`👤 Finance user: ${financeUser.name}`);
    console.log(`👤 Audit user: ${auditUser.name}`);
    
    // 3. Set up Audit WebSocket (simulating the FIXED browser behavior)
    console.log('\n🔌 Setting up Audit WebSocket with FIX...');
    
    auditSocket = io('http://************:8080', {
      transports: ['websocket'],
      timeout: 5000,
      forceNew: true
    });
    
    let connected = false;
    let roomJoined = false;
    let notificationReceived = false;
    let notificationData = null;
    
    auditSocket.on('connect', () => {
      console.log('✅ Audit WebSocket connected');
      connected = true;
      
      // CRITICAL: Join department room immediately (this is the fix)
      console.log('📡 Joining AUDIT department room...');
      auditSocket.emit('join_department', { 
        department: 'AUDIT',
        userId: auditUser.id,
        userName: auditUser.name
      });
    });
    
    auditSocket.on('joined_department', (data) => {
      console.log('✅ Joined department room:', data);
      roomJoined = true;
    });
    
    auditSocket.on('new_batch_notification', (data) => {
      console.log('🔔 REAL-TIME NOTIFICATION RECEIVED!');
      console.log('📄 Data:', JSON.stringify(data, null, 2));
      notificationReceived = true;
      notificationData = data;
    });
    
    // Wait for WebSocket setup
    console.log('⏳ Waiting for WebSocket setup...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    if (!connected || !roomJoined) {
      console.log('❌ WebSocket setup failed');
      return;
    }
    
    console.log('✅ Audit WebSocket ready for notifications');
    
    // 4. Get existing voucher or create one via API
    console.log('\n📝 Getting existing voucher...');

    // Check for existing vouchers
    const [existingVouchers] = await connection.execute(`
      SELECT id FROM vouchers
      WHERE department = 'FINANCE'
      AND status = 'PENDING'
      AND sent_to_audit = 0
      LIMIT 1
    `);

    let voucherId;

    if (existingVouchers.length > 0) {
      voucherId = existingVouchers[0].id;
      console.log(`✅ Using existing voucher: ${voucherId}`);
    } else {
      // Create via API instead of direct database insert
      console.log('📝 Creating new voucher via API...');

      const createResponse = await fetch('http://************:8080/api/vouchers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-session-id': 'test-session-' + Date.now()
        },
        body: JSON.stringify({
          claimant: 'TEST CLAIMANT',
          description: 'Test voucher for real-time notification',
          amount: 1000.00,
          currency: 'GHS',
          department: 'FINANCE'
        })
      });

      if (createResponse.ok) {
        const createResult = await createResponse.json();
        voucherId = createResult.voucher.id;
        console.log(`✅ Created voucher via API: ${voucherId}`);
      } else {
        console.log('❌ Failed to create voucher via API');
        return;
      }
    }
    
    // 5. Send voucher to audit via API
    console.log('\n📤 Sending voucher to Audit...');
    
    const response = await fetch('http://************:8080/api/batches', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-session-id': 'test-session-' + Date.now()
      },
      body: JSON.stringify({
        department: 'FINANCE',
        voucherIds: [voucherId],
        fromAudit: false,
        dispatchedBy: financeUser.name
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Voucher sent to Audit successfully');
      console.log('📦 Batch ID:', result.batchId);
    } else {
      console.log('❌ Failed to send voucher:', response.status);
      const errorText = await response.text();
      console.log('Error:', errorText);
      return;
    }
    
    // 6. Wait for real-time notification
    console.log('\n⏳ Waiting for real-time notification...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 7. Check results
    console.log('\n📊 FINAL RESULTS:');
    console.log(`   WebSocket Connected: ${connected ? '✅' : '❌'}`);
    console.log(`   Department Room Joined: ${roomJoined ? '✅' : '❌'}`);
    console.log(`   Real-time Notification: ${notificationReceived ? '✅' : '❌'}`);
    
    if (notificationReceived) {
      console.log('\n🎉 SUCCESS! REAL-TIME NOTIFICATIONS ARE WORKING!');
      console.log('📄 Notification data:', notificationData);
      console.log('\n✅ The "NEWLY ARRIVED VOUCHERS" panel should now appear in the Audit dashboard');
      console.log('✅ The audit user should see the notification in real-time');
    } else {
      console.log('\n❌ REAL-TIME NOTIFICATION NOT RECEIVED');
      console.log('💡 Possible issues:');
      console.log('   - Server not broadcasting notifications');
      console.log('   - WebSocket room joining failed');
      console.log('   - Event listener not properly set up');
    }
    
    // Cleanup
    if (auditSocket) {
      auditSocket.disconnect();
    }
    
    if (connection) {
      await connection.end();
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Cleanup on error
    if (auditSocket) {
      auditSocket.disconnect();
    }
    
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
finalRealtimeNotificationTest();
