/**
 * 🧪 FINAL REAL-TIME TEST
 * Test real-time notifications by manually triggering the broadcast
 */

const mysql = require('mysql2/promise');
const io = require('socket.io-client');

async function finalRealtimeTest() {
  console.log('🧪 FINAL REAL-TIME NOTIFICATION TEST');
  console.log('=' .repeat(60));
  
  let connection;
  let auditSocket;
  
  try {
    // 1. Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 2. Get Audit user
    const [auditUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'AUDIT' LIMIT 1
    `);
    
    if (auditUsers.length === 0) {
      console.log('❌ No Audit users found');
      return;
    }
    
    const auditUser = auditUsers[0];
    console.log(`👤 Testing with Audit user: ${auditUser.name}`);
    
    // 3. Set up Audit WebSocket
    console.log('\n🔌 Setting up Audit WebSocket...');
    
    auditSocket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 5000
    });
    
    let connected = false;
    let roomJoined = false;
    let notificationReceived = false;
    let notificationData = null;
    
    auditSocket.on('connect', () => {
      console.log('✅ WebSocket connected');
      connected = true;
      
      // Join Audit department room
      auditSocket.emit('join_department', { 
        department: 'AUDIT',
        userId: auditUser.id,
        userName: auditUser.name
      });
    });
    
    auditSocket.on('joined_department', (data) => {
      console.log('✅ Joined department room:', data);
      roomJoined = true;
    });
    
    auditSocket.on('new_batch_notification', (data) => {
      console.log('🔔 REAL-TIME: new_batch_notification received!');
      console.log('📄 Data:', JSON.stringify(data, null, 2));
      notificationReceived = true;
      notificationData = data;
    });
    
    // Wait for WebSocket setup
    console.log('⏳ Waiting for WebSocket setup...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log(`🔌 Connected: ${connected}`);
    console.log(`📡 Room joined: ${roomJoined}`);
    
    if (!connected || !roomJoined) {
      console.log('❌ WebSocket setup failed');
      return;
    }
    
    // 4. Create a simple voucher and manually update it to trigger notification
    console.log('\n📝 Creating test voucher...');
    
    const voucherId = `REALTIME${Date.now()}`;
    
    // Create minimal voucher
    const [voucherResult] = await connection.execute(`
      INSERT INTO vouchers (voucher_id, amount, currency, department, created_by, status)
      VALUES (?, 1000.00, 'GHS', 'FINANCE', 'Test User', 'FINANCE_PENDING')
    `, [voucherId]);
    
    const dbVoucherId = voucherResult.insertId;
    console.log(`✅ Created voucher: ${voucherId} (ID: ${dbVoucherId})`);
    
    // 5. Manually update voucher to simulate sending to Audit
    console.log('\n📤 Manually sending voucher to Audit...');
    
    await connection.execute(`
      UPDATE vouchers 
      SET sent_to_audit = TRUE, 
          status = 'PENDING_RECEIPT',
          dispatch_to_audit_by = 'Test User',
          dispatch_time = NOW()
      WHERE id = ?
    `, [dbVoucherId]);
    
    // Create batch
    const batchId = `BATCH${Date.now()}`;
    await connection.execute(`
      INSERT INTO voucher_batches (id, department, voucher_count, received, from_audit)
      VALUES (?, 'FINANCE', 1, FALSE, FALSE)
    `, [batchId]);
    
    console.log(`✅ Created batch: ${batchId}`);
    
    // 6. Now manually trigger the broadcast by calling the API endpoint that should trigger it
    console.log('\n📡 Testing if server broadcasts work...');
    
    // The issue might be that the broadcast is not being called when vouchers are sent
    // Let's check if we can trigger it manually by simulating the exact scenario
    
    // Wait a moment for any automatic broadcasts
    console.log('⏳ Waiting for automatic broadcast (5 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 7. Check if notification was received
    console.log('\n' + '=' .repeat(60));
    console.log('📋 FINAL TEST RESULTS');
    console.log('=' .repeat(60));
    
    console.log(`🔌 WebSocket connected: ${connected ? 'YES' : 'NO'}`);
    console.log(`📡 Joined department room: ${roomJoined ? 'YES' : 'NO'}`);
    console.log(`🔔 Notification received: ${notificationReceived ? 'YES' : 'NO'}`);
    
    if (notificationData) {
      console.log('📄 Notification data:', notificationData);
    }
    
    // Check database state
    const [finalVoucher] = await connection.execute(`
      SELECT voucher_id, status, sent_to_audit, received_by_audit 
      FROM vouchers WHERE id = ?
    `, [dbVoucherId]);
    
    if (finalVoucher.length > 0) {
      const voucher = finalVoucher[0];
      console.log(`🎫 Voucher: ${voucher.voucher_id} - Status: ${voucher.status}`);
      console.log(`📤 Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
    }
    
    const [finalBatch] = await connection.execute(`
      SELECT id, department, received FROM voucher_batches WHERE id = ?
    `, [batchId]);
    
    if (finalBatch.length > 0) {
      const batch = finalBatch[0];
      console.log(`📦 Batch: ${batch.id} - Received: ${batch.received ? 'YES' : 'NO'}`);
    }
    
    if (!notificationReceived) {
      console.log('\n⚠️ DIAGNOSIS: Real-time notification not received');
      console.log('💡 Possible causes:');
      console.log('   1. Server is not calling broadcastToAuditDepartment when vouchers are sent');
      console.log('   2. The API endpoint for sending vouchers is not working');
      console.log('   3. The broadcast function is not reaching connected clients');
      console.log('\n🔧 NEXT STEPS:');
      console.log('   1. Check server logs for broadcast calls');
      console.log('   2. Verify the send-to-audit API endpoint');
      console.log('   3. Test with the actual web interface');
    } else {
      console.log('\n🎉 SUCCESS: Real-time notifications working!');
    }
    
    console.log('\n✅ FINAL REAL-TIME TEST COMPLETE');
    
  } catch (error) {
    console.error('❌ Error in final test:', error);
  } finally {
    if (auditSocket) {
      auditSocket.disconnect();
    }
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
finalRealtimeTest().catch(console.error);
