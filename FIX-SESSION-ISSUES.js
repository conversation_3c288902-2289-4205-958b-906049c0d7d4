/**
 * 🔧 PRODUCTION FIX: Clear Invalid Sessions
 * Fix the 401 authentication errors caused by invalid session IDs
 */

const mysql = require('mysql2/promise');

async function fixSessionIssues() {
  console.log('🔧 PRODUCTION FIX: Clearing Invalid Sessions');
  console.log('=' .repeat(60));
  
  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 1. Check current sessions
    const [sessions] = await connection.execute('SELECT * FROM sessions');
    console.log(`📊 Current sessions in database: ${sessions.length}`);
    
    if (sessions.length > 0) {
      console.log('📋 Session details:');
      sessions.forEach((session, index) => {
        console.log(`  ${index + 1}. ID: ${session.session_id}`);
        console.log(`     User ID: ${session.user_id}`);
        console.log(`     Created: ${session.created_at}`);
        console.log(`     Expires: ${session.expires_at}`);
        console.log('');
      });
    }
    
    // 2. Clear all sessions to force fresh login
    console.log('🧹 Clearing all sessions...');
    const [deleteResult] = await connection.execute('DELETE FROM sessions');
    console.log(`✅ Cleared ${deleteResult.affectedRows} sessions`);
    
    // 3. Verify sessions are cleared
    const [remainingSessions] = await connection.execute('SELECT COUNT(*) as count FROM sessions');
    console.log(`📊 Remaining sessions: ${remainingSessions[0].count}`);
    
    // 4. Check user accounts are still intact
    const [users] = await connection.execute('SELECT id, name, department FROM users ORDER BY department, name');
    console.log(`👥 User accounts verified: ${users.length} users`);
    
    console.log('\n📋 Available users for login:');
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.name} (${user.department})`);
    });
    
    console.log('\n' + '=' .repeat(60));
    console.log('✅ SESSION FIX COMPLETE');
    console.log('🔄 Users need to login again with fresh credentials');
    console.log('🔐 All invalid session IDs have been cleared');
    console.log('=' .repeat(60));
    
  } catch (error) {
    console.error('❌ Error fixing sessions:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the fix
fixSessionIssues().catch(console.error);
