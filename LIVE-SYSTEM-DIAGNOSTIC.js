/**
 * 🚨 LIVE SYSTEM DIAGNOSTIC
 * Critical diagnostic for production issues
 */

const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'vms@2025@1989',
  database: 'vms_production',
  charset: 'utf8mb4'
};

async function runLiveSystemDiagnostic() {
  console.log('🚨 LIVE SYSTEM DIAGNOSTIC - PRODUCTION ISSUES');
  console.log('=' .repeat(70));
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection established');
    
    // 1. Check voucher counts by department and status
    console.log('\n📊 VOUCHER ANALYSIS:');
    console.log('-' .repeat(50));
    
    const voucherStats = await connection.query(`
      SELECT 
        department,
        status,
        workflow_state,
        COUNT(*) as count
      FROM vouchers 
      GROUP BY department, status, workflow_state
      ORDER BY department, status
    `);
    
    console.log('Voucher Distribution:');
    voucherStats[0].forEach(row => {
      console.log(`  ${row.department}: ${row.status} (${row.workflow_state}) - ${row.count} vouchers`);
    });
    
    // 2. Check batch status
    console.log('\n📦 BATCH ANALYSIS:');
    console.log('-' .repeat(50));
    
    const batchStats = await connection.query(`
      SELECT 
        department,
        received,
        from_audit,
        COUNT(*) as count
      FROM voucher_batches 
      GROUP BY department, received, from_audit
      ORDER BY department
    `);
    
    console.log('Batch Distribution:');
    batchStats[0].forEach(row => {
      const status = row.received ? 'RECEIVED' : 'PENDING';
      const direction = row.from_audit ? 'FROM_AUDIT' : 'TO_AUDIT';
      console.log(`  ${row.department}: ${status} ${direction} - ${row.count} batches`);
    });
    
    // 3. Check provisional cash records
    console.log('\n💰 PROVISIONAL CASH ANALYSIS:');
    console.log('-' .repeat(50));
    
    const provisionalStats = await connection.query(`
      SELECT COUNT(*) as count FROM provisional_cash_records
    `);
    
    console.log('Provisional Cash Distribution:');
    const provisionalData = provisionalStats[0][0];
    if (provisionalData.count === 0) {
      console.log('  ⚠️  NO PROVISIONAL CASH RECORDS FOUND');
    } else {
      console.log(`  Total: ${provisionalData.count} records`);
    }
    
    // 4. Check recent vouchers (last 24 hours)
    console.log('\n🕐 RECENT ACTIVITY (Last 24 Hours):');
    console.log('-' .repeat(50));
    
    const recentVouchers = await connection.query(`
      SELECT 
        voucher_id,
        department,
        status,
        workflow_state,
        created_at,
        sent_to_audit,
        received_by_audit
      FROM vouchers 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      ORDER BY created_at DESC
      LIMIT 10
    `);
    
    if (recentVouchers[0].length === 0) {
      console.log('  ⚠️  NO RECENT VOUCHERS FOUND (Last 24 hours)');
    } else {
      console.log('Recent Vouchers:');
      recentVouchers[0].forEach(row => {
        const auditStatus = row.sent_to_audit ? (row.received_by_audit ? 'RECEIVED_BY_AUDIT' : 'SENT_TO_AUDIT') : 'NOT_SENT';
        console.log(`  ${row.voucher_id}: ${row.department} → ${row.status} (${row.workflow_state}) [${auditStatus}]`);
      });
    }
    
    // 5. Check audit-specific vouchers
    console.log('\n🔍 AUDIT DEPARTMENT ANALYSIS:');
    console.log('-' .repeat(50));
    
    const auditVouchers = await connection.query(`
      SELECT 
        COUNT(*) as total_audit_vouchers,
        SUM(CASE WHEN status LIKE 'AUDIT:%' THEN 1 ELSE 0 END) as audit_status_vouchers,
        SUM(CASE WHEN department = 'AUDIT' THEN 1 ELSE 0 END) as audit_dept_vouchers,
        SUM(CASE WHEN sent_to_audit = 1 THEN 1 ELSE 0 END) as sent_to_audit_vouchers,
        SUM(CASE WHEN received_by_audit = 1 THEN 1 ELSE 0 END) as received_by_audit_vouchers
      FROM vouchers
    `);
    
    const auditData = auditVouchers[0][0];
    console.log('Audit Voucher Breakdown:');
    console.log(`  Total vouchers: ${auditData.total_audit_vouchers}`);
    console.log(`  With AUDIT status: ${auditData.audit_status_vouchers}`);
    console.log(`  In AUDIT department: ${auditData.audit_dept_vouchers}`);
    console.log(`  Sent to audit: ${auditData.sent_to_audit_vouchers}`);
    console.log(`  Received by audit: ${auditData.received_by_audit_vouchers}`);
    
    // 6. Check notifications
    console.log('\n🔔 NOTIFICATION ANALYSIS:');
    console.log('-' .repeat(50));
    
    const notifications = await connection.query(`
      SELECT 
        type,
        is_read,
        COUNT(*) as count
      FROM notifications 
      WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      GROUP BY type, is_read
      ORDER BY type
    `);
    
    if (notifications[0].length === 0) {
      console.log('  ⚠️  NO RECENT NOTIFICATIONS FOUND');
    } else {
      console.log('Recent Notifications:');
      notifications[0].forEach(row => {
        const status = row.is_read ? 'READ' : 'unread';
        console.log(`  ${row.type}: ${row.count} ${status}`);
      });
    }
    
    // 7. Check active sessions
    console.log('\n👥 ACTIVE SESSIONS:');
    console.log('-' .repeat(50));
    
    const sessions = await connection.query(`
      SELECT 
        u.name,
        u.department,
        s.is_active,
        s.last_activity,
        s.socket_id
      FROM active_sessions s
      JOIN users u ON s.user_id = u.id
      WHERE s.is_active = 1
      ORDER BY s.last_activity DESC
    `);
    
    if (sessions[0].length === 0) {
      console.log('  ⚠️  NO ACTIVE SESSIONS FOUND');
    } else {
      console.log('Active Sessions:');
      sessions[0].forEach(row => {
        const socketStatus = row.socket_id ? 'WebSocket Connected' : 'HTTP Only';
        console.log(`  ${row.name} (${row.department}): ${socketStatus} - Last: ${row.last_activity}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
  
  console.log('\n' + '=' .repeat(70));
  console.log('🚨 LIVE SYSTEM DIAGNOSTIC COMPLETE');
  console.log('=' .repeat(70));
}

// Run diagnostic
runLiveSystemDiagnostic().catch(console.error);
