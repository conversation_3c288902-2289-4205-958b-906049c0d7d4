/**
 * 🚨 PROVISIONAL CASH DIAGNOSTIC TEST
 * Test provisional cash creation for the existing voucher
 */

const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'vms@2025@1989',
  database: 'vms_production',
  charset: 'utf8mb4'
};

async function testProvisionalCashCreation() {
  console.log('🚨 PROVISIONAL CASH DIAGNOSTIC TEST');
  console.log('=' .repeat(60));
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection established');
    
    // 1. Get the existing voucher details
    console.log('\n📋 EXISTING VOUCHER ANALYSIS:');
    console.log('-' .repeat(40));
    
    const vouchers = await connection.query(`
      SELECT 
        id,
        voucher_id,
        claimant,
        description,
        amount,
        currency,
        department,
        status,
        workflow_state,
        post_provisional_cash,
        work_started,
        pre_audited_amount,
        pre_audited_by
      FROM vouchers 
      WHERE department = 'AUDIT'
      ORDER BY created_at DESC
      LIMIT 1
    `);
    
    if (vouchers[0].length === 0) {
      console.log('❌ No vouchers found in AUDIT department');
      return;
    }
    
    const voucher = vouchers[0][0];
    console.log('Voucher Details:');
    console.log(`  ID: ${voucher.id}`);
    console.log(`  Voucher ID: ${voucher.voucher_id}`);
    console.log(`  Claimant: ${voucher.claimant}`);
    console.log(`  Amount: ₦${voucher.amount}`);
    console.log(`  Status: ${voucher.status}`);
    console.log(`  Workflow State: ${voucher.workflow_state}`);
    console.log(`  Post Provisional Cash: ${voucher.post_provisional_cash ? 'YES' : 'NO'}`);
    console.log(`  Work Started: ${voucher.work_started ? 'YES' : 'NO'}`);
    console.log(`  Pre-audited Amount: ${voucher.pre_audited_amount || 'Not set'}`);
    console.log(`  Pre-audited By: ${voucher.pre_audited_by || 'Not set'}`);
    
    // 2. Check if provisional cash record exists for this voucher
    console.log('\n💰 PROVISIONAL CASH CHECK:');
    console.log('-' .repeat(40));
    
    const existingRecords = await connection.query(`
      SELECT * FROM provisional_cash_records 
      WHERE voucher_id = ?
    `, [voucher.id]);
    
    if (existingRecords[0].length > 0) {
      console.log('✅ Provisional cash record exists:');
      existingRecords[0].forEach((record, index) => {
        console.log(`  Record ${index + 1}:`);
        console.log(`    ID: ${record.id}`);
        console.log(`    Claimant: ${record.claimant}`);
        console.log(`    Amount: ₦${record.main_amount}`);
        console.log(`    Created: ${record.created_at}`);
      });
    } else {
      console.log('⚠️  NO provisional cash record found for this voucher');
      
      // 3. Test creating provisional cash record
      console.log('\n🧪 TESTING PROVISIONAL CASH CREATION:');
      console.log('-' .repeat(40));
      
      try {
        const { v4: uuidv4 } = require('uuid');
        const recordId = uuidv4();
        
        // Create provisional cash record
        await connection.query(`
          INSERT INTO provisional_cash_records (
            id, voucher_id, voucher_ref, claimant, description, main_amount, currency, date
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          recordId,
          voucher.id,
          voucher.voucher_id,
          voucher.claimant,
          voucher.description,
          voucher.pre_audited_amount || voucher.amount,
          voucher.currency,
          new Date().toISOString().split('T')[0]
        ]);
        
        // Update voucher
        await connection.query(`
          UPDATE vouchers SET post_provisional_cash = TRUE WHERE id = ?
        `, [voucher.id]);
        
        console.log('✅ TEST SUCCESSFUL: Provisional cash record created');
        console.log(`  Record ID: ${recordId}`);
        console.log(`  Amount: ₦${voucher.pre_audited_amount || voucher.amount}`);
        
        // Verify creation
        const verifyRecords = await connection.query(`
          SELECT COUNT(*) as count FROM provisional_cash_records 
          WHERE voucher_id = ?
        `, [voucher.id]);
        
        console.log(`  Verification: ${verifyRecords[0][0].count} record(s) now exist`);
        
      } catch (error) {
        console.error('❌ TEST FAILED: Error creating provisional cash record:', error.message);
      }
    }
    
    // 4. Check table structure
    console.log('\n🔍 TABLE STRUCTURE ANALYSIS:');
    console.log('-' .repeat(40));
    
    const tableInfo = await connection.query(`
      DESCRIBE provisional_cash_records
    `);
    
    console.log('Provisional Cash Records Table Structure:');
    tableInfo[0].forEach(column => {
      console.log(`  ${column.Field}: ${column.Type} ${column.Null === 'NO' ? '(Required)' : '(Optional)'}`);
    });
    
    // 5. Final count
    console.log('\n📊 FINAL COUNTS:');
    console.log('-' .repeat(40));
    
    const finalCounts = await connection.query(`
      SELECT 
        (SELECT COUNT(*) FROM vouchers WHERE department = 'AUDIT') as audit_vouchers,
        (SELECT COUNT(*) FROM provisional_cash_records) as provisional_records,
        (SELECT COUNT(*) FROM vouchers WHERE post_provisional_cash = 1) as vouchers_with_provisional
    `);
    
    const counts = finalCounts[0][0];
    console.log(`  Audit Vouchers: ${counts.audit_vouchers}`);
    console.log(`  Provisional Cash Records: ${counts.provisional_records}`);
    console.log(`  Vouchers with Provisional Flag: ${counts.vouchers_with_provisional}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('🚨 PROVISIONAL CASH TEST COMPLETE');
  console.log('=' .repeat(60));
}

// Run test
testProvisionalCashCreation().catch(console.error);
