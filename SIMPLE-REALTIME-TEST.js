/**
 * 🧪 SIMPLE REAL-TIME TEST
 * Test real-time notifications by checking existing vouchers and WebSocket
 */

const mysql = require('mysql2/promise');
const io = require('socket.io-client');

async function simpleRealtimeTest() {
  console.log('🧪 SIMPLE REAL-TIME NOTIFICATION TEST');
  console.log('=' .repeat(60));
  
  let connection;
  let auditSocket;
  
  try {
    // 1. Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 2. Check vouchers table structure
    console.log('\n📋 Checking vouchers table structure...');
    const [columns] = await connection.execute('DESCRIBE vouchers');
    console.log('📊 Vouchers table columns:');
    columns.forEach((col, index) => {
      console.log(`  ${index + 1}. ${col.Field} (${col.Type})`);
    });
    
    // 3. Check current vouchers
    console.log('\n🎫 Checking current vouchers...');
    const [vouchers] = await connection.execute(`
      SELECT id, voucher_id, department, status, sent_to_audit, received_by_audit 
      FROM vouchers 
      ORDER BY id DESC 
      LIMIT 3
    `);
    
    console.log(`📊 Current vouchers: ${vouchers.length}`);
    vouchers.forEach(v => {
      console.log(`   ${v.voucher_id} (${v.department}) - Status: ${v.status}`);
      console.log(`   Sent to Audit: ${v.sent_to_audit ? 'YES' : 'NO'}, Received: ${v.received_by_audit ? 'YES' : 'NO'}`);
    });
    
    // 4. Check current batches
    console.log('\n📦 Checking current batches...');
    const [batches] = await connection.execute(`
      SELECT * FROM voucher_batches 
      ORDER BY id DESC 
      LIMIT 3
    `);
    
    console.log(`📊 Current batches: ${batches.length}`);
    batches.forEach(b => {
      console.log(`   Batch ${b.id} (${b.department}) - Received: ${b.received ? 'YES' : 'NO'}`);
    });
    
    // 5. Get Audit user
    const [auditUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'AUDIT' LIMIT 1
    `);
    
    if (auditUsers.length === 0) {
      console.log('❌ No Audit users found');
      return;
    }
    
    const auditUser = auditUsers[0];
    console.log(`\n👤 Testing with Audit user: ${auditUser.name}`);
    
    // 6. Test WebSocket connection
    console.log('\n🔌 Testing WebSocket connection...');
    
    auditSocket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 5000
    });
    
    let connected = false;
    let joinedRoom = false;
    let eventsReceived = [];
    
    auditSocket.on('connect', () => {
      console.log('✅ WebSocket connected');
      connected = true;
      
      // Join Audit department room
      auditSocket.emit('join_department', { 
        department: 'AUDIT',
        userId: auditUser.id,
        userName: auditUser.name
      });
      
      console.log('📡 Attempting to join AUDIT department room...');
    });
    
    auditSocket.on('joined_department', (data) => {
      console.log('✅ Successfully joined department room:', data);
      joinedRoom = true;
    });
    
    auditSocket.on('new_batch_notification', (data) => {
      console.log('🔔 REAL-TIME: new_batch_notification received:', data);
      eventsReceived.push('new_batch_notification');
    });
    
    auditSocket.on('batch_update', (data) => {
      console.log('📦 REAL-TIME: batch_update received:', data);
      eventsReceived.push('batch_update');
    });
    
    auditSocket.on('voucher_update', (data) => {
      console.log('🎫 REAL-TIME: voucher_update received:', data);
      eventsReceived.push('voucher_update');
    });
    
    auditSocket.on('user_online', (data) => {
      console.log('👤 REAL-TIME: user_online received:', data);
      eventsReceived.push('user_online');
    });
    
    auditSocket.on('connect_error', (error) => {
      console.log('❌ WebSocket connection error:', error.message);
    });
    
    auditSocket.on('disconnect', (reason) => {
      console.log('🔌 WebSocket disconnected:', reason);
    });
    
    // Wait for connection and room join
    console.log('\n⏳ Waiting for WebSocket setup (5 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 7. Check if there are pending notifications that should trigger
    console.log('\n🔍 Checking for pending notifications...');
    
    const pendingVouchers = vouchers.filter(v => v.sent_to_audit && !v.received_by_audit);
    const pendingBatches = batches.filter(b => !b.received && !b.from_audit);
    
    console.log(`🎫 Vouchers pending Audit receipt: ${pendingVouchers.length}`);
    console.log(`📦 Batches pending Audit receipt: ${pendingBatches.length}`);
    
    if (pendingVouchers.length > 0 || pendingBatches.length > 0) {
      console.log('\n💡 There are pending items that should show notifications');
      console.log('🔄 In a working system, these should appear immediately when Audit user logs in');
    }
    
    // 8. Test manual event emission (if possible)
    console.log('\n🧪 Testing manual event emission...');
    
    if (connected && pendingBatches.length > 0) {
      // Try to trigger a refresh that might cause notifications
      console.log('📡 Emitting test events...');
      
      auditSocket.emit('request_notifications', { department: 'AUDIT' });
      auditSocket.emit('request_batches', { department: 'AUDIT' });
      
      // Wait for any responses
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    // 9. Results
    console.log('\n' + '=' .repeat(60));
    console.log('📋 REAL-TIME TEST RESULTS');
    console.log('=' .repeat(60));
    
    console.log(`🔌 WebSocket connected: ${connected ? 'YES' : 'NO'}`);
    console.log(`📡 Joined department room: ${joinedRoom ? 'YES' : 'NO'}`);
    console.log(`📨 Events received: ${eventsReceived.length}`);
    
    if (eventsReceived.length > 0) {
      console.log('📋 Events received:');
      eventsReceived.forEach((event, index) => {
        console.log(`   ${index + 1}. ${event}`);
      });
    }
    
    console.log(`🎫 Pending vouchers: ${pendingVouchers.length}`);
    console.log(`📦 Pending batches: ${pendingBatches.length}`);
    
    if (pendingVouchers.length > 0 && eventsReceived.length === 0) {
      console.log('\n⚠️ ISSUE CONFIRMED: Pending vouchers exist but no real-time events received');
      console.log('💡 The real-time notification system is not working properly');
      console.log('🔧 Need to investigate:');
      console.log('   1. Server WebSocket broadcast functions');
      console.log('   2. Department room joining mechanism');
      console.log('   3. Event emission timing');
    } else if (eventsReceived.length > 0) {
      console.log('\n✅ REAL-TIME EVENTS WORKING');
      console.log('🎉 WebSocket communication is functional');
    } else {
      console.log('\n📝 NO PENDING ITEMS TO TEST');
      console.log('💡 Create a voucher and send to Audit to test real-time notifications');
    }
    
    console.log('\n✅ SIMPLE REAL-TIME TEST COMPLETE');
    
  } catch (error) {
    console.error('❌ Error in simple real-time test:', error);
  } finally {
    if (auditSocket) {
      auditSocket.disconnect();
    }
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
simpleRealtimeTest().catch(console.error);
