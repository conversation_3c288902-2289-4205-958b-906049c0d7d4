/**
 * 🎯 COMPREHENSIVE SYSTEM HEALTH VALIDATION
 * Validates all critical system components after HTTP/HTTPS fixes
 */

const axios = require('axios');

const BASE_URL = 'http://10.25.41.232:8080';

async function validateSystemHealth() {
  console.log('🔍 COMPREHENSIVE SYSTEM HEALTH VALIDATION');
  console.log('=' .repeat(60));
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };

  // Test 1: Server Health Check
  try {
    console.log('1️⃣  Testing Server Health...');
    const response = await axios.get(`${BASE_URL}/health`);
    
    if (response.status === 200 && response.data.status === 'healthy') {
      console.log('   ✅ Server health: HEALTHY');
      results.passed++;
      results.tests.push({ name: 'Server Health', status: 'PASS' });
    } else {
      throw new Error('Unhealthy response');
    }
  } catch (error) {
    console.log('   ❌ Server health: FAILED');
    results.failed++;
    results.tests.push({ name: 'Server Health', status: 'FAIL', error: error.message });
  }

  // Test 2: API Endpoints
  try {
    console.log('2️⃣  Testing API Endpoints...');
    const response = await axios.get(`${BASE_URL}/api`);
    
    if (response.status === 200 && response.data.name === 'Voucher Management System API') {
      console.log('   ✅ API endpoints: ACTIVE');
      results.passed++;
      results.tests.push({ name: 'API Endpoints', status: 'PASS' });
    } else {
      throw new Error('Invalid API response');
    }
  } catch (error) {
    console.log('   ❌ API endpoints: FAILED');
    results.failed++;
    results.tests.push({ name: 'API Endpoints', status: 'FAIL', error: error.message });
  }

  // Test 3: Static File Serving
  try {
    console.log('3️⃣  Testing Static File Serving...');
    const response = await axios.get(`${BASE_URL}/assets/index-DOQanLTi-1753698087183.js`, {
      maxRedirects: 0,
      validateStatus: (status) => status === 200
    });
    
    if (response.status === 200 && response.headers['content-type'].includes('javascript')) {
      console.log('   ✅ Static files: SERVING CORRECTLY');
      results.passed++;
      results.tests.push({ name: 'Static File Serving', status: 'PASS' });
    } else {
      throw new Error('Invalid static file response');
    }
  } catch (error) {
    console.log('   ❌ Static files: FAILED');
    results.failed++;
    results.tests.push({ name: 'Static File Serving', status: 'FAIL', error: error.message });
  }

  // Test 4: Security Headers
  try {
    console.log('4️⃣  Testing Security Headers...');
    const response = await axios.get(`${BASE_URL}/`);
    
    const headers = response.headers;
    const hasProperHeaders = 
      headers['x-content-type-options'] === 'nosniff' &&
      headers['x-frame-options'] === 'SAMEORIGIN' &&
      !headers['strict-transport-security'] && // Should NOT have HSTS
      !headers['content-security-policy']; // Should NOT have CSP
    
    if (hasProperHeaders) {
      console.log('   ✅ Security headers: PROPERLY CONFIGURED');
      results.passed++;
      results.tests.push({ name: 'Security Headers', status: 'PASS' });
    } else {
      throw new Error('Incorrect security headers');
    }
  } catch (error) {
    console.log('   ❌ Security headers: FAILED');
    results.failed++;
    results.tests.push({ name: 'Security Headers', status: 'FAIL', error: error.message });
  }

  // Test 5: CORS Configuration
  try {
    console.log('5️⃣  Testing CORS Configuration...');
    const response = await axios.options(`${BASE_URL}/api`);
    
    const headers = response.headers;
    const hasCORS = 
      headers['access-control-allow-origin'] === '*' &&
      headers['access-control-allow-credentials'] === 'true';
    
    if (hasCORS) {
      console.log('   ✅ CORS: PROPERLY CONFIGURED');
      results.passed++;
      results.tests.push({ name: 'CORS Configuration', status: 'PASS' });
    } else {
      throw new Error('Incorrect CORS configuration');
    }
  } catch (error) {
    console.log('   ❌ CORS: FAILED');
    results.failed++;
    results.tests.push({ name: 'CORS Configuration', status: 'FAIL', error: error.message });
  }

  // Test 6: Main Application Loading
  try {
    console.log('6️⃣  Testing Main Application...');
    const response = await axios.get(`${BASE_URL}/`);
    
    if (response.status === 200 && response.data.includes('voucher-guardian-system')) {
      console.log('   ✅ Main application: LOADING CORRECTLY');
      results.passed++;
      results.tests.push({ name: 'Main Application', status: 'PASS' });
    } else {
      throw new Error('Application not loading properly');
    }
  } catch (error) {
    console.log('   ❌ Main application: FAILED');
    results.failed++;
    results.tests.push({ name: 'Main Application', status: 'FAIL', error: error.message });
  }

  // Test 7: Network Accessibility
  try {
    console.log('7️⃣  Testing Network Accessibility...');
    const response = await axios.get(`${BASE_URL}/health`, {
      timeout: 5000
    });
    
    if (response.status === 200) {
      console.log('   ✅ Network access: WORKING FROM NETWORK');
      results.passed++;
      results.tests.push({ name: 'Network Accessibility', status: 'PASS' });
    } else {
      throw new Error('Network access failed');
    }
  } catch (error) {
    console.log('   ❌ Network access: FAILED');
    results.failed++;
    results.tests.push({ name: 'Network Accessibility', status: 'FAIL', error: error.message });
  }

  // Final Results
  console.log('\n' + '=' .repeat(60));
  console.log('🎯 SYSTEM HEALTH VALIDATION RESULTS');
  console.log('=' .repeat(60));
  console.log(`✅ PASSED: ${results.passed}`);
  console.log(`❌ FAILED: ${results.failed}`);
  console.log(`📊 SUCCESS RATE: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 ALL SYSTEMS OPERATIONAL - PRODUCTION READY!');
  } else {
    console.log('\n⚠️  ISSUES DETECTED - REVIEW FAILED TESTS');
    console.log('\nFailed Tests:');
    results.tests.filter(t => t.status === 'FAIL').forEach(test => {
      console.log(`   - ${test.name}: ${test.error}`);
    });
  }
  
  return results;
}

// Run validation
validateSystemHealth().catch(console.error);
