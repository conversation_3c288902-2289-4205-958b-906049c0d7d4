-- VMS Production Database Backup (AUTOMATED)
-- Generated: 2025-08-01T15:27:53.001Z
-- Database: vms_production
-- Type: Automated Backup

SET FOREIGN_KEY_CHECKS = 0;

-- Table: active_sessions
DROP TABLE IF EXISTS `active_sessions`;
CREATE TABLE `active_sessions` (
  `id` varchar(100) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `token` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` datetime DEFAULT ((now() + interval 24 hour)),
  `user_name` varchar(255) DEFAULT NULL,
  `department` varchar(50) DEFAULT NULL,
  `socket_id` varchar(100) DEFAULT NULL,
  `session_start` datetime NOT NULL,
  `last_activity` datetime NOT NULL,
  `session_end` datetime DEFAULT NULL,
  `client_ip` varchar(50) DEFAULT NULL,
  `user_agent` text,
  `is_active` tinyint(1) DEFAULT '1',
  `role` varchar(50) DEFAULT 'User',
  `selected_year` int DEFAULT NULL,
  `selected_database` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `is_active` (`is_active`),
  KEY `department` (`department`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: active_sessions
INSERT INTO `active_sessions` VALUES
('08eaed03-0789-4652-81f2-6861adb609e0', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 14:31:07', '2025-08-02 14:31:07', 'CHARIS', 'MISSIONS', NULL, '2025-08-01 14:31:07', '2025-08-01 14:31:33', '2025-08-01 14:31:33', NULL, NULL, 0, 'User', NULL, NULL),
('0cdcb5c2-1ec8-405d-bc27-f69611022582', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:11:35', '2025-08-02 13:11:35', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 13:11:35', '2025-08-01 13:17:37', '2025-08-01 13:27:28', NULL, NULL, 0, 'User', NULL, NULL),
('0df6a512-264e-4f55-8873-b7a15daf8a84', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:17:37', '2025-08-02 13:17:37', 'EMMANUEL AMOAKOH', 'AUDIT', 'GdruNpMp1uZGgsG_AAAF', '2025-08-01 13:17:37', '2025-08-01 13:21:32', '2025-08-01 13:21:32', NULL, NULL, 0, 'User', NULL, NULL),
('213ade58-5829-4905-a88b-db6e1e947fa5', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 14:45:26', '2025-08-02 14:45:26', 'CHARIS', 'MISSIONS', NULL, '2025-08-01 14:45:26', '2025-08-01 14:45:32', '2025-08-01 14:45:32', NULL, NULL, 0, 'User', NULL, NULL),
('2fb8c041-b7e6-4943-8ff6-6c0fe995703d', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', NULL, '2025-08-01 15:23:49', '2025-08-02 15:23:49', 'SELORM', 'AUDIT', 'NXzp7V135jRo00HsAAAg', '2025-08-01 15:23:49', '2025-08-01 15:27:51', NULL, NULL, NULL, 1, 'User', NULL, NULL),
('3a013862-294d-44cc-bb08-5d34c3204e42', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 14:26:48', '2025-08-02 14:26:48', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 14:26:48', '2025-08-01 14:31:07', '2025-08-01 14:44:38', NULL, NULL, 0, 'User', NULL, NULL),
('42fda752-5924-4176-bc7e-a48db8ed4e12', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 15:19:07', '2025-08-02 15:19:07', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 15:19:07', '2025-08-01 15:23:29', '2025-08-01 15:23:29', NULL, NULL, 0, 'User', NULL, NULL),
('4d06bf73-6c94-4ede-baa5-f24e06508613', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 14:31:40', '2025-08-02 14:31:40', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 14:31:40', '2025-08-01 14:45:26', '2025-08-01 14:54:08', NULL, NULL, 0, 'User', NULL, NULL),
('4f321baa-ed8a-423c-8261-1cb7846575f4', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:22:19', '2025-08-02 13:22:19', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 13:22:19', '2025-08-01 13:29:35', '2025-08-01 13:36:58', NULL, NULL, 0, 'User', NULL, NULL),
('541da564-7d2a-48be-b40b-372798e654e1', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:33:28', '2025-08-02 13:33:28', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 13:33:28', '2025-08-01 14:26:48', '2025-08-01 14:26:59', NULL, NULL, 0, 'User', NULL, NULL),
('68007fe8-763c-4b89-a25a-89e78ed98cc7', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:21:45', '2025-08-02 13:21:45', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 13:21:45', '2025-08-01 13:23:34', '2025-08-01 13:22:09', NULL, NULL, 0, 'User', NULL, NULL),
('9b89eac4-a197-4b72-a581-f316a6ff70ae', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 12:14:28', '2025-08-02 12:14:28', 'CHARIS', 'MISSIONS', 'VAGA-j27r-tngnxQAAAT', '2025-08-01 12:14:28', '2025-08-01 12:34:56', '2025-08-01 12:34:56', NULL, NULL, 0, 'User', NULL, NULL),
('a41a6a98-17d5-45e2-acf4-6ffa157e3253', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 12:14:42', '2025-08-02 12:14:42', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 12:14:42', '2025-08-01 12:34:51', '2025-08-01 12:34:51', NULL, NULL, 0, 'User', NULL, NULL),
('ba70e61a-071d-42eb-8618-2c0acebf740c', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 13:29:35', '2025-08-02 13:29:35', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 13:29:35', '2025-08-01 13:33:28', '2025-08-01 13:36:58', NULL, NULL, 0, 'User', NULL, NULL),
('cbd07dde-5aaf-47e3-be9d-164d9aaed2da', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', NULL, '2025-08-01 14:45:41', '2025-08-02 14:45:41', 'EMMANUEL AMOAKOH', 'AUDIT', NULL, '2025-08-01 14:45:41', '2025-08-01 15:19:07', '2025-08-01 15:23:23', NULL, NULL, 0, 'User', NULL, NULL),
('cc9aa47f-4a18-4e96-98ca-75568de3a01d', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 14:48:55', '2025-08-02 14:48:55', 'CHARIS', 'MISSIONS', 'lobUVuYpk-YkpY30AAAj', '2025-08-01 14:48:55', '2025-08-01 15:27:52', NULL, NULL, NULL, 1, 'User', NULL, NULL),
('e5e364ea-b430-48b4-92e6-4f99d7f72454', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', NULL, '2025-08-01 13:11:58', '2025-08-02 13:11:58', 'CHARIS', 'MISSIONS', 'aWmoirp4VKG6iWmsAAAT', '2025-08-01 13:11:58', '2025-08-01 13:29:07', '2025-08-01 13:29:07', NULL, NULL, 0, 'User', NULL, NULL);

-- Table: audit_logs
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE `audit_logs` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `user_name` varchar(255) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `action` varchar(255) NOT NULL,
  `description` text,
  `resource_type` varchar(50) NOT NULL,
  `resource_id` varchar(36) DEFAULT NULL,
  `details` text,
  `timestamp` datetime NOT NULL,
  `ip_address` varchar(50) DEFAULT NULL,
  `user_agent` text,
  `severity` varchar(50) DEFAULT 'INFO',
  PRIMARY KEY (`id`),
  KEY `idx_audit_logs_user_id` (`user_id`),
  KEY `idx_audit_logs_timestamp` (`timestamp`),
  KEY `idx_audit_logs_resource` (`resource_type`,`resource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: audit_logs
INSERT INTO `audit_logs` VALUES
('05213437-551c-40e2-9063-463542f67ba2', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 14:31:33', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('07fc17ee-58da-42d8-9cdd-eb1a20a21ac5', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:11:35', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('08ffc86a-3e3a-4b2c-a518-39cd97886258', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 15:23:29', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('0b5a484c-f48f-427e-a1be-a2ce8558e892', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 14:48:55', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('0cc96c03-d534-4390-b44c-6450719be84a', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 14:45:26', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('158a8292-daf3-46e6-9e8d-53e4d9d88923', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 13:11:58', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('36c91f79-526f-472e-9d0d-b3547304cbab', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:33:28', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('47cf3ed2-33b1-4fb2-9d7d-3ec62575d3e1', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 14:31:07', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('49082e63-7ed4-4d4e-a2dc-3d38fde6ce09', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:22:19', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('4d7625b1-ad70-41b7-91a0-96ff990b810f', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', 'AUDIT', 'LOGIN', 'SELORM logged in to AUDIT department', 'USER', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', '{}', '2025-08-01 15:23:49', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('65e72c06-4cfe-4444-99bc-f223a025eb62', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 14:26:48', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('75669716-ea35-4e5e-abad-2ef635fb156a', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 14:45:41', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('7ebace76-b8e2-4420-bd50-fc8e0f15a061', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 12:34:51', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('87ed37dd-b93c-4f88-8397-848433e3b689', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:21:32', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('8b357d68-5d4d-49c2-bcd0-afe6d896d58b', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGOUT', 'EMMANUEL AMOAKOH logged out from AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:22:09', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('9266b84b-0b5f-43ec-ac3a-f2249d21894c', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 12:34:56', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('9d780475-7384-4ea5-9c9f-d81ba32f3cd0', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:29:35', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('b31a20b7-33f4-4c37-9070-7a3480f66481', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:21:45', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('b4ebd83d-e5b8-4b1e-a5cc-04297f1378d3', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 13:29:07', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('b9d7847f-bd70-48f9-bea8-9888e76d1c59', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 15:19:07', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('c0862317-31c2-4f42-bb71-3d0d0da90712', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 14:31:40', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('ca3982ec-019a-4399-9d2e-ef1750bcc4bf', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGIN', 'CHARIS logged in to MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 12:14:28', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('d2e71cab-9039-4f9f-8888-50121bd474ba', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 12:14:42', '10.25.42.63', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('ef337844-ca7e-493c-82c8-46367ffd3aa9', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', 'LOGIN', 'EMMANUEL AMOAKOH logged in to AUDIT department', 'USER', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '{}', '2025-08-01 13:17:36', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('fbfc27c0-638f-4f2e-a1a7-d0ca197e7aaf', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', 'MISSIONS', 'LOGOUT', 'CHARIS logged out from MISSIONS department', 'USER', '7e3826ee-dfce-4c9a-9cca-251721fe0ca0', '{}', '2025-08-01 14:45:32', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO');

-- Table: audit_voucher_attachments
DROP TABLE IF EXISTS `audit_voucher_attachments`;
CREATE TABLE `audit_voucher_attachments` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `stored_filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `uploaded_by` varchar(36) NOT NULL,
  `uploaded_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_active` (`is_active`),
  KEY `idx_uploaded_at` (`uploaded_at`),
  CONSTRAINT `audit_voucher_attachments_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `audit_voucher_attachments_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Stores metadata for audit department voucher attachments';

-- Data for table: audit_voucher_attachments
INSERT INTO `audit_voucher_attachments` VALUES
('2f76525b-a930-484e-bf29-14242213cd68', '3bc2c7b4-451c-40f5-b34f-d15a61fb61c7', 'Tumu-2025 Training grant budget  (1).pdf', 'OFORI_ATTA__BUILDING_EXP.pdf', 'C:\Users\<USER>\Desktop\VMS-PRODUCTION\uploads\audit-attachments\2025\voucher-3bc2c7b4-451c-40f5-b34f-d15a61fb61c7\OFORI_ATTA__BUILDING_EXP.pdf', 379457, 'application/pdf', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', '2025-07-29 11:27:19', 1);

-- Table: batch_vouchers
DROP TABLE IF EXISTS `batch_vouchers`;
CREATE TABLE `batch_vouchers` (
  `batch_id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  PRIMARY KEY (`batch_id`,`voucher_id`),
  KEY `idx_batch_vouchers_batch_id` (`batch_id`),
  KEY `idx_batch_vouchers_voucher_id` (`voucher_id`),
  CONSTRAINT `batch_vouchers_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `voucher_batches` (`id`) ON DELETE CASCADE,
  CONSTRAINT `batch_vouchers_ibfk_2` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: batch_vouchers
INSERT INTO `batch_vouchers` VALUES
('85d46515-8ccf-4da9-a48c-b1a0152846e4', 'b30466b6-6636-4b0a-9ee3-675b238134af'),
('860608f2-dcff-4fef-bb47-11ab1229c549', '6ec477c1-16ff-4a74-876c-ccfa14e0d1f4'),
('8c261ce1-0e71-4d84-b1f2-129bf3099f54', '9f34493f-d604-4f6d-8a0e-8edba3c5f85f'),
('c01ad37e-0d98-49d4-813e-6d840f30ba4c', 'b30466b6-6636-4b0a-9ee3-675b238134af');

-- Table: batches
DROP TABLE IF EXISTS `batches`;
CREATE TABLE `batches` (
  `id` varchar(36) NOT NULL,
  `batch_number` varchar(50) NOT NULL,
  `department` varchar(100) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(50) DEFAULT 'PENDING',
  `voucher_count` int DEFAULT '0',
  `total_amount` decimal(15,2) DEFAULT '0.00',
  `dispatch_date` timestamp NULL DEFAULT NULL,
  `received_date` timestamp NULL DEFAULT NULL,
  `received_by` varchar(36) DEFAULT NULL,
  `notes` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: batches
INSERT INTO `batches` VALUES
('BATCH_1753477346146', 'BATCH_1753477346146', 'AUDIT', 'SAMUEL', '2025-07-25 21:02:26', 'PENDING', 1, '1000.00', NULL, NULL, NULL, NULL);

-- Table: blacklisted_voucher_ids
DROP TABLE IF EXISTS `blacklisted_voucher_ids`;
CREATE TABLE `blacklisted_voucher_ids` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(50) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `voucher_id` (`voucher_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: concurrent_users_monitor
DROP TABLE IF EXISTS `concurrent_users_monitor`;
CREATE TABLE `concurrent_users_monitor` (
  `id` int NOT NULL AUTO_INCREMENT,
  `department` varchar(50) NOT NULL,
  `active_users_count` int DEFAULT '0',
  `max_users_limit` int DEFAULT '10',
  `workstations_count` int DEFAULT '0',
  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `peak_usage_time` time DEFAULT NULL,
  `peak_users_count` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_department` (`department`),
  KEY `idx_concurrent_department` (`department`),
  KEY `idx_concurrent_updated` (`last_updated`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: concurrent_users_monitor
INSERT INTO `concurrent_users_monitor` VALUES
(1, 'AUDIT', 0, 10, 3, '2025-05-29 20:26:56', NULL, 0),
(2, 'FINANCE', 0, 10, 3, '2025-05-29 20:26:56', NULL, 0),
(3, 'MINISTRIES', 0, 8, 2, '2025-05-29 20:26:56', NULL, 0),
(4, 'PENSIONS', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(5, 'PENTMEDIA', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(6, 'MISSIONS', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(7, 'PENTSOS', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(8, 'SYSTEM ADMIN', 0, 5, 1, '2025-05-29 20:26:56', NULL, 0);

-- Table: departments
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments` (
  `id` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(10) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: departments
INSERT INTO `departments` VALUES
('4', 'HR', '', 'Human Resources', 1, '2025-07-10 15:13:26', '2025-07-10 15:13:26'),
('dept_admin', 'SYSTEM ADMIN', 'ADM', 'System Administration', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_audit', 'AUDIT', 'AUD', 'Audit Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_finance', 'FINANCE', 'FIN', 'Finance Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_ministries', 'MINISTRIES', 'MIN', 'Ministries Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_missions', 'MISSIONS', 'MIS', 'Missions Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_pensions', 'PENSIONS', 'PEN', 'Pensions Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_pentmedia', 'PENTMEDIA', 'PMD', 'Pentmedia Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_pentsos', 'PENTSOS', 'PSO', 'Pentsos Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06');

-- Table: lan_resource_locks
DROP TABLE IF EXISTS `lan_resource_locks`;
CREATE TABLE `lan_resource_locks` (
  `id` varchar(36) NOT NULL,
  `resource_type` enum('VOUCHER','BATCH','CASH_RECORD','USER') NOT NULL,
  `resource_id` varchar(100) NOT NULL,
  `locked_by_user_id` varchar(36) NOT NULL,
  `locked_by_session_id` varchar(255) NOT NULL,
  `workstation_id` varchar(100) DEFAULT NULL,
  `lock_type` enum('read','write','exclusive') DEFAULT 'write',
  `locked_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_lan_locks_user` (`locked_by_user_id`),
  KEY `idx_lan_locks_session` (`locked_by_session_id`),
  KEY `idx_lan_locks_expires` (`expires_at`),
  KEY `idx_lan_locks_active` (`is_active`),
  KEY `idx_lan_locks_resource` (`resource_type`,`resource_id`),
  CONSTRAINT `lan_resource_locks_ibfk_1` FOREIGN KEY (`locked_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `lan_resource_locks_ibfk_2` FOREIGN KEY (`locked_by_session_id`) REFERENCES `lan_user_sessions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: lan_security_events
DROP TABLE IF EXISTS `lan_security_events`;
CREATE TABLE `lan_security_events` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) DEFAULT NULL,
  `event_type` enum('LOGIN_SUCCESS','LOGIN_FAILED','LOGOUT','PASSWORD_CHANGE','MFA_ENABLED','MFA_DISABLED','ACCOUNT_LOCKED','ACCOUNT_UNLOCKED','SUSPICIOUS_ACTIVITY') NOT NULL,
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') DEFAULT 'MEDIUM',
  `description` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `workstation_id` varchar(100) DEFAULT NULL,
  `additional_data` json DEFAULT NULL,
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_lan_security_user_id` (`user_id`),
  KEY `idx_lan_security_type` (`event_type`),
  KEY `idx_lan_security_severity` (`severity`),
  KEY `idx_lan_security_timestamp` (`timestamp`),
  CONSTRAINT `lan_security_events_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: lan_user_sessions
DROP TABLE IF EXISTS `lan_user_sessions`;
CREATE TABLE `lan_user_sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `refresh_token` varchar(500) NOT NULL,
  `workstation_id` varchar(100) DEFAULT NULL,
  `workstation_name` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `mac_address` varchar(17) DEFAULT NULL,
  `user_agent` text,
  `department` varchar(50) DEFAULT NULL,
  `device_info` json DEFAULT NULL,
  `network_info` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL,
  `last_activity` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_heartbeat` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `session_type` enum('WORKSTATION','MOBILE','ADMIN') DEFAULT 'WORKSTATION',
  `concurrent_session_count` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_lan_sessions_user_id` (`user_id`),
  KEY `idx_lan_sessions_workstation` (`workstation_id`),
  KEY `idx_lan_sessions_department` (`department`),
  KEY `idx_lan_sessions_expires_at` (`expires_at`),
  KEY `idx_lan_sessions_active` (`is_active`),
  KEY `idx_lan_sessions_heartbeat` (`last_heartbeat`),
  CONSTRAINT `lan_user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: lan_workstations
DROP TABLE IF EXISTS `lan_workstations`;
CREATE TABLE `lan_workstations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `workstation_id` varchar(100) NOT NULL,
  `workstation_name` varchar(100) NOT NULL,
  `department` varchar(50) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `mac_address` varchar(17) DEFAULT NULL,
  `computer_name` varchar(100) DEFAULT NULL,
  `os_info` varchar(200) DEFAULT NULL,
  `last_seen` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `is_authorized` tinyint(1) DEFAULT '1',
  `max_concurrent_users` int DEFAULT '3',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workstation_id` (`workstation_id`),
  KEY `idx_workstations_department` (`department`),
  KEY `idx_workstations_active` (`is_active`),
  KEY `idx_workstations_ip` (`ip_address`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: lan_workstations
INSERT INTO `lan_workstations` VALUES
(1, 'AUDIT-WS-01', 'Audit Workstation 1', 'AUDIT', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(2, 'AUDIT-WS-02', 'Audit Workstation 2', 'AUDIT', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(3, 'AUDIT-WS-03', 'Audit Workstation 3', 'AUDIT', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(4, 'FINANCE-WS-01', 'Finance Workstation 1', 'FINANCE', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(5, 'FINANCE-WS-02', 'Finance Workstation 2', 'FINANCE', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(6, 'FINANCE-WS-03', 'Finance Workstation 3', 'FINANCE', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(7, 'MINISTRIES-WS-01', 'Ministries Workstation 1', 'MINISTRIES', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(8, 'MINISTRIES-WS-02', 'Ministries Workstation 2', 'MINISTRIES', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(9, 'PENSIONS-WS-01', 'Pensions Workstation 1', 'PENSIONS', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(10, 'PENTMEDIA-WS-01', 'Pentmedia Workstation 1', 'PENTMEDIA', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(11, 'MISSIONS-WS-01', 'Missions Workstation 1', 'MISSIONS', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(12, 'PENTSOS-WS-01', 'Pentsos Workstation 1', 'PENTSOS', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(13, 'ADMIN-WS-01', 'System Admin Workstation', 'SYSTEM ADMIN', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 5, '2025-05-29 20:26:56');

-- Table: migrations
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `applied_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_migration_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: notifications
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `timestamp` varchar(50) NOT NULL,
  `voucher_id` varchar(36) DEFAULT NULL,
  `batch_id` varchar(36) DEFAULT NULL,
  `type` varchar(50) NOT NULL,
  `from_audit` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_notifications_user_id` (`user_id`),
  KEY `idx_notifications_read` (`is_read`),
  KEY `idx_notifications_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: notifications
INSERT INTO `notifications` VALUES
('3a62f885-ba32-422f-b197-b2ed25eac145', 'AUDIT', 'Voucher MISAUG0001 received from Audit', 0, '2025-08-01 12:16:27', 'b30466b6-6636-4b0a-9ee3-675b238134af', NULL, 'VOUCHER_RECEIVED', 0),
('3b9186b0-8bcc-4bbf-8ac0-90a7636e4120', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'New batch received from MISSIONS', 1, '2025-08-01 13:13:06', NULL, '860608f2-dcff-4fef-bb47-11ab1229c549', 'NEW_BATCH', 0),
('6c527d3d-0033-4c70-a5b2-845e9b7b63d2', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'New batch received from MISSIONS', 1, '2025-08-01 12:15:16', NULL, '85d46515-8ccf-4da9-a48c-b1a0152846e4', 'NEW_BATCH', 0),
('75a7be1c-7228-44a6-a556-d88994cded5d', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'New batch received from MISSIONS', 1, '2025-08-01 15:24:37', NULL, '8c261ce1-0e71-4d84-b1f2-129bf3099f54', 'NEW_BATCH', 0),
('76be8eec-4988-4c42-ab74-98d92b7ca763', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'New batch received from MISSIONS', 1, '2025-08-01 15:24:37', NULL, '8c261ce1-0e71-4d84-b1f2-129bf3099f54', 'NEW_BATCH', 0),
('8b4a707a-6fe2-42f6-a98f-b1a6184fdfb7', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'New batch received from MISSIONS', 1, '2025-08-01 13:13:06', NULL, '860608f2-dcff-4fef-bb47-11ab1229c549', 'NEW_BATCH', 0),
('8fa7838b-159d-4d34-89a2-86727e6b697b', 'MISSIONS', 'Voucher MISAUG0004 certified by Audit', 0, '2025-08-01 13:13:11', '6ec477c1-16ff-4a74-876c-ccfa14e0d1f4', NULL, 'VOUCHER_CERTIFIED', 0),
('9e1b6ba3-2b6d-4257-97a0-2a3b87995e13', '8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'New batch received from MISSIONS', 1, '2025-08-01 12:15:16', NULL, '85d46515-8ccf-4da9-a48c-b1a0152846e4', 'NEW_BATCH', 0),
('c12023f1-a124-4146-a352-933c780b85ec', 'MISSIONS', 'Voucher MISAUG0001 certified by Audit', 0, '2025-08-01 12:15:35', 'b30466b6-6636-4b0a-9ee3-675b238134af', NULL, 'VOUCHER_CERTIFIED', 0),
('fe1b5f4d-640b-400f-a2dd-58578ee7bf70', 'MISSIONS', 'Voucher MISAUG0005 certified by Audit', 0, '2025-08-01 15:24:42', '9f34493f-d604-4f6d-8a0e-8edba3c5f85f', NULL, 'VOUCHER_CERTIFIED', 0);

-- Table: password_change_requests
DROP TABLE IF EXISTS `password_change_requests`;
CREATE TABLE `password_change_requests` (
  `id` varchar(50) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `user_name` varchar(100) NOT NULL,
  `user_department` varchar(50) NOT NULL,
  `new_password_hash` text NOT NULL,
  `status` enum('PENDING','APPROVED','REJECTED') DEFAULT 'PENDING',
  `requested_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `requested_by_ip` varchar(45) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `processed_by` varchar(100) DEFAULT NULL,
  `admin_notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_requested_at` (`requested_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: password_change_requests
INSERT INTO `password_change_requests` VALUES
('PWD_REQ_1753545774953', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'newpass123', 'REJECTED', '2025-07-26 16:02:54', '::1', '2025-07-26 16:12:15', 'ADMIN', 'Rejected by administrator', '2025-07-26 16:02:54', '2025-07-26 16:12:15'),
('PWD_REQ_1753547895718', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'newpassword456', 'REJECTED', '2025-07-26 16:38:15', '::1', '2025-07-26 16:40:33', 'ADMIN', 'Rejected by administrator', '2025-07-26 16:38:15', '2025-07-26 16:40:33'),
('PWD_REQ_1753547959991', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'newpassword456', 'REJECTED', '2025-07-26 16:39:20', '::1', '2025-07-26 16:40:30', 'ADMIN', 'Rejected by administrator', '2025-07-26 16:39:20', '2025-07-26 16:40:30'),
('PWD_REQ_1753548078067', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'james123', 'APPROVED', '2025-07-26 16:41:18', '::1', '2025-07-26 16:50:05', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-26 16:41:18', '2025-07-26 16:50:05'),
('PWD_REQ_1753548719134', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'james123', 'APPROVED', '2025-07-26 16:51:59', '::1', '2025-07-26 16:52:32', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-26 16:51:59', '2025-07-26 16:52:32'),
('PWD_REQ_1753712987545', 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', 'AUDIT', '123123', 'APPROVED', '2025-07-28 14:29:47', '10.25.42.61', '2025-07-28 14:31:34', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-28 14:29:47', '2025-07-28 14:31:34'),
('PWD_REQ_1753713012221', 'abe28dd4-00f4-4d23-80f6-246235a79c51', 'SAMMY MAWUKO', 'MINISTRIES', '123123', 'APPROVED', '2025-07-28 14:30:12', '10.25.42.61', '2025-07-28 14:31:32', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-28 14:30:12', '2025-07-28 14:31:32'),
('PWD_REQ_1753713049142', '97a5561d-13c5-4692-be66-5dddcc6819fc', 'JERRY JOHN', 'PENSIONS', '123123', 'APPROVED', '2025-07-28 14:30:49', '10.25.42.61', '2025-07-28 14:31:30', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-28 14:30:49', '2025-07-28 14:31:30'),
('PWD_REQ_1753713078682', '30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', 'FINANCE', '123123', 'APPROVED', '2025-07-28 14:31:18', '10.25.42.61', '2025-07-28 14:31:27', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-28 14:31:18', '2025-07-28 14:31:27'),
('PWD_REQ_1753802611249', '8627ce90-b753-4845-a467-7475fc7cf994', 'JAMES ', 'MINISTRIES', '123123', 'APPROVED', '2025-07-29 15:23:31', '10.25.41.192', '2025-07-29 15:23:40', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-29 15:23:31', '2025-07-29 15:23:40'),
('PWD_REQ_1753802753619', '8627ce90-b753-4845-a467-7475fc7cf994', 'JAMES ', 'MINISTRIES', '123456', 'APPROVED', '2025-07-29 15:25:53', '10.25.42.61', '2025-07-29 15:26:31', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-29 15:25:53', '2025-07-29 15:26:31');

-- Table: pending_registrations
DROP TABLE IF EXISTS `pending_registrations`;
CREATE TABLE `pending_registrations` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `department` varchar(50) NOT NULL,
  `date_requested` varchar(50) NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: pending_registrations
INSERT INTO `pending_registrations` VALUES
('120a94dc-b6e0-4e05-8636-2eaff2937f68', 'SAMMY MAWUKO', '123456789', 'MINISTRIES', '2025-07-28 11:24:13', 'approved'),
('313ecfce-ef95-4458-8256-0b01699e5bc2', 'TEST USER REALTIME', 'testpass123', 'FINANCE', '2025-07-28 11:06:15', 'rejected'),
('3343ae67-03cc-484c-a0d1-7289e02e26a1', 'WILLIAM AKUAMOAH', '123', 'AUDIT', '2025-07-16 09:15:55', 'approved'),
('396fcb4e-c87f-4143-8a76-2201f83e4a6b', 'JAMES ARTHUR', '123123', 'PENTMEDIA', '2025-07-30 20:06:02', 'approved'),
('3ec42744-9fe5-4327-9f6b-bc4fd3b41567', 'JAMES ', '123123', 'MINISTRIES', '2025-07-29 15:21:59', 'approved'),
('47ea70bf-54c3-43ec-a456-7a88e8efc98b', 'JERRY JOHN', '123', 'PENSIONS', '2025-07-28 07:29:57', 'approved'),
('578933af-2c54-4fab-a312-ef5861ed1ebd', 'GYAMPOH', '123123', 'PENTSOS', '2025-07-30 20:07:35', 'approved'),
('6b3e9871-aecf-4069-a71c-38097afaf33b', 'JERRY JOHN', '123', 'PENSIONS', '2025-07-27 22:05:42', 'approved'),
('88e0b4b8-bb89-45e8-a66e-d24b37d70690', 'JAMES NABEL', '123', 'FINANCE', '2025-07-16 09:13:59', 'approved'),
('a230930c-1d91-472f-a90a-36c4558986d5', 'CHARIS', '123123', 'MISSIONS', '2025-07-30 20:06:35', 'approved'),
('a2b6ca0b-225f-4122-ac97-88e69a3a4208', 'EMMANUEL AMOAKOH', '123456789', 'AUDIT', '2025-07-28 10:59:07', 'approved'),
('c53c5b75-fe20-4578-a946-4133191eb4dc', 'MR. FELIX AYISI', '123456789', 'FINANCE', '2025-07-28 11:25:52', 'approved'),
('dfd81ba0-8ff1-4cfc-b98b-dac6e647853a', 'REALTIME TEST USER', 'testpass123', 'FINANCE', '2025-07-28 11:20:54', 'rejected'),
('e3d422ce-460f-428a-9f9a-7254cfe2ebe9', 'SELORM', '123123', 'AUDIT', '2025-07-29 13:29:16', 'approved'),
('f2b8ff4e-56b7-443c-9d22-76196fc45aa5', 'JJ', '123456789', 'PENSIONS', '2025-07-28 11:08:45', 'approved');

-- Table: permissions
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `display_name` varchar(150) NOT NULL,
  `description` text,
  `resource` varchar(50) NOT NULL,
  `action` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: permissions
INSERT INTO `permissions` VALUES
(1, 'system.admin', 'System Administration', 'Full system administration access', 'system', 'admin', '2025-05-29 20:26:54'),
(2, 'system.config', 'System Configuration', 'Configure system settings', 'system', 'config', '2025-05-29 20:26:54'),
(3, 'users.create', 'Create Users', 'Create new user accounts', 'users', 'create', '2025-05-29 20:26:54'),
(4, 'users.view', 'View Users', 'View user information', 'users', 'view', '2025-05-29 20:26:54'),
(5, 'users.edit', 'Edit Users', 'Edit user information', 'users', 'edit', '2025-05-29 20:26:54'),
(6, 'users.delete', 'Delete Users', 'Delete user accounts', 'users', 'delete', '2025-05-29 20:26:54'),
(7, 'users.manage_roles', 'Manage User Roles', 'Assign and modify user roles', 'users', 'manage_roles', '2025-05-29 20:26:54'),
(8, 'audit.vouchers.view', 'View Audit Vouchers', 'View vouchers in audit', 'audit.vouchers', 'view', '2025-05-29 20:26:54'),
(9, 'audit.vouchers.process', 'Process Audit Vouchers', 'Process and approve vouchers', 'audit.vouchers', 'process', '2025-05-29 20:26:54'),
(10, 'audit.vouchers.reject', 'Reject Audit Vouchers', 'Reject vouchers with comments', 'audit.vouchers', 'reject', '2025-05-29 20:26:54'),
(11, 'audit.batches.receive', 'Receive Voucher Batches', 'Receive voucher batches from departments', 'audit.batches', 'receive', '2025-05-29 20:26:54'),
(12, 'audit.batches.dispatch', 'Dispatch Voucher Batches', 'Send vouchers back to departments', 'audit.batches', 'dispatch', '2025-05-29 20:26:54'),
(13, 'department.vouchers.create', 'Create Department Vouchers', 'Create new vouchers', 'department.vouchers', 'create', '2025-05-29 20:26:54'),
(14, 'department.vouchers.view', 'View Department Vouchers', 'View department vouchers', 'department.vouchers', 'view', '2025-05-29 20:26:54'),
(15, 'department.vouchers.edit', 'Edit Department Vouchers', 'Edit voucher information', 'department.vouchers', 'edit', '2025-05-29 20:26:54'),
(16, 'department.vouchers.send', 'Send Vouchers to Audit', 'Send vouchers to audit department', 'department.vouchers', 'send', '2025-05-29 20:26:54'),
(17, 'vouchers.create', 'Create Vouchers', 'Create new vouchers', 'vouchers', 'create', '2025-05-29 20:26:54'),
(18, 'vouchers.view', 'View Vouchers', 'View voucher information', 'vouchers', 'view', '2025-05-29 20:26:54'),
(19, 'vouchers.edit', 'Edit Vouchers', 'Edit voucher information', 'vouchers', 'edit', '2025-05-29 20:26:54'),
(20, 'vouchers.delete', 'Delete Vouchers', 'Delete vouchers', 'vouchers', 'delete', '2025-05-29 20:26:54'),
(21, 'reports.view', 'View Reports', 'View system reports', 'reports', 'view', '2025-05-29 20:26:54'),
(22, 'reports.create', 'Create Reports', 'Create custom reports', 'reports', 'create', '2025-05-29 20:26:54'),
(23, 'reports.export', 'Export Reports', 'Export reports to various formats', 'reports', 'export', '2025-05-29 20:26:54');

-- Table: provisional_cash_records
DROP TABLE IF EXISTS `provisional_cash_records`;
CREATE TABLE `provisional_cash_records` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `voucher_ref` varchar(50) NOT NULL,
  `claimant` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `main_amount` decimal(15,2) NOT NULL,
  `currency` enum('GHS','USD','GBP','EUR','CFA') NOT NULL,
  `amount_retired` decimal(15,2) DEFAULT NULL,
  `clearance_remark` enum('CLEARED','REFUNDED TO CHEST','DUE STAFF','RETURNED') DEFAULT NULL,
  `date_retired` varchar(50) DEFAULT NULL,
  `cleared_by` varchar(255) DEFAULT NULL,
  `comment` text,
  `date` varchar(50) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_provisional_cash_voucher_id` (`voucher_id`),
  KEY `idx_provisional_cash_date` (`date`),
  CONSTRAINT `provisional_cash_records_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: provisional_cash_records
INSERT INTO `provisional_cash_records` VALUES
('202198b8-f6e3-4855-b9e6-d7a442eb7335', 'b15ebfe1-6b2c-4cba-9ef4-60bd18b20a57', 'FINJUL0004', 'louis', 'test', '9000.00', 'GHS', NULL, NULL, NULL, NULL, NULL, '2025-07-28_14-09-31'),
('3318cc72-e34e-49e9-873e-7bacc3dacf74', '18e93e0c-d1c5-44e5-a753-fed07cd2d120', 'FINJUL0005', 'TOM', 'TESTER', '7800.00', 'GHS', NULL, NULL, NULL, NULL, NULL, '2025-07-28_15-22-12'),
('4f5bb8f3-defd-4096-9756-6f4ccd591e55', 'bd459552-c902-453b-9ce4-45bcd1404aec', 'FINJUL0002', 'YAW MENSAH', 'OVER TIME ', '15400.00', 'GHS', '1800.00', 'REFUNDED TO CHEST', '29-JUL-2025 12:50:21 PM', 'EMMANUEL AMOAKOH', '', '2025-07-29_10-46-59'),
('531d83ec-838b-4534-bd4c-85a3fc9b4545', '504e8a6d-e2f6-414e-80d3-d59fd5897619', 'FINJUL0002', 'KOOO NIMO', 'AIR TICKET ', '2500.00', 'USD', '9000.00', 'DUE STAFF', '29-JUL-2025 09:32:38 PM', 'EMMANUEL AMOAKOH', '', '2025-07-29_15-05-49'),
('55d128a0-214e-462e-bf3e-6d6a023263e2', '172f145a-9b9f-409a-be3f-8bcbd78bb0b3', 'FINJUL0001', 'KOFI YESU', 'MEDICAL BILLS ', '70000.00', 'GHS', NULL, NULL, NULL, NULL, NULL, '2025-07-29_10-07-00'),
('f335ca07-313a-4b76-91ef-082bcd59bc21', 'ec7b5f4e-d7f0-4f3a-9210-f6070aa3e817', 'FINJUL0008', 'OFORI ATTA ', 'WOMEN LACOST ', '47000.00', 'GHS', NULL, NULL, NULL, NULL, NULL, '2025-07-29_11-35-22');

-- Table: resource_locks
DROP TABLE IF EXISTS `resource_locks`;
CREATE TABLE `resource_locks` (
  `id` varchar(36) NOT NULL,
  `resource_type` varchar(50) NOT NULL,
  `resource_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `department` varchar(50) NOT NULL,
  `lock_time` datetime NOT NULL,
  `expiry_time` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_resource` (`resource_type`,`resource_id`),
  KEY `idx_resource_locks_type_id` (`resource_type`,`resource_id`),
  KEY `idx_resource_locks_user` (`user_id`),
  KEY `idx_resource_locks_expiry` (`expiry_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: role_permissions
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` int NOT NULL,
  `permission_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_role_permission` (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: roles
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `description` text,
  `permissions` json DEFAULT NULL,
  `is_system_role` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: roles
INSERT INTO `roles` VALUES
(1, 'SUPER_ADMIN', 'Super Administrator', 'Full system access with all permissions', *, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(2, 'AUDIT_MANAGER', 'Audit Manager', 'Full audit department management access', audit.*,users.view,reports.*, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(3, 'AUDIT_USER', 'Audit User', 'Standard audit user with voucher processing access', audit.vouchers.*,audit.batches.*,reports.view, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(4, 'DEPT_MANAGER', 'Department Manager', 'Department management access', department.*,vouchers.*,users.view, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(5, 'DEPT_USER', 'Department User', 'Standard department user access', vouchers.create,vouchers.view,vouchers.edit, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(6, 'VIEWER', 'Viewer', 'Read-only access to assigned resources', vouchers.view,reports.view, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54');

-- Table: system_settings
DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fiscal_year_start` enum('JAN','FEB','MAR','APR','MAY','JUN','JUL','AUG','SEP','OCT','NOV','DEC') DEFAULT 'JAN',
  `fiscal_year_end` enum('JAN','FEB','MAR','APR','MAY','JUN','JUL','AUG','SEP','OCT','NOV','DEC') DEFAULT 'DEC',
  `current_fiscal_year` int NOT NULL,
  `system_time` varchar(50) NOT NULL,
  `auto_backup_enabled` tinyint(1) DEFAULT '1',
  `session_timeout` int DEFAULT '30',
  `last_backup_date` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: system_settings
INSERT INTO `system_settings` VALUES
(1, 'JAN', 'DEC', 2025, '2025-07-12 19:48:10', 1, 30, '2025-08-01 14:49:08'),
(2, 'JAN', 'DEC', 2025, '2025-06-19 14:21:15', 1, 30, NULL);

-- Table: users
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','USER','viewer') NOT NULL,
  `department` varchar(50) NOT NULL,
  `date_created` datetime NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `email` varchar(255) DEFAULT NULL,
  `last_selected_year` int DEFAULT '2025',
  PRIMARY KEY (`id`),
  KEY `idx_users_department` (`department`),
  KEY `idx_users_role` (`role`),
  KEY `idx_users_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: users
INSERT INTO `users` VALUES
('0c3f28c0-a2a1-472f-a601-0e3a0f054d13', 'JAMES ARTHUR', '123123', 'USER', 'PENTMEDIA', '2025-07-30 20:08:06', '2025-07-31 12:36:08', 1, NULL, 2025),
('24f05c6c-a7d4-49c6-a1b1-3649e5f473cb', 'GYAMPOH', '123123', 'USER', 'PENTSOS', '2025-07-30 20:08:09', '2025-07-31 12:43:25', 1, NULL, 2025),
('30e94a63-99b2-41ed-a92e-8c4ff7c5dd84', 'MR. FELIX AYISI', '123123', 'USER', 'FINANCE', '2025-07-28 11:27:08', '2025-07-30 21:39:06', 1, NULL, 2025),
('7e3826ee-dfce-4c9a-9cca-251721fe0ca0', 'CHARIS', '123123', 'USER', 'MISSIONS', '2025-07-30 20:08:12', '2025-08-01 14:48:55', 1, NULL, 2025),
('8627ce90-b753-4845-a467-7475fc7cf994', 'JAMES ', '123456', 'USER', 'MINISTRIES', '2025-07-29 15:22:14', '2025-07-29 15:38:05', 1, NULL, 2025),
('8aa62d84-53de-4e65-8f7f-bb8e87e92276', 'SELORM', '123123', 'USER', 'AUDIT', '2025-07-29 13:29:27', '2025-08-01 15:23:49', 1, NULL, 2025),
('97a5561d-13c5-4692-be66-5dddcc6819fc', 'JERRY JOHN', '123123', 'USER', 'PENSIONS', '2025-07-28 08:32:38', '2025-07-30 21:14:19', 1, NULL, 2025),
('abe28dd4-00f4-4d23-80f6-246235a79c51', 'SAMMY MAWUKO', '123123', 'USER', 'MINISTRIES', '2025-07-28 11:24:27', '2025-07-30 21:11:46', 1, NULL, 2025),
('admin-default', 'SYSTEM ADMINISTRATOR', 'enter123', 'admin', 'SYSTEM ADMIN', '2025-06-19 14:21:15', '2025-07-30 20:15:56', 1, NULL, 2025),
('d97a34d9-af6d-4026-a5e8-00db2d7a6a98', 'EMMANUEL AMOAKOH', '123123', 'USER', 'AUDIT', '2025-07-28 10:59:21', '2025-08-01 15:19:07', 1, NULL, 2025);

-- Table: voucher_audit_log
DROP TABLE IF EXISTS `voucher_audit_log`;
CREATE TABLE `voucher_audit_log` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `voucher_id` varchar(50) NOT NULL,
  `action` varchar(100) NOT NULL,
  `performed_by` varchar(255) NOT NULL,
  `department` varchar(50) NOT NULL,
  `audit_reason` text,
  `changes_json` text,
  `original_values_json` text,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_performed_by` (`performed_by`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: voucher_audit_log
INSERT INTO `voucher_audit_log` VALUES
('2d7cbf3f-694e-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'TAX WRONGLY CALCULATED', '{"claimant":"DAN","description":"TEST","amount":"4500.00","preAuditedAmount":"5000.00","taxType":"GOODS 3%","taxAmount":250,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"DAN","description":"TEST","amount":"4500.00","preAuditedAmount":"5000.00","taxType":"SERVICE 7.5%","taxAmount":"600.00","preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-25 11:54:53'),
('4d12766e-6c6b-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'EMMANUEL AMOAKOH', 'AUDIT', 'at managers request', '{"claimant":"KOFI YESU","description":"MEDICAL BILLS ","amount":"70000.00","preAuditedAmount":15000,"taxType":"GOODS 3%","taxAmount":6000,"preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"USD"}', '{"claimant":"KOFI YESU","description":"MEDICAL BILLS ","amount":"70000.00","preAuditedAmount":"8000.00","taxType":"SERVICE 7.5%","taxAmount":"800.00","preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"GHS"}', '2025-07-29 11:00:55'),
('5f532392-6b00-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'TRYING THIS OUT', '{"claimant":"DUAH","description":"TEST","amount":"5600.00","preAuditedAmount":9000,"taxType":"GOODS 3%","taxAmount":900,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"DUAH","description":"TEST","amount":"5600.00","preAuditedAmount":"8000.00","taxType":"SERVICE 7.5%","taxAmount":"250.00","preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-27 15:42:59'),
('96aaa013-6c6a-11f0-8409-0a0027000013', 'FINJUL0002', 'POST_TRANSACTION_EDIT', 'EMMANUEL AMOAKOH', 'AUDIT', 'manager requested we do these chnages', '{"claimant":"YAW MENSAH","description":"MEDICALS","amount":"15400.00","preAuditedAmount":74999.99,"taxType":"SERVICE 7.5%","taxAmount":0,"preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"EUR"}', '{"claimant":"YAW MENSAH","description":"OVER TIME ","amount":"15400.00","preAuditedAmount":"16000.00","taxType":"SERVICE 7.5%","taxAmount":null,"preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"GHS"}', '2025-07-29 10:55:49'),
('a5cefe94-6a7f-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'JUST TRYING', '{"claimant":"TAN","description":"TEST","amount":"4500.00","preAuditedAmount":9500,"taxType":"SERVICE 7.5%","taxAmount":250,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"TAN","description":"test","amount":"4500.00","preAuditedAmount":"8000.00","taxType":null,"taxAmount":null,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-27 00:21:32'),
('b50df61f-6b37-11f0-8409-0a0027000013', 'PENJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'TELLING IT ALL TO THEM', '{"claimant":"JERRY","description":"TEST","amount":"8900.00","preAuditedAmount":15000,"taxType":"SERVICE 7.5%","taxAmount":1500,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"JERRY","description":"TEST","amount":"8900.00","preAuditedAmount":"90000.00","taxType":null,"taxAmount":null,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-27 22:19:05'),
('b8baf5ec-6a80-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'AM TRYING THIS OUT FOR TESTING', '{"claimant":"TAN","description":"TEST","amount":"4500.00","preAuditedAmount":15000,"taxType":"SERVICE 7.5%","taxAmount":500,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"TAN","description":"TEST","amount":"4500.00","preAuditedAmount":"9500.00","taxType":"SERVICE 7.5%","taxAmount":"250.00","preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-27 00:29:13'),
('c8554283-6e0b-11f0-af6f-a8b13b1317f2', 'PSOJUL0001', 'POST_TRANSACTION_EDIT', 'EMMANUEL AMOAKOH', 'AUDIT', 'UNDER FADS INSTRUCTIONS', '{"claimant":"GYAMPOH","description":"PURCHASES","amount":"9000.00","preAuditedAmount":800000,"taxType":"GOODS 3%","taxAmount":1000,"preAuditedBy":"SELORM","certifiedBy":"EMMANUEL AMOAKOH","currency":"CFA"}', '{"claimant":"GYAMPOH","description":"TEST","amount":"9000.00","preAuditedAmount":"700000.00","taxType":"SERVICE 7.5%","taxAmount":"900.00","preAuditedBy":"SELORM","certifiedBy":"EMMANUEL AMOAKOH","currency":"GHS"}', '2025-07-31 12:42:13'),
('d4b07245-6c6a-11f0-8409-0a0027000013', 'FINJUL0002', 'POST_TRANSACTION_EDIT', 'EMMANUEL AMOAKOH', 'AUDIT', 'AT MANAGERS REQUEST', '{"claimant":"YAW MENSAH","description":"MEDICALS","amount":"15400.00","preAuditedAmount":96000,"taxType":"GOODS 3%","taxAmount":500,"preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"EUR"}', '{"claimant":"YAW MENSAH","description":"MEDICALS","amount":"15400.00","preAuditedAmount":"74999.99","taxType":"SERVICE 7.5%","taxAmount":"0.00","preAuditedBy":"EMMANUEL AMOAKOH","certifiedBy":"EMMANUEL AMOAKOH","currency":"EUR"}', '2025-07-29 10:57:33'),
('f87a2892-694d-11f0-8409-0a0027000013', 'TEST-POST-EDIT-001', 'POST_TRANSACTION_EDIT', 'WILLIAM AKUAMOAH', 'AUDIT', 'Test post-transaction edit', '{"amount":1200,"description":"Updated description via post-transaction edit","claimant":"Updated Claimant Name"}', '{"amount":"1000.00","description":"Test voucher for post-transaction edit","claimant":"Test Claimant"}', '2025-07-25 11:53:25');

-- Table: voucher_batches
DROP TABLE IF EXISTS `voucher_batches`;
CREATE TABLE `voucher_batches` (
  `id` varchar(36) NOT NULL,
  `department` varchar(50) NOT NULL,
  `sent_by` varchar(255) NOT NULL,
  `sent_time` varchar(50) NOT NULL,
  `received` tinyint(1) DEFAULT '0',
  `from_audit` tinyint(1) DEFAULT '0',
  `voucher_ids` text,
  `contains_rejected_vouchers` tinyint(1) DEFAULT '0',
  `rejected_voucher_count` int DEFAULT '0',
  `contains_rejected_copies` tinyint(1) DEFAULT '0',
  `rejected_copy_count` int DEFAULT '0',
  `normal_voucher_count` int DEFAULT '0',
  `contains_resubmissions` tinyint(1) DEFAULT '0',
  `resubmission_count` int DEFAULT '0',
  `contains_returned_vouchers` tinyint(1) DEFAULT '0',
  `returned_voucher_count` int DEFAULT '0',
  `received_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_batches_department` (`department`),
  KEY `idx_voucher_batches_received` (`received`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: voucher_batches
INSERT INTO `voucher_batches` VALUES
('85d46515-8ccf-4da9-a48c-b1a0152846e4', 'MISSIONS', 'CHARIS', '2025-08-01 12:15:16', 1, 0, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'EMMANUEL AMOAKOH'),
('860608f2-dcff-4fef-bb47-11ab1229c549', 'MISSIONS', 'CHARIS', '2025-08-01 13:13:06', 1, 0, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'EMMANUEL AMOAKOH'),
('8c261ce1-0e71-4d84-b1f2-129bf3099f54', 'MISSIONS', 'CHARIS', '2025-08-01 15:24:37', 1, 0, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 'SELORM'),
('c01ad37e-0d98-49d4-813e-6d840f30ba4c', 'MISSIONS', 'EMMANUEL AMOAKOH', '2025-08-01 12:16:18', 1, 1, NULL, 0, 0, 0, 0, 0, 0, 0, 1, 1, 'CHARIS');

-- Table: voucher_complete_history
DROP TABLE IF EXISTS `voucher_complete_history`;
undefined;

-- Data for table: voucher_complete_history
INSERT INTO `voucher_complete_history` VALUES
('MISAUG0001-MISSIONS-RETURN-COPY', 'ADDY SABAH', 'VOUCHER RETURNED', 'MISSIONS', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('MISAUG0004', 'THOMAS', 'AUDIT: PROCESSING', 'AUDIT', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('MISAUG0001-RETURN-COPY', 'ADDY SABAH', 'VOUCHER RETURNED', 'AUDIT', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('MISAUG0005', 'louis', 'AUDIT: PROCESSING', 'AUDIT', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0),
('MISAUG0001', 'ADDY SABAH', 'VOUCHER RETURNED', 'MISSIONS', 0, 0, 0, NULL, NULL, NULL, NULL, 0, NULL, NULL, 'NORMAL', 0);

-- Table: voucher_corrections
DROP TABLE IF EXISTS `voucher_corrections`;
CREATE TABLE `voucher_corrections` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `voucher_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `corrected_by` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `correction_time` datetime NOT NULL,
  `field_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `old_value` text COLLATE utf8mb4_unicode_ci,
  `new_value` text COLLATE utf8mb4_unicode_ci,
  `reason` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_corrected_by` (`corrected_by`),
  KEY `idx_correction_time` (`correction_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: voucher_department_visibility
DROP TABLE IF EXISTS `voucher_department_visibility`;
CREATE TABLE `voucher_department_visibility` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `department` varchar(50) NOT NULL,
  `visibility_reason` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_voucher_dept` (`voucher_id`,`department`),
  KEY `idx_voucher_department_visibility_voucher_id` (`voucher_id`),
  KEY `idx_voucher_department_visibility_department` (`department`),
  KEY `idx_voucher_department_visibility_created_at` (`created_at`),
  CONSTRAINT `voucher_department_visibility_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: voucher_logs
DROP TABLE IF EXISTS `voucher_logs`;
CREATE TABLE `voucher_logs` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `from_status` varchar(50) NOT NULL,
  `to_status` varchar(50) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `comment` text,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_voucher_logs_voucher_id` (`voucher_id`),
  KEY `idx_voucher_logs_created_at` (`created_at`),
  CONSTRAINT `voucher_logs_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `voucher_logs_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: voucher_relationships
DROP TABLE IF EXISTS `voucher_relationships`;
CREATE TABLE `voucher_relationships` (
  `id` int NOT NULL AUTO_INCREMENT,
  `original_voucher_id` varchar(255) NOT NULL,
  `related_voucher_id` varchar(255) NOT NULL,
  `relationship_type` enum('REJECTION_COPY','RESUBMISSION','CORRECTION') NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_original_voucher` (`original_voucher_id`),
  KEY `idx_related_voucher` (`related_voucher_id`),
  KEY `idx_relationship_type` (`relationship_type`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: voucher_sequences
DROP TABLE IF EXISTS `voucher_sequences`;
CREATE TABLE `voucher_sequences` (
  `department` varchar(50) NOT NULL,
  `month_year` varchar(10) NOT NULL,
  `next_number` int NOT NULL DEFAULT '1',
  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`department`),
  UNIQUE KEY `unique_dept_month` (`department`,`month_year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: vouchers
DROP TABLE IF EXISTS `vouchers`;
CREATE TABLE `vouchers` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(50) NOT NULL,
  `date` varchar(50) NOT NULL,
  `claimant` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `currency` enum('GHS','USD','GBP','EUR','CFA') NOT NULL,
  `department` varchar(50) NOT NULL,
  `dispatched_by` varchar(255) DEFAULT NULL,
  `dispatch_time` varchar(50) DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `sent_to_audit` tinyint(1) DEFAULT '0',
  `created_by` varchar(255) NOT NULL,
  `batch_id` varchar(36) DEFAULT NULL,
  `received_by` varchar(255) DEFAULT NULL,
  `receipt_time` varchar(50) DEFAULT NULL,
  `comment` text,
  `tax_type` varchar(50) DEFAULT NULL,
  `tax_details` text,
  `tax_amount` decimal(15,2) DEFAULT NULL,
  `pre_audited_amount` decimal(15,2) DEFAULT NULL,
  `pre_audited_by` varchar(255) DEFAULT NULL,
  `certified_by` varchar(255) DEFAULT NULL,
  `audit_dispatch_time` varchar(50) DEFAULT NULL,
  `audit_dispatched_by` varchar(255) DEFAULT NULL,
  `dispatch_to_on_department` tinyint(1) DEFAULT '0',
  `post_provisional_cash` tinyint(1) DEFAULT '0',
  `dispatched` tinyint(1) DEFAULT '0',
  `dispatch_to_audit_by` varchar(255) DEFAULT NULL,
  `is_returned` tinyint(1) DEFAULT '0',
  `return_comment` text,
  `return_time` varchar(50) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT '0',
  `deletion_time` varchar(50) DEFAULT NULL,
  `rejection_time` varchar(50) DEFAULT NULL,
  `department_receipt_time` varchar(50) DEFAULT NULL,
  `department_received_by` varchar(255) DEFAULT NULL,
  `department_rejected` tinyint(1) DEFAULT '0',
  `rejected_by` varchar(255) DEFAULT NULL,
  `pending_return` tinyint(1) DEFAULT '0',
  `return_initiated_time` varchar(50) DEFAULT NULL,
  `reference_id` varchar(50) DEFAULT NULL,
  `idempotency_key` varchar(255) DEFAULT NULL,
  `flags` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `original_department` varchar(50) DEFAULT NULL,
  `received_by_audit` tinyint(1) DEFAULT '0',
  `work_started` tinyint(1) DEFAULT '0',
  `batch_processed` tinyint(1) DEFAULT '0',
  `batch_processed_time` timestamp NULL DEFAULT NULL,
  `rejection_type` varchar(50) DEFAULT NULL,
  `parent_voucher_id` varchar(255) DEFAULT NULL,
  `version` int NOT NULL DEFAULT '1',
  `is_rejection_copy` tinyint(1) DEFAULT '0',
  `rejection_workflow_stage` enum('INITIAL','REJECTED_DUAL','DISPATCHED','RECEIVED_BY_FINANCE','RESUBMITTED','RESUBMITTED_CERTIFIED','PENDING_DISPATCH','FINANCE_PERMANENT','DISPATCHED_TO_FINANCE') DEFAULT 'INITIAL',
  `correction_count` int DEFAULT '0',
  `last_corrected_by` varchar(255) DEFAULT NULL,
  `last_correction_time` datetime DEFAULT NULL,
  `correction_reason` text,
  `is_corrected` tinyint(1) DEFAULT '0',
  `resubmission_count` int DEFAULT '0',
  `original_rejection_date` datetime DEFAULT NULL COMMENT 'Date when this voucher was first rejected',
  `resubmission_history` json DEFAULT NULL COMMENT 'History of resubmissions with dates and reasons',
  `resubmission_badge` varchar(100) DEFAULT NULL COMMENT 'Badge text to display for resubmitted vouchers',
  `original_rejection_reason` text,
  `resubmission_status` varchar(255) DEFAULT NULL,
  `original_rejected_by` varchar(255) DEFAULT NULL,
  `last_resubmitted_by` varchar(255) DEFAULT NULL,
  `last_resubmission_date` datetime DEFAULT NULL,
  `resubmission_comment` text,
  `is_resubmitted_voucher` tinyint(1) DEFAULT '0',
  `workflow_state` varchar(100) DEFAULT NULL,
  `badge_type` varchar(20) DEFAULT 'NONE',
  `is_copy` tinyint(1) DEFAULT '0',
  `workflow_history` json DEFAULT NULL,
  `rejection_audit_trail` json DEFAULT NULL,
  `resubmission_audit_trail` json DEFAULT NULL,
  `is_rejected` tinyint(1) DEFAULT '0',
  `original_voucher_id` varchar(255) DEFAULT NULL,
  `voucher_type` enum('ORIGINAL','COPY') DEFAULT 'ORIGINAL',
  `is_returned_copy` tinyint(1) DEFAULT '0',
  `returned_by` varchar(255) DEFAULT NULL,
  `is_resubmitted` tinyint(1) DEFAULT '0',
  `is_resubmission` tinyint(1) DEFAULT '0',
  `finance_received` tinyint(1) DEFAULT '0' COMMENT 'Track if Finance has received the voucher from Audit',
  `resubmission_certified_visible_to_finance` tinyint(1) DEFAULT '0',
  `resubmission_tracking_visible_to_audit` tinyint(1) DEFAULT '0',
  `badge_persistence_flags` json DEFAULT NULL,
  `is_returned_voucher` tinyint(1) DEFAULT '0',
  `return_count` int DEFAULT '0',
  `last_return_date` datetime DEFAULT NULL,
  `return_certified_visible_to_finance` tinyint(1) DEFAULT '0',
  `return_audit_visible` tinyint(1) DEFAULT '0',
  `original_return_reason` text,
  `original_returned_by` varchar(255) DEFAULT NULL,
  `last_edit_reason` text,
  `last_edited_by` varchar(255) DEFAULT NULL,
  `last_edit_time` datetime DEFAULT NULL,
  `is_on_hold` tinyint(1) DEFAULT '0' COMMENT 'Whether the voucher is currently on hold',
  `hold_comment` text COMMENT 'Reason why the voucher is on hold',
  `hold_by` varchar(255) DEFAULT NULL COMMENT 'User who put the voucher on hold',
  `hold_time` timestamp NULL DEFAULT NULL COMMENT 'When the voucher was put on hold',
  PRIMARY KEY (`id`),
  UNIQUE KEY `voucher_id` (`voucher_id`),
  KEY `idx_idempotency` (`created_by`,`department`,`idempotency_key`),
  KEY `idx_vouchers_rejection_type` (`rejection_type`),
  KEY `idx_vouchers_parent_voucher_id` (`parent_voucher_id`),
  KEY `idx_vouchers_rejection_copy` (`is_rejection_copy`),
  KEY `idx_vouchers_workflow_stage` (`rejection_workflow_stage`),
  KEY `idx_vouchers_dept_status_rejection` (`department`,`status`,`rejection_type`),
  KEY `idx_vouchers_orig_dept_status` (`original_department`,`status`),
  KEY `idx_vouchers_workflow_stage_dept` (`rejection_workflow_stage`,`department`),
  KEY `idx_vouchers_correction_count` (`correction_count`),
  KEY `idx_vouchers_is_corrected` (`is_corrected`),
  KEY `idx_vouchers_last_correction_time` (`last_correction_time`),
  KEY `idx_vouchers_resubmission_count` (`resubmission_count`),
  KEY `idx_vouchers_original_rejection_date` (`original_rejection_date`),
  KEY `idx_vouchers_workflow_state` (`workflow_state`),
  KEY `idx_vouchers_parent_id` (`parent_voucher_id`),
  KEY `idx_vouchers_workflow_department` (`workflow_state`,`department`),
  KEY `idx_vouchers_workflow_original_dept` (`workflow_state`,`original_department`),
  KEY `idx_vouchers_badge_type` (`badge_type`),
  KEY `idx_vouchers_is_copy` (`is_copy`),
  KEY `idx_vouchers_version` (`version`),
  KEY `idx_vouchers_is_resubmitted` (`is_resubmitted`),
  KEY `idx_vouchers_resubmission_visibility` (`resubmission_certified_visible_to_finance`,`resubmission_tracking_visible_to_audit`),
  KEY `idx_vouchers_department` (`department`),
  KEY `idx_vouchers_status` (`status`),
  KEY `idx_vouchers_created_at` (`created_at`),
  KEY `idx_vouchers_dept_status` (`department`,`status`),
  KEY `idx_vouchers_dept_status_created` (`department`,`status`,`created_at`),
  KEY `idx_vouchers_workflow_dept` (`workflow_state`,`department`),
  KEY `idx_vouchers_hold_status` (`is_on_hold`,`department`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Vouchers table with hold functionality for audit workflow management';

-- Data for table: vouchers
INSERT INTO `vouchers` VALUES
('28a000ca-7661-478f-9f66-842486c6e647', 'MISAUG0001-MISSIONS-RETURN-COPY', 'AUGUST 01, 2025 AT 12:15 PM', 'ADDY SABAH', 'MISSIONS RETURN COPY: TRAVEL', '8000.00', 'GHS', 'MISSIONS', NULL, NULL, 'VOUCHER RETURNED', 1, 'CHARIS', NULL, NULL, NULL, 'TESTING THE SYSTEM', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, '2025-08-01 12:16:18', 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2025-08-01 12:15:07', 'MISSIONS', 1, 0, 0, NULL, NULL, 'b30466b6-6636-4b0a-9ee3-675b238134af', 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'MISSIONS_RETURNED_COPY', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 1, 'EMMANUEL AMOAKOH', 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('6ec477c1-16ff-4a74-876c-ccfa14e0d1f4', 'MISAUG0004', 'AUGUST 01, 2025 AT 01:12 PM', 'THOMAS', 'TEST', '5600.00', 'GHS', 'AUDIT', '', '2025-08-01 13:13:06', 'AUDIT: PROCESSING', 1, 'CHARIS', '860608f2-dcff-4fef-bb47-11ab1229c549', 'EMMANUEL AMOAKOH', '2025-08-01 13:13:11', 'null', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 'CHARIS', 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'MISSIONS-1754053980059-4zcps7y4w', [object Object], '2025-08-01 13:12:58', 'AUDIT', 1, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'AUDIT_NEW', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 1, 'TESTING SYSTEM', 'EMMANUEL AMOAKOH', '2025-08-01 15:19:28'),
('85e06789-7a3a-47ae-a83b-2a0cdfa2ec31', 'MISAUG0001-RETURN-COPY', 'AUGUST 01, 2025 AT 12:15 PM', 'ADDY SABAH', 'RETURN COPY: TRAVEL', '8000.00', 'GHS', 'AUDIT', NULL, NULL, 'VOUCHER RETURNED', 1, 'CHARIS', NULL, NULL, NULL, 'TESTING THE SYSTEM', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, NULL, 0, NULL, '2025-08-01 12:16:18', 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, '2025-08-01 12:15:07', 'MISSIONS', 1, 0, 0, NULL, NULL, 'b30466b6-6636-4b0a-9ee3-675b238134af', 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'AUDIT_RETURNED_COPY', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 1, 'EMMANUEL AMOAKOH', 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL),
('9f34493f-d604-4f6d-8a0e-8edba3c5f85f', 'MISAUG0005', 'AUGUST 01, 2025 AT 03:24 PM', 'louis', 'TEST', '566666.00', 'GHS', 'AUDIT', '', '2025-08-01 15:24:37', 'AUDIT: PROCESSING', 1, 'CHARIS', '8c261ce1-0e71-4d84-b1f2-129bf3099f54', 'SELORM', '2025-08-01 15:24:42', 'null', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 'CHARIS', 0, NULL, NULL, 0, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, 'MISSIONS-1754061873894-2q0um0g26', [object Object], '2025-08-01 15:24:32', 'AUDIT', 1, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'AUDIT_NEW', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, NULL, 0, 0, 0, 0, 0, NULL, 0, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 1, 'TESTING THE SYSTEM', 'SELORM', '2025-08-01 15:25:02'),
('b30466b6-6636-4b0a-9ee3-675b238134af', 'MISAUG0001', 'AUGUST 01, 2025 AT 12:15 PM', 'ADDY SABAH', 'TRAVEL', '8000.00', 'GHS', 'MISSIONS', '', '2025-08-01 12:15:16', 'VOUCHER RETURNED', 1, 'CHARIS', 'c01ad37e-0d98-49d4-813e-6d840f30ba4c', 'CHARIS', '2025-08-01 12:16:27', 'TESTING THE SYSTEM', NULL, NULL, NULL, NULL, NULL, NULL, '2025-08-01 12:16:18', 'EMMANUEL AMOAKOH', 0, 0, 0, 'CHARIS', 0, NULL, '2025-08-01 12:16:18', 0, NULL, NULL, '2025-08-01 12:16:27', 'CHARIS', 0, NULL, 0, NULL, NULL, 'MISSIONS-1754050507787-znq008gfs', [object Object], '2025-08-01 12:15:07', 'MISSIONS', 1, 0, 0, NULL, NULL, NULL, 1, 0, 'INITIAL', 0, NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 'FINANCE_RETURNED', 'NONE', 0, NULL, NULL, NULL, 0, NULL, 'ORIGINAL', 0, 'EMMANUEL AMOAKOH', 0, 0, 0, 0, 0, NULL, 1, 1, '2025-08-01 12:16:18', 0, 0, 'TESTING THE SYSTEM', 'EMMANUEL AMOAKOH', NULL, NULL, NULL, 0, NULL, NULL, NULL);

-- Table: workflow_audit_log
DROP TABLE IF EXISTS `workflow_audit_log`;
CREATE TABLE `workflow_audit_log` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `from_state` varchar(50) NOT NULL,
  `to_state` varchar(50) NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `copy_id` varchar(36) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_user_id` (`user_id`),
  KEY `copy_id` (`copy_id`),
  CONSTRAINT `workflow_audit_log_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `workflow_audit_log_ibfk_2` FOREIGN KEY (`copy_id`) REFERENCES `vouchers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: workflow_transitions
DROP TABLE IF EXISTS `workflow_transitions`;
CREATE TABLE `workflow_transitions` (
  `id` varchar(36) NOT NULL,
  `from_state` varchar(50) NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `to_state` varchar(50) NOT NULL,
  `requires_copy` tinyint(1) DEFAULT '0',
  `copy_state` varchar(50) DEFAULT NULL,
  `badge_type` varchar(20) DEFAULT 'NONE',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transition` (`from_state`,`event_type`),
  KEY `idx_from_state` (`from_state`),
  KEY `idx_event_type` (`event_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

SET FOREIGN_KEY_CHECKS = 1;
