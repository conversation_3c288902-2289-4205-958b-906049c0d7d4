-- VMS Production Database Backup (PENDRIVE)
-- Generated: 2025-07-27T14:29:23.518Z
-- Database: vms_production
-- Year: 2025
-- Type: End-of-Day Backup

SET FOREIGN_KEY_CHECKS = 0;

-- Table: active_sessions
DROP TABLE IF EXISTS `active_sessions`;
CREATE TABLE `active_sessions` (
  `id` varchar(100) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `token` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` datetime DEFAULT ((now() + interval 24 hour)),
  `user_name` varchar(255) DEFAULT NULL,
  `department` varchar(50) DEFAULT NULL,
  `socket_id` varchar(100) DEFAULT NULL,
  `session_start` datetime NOT NULL,
  `last_activity` datetime NOT NULL,
  `session_end` datetime DEFAULT NULL,
  `client_ip` varchar(50) DEFAULT NULL,
  `user_agent` text,
  `is_active` tinyint(1) DEFAULT '1',
  `role` varchar(50) DEFAULT 'User',
  `selected_year` int DEFAULT NULL,
  `selected_database` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `is_active` (`is_active`),
  KEY `department` (`department`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: active_sessions
INSERT INTO `active_sessions` VALUES
('0429f1b5-2c74-4886-84b4-09380c7c4e7b', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-27 00:08:54', '2025-07-28 00:08:54', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-27 00:08:54', '2025-07-27 00:09:26', '2025-07-27 00:09:26', NULL, NULL, 0, 'User', NULL, NULL),
('04dd71e1-343d-49d3-8782-744eab6e29c0', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 22:51:32', '2025-07-27 22:51:32', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 22:51:32', '2025-07-26 22:52:19', '2025-07-26 22:52:19', NULL, NULL, 0, 'User', NULL, NULL),
('057e68f6-47fd-4811-b062-be22e1bdde6f', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 17:26:50', '2025-07-27 17:26:50', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 17:26:50', '2025-07-26 17:26:50', '2025-07-26 17:45:29', NULL, NULL, 0, 'User', NULL, NULL),
('05c8f0da-8a93-4585-a5c5-2f3742313e9f', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 22:01:24', '2025-07-27 22:01:24', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 22:01:24', '2025-07-26 22:06:46', '2025-07-26 22:06:46', NULL, NULL, 0, 'User', NULL, NULL),
('06485224-a829-47c2-8a30-0d6494e4c911', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 19:43:36', '2025-07-27 19:43:36', 'FELIX AYISI', 'FINANCE', 'Ads4MY-0cxEAAXfrAAAE', '2025-07-26 19:43:36', '2025-07-26 19:43:55', '2025-07-26 19:43:55', NULL, NULL, 0, 'User', NULL, NULL),
('09748cc9-d0fa-4914-9ad6-baf827d2957d', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 21:14:18', '2025-07-27 21:14:18', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 21:14:18', '2025-07-26 21:15:12', '2025-07-26 21:15:12', NULL, NULL, 0, 'User', NULL, NULL),
('0b3d2440-9afe-4cab-acb0-b2090a672cc9', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-27 00:19:08', '2025-07-28 00:19:08', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-27 00:19:08', '2025-07-27 00:19:25', '2025-07-27 00:19:25', NULL, NULL, 0, 'User', NULL, NULL),
('0e323131-c56f-4560-9c8b-591408b5715e', 'admin-default', NULL, '2025-07-26 15:08:05', '2025-07-27 15:08:05', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 15:08:05', '2025-07-26 15:08:59', '2025-07-26 15:11:45', NULL, NULL, 0, 'User', NULL, NULL),
('0e582379-91e4-4517-81f3-e6d382071bf3', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:14:30', '2025-07-27 22:14:30', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:14:30', '2025-07-26 22:14:47', '2025-07-26 22:14:47', NULL, NULL, 0, 'User', NULL, NULL),
('1181ed56-ea64-43f3-b29d-953a23fbecca', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-27 00:07:18', '2025-07-28 00:07:18', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-27 00:07:18', '2025-07-27 00:07:44', '2025-07-27 00:07:44', NULL, NULL, 0, 'User', NULL, NULL),
('159c4fc2-0162-431f-90d0-12babc162ca1', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 20:58:31', '2025-07-27 20:58:31', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 20:58:31', '2025-07-26 21:01:05', '2025-07-26 20:59:40', NULL, NULL, 0, 'User', NULL, NULL),
('1918648b-89a3-4fd3-badb-557f3e39a351', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 19:21:55', '2025-07-27 19:21:55', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 19:21:55', '2025-07-26 19:40:12', '2025-07-26 19:41:30', NULL, NULL, 0, 'User', NULL, NULL),
('1a9851a1-c8eb-446a-b1d8-5bda67d08951', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 20:17:15', '2025-07-27 20:17:15', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 20:17:15', '2025-07-26 20:25:45', '2025-07-26 20:24:20', NULL, NULL, 0, 'User', NULL, NULL),
('1ab1ccd9-5fe2-49be-a80d-ce201e04ad5f', 'admin-default', NULL, '2025-07-26 16:04:00', '2025-07-27 16:04:00', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 16:04:00', '2025-07-26 16:06:56', '2025-07-26 16:05:31', NULL, NULL, 0, 'User', NULL, NULL),
('203d99d1-ab14-42a3-88c2-4279a6249d48', '910337af-c186-4026-962d-628a3fe1621c', NULL, '2025-07-26 22:57:59', '2025-07-27 22:57:59', 'WILLIAM AKUAMOAH', 'AUDIT', NULL, '2025-07-26 22:57:59', '2025-07-26 22:58:05', '2025-07-26 22:58:05', NULL, NULL, 0, 'User', NULL, NULL),
('205351ca-ae89-44b9-80f6-2948e8a83b30', 'admin-default', NULL, '2025-07-26 16:50:05', '2025-07-27 16:50:05', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 16:50:05', '2025-07-26 16:50:05', '2025-07-26 17:55:29', NULL, NULL, 0, 'User', NULL, NULL),
('21ed8225-6203-41c3-81a0-a6837b12c020', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 17:47:41', '2025-07-27 17:47:41', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 17:47:41', '2025-07-26 18:02:15', '2025-07-26 18:00:50', NULL, NULL, 0, 'User', NULL, NULL),
('23b8b6ec-e421-4c7c-8fb2-cfe532042fa4', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 23:36:37', '2025-07-27 23:36:37', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 23:36:37', '2025-07-26 23:38:28', '2025-07-26 23:37:03', NULL, NULL, 0, 'User', NULL, NULL),
('24e5f3bb-9695-49d3-8cc6-5765a1102032', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-27 00:07:51', '2025-07-28 00:07:51', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-27 00:07:51', '2025-07-27 00:10:14', '2025-07-27 00:08:49', NULL, NULL, 0, 'User', NULL, NULL),
('27727f8d-02bf-4a69-9446-b810900ed266', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-27 00:23:59', '2025-07-28 00:23:59', 'FELIX AYISI', 'FINANCE', 'xNUiScy--c8I_jFKAAAf', '2025-07-27 00:23:59', '2025-07-27 00:25:59', '2025-07-27 04:22:55', NULL, NULL, 0, 'User', NULL, NULL),
('2c411978-efdb-4e63-94e8-d02b9cadf7dd', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:49:22', '2025-07-27 22:49:22', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:49:22', '2025-07-26 22:51:26', '2025-07-26 22:51:26', NULL, NULL, 0, 'User', NULL, NULL),
('2c4a9383-bb06-4afc-99f4-998cb5d75937', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 20:24:26', '2025-07-27 20:24:26', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 20:24:26', '2025-07-26 20:25:18', '2025-07-26 20:25:18', NULL, NULL, 0, 'User', NULL, NULL),
('2ceec8de-bce6-4ca6-a1e6-4522234438b2', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:07:49', '2025-07-27 22:07:49', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:07:49', '2025-07-26 22:08:28', '2025-07-26 22:08:28', NULL, NULL, 0, 'User', NULL, NULL),
('2d2ba4f6-41d6-48e8-8a83-fae5a2bcb048', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 18:32:30', '2025-07-27 18:32:30', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 18:32:30', '2025-07-26 18:41:25', '2025-07-26 18:45:29', NULL, NULL, 0, 'User', NULL, NULL),
('2dbc001b-45d5-4ba6-9f9f-e250098b6e6a', 'admin-default', NULL, '2025-07-26 15:50:30', '2025-07-27 15:50:30', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 15:50:30', '2025-07-26 15:50:37', '2025-07-26 15:50:37', NULL, NULL, 0, 'User', NULL, NULL),
('2f63ba75-783f-4973-be5f-138428662fdc', 'admin-default', NULL, '2025-07-26 15:33:38', '2025-07-27 15:33:38', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 15:33:38', '2025-07-26 16:15:43', '2025-07-26 16:19:22', NULL, NULL, 0, 'User', NULL, NULL),
('2ff7b2ee-ed0c-4b87-8f9e-31c1e3fd0cf8', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 22:47:50', '2025-07-27 22:47:50', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 22:47:50', '2025-07-26 22:50:06', '2025-07-26 22:48:41', NULL, NULL, 0, 'User', NULL, NULL),
('3099373c-a40d-447a-90a4-f5a40c1b2a29', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 19:59:06', '2025-07-27 19:59:06', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 19:59:06', '2025-07-26 20:08:23', '2025-07-26 20:08:23', NULL, NULL, 0, 'User', NULL, NULL),
('3225fbaa-ff9c-47f8-acb1-fc17c02b643f', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 21:41:47', '2025-07-27 21:41:47', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 21:41:47', '2025-07-26 21:42:30', '2025-07-26 21:42:30', NULL, NULL, 0, 'User', NULL, NULL),
('3588fc15-42f9-4c20-9028-ea61cb3ab862', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 20:31:10', '2025-07-27 20:31:10', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 20:31:10', '2025-07-26 20:43:30', '2025-07-26 20:43:30', NULL, NULL, 0, 'User', NULL, NULL),
('37ae77ec-d631-4001-ae30-70d3be6e508a', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-27 00:09:32', '2025-07-28 00:09:32', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-27 00:09:32', '2025-07-27 00:11:17', '2025-07-27 00:09:53', NULL, NULL, 0, 'User', NULL, NULL),
('3c66025b-1c8d-4510-a04a-70c039d8e2a0', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 20:53:29', '2025-07-27 20:53:29', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 20:53:29', '2025-07-26 20:54:06', '2025-07-26 20:54:06', NULL, NULL, 0, 'User', NULL, NULL),
('3eddf02f-1dbe-435e-86ee-e0a804dcb5e5', '15d83e77-7daa-495b-a1af-38d8d97cae47', NULL, '2025-07-26 16:53:36', '2025-07-27 16:53:36', 'JAMES NABEL', 'FINANCE', NULL, '2025-07-26 16:53:36', '2025-07-26 16:53:42', '2025-07-26 16:53:42', NULL, NULL, 0, 'User', NULL, NULL),
('4516787c-c65b-4a09-b848-a33d8ac8bbaf', 'admin-default', NULL, '2025-07-27 14:21:01', '2025-07-28 14:21:01', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-27 14:21:01', '2025-07-27 14:24:52', '2025-07-27 14:24:52', NULL, NULL, 0, 'User', NULL, NULL),
('48deb8fc-f9aa-4ef5-a676-ac2765d217f1', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 18:41:37', '2025-07-27 18:41:37', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 18:41:37', '2025-07-26 18:45:58', '2025-07-26 18:55:29', NULL, NULL, 0, 'User', NULL, NULL),
('4e766834-fd5b-4e9d-92f9-9e04fea97e33', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-27 00:10:28', '2025-07-28 00:10:28', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-27 00:10:28', '2025-07-27 00:10:59', '2025-07-27 00:10:59', NULL, NULL, 0, 'User', NULL, NULL),
('5250ec81-198b-42cf-a450-26afa3a8510c', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 22:30:04', '2025-07-27 22:30:04', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 22:30:04', '2025-07-26 22:30:42', '2025-07-26 22:30:42', NULL, NULL, 0, 'User', NULL, NULL),
('58e0060b-204e-4e11-80fc-9d59d8301477', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-27 00:10:02', '2025-07-28 00:10:02', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-27 00:10:02', '2025-07-27 00:11:44', '2025-07-27 00:10:19', NULL, NULL, 0, 'User', NULL, NULL),
('5aeca23c-27a2-4e6f-8c07-713e09f0ea49', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 17:26:28', '2025-07-27 17:26:28', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 17:26:28', '2025-07-26 17:26:28', '2025-07-26 17:35:29', NULL, NULL, 0, 'User', NULL, NULL),
('5b428f5c-168e-4dea-9b53-b17bb262a2bd', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 20:48:43', '2025-07-27 20:48:43', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 20:48:43', '2025-07-26 20:49:15', '2025-07-26 20:49:15', NULL, NULL, 0, 'User', NULL, NULL),
('5c00da7b-39b1-4da4-be7e-c19027474f8a', 'admin-default', NULL, '2025-07-26 15:19:08', '2025-07-27 15:19:08', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 15:19:08', '2025-07-26 15:19:30', '2025-07-26 15:19:30', NULL, NULL, 0, 'User', NULL, NULL),
('5ce47a25-0e88-44db-923e-61fb0ff4e089', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 22:08:33', '2025-07-27 22:08:33', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 22:08:33', '2025-07-26 22:14:21', '2025-07-26 22:14:21', NULL, NULL, 0, 'User', NULL, NULL),
('5d9673c8-ae83-4299-8586-89566ac9c724', 'admin-default', NULL, '2025-07-27 13:54:04', '2025-07-28 13:54:04', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'G5QB-8KjBeuMggEEAAAE', '2025-07-27 13:54:04', '2025-07-27 13:54:22', '2025-07-27 13:54:22', NULL, NULL, 0, 'User', NULL, NULL),
('60ab610a-8dd1-4fd1-ae3e-ce579085b85d', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 21:43:09', '2025-07-27 21:43:09', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 21:43:09', '2025-07-26 21:53:47', '2025-07-26 21:53:47', NULL, NULL, 0, 'User', NULL, NULL),
('60c64b10-c03d-4d77-b230-831d439a3e4b', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 18:21:52', '2025-07-27 18:21:52', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 18:21:52', '2025-07-26 18:28:16', '2025-07-26 18:26:51', NULL, NULL, 0, 'User', NULL, NULL),
('618a22a3-c8a4-4676-8116-485134a42828', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 18:51:05', '2025-07-27 18:51:05', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 18:51:05', '2025-07-26 18:51:30', '2025-07-26 19:22:00', NULL, NULL, 0, 'User', NULL, NULL),
('6198df0d-1843-461c-851c-40a7b195c31e', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-27 00:28:08', '2025-07-28 00:28:08', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-27 00:28:08', '2025-07-27 00:38:36', '2025-07-27 00:29:37', NULL, NULL, 0, 'User', NULL, NULL),
('625b1457-5493-4f58-8a88-21eff39e18dd', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 20:47:22', '2025-07-27 20:47:22', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 20:47:22', '2025-07-26 20:50:03', '2025-07-26 20:48:38', NULL, NULL, 0, 'User', NULL, NULL),
('63c096f5-fc24-47ba-aaca-8de1f8ca0d5f', 'admin-default', NULL, '2025-07-26 15:12:27', '2025-07-27 15:12:27', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 15:12:27', '2025-07-26 15:20:55', '2025-07-26 15:41:27', NULL, NULL, 0, 'User', NULL, NULL),
('683e5860-3b63-4cea-b179-f00b9c45c67e', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 19:41:46', '2025-07-27 19:41:46', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 19:41:46', '2025-07-26 19:42:00', '2025-07-26 19:42:00', NULL, NULL, 0, 'User', NULL, NULL),
('6ad4e4f3-0e87-43c6-9cdb-1fb5edef70a7', 'admin-default', NULL, '2025-07-27 14:13:42', '2025-07-28 14:13:42', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'nEaVL3sUXhFNMNvIAAAO', '2025-07-27 14:13:42', '2025-07-27 14:18:20', '2025-07-27 14:18:20', NULL, NULL, 0, 'User', NULL, NULL),
('6b34a5c0-69ff-4822-8686-b011bb4bacfc', 'admin-default', NULL, '2025-07-26 16:41:35', '2025-07-27 16:41:35', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'xxscPdAgZk8ew-xqAAAB', '2025-07-26 16:41:35', '2025-07-26 16:49:47', '2025-07-26 16:50:16', NULL, NULL, 0, 'User', NULL, NULL),
('6b796e70-c7ba-43ca-b7a0-932158ebff3c', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:47:24', '2025-07-27 22:47:24', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:47:24', '2025-07-26 22:47:44', '2025-07-26 22:47:44', NULL, NULL, 0, 'User', NULL, NULL),
('6ca703f5-aeb1-4959-af57-57a4582dec0f', 'admin-default', NULL, '2025-07-26 16:52:16', '2025-07-27 16:52:16', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 16:52:16', '2025-07-26 16:54:11', '2025-07-26 16:52:46', NULL, NULL, 0, 'User', NULL, NULL),
('75210d1b-9741-4ed9-bd2e-8bad6dbe1bb9', '15d83e77-7daa-495b-a1af-38d8d97cae47', NULL, '2025-07-26 16:03:33', '2025-07-27 16:03:33', 'JAMES NABEL', 'FINANCE', NULL, '2025-07-26 16:03:33', '2025-07-26 16:03:36', '2025-07-26 16:03:36', NULL, NULL, 0, 'User', NULL, NULL),
('75c921ff-5c40-42b7-a0da-c56be3b4c3ef', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 17:40:57', '2025-07-27 17:40:57', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 17:40:57', '2025-07-26 17:41:02', '2025-07-26 17:41:03', NULL, NULL, 0, 'User', NULL, NULL),
('77250912-ec9b-4e76-8dbc-f0ab56146da2', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:29:27', '2025-07-27 22:29:27', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:29:27', '2025-07-26 22:29:48', '2025-07-26 22:29:48', NULL, NULL, 0, 'User', NULL, NULL),
('7ce153f5-f0d6-462e-8a7c-e33449d5e46f', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 22:14:52', '2025-07-27 22:14:52', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 22:14:52', '2025-07-26 22:18:53', '2025-07-26 22:17:28', NULL, NULL, 0, 'User', NULL, NULL),
('7e23755e-068b-4c73-b23c-6b721b53214c', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 20:54:13', '2025-07-27 20:54:13', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 20:54:13', '2025-07-26 20:57:24', '2025-07-26 20:55:59', NULL, NULL, 0, 'User', NULL, NULL),
('7ed73881-b00f-4d2e-be01-466f5831d70a', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 21:13:48', '2025-07-27 21:13:48', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 21:13:48', '2025-07-26 21:14:13', '2025-07-26 21:14:13', NULL, NULL, 0, 'User', NULL, NULL),
('8266d833-1e9f-4e45-b8ec-c67f8c4f625e', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 19:40:25', '2025-07-27 19:40:25', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 19:40:25', '2025-07-26 19:41:40', '2025-07-26 19:41:40', NULL, NULL, 0, 'User', NULL, NULL),
('83a218f2-ef32-4bdf-b279-80eb89109a2f', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 21:41:24', '2025-07-27 21:41:24', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 21:41:24', '2025-07-26 21:41:42', '2025-07-26 21:41:42', NULL, NULL, 0, 'User', NULL, NULL),
('881c00d3-4f39-4526-9d31-aa698dcd9dfb', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 18:30:58', '2025-07-27 18:30:58', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 18:30:58', '2025-07-26 18:32:24', '2025-07-26 18:35:29', NULL, NULL, 0, 'User', NULL, NULL),
('8d5dd75e-5b24-4dfb-99c2-a5966147b422', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 23:36:04', '2025-07-27 23:36:04', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 23:36:04', '2025-07-26 23:36:31', '2025-07-26 23:36:31', NULL, NULL, 0, 'User', NULL, NULL),
('8ed4a7fa-81e4-4d27-aedc-937b56d79caa', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 23:38:52', '2025-07-27 23:38:52', 'FELIX AYISI', 'FINANCE', 'QNPxey7zKQi3XXJyAAAC', '2025-07-26 23:38:52', '2025-07-27 00:00:18', '2025-07-27 00:28:14', NULL, NULL, 0, 'User', NULL, NULL),
('8f127c41-abeb-4b05-941e-960db18fae5b', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 20:56:05', '2025-07-27 20:56:05', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 20:56:05', '2025-07-26 20:56:16', '2025-07-26 20:56:16', NULL, NULL, 0, 'User', NULL, NULL),
('91013dd0-3096-471f-b590-e0f60b0bb0a3', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-27 11:24:05', '2025-07-28 11:24:05', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-27 11:24:05', '2025-07-27 11:24:43', '2025-07-27 12:26:47', NULL, NULL, 0, 'User', NULL, NULL),
('939962bb-cff8-41ec-9e61-875cca19b5f0', 'admin-default', NULL, '2025-07-27 14:27:32', '2025-07-28 14:27:32', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', '-FOvWfmSLIL1zaVGAAAB', '2025-07-27 14:27:32', '2025-07-27 14:29:23', NULL, NULL, NULL, 1, 'User', NULL, NULL),
('95a5d3ef-5c0d-4669-a1ca-259af262c6e5', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 17:41:08', '2025-07-27 17:41:08', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 17:41:08', '2025-07-26 17:41:29', '2025-07-26 17:41:29', NULL, NULL, 0, 'User', NULL, NULL),
('99583d73-1ff1-4900-8e74-e7353d840835', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 20:25:29', '2025-07-27 20:25:29', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 20:25:29', '2025-07-26 20:30:19', '2025-07-26 20:28:54', NULL, NULL, 0, 'User', NULL, NULL),
('9ddc5948-840f-42e8-83e7-98bc131b1e48', '15d83e77-7daa-495b-a1af-38d8d97cae47', NULL, '2025-07-26 16:40:46', '2025-07-27 16:40:46', 'JAMES NABEL', 'FINANCE', NULL, '2025-07-26 16:40:46', '2025-07-26 16:42:24', '2025-07-26 16:40:59', NULL, NULL, 0, 'User', NULL, NULL),
('9e51c0c0-25ed-4cc4-a7fa-bdf1cef4196d', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 18:46:03', '2025-07-27 18:46:03', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 18:46:03', '2025-07-26 18:49:15', '2025-07-26 18:55:29', NULL, NULL, 0, 'User', NULL, NULL),
('9f853125-d7e1-4f33-ac6b-189f3a8c729b', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:48:47', '2025-07-27 22:48:47', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:48:47', '2025-07-26 22:49:02', '2025-07-26 22:49:02', NULL, NULL, 0, 'User', NULL, NULL),
('a5291ba8-edcc-418b-be96-8c9fd3c7e8d2', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 23:12:07', '2025-07-27 23:12:07', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 23:12:07', '2025-07-26 23:12:43', '2025-07-26 23:12:43', NULL, NULL, 0, 'User', NULL, NULL),
('a5d44275-1c28-4cd2-b3b2-7e669f042ffa', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 23:09:56', '2025-07-27 23:09:56', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 23:09:56', '2025-07-26 23:11:12', '2025-07-26 23:11:12', NULL, NULL, 0, 'User', NULL, NULL),
('a6abc05b-ec43-42f0-85c1-cd48e174881a', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 21:53:52', '2025-07-27 21:53:52', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 21:53:52', '2025-07-26 21:54:07', '2025-07-26 21:54:07', NULL, NULL, 0, 'User', NULL, NULL),
('aa39ba01-3061-422e-a61d-c41ac9ba876e', 'admin-default', NULL, '2025-07-26 15:09:06', '2025-07-27 15:09:06', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 15:09:06', '2025-07-26 15:09:52', '2025-07-26 15:18:29', NULL, NULL, 0, 'User', NULL, NULL),
('aa94fbe7-f893-4b15-92d5-3be29e64380c', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 23:37:11', '2025-07-27 23:37:11', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 23:37:11', '2025-07-26 23:37:37', '2025-07-26 23:37:37', NULL, NULL, 0, 'User', NULL, NULL),
('abb1a469-d4fa-4c6b-bf16-6d4c682f1afa', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:01:02', '2025-07-27 22:01:02', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:01:02', '2025-07-26 22:01:20', '2025-07-26 22:01:20', NULL, NULL, 0, 'User', NULL, NULL),
('ad029bc5-e4fe-4cd5-907b-0859dbbb7d10', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:52:27', '2025-07-27 22:52:27', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:52:27', '2025-07-26 22:57:52', '2025-07-26 22:57:52', NULL, NULL, 0, 'User', NULL, NULL),
('b46c8a44-9b38-4fee-8060-0ca16c2c34a5', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 22:49:10', '2025-07-27 22:49:10', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 22:49:10', '2025-07-26 22:49:16', '2025-07-26 22:49:16', NULL, NULL, 0, 'User', NULL, NULL),
('b7b0e673-732d-44ff-b794-1c285d15290d', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 21:42:35', '2025-07-27 21:42:35', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 21:42:35', '2025-07-26 21:43:01', '2025-07-26 21:43:01', NULL, NULL, 0, 'User', NULL, NULL),
('b7f55fc3-1c18-4d15-a9cd-713d6cd71f31', 'admin-default', NULL, '2025-07-26 16:18:46', '2025-07-27 16:18:46', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 16:18:46', '2025-07-26 16:28:58', '2025-07-26 16:31:39', NULL, NULL, 0, 'User', NULL, NULL),
('b89bee3c-ec4b-455f-8bcf-e69b993d94d9', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 20:56:23', '2025-07-27 20:56:23', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 20:56:23', '2025-07-26 20:58:25', '2025-07-26 20:58:25', NULL, NULL, 0, 'User', NULL, NULL),
('b96b1502-b0f5-49d7-bbbd-0dd553be8025', 'admin-default', NULL, '2025-07-27 14:05:39', '2025-07-28 14:05:39', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-27 14:05:39', '2025-07-27 14:12:52', '2025-07-27 14:12:52', NULL, NULL, 0, 'User', NULL, NULL),
('bc3879f7-2257-4d0f-b498-21c657f988e8', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 18:49:27', '2025-07-27 18:49:27', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 18:49:27', '2025-07-26 18:50:56', '2025-07-26 18:55:29', NULL, NULL, 0, 'User', NULL, NULL),
('bd651a7b-9493-4fc3-974f-ded9059ad80e', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:06:56', '2025-07-27 22:06:56', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:06:56', '2025-07-26 22:07:14', '2025-07-26 22:07:14', NULL, NULL, 0, 'User', NULL, NULL),
('be103064-3a83-4e0e-80de-53efff87f7fa', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 21:15:50', '2025-07-27 21:15:50', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 21:15:50', '2025-07-26 21:16:19', '2025-07-26 21:16:19', NULL, NULL, 0, 'User', NULL, NULL),
('bf9bf816-77c8-4964-b5da-60d4a266f330', '910337af-c186-4026-962d-628a3fe1621c', NULL, '2025-07-26 23:12:56', '2025-07-27 23:12:56', 'WILLIAM AKUAMOAH', 'AUDIT', NULL, '2025-07-26 23:12:56', '2025-07-26 23:20:06', '2025-07-27 00:28:14', NULL, NULL, 0, 'User', NULL, NULL),
('c1268fc4-1dc8-47c9-b082-0b10f8bd11f3', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 21:54:17', '2025-07-27 21:54:17', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 21:54:17', '2025-07-26 21:55:03', '2025-07-26 21:55:03', NULL, NULL, 0, 'User', NULL, NULL),
('c177160a-17cd-4fda-a8a8-b831b7766edb', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 21:02:05', '2025-07-27 21:02:05', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 21:02:05', '2025-07-26 21:11:45', '2025-07-26 21:11:45', NULL, NULL, 0, 'User', NULL, NULL),
('c45e443c-b9f6-41f7-851a-157230551e75', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 20:13:38', '2025-07-27 20:13:38', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 20:13:38', '2025-07-26 20:18:30', '2025-07-26 20:17:05', NULL, NULL, 0, 'User', NULL, NULL),
('c591f71b-3ab9-44bf-b6c0-bb6deda9e977', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 21:11:50', '2025-07-27 21:11:50', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 21:11:50', '2025-07-26 21:11:58', '2025-07-26 21:11:58', NULL, NULL, 0, 'User', NULL, NULL),
('c7ef882f-c16f-4d31-ba5d-8fe198051c45', '15d83e77-7daa-495b-a1af-38d8d97cae47', NULL, '2025-07-26 16:53:05', '2025-07-27 16:53:05', 'JAMES NABEL', 'FINANCE', NULL, '2025-07-26 16:53:05', '2025-07-26 16:53:19', '2025-07-26 16:53:19', NULL, NULL, 0, 'User', NULL, NULL),
('ca0f380b-b1d8-48fe-8aff-8467084aff17', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-27 00:05:45', '2025-07-28 00:05:45', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-27 00:05:45', '2025-07-27 00:06:15', '2025-07-27 00:06:15', NULL, NULL, 0, 'User', NULL, NULL),
('cb11af96-10aa-4fe8-a5b7-fceef7799d2e', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:17:34', '2025-07-27 22:17:34', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:17:34', '2025-07-26 22:27:35', '2025-07-26 22:47:36', NULL, NULL, 0, 'User', NULL, NULL),
('ce521a51-e4d3-42cb-baab-dc4075e364da', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 20:49:21', '2025-07-27 20:49:21', 'SAMUEL ASIEDU', 'AUDIT', 'IbVzQX5EXKXGCT5RAACX', '2025-07-26 20:49:21', '2025-07-26 20:53:24', '2025-07-26 20:53:24', NULL, NULL, 0, 'User', NULL, NULL),
('cea80705-3926-42db-bc24-d1ffb55ec88c', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-27 00:29:43', '2025-07-28 00:29:43', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-27 00:29:43', '2025-07-27 00:29:51', '2025-07-27 00:29:51', NULL, NULL, 0, 'User', NULL, NULL),
('cf3fd45e-967c-43a1-96f5-61df9ec7259b', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 19:44:00', '2025-07-27 19:44:00', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 19:44:00', '2025-07-26 19:55:09', '2025-07-26 20:05:11', NULL, NULL, 0, 'User', NULL, NULL),
('d7bd0d18-7de4-4cf5-9041-877c40dcd919', 'admin-default', NULL, '2025-07-26 14:54:17', '2025-07-27 14:54:17', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 14:54:17', '2025-07-26 14:58:29', '2025-07-26 14:58:29', NULL, NULL, 0, 'User', NULL, NULL),
('d84b7ccc-707e-48e3-ba64-6726f5736f63', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-27 00:20:46', '2025-07-28 00:20:46', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-27 00:20:46', '2025-07-27 00:23:54', '2025-07-27 00:23:54', NULL, NULL, 0, 'User', NULL, NULL),
('d85b85ac-e5c1-48b4-ae6e-e1796c650abc', 'admin-default', NULL, '2025-07-26 16:33:01', '2025-07-27 16:33:01', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 16:33:01', '2025-07-26 16:40:40', '2025-07-26 16:40:40', NULL, NULL, 0, 'User', NULL, NULL),
('dad77921-7f62-47d7-8e3d-5acf157ec2ab', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 21:55:12', '2025-07-27 21:55:12', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 21:55:12', '2025-07-26 21:55:25', '2025-07-26 21:55:25', NULL, NULL, 0, 'User', NULL, NULL),
('e09c2be0-f0b1-4399-8869-32cd46853873', 'admin-default', NULL, '2025-07-26 16:49:07', '2025-07-27 16:49:07', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 16:49:07', '2025-07-26 16:51:00', '2025-07-26 16:50:16', NULL, NULL, 0, 'User', NULL, NULL),
('e5fc03e4-bc14-47f2-900d-94a2ffbc54b8', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 21:55:30', '2025-07-27 21:55:30', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 21:55:30', '2025-07-26 22:00:53', '2025-07-26 22:00:53', NULL, NULL, 0, 'User', NULL, NULL),
('e63c190c-a962-43b8-8d92-f6190986f20e', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 21:25:54', '2025-07-27 21:25:54', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 21:25:54', '2025-07-26 22:08:39', '2025-07-26 22:14:38', NULL, NULL, 0, 'User', NULL, NULL),
('e78bbacc-1701-48b7-b7a1-f4294d2d5b4a', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 23:28:52', '2025-07-27 23:28:52', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 23:28:52', '2025-07-26 23:34:28', '2025-07-26 23:34:28', NULL, NULL, 0, 'User', NULL, NULL),
('e859bb12-13b8-4f15-b3a6-27511403b395', 'admin-default', NULL, '2025-07-26 16:29:20', '2025-07-27 16:29:20', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 16:29:20', '2025-07-26 16:31:54', '2025-07-26 16:39:19', NULL, NULL, 0, 'User', NULL, NULL),
('e89baa0c-9155-4066-9e54-40a94ae30869', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 21:16:24', '2025-07-27 21:16:24', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 21:16:24', '2025-07-26 21:25:47', '2025-07-26 21:25:47', NULL, NULL, 0, 'User', NULL, NULL),
('ea62f6f1-a2a6-4486-a73e-996a5a817db9', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 17:41:35', '2025-07-27 17:41:35', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 17:41:35', '2025-07-26 17:46:57', '2025-07-26 17:45:32', NULL, NULL, 0, 'User', NULL, NULL),
('ea66587d-5dee-4e57-98f0-5f0c2311d9e7', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 17:39:48', '2025-07-27 17:39:48', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 17:39:48', '2025-07-26 17:39:48', '2025-07-26 17:45:29', NULL, NULL, 0, 'User', NULL, NULL),
('f218c54b-2290-4514-82a7-27f00b4e1111', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 20:59:46', '2025-07-27 20:59:46', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 20:59:46', '2025-07-26 21:01:59', '2025-07-26 21:01:59', NULL, NULL, 0, 'User', NULL, NULL),
('f3729114-fe01-41f8-b863-ce4da4d9c9f4', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 21:15:18', '2025-07-27 21:15:18', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 21:15:18', '2025-07-26 21:17:09', '2025-07-26 21:15:44', NULL, NULL, 0, 'User', NULL, NULL),
('f379c88b-a466-4f9e-9b88-cbe884dce5d9', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 23:37:43', '2025-07-27 23:37:43', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 23:37:43', '2025-07-26 23:38:47', '2025-07-26 23:38:47', NULL, NULL, 0, 'User', NULL, NULL),
('f4a39486-5806-4f35-a4fe-aeab311d0cf3', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-26 22:30:47', '2025-07-27 22:30:47', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-26 22:30:47', '2025-07-26 22:31:00', '2025-07-26 22:31:00', NULL, NULL, 0, 'User', NULL, NULL),
('f4b37ef7-b890-4003-9d64-47d9fdbe832d', 'admin-default', NULL, '2025-07-26 16:51:19', '2025-07-27 16:51:19', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 16:51:19', '2025-07-26 16:52:49', '2025-07-26 16:51:24', NULL, NULL, 0, 'User', NULL, NULL),
('fa240f40-95d3-4627-8d00-8af9056d6186', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-27 00:06:21', '2025-07-28 00:06:21', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-27 00:06:21', '2025-07-27 00:08:38', '2025-07-27 00:07:13', NULL, NULL, 0, 'User', NULL, NULL),
('fb347f6e-15dd-422d-9408-f01dfdf3946b', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 20:09:33', '2025-07-27 20:09:33', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 20:09:33', '2025-07-26 20:13:18', '2025-07-26 20:15:11', NULL, NULL, 0, 'User', NULL, NULL),
('fb90f546-cbdd-4fbe-8c34-de3538387548', 'c52a4077-1d14-434f-afd2-55bd05087960', NULL, '2025-07-27 00:19:30', '2025-07-28 00:19:30', 'FELIX AYISI', 'FINANCE', NULL, '2025-07-27 00:19:30', '2025-07-27 00:19:49', '2025-07-27 00:19:49', NULL, NULL, 0, 'User', NULL, NULL),
('fba94dfb-ac75-429a-8842-a2d3ccd1f142', 'admin-default', NULL, '2025-07-26 16:11:46', '2025-07-27 16:11:46', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', NULL, '2025-07-26 16:11:46', '2025-07-26 16:14:18', '2025-07-26 16:14:18', NULL, NULL, 0, 'User', NULL, NULL),
('ff24f7fe-f5c7-431e-9cc4-a6da6dfe8552', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', NULL, '2025-07-26 23:11:17', '2025-07-27 23:11:17', 'SAMUEL ASIEDU', 'AUDIT', NULL, '2025-07-26 23:11:17', '2025-07-26 23:13:27', '2025-07-26 23:12:02', NULL, NULL, 0, 'User', NULL, NULL);

-- Table: audit_logs
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE `audit_logs` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `user_name` varchar(255) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `action` varchar(255) NOT NULL,
  `description` text,
  `resource_type` varchar(50) NOT NULL,
  `resource_id` varchar(36) DEFAULT NULL,
  `details` text,
  `timestamp` datetime NOT NULL,
  `ip_address` varchar(50) DEFAULT NULL,
  `user_agent` text,
  `severity` varchar(50) DEFAULT 'INFO',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: audit_logs
INSERT INTO `audit_logs` VALUES
('1a1aa588-184b-4344-9b15-7e1bf8b201f5', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGOUT', 'SYSTEM ADMINISTRATOR logged out from SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-07-27 14:18:20', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('26b67879-9828-4321-a3ba-280f3154ede9', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGOUT', 'SYSTEM ADMINISTRATOR logged out from SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-07-27 14:12:52', '::ffff:127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('44c5b57c-e6b6-4117-b9f2-c1fed490534d', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGOUT', 'SYSTEM ADMINISTRATOR logged out from SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-07-27 14:24:52', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('5f20fa3d-3f1a-47e6-8727-f56d84b831c0', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGIN', 'SYSTEM ADMINISTRATOR logged in to SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-07-27 14:21:01', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('61cfa6ea-7a74-446a-9b8a-317911b31118', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGIN', 'SYSTEM ADMINISTRATOR logged in to SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-07-27 14:27:32', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('8a801463-0a26-45aa-9df2-5ebc95d524e7', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGIN', 'SYSTEM ADMINISTRATOR logged in to SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-07-27 13:54:04', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('e200e1f5-a612-45f1-8dee-44ed142a5a2c', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGIN', 'SYSTEM ADMINISTRATOR logged in to SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-07-27 14:05:39', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'INFO'),
('f7e22a33-b2ca-4fe4-9140-fefb427d4ddb', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGOUT', 'SYSTEM ADMINISTRATOR logged out from SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-07-27 13:54:22', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO'),
('fb745ff7-0068-4dd1-833c-94e3ed7eea50', 'admin-default', 'SYSTEM ADMINISTRATOR', 'SYSTEM ADMIN', 'LOGIN', 'SYSTEM ADMINISTRATOR logged in to SYSTEM ADMIN department', 'USER', 'admin-default', '{}', '2025-07-27 14:13:42', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'INFO');

-- Table: audit_voucher_attachments
DROP TABLE IF EXISTS `audit_voucher_attachments`;
CREATE TABLE `audit_voucher_attachments` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `stored_filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `uploaded_by` varchar(36) NOT NULL,
  `uploaded_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_active` (`is_active`),
  KEY `idx_uploaded_at` (`uploaded_at`),
  CONSTRAINT `audit_voucher_attachments_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `audit_voucher_attachments_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Stores metadata for audit department voucher attachments';

-- Data for table: audit_voucher_attachments
INSERT INTO `audit_voucher_attachments` VALUES
('96b1d186-2e96-428d-80da-79fea86a8156', 'c9cdab8b-89d2-4801-8440-5aedfb3cd067', 'MN PAYSLIPS JUL 2025 TEST FINAL.pdf', 'ANT_TEST.pdf', 'C:\Users\<USER>\Desktop\VMS-PRODUCTION\uploads\audit-attachments\2025\voucher-c9cdab8b-89d2-4801-8440-5aedfb3cd067\ANT_TEST.pdf', 10000267, 'application/pdf', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', '2025-07-26 20:52:00', 1),
('f457eee7-2fc8-4aae-a9aa-4afa5985fd29', '48831b80-d070-4624-bee1-87ebbe16a095', 'ASANKRANGUA AREA BILL DETAIL.pdf', 'MAX_TEST.pdf', 'C:\Users\<USER>\Desktop\VMS-PRODUCTION\uploads\audit-attachments\2025\voucher-48831b80-d070-4624-bee1-87ebbe16a095\MAX_TEST.pdf', 346813, 'application/pdf', 'f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', '2025-07-26 19:59:36', 1);

-- Table: batch_vouchers
DROP TABLE IF EXISTS `batch_vouchers`;
CREATE TABLE `batch_vouchers` (
  `batch_id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  PRIMARY KEY (`batch_id`,`voucher_id`),
  KEY `voucher_id` (`voucher_id`),
  CONSTRAINT `batch_vouchers_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `voucher_batches` (`id`) ON DELETE CASCADE,
  CONSTRAINT `batch_vouchers_ibfk_2` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: batches
DROP TABLE IF EXISTS `batches`;
CREATE TABLE `batches` (
  `id` varchar(36) NOT NULL,
  `batch_number` varchar(50) NOT NULL,
  `department` varchar(100) NOT NULL,
  `created_by` varchar(36) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(50) DEFAULT 'PENDING',
  `voucher_count` int DEFAULT '0',
  `total_amount` decimal(15,2) DEFAULT '0.00',
  `dispatch_date` timestamp NULL DEFAULT NULL,
  `received_date` timestamp NULL DEFAULT NULL,
  `received_by` varchar(36) DEFAULT NULL,
  `notes` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: batches
INSERT INTO `batches` VALUES
('BATCH_1753477346146', 'BATCH_1753477346146', 'AUDIT', 'SAMUEL', '2025-07-25 21:02:26', 'PENDING', 1, '1000.00', NULL, NULL, NULL, NULL);

-- Table: blacklisted_voucher_ids
DROP TABLE IF EXISTS `blacklisted_voucher_ids`;
CREATE TABLE `blacklisted_voucher_ids` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(50) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `voucher_id` (`voucher_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: concurrent_users_monitor
DROP TABLE IF EXISTS `concurrent_users_monitor`;
CREATE TABLE `concurrent_users_monitor` (
  `id` int NOT NULL AUTO_INCREMENT,
  `department` varchar(50) NOT NULL,
  `active_users_count` int DEFAULT '0',
  `max_users_limit` int DEFAULT '10',
  `workstations_count` int DEFAULT '0',
  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `peak_usage_time` time DEFAULT NULL,
  `peak_users_count` int DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_department` (`department`),
  KEY `idx_concurrent_department` (`department`),
  KEY `idx_concurrent_updated` (`last_updated`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: concurrent_users_monitor
INSERT INTO `concurrent_users_monitor` VALUES
(1, 'AUDIT', 0, 10, 3, '2025-05-29 20:26:56', NULL, 0),
(2, 'FINANCE', 0, 10, 3, '2025-05-29 20:26:56', NULL, 0),
(3, 'MINISTRIES', 0, 8, 2, '2025-05-29 20:26:56', NULL, 0),
(4, 'PENSIONS', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(5, 'PENTMEDIA', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(6, 'MISSIONS', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(7, 'PENTSOS', 0, 6, 1, '2025-05-29 20:26:56', NULL, 0),
(8, 'SYSTEM ADMIN', 0, 5, 1, '2025-05-29 20:26:56', NULL, 0);

-- Table: departments
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments` (
  `id` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(10) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: departments
INSERT INTO `departments` VALUES
('4', 'HR', '', 'Human Resources', 1, '2025-07-10 15:13:26', '2025-07-10 15:13:26'),
('dept_admin', 'SYSTEM ADMIN', 'ADM', 'System Administration', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_audit', 'AUDIT', 'AUD', 'Audit Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_finance', 'FINANCE', 'FIN', 'Finance Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_ministries', 'MINISTRIES', 'MIN', 'Ministries Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_missions', 'MISSIONS', 'MIS', 'Missions Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_pensions', 'PENSIONS', 'PEN', 'Pensions Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_pentmedia', 'PENTMEDIA', 'PMD', 'Pentmedia Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06'),
('dept_pentsos', 'PENTSOS', 'PSO', 'Pentsos Department', 1, '2025-06-14 23:09:06', '2025-06-14 23:09:06');

-- Table: lan_resource_locks
DROP TABLE IF EXISTS `lan_resource_locks`;
CREATE TABLE `lan_resource_locks` (
  `id` varchar(36) NOT NULL,
  `resource_type` enum('VOUCHER','BATCH','CASH_RECORD','USER') NOT NULL,
  `resource_id` varchar(100) NOT NULL,
  `locked_by_user_id` varchar(36) NOT NULL,
  `locked_by_session_id` varchar(255) NOT NULL,
  `workstation_id` varchar(100) DEFAULT NULL,
  `lock_type` enum('read','write','exclusive') DEFAULT 'write',
  `locked_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_lan_locks_user` (`locked_by_user_id`),
  KEY `idx_lan_locks_session` (`locked_by_session_id`),
  KEY `idx_lan_locks_expires` (`expires_at`),
  KEY `idx_lan_locks_active` (`is_active`),
  KEY `idx_lan_locks_resource` (`resource_type`,`resource_id`),
  CONSTRAINT `lan_resource_locks_ibfk_1` FOREIGN KEY (`locked_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `lan_resource_locks_ibfk_2` FOREIGN KEY (`locked_by_session_id`) REFERENCES `lan_user_sessions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: lan_security_events
DROP TABLE IF EXISTS `lan_security_events`;
CREATE TABLE `lan_security_events` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) DEFAULT NULL,
  `event_type` enum('LOGIN_SUCCESS','LOGIN_FAILED','LOGOUT','PASSWORD_CHANGE','MFA_ENABLED','MFA_DISABLED','ACCOUNT_LOCKED','ACCOUNT_UNLOCKED','SUSPICIOUS_ACTIVITY') NOT NULL,
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') DEFAULT 'MEDIUM',
  `description` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `workstation_id` varchar(100) DEFAULT NULL,
  `additional_data` json DEFAULT NULL,
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_lan_security_user_id` (`user_id`),
  KEY `idx_lan_security_type` (`event_type`),
  KEY `idx_lan_security_severity` (`severity`),
  KEY `idx_lan_security_timestamp` (`timestamp`),
  CONSTRAINT `lan_security_events_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: lan_user_sessions
DROP TABLE IF EXISTS `lan_user_sessions`;
CREATE TABLE `lan_user_sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `refresh_token` varchar(500) NOT NULL,
  `workstation_id` varchar(100) DEFAULT NULL,
  `workstation_name` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `mac_address` varchar(17) DEFAULT NULL,
  `user_agent` text,
  `department` varchar(50) DEFAULT NULL,
  `device_info` json DEFAULT NULL,
  `network_info` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL,
  `last_activity` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_heartbeat` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `session_type` enum('WORKSTATION','MOBILE','ADMIN') DEFAULT 'WORKSTATION',
  `concurrent_session_count` int DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_lan_sessions_user_id` (`user_id`),
  KEY `idx_lan_sessions_workstation` (`workstation_id`),
  KEY `idx_lan_sessions_department` (`department`),
  KEY `idx_lan_sessions_expires_at` (`expires_at`),
  KEY `idx_lan_sessions_active` (`is_active`),
  KEY `idx_lan_sessions_heartbeat` (`last_heartbeat`),
  CONSTRAINT `lan_user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: lan_workstations
DROP TABLE IF EXISTS `lan_workstations`;
CREATE TABLE `lan_workstations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `workstation_id` varchar(100) NOT NULL,
  `workstation_name` varchar(100) NOT NULL,
  `department` varchar(50) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `mac_address` varchar(17) DEFAULT NULL,
  `computer_name` varchar(100) DEFAULT NULL,
  `os_info` varchar(200) DEFAULT NULL,
  `last_seen` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `is_authorized` tinyint(1) DEFAULT '1',
  `max_concurrent_users` int DEFAULT '3',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workstation_id` (`workstation_id`),
  KEY `idx_workstations_department` (`department`),
  KEY `idx_workstations_active` (`is_active`),
  KEY `idx_workstations_ip` (`ip_address`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: lan_workstations
INSERT INTO `lan_workstations` VALUES
(1, 'AUDIT-WS-01', 'Audit Workstation 1', 'AUDIT', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(2, 'AUDIT-WS-02', 'Audit Workstation 2', 'AUDIT', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(3, 'AUDIT-WS-03', 'Audit Workstation 3', 'AUDIT', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(4, 'FINANCE-WS-01', 'Finance Workstation 1', 'FINANCE', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(5, 'FINANCE-WS-02', 'Finance Workstation 2', 'FINANCE', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(6, 'FINANCE-WS-03', 'Finance Workstation 3', 'FINANCE', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(7, 'MINISTRIES-WS-01', 'Ministries Workstation 1', 'MINISTRIES', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(8, 'MINISTRIES-WS-02', 'Ministries Workstation 2', 'MINISTRIES', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(9, 'PENSIONS-WS-01', 'Pensions Workstation 1', 'PENSIONS', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(10, 'PENTMEDIA-WS-01', 'Pentmedia Workstation 1', 'PENTMEDIA', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(11, 'MISSIONS-WS-01', 'Missions Workstation 1', 'MISSIONS', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(12, 'PENTSOS-WS-01', 'Pentsos Workstation 1', 'PENTSOS', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 3, '2025-05-29 20:26:56'),
(13, 'ADMIN-WS-01', 'System Admin Workstation', 'SYSTEM ADMIN', NULL, NULL, NULL, NULL, '2025-05-29 20:26:56', 1, 1, 5, '2025-05-29 20:26:56');

-- Table: migrations
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `applied_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_migration_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: notifications
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `timestamp` varchar(50) NOT NULL,
  `voucher_id` varchar(36) DEFAULT NULL,
  `batch_id` varchar(36) DEFAULT NULL,
  `type` varchar(50) NOT NULL,
  `from_audit` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: password_change_requests
DROP TABLE IF EXISTS `password_change_requests`;
CREATE TABLE `password_change_requests` (
  `id` varchar(50) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `user_name` varchar(100) NOT NULL,
  `user_department` varchar(50) NOT NULL,
  `new_password_hash` text NOT NULL,
  `status` enum('PENDING','APPROVED','REJECTED') DEFAULT 'PENDING',
  `requested_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `requested_by_ip` varchar(45) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `processed_by` varchar(100) DEFAULT NULL,
  `admin_notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_requested_at` (`requested_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: password_change_requests
INSERT INTO `password_change_requests` VALUES
('PWD_REQ_1753545774953', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'newpass123', 'REJECTED', '2025-07-26 16:02:54', '::1', '2025-07-26 16:12:15', 'ADMIN', 'Rejected by administrator', '2025-07-26 16:02:54', '2025-07-26 16:12:15'),
('PWD_REQ_1753547895718', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'newpassword456', 'REJECTED', '2025-07-26 16:38:15', '::1', '2025-07-26 16:40:33', 'ADMIN', 'Rejected by administrator', '2025-07-26 16:38:15', '2025-07-26 16:40:33'),
('PWD_REQ_1753547959991', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'newpassword456', 'REJECTED', '2025-07-26 16:39:20', '::1', '2025-07-26 16:40:30', 'ADMIN', 'Rejected by administrator', '2025-07-26 16:39:20', '2025-07-26 16:40:30'),
('PWD_REQ_1753548078067', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'james123', 'APPROVED', '2025-07-26 16:41:18', '::1', '2025-07-26 16:50:05', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-26 16:41:18', '2025-07-26 16:50:05'),
('PWD_REQ_1753548719134', '15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'FINANCE', 'james123', 'APPROVED', '2025-07-26 16:51:59', '::1', '2025-07-26 16:52:32', 'SYSTEM ADMINISTRATOR', 'Approved by SYSTEM ADMINISTRATOR', '2025-07-26 16:51:59', '2025-07-26 16:52:32');

-- Table: pending_registrations
DROP TABLE IF EXISTS `pending_registrations`;
CREATE TABLE `pending_registrations` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `department` varchar(50) NOT NULL,
  `date_requested` varchar(50) NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: pending_registrations
INSERT INTO `pending_registrations` VALUES
('3343ae67-03cc-484c-a0d1-7289e02e26a1', 'WILLIAM AKUAMOAH', '123', 'AUDIT', '2025-07-16 09:15:55', 'approved'),
('88e0b4b8-bb89-45e8-a66e-d24b37d70690', 'JAMES NABEL', '123', 'FINANCE', '2025-07-16 09:13:59', 'approved');

-- Table: permissions
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `display_name` varchar(150) NOT NULL,
  `description` text,
  `resource` varchar(50) NOT NULL,
  `action` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: permissions
INSERT INTO `permissions` VALUES
(1, 'system.admin', 'System Administration', 'Full system administration access', 'system', 'admin', '2025-05-29 20:26:54'),
(2, 'system.config', 'System Configuration', 'Configure system settings', 'system', 'config', '2025-05-29 20:26:54'),
(3, 'users.create', 'Create Users', 'Create new user accounts', 'users', 'create', '2025-05-29 20:26:54'),
(4, 'users.view', 'View Users', 'View user information', 'users', 'view', '2025-05-29 20:26:54'),
(5, 'users.edit', 'Edit Users', 'Edit user information', 'users', 'edit', '2025-05-29 20:26:54'),
(6, 'users.delete', 'Delete Users', 'Delete user accounts', 'users', 'delete', '2025-05-29 20:26:54'),
(7, 'users.manage_roles', 'Manage User Roles', 'Assign and modify user roles', 'users', 'manage_roles', '2025-05-29 20:26:54'),
(8, 'audit.vouchers.view', 'View Audit Vouchers', 'View vouchers in audit', 'audit.vouchers', 'view', '2025-05-29 20:26:54'),
(9, 'audit.vouchers.process', 'Process Audit Vouchers', 'Process and approve vouchers', 'audit.vouchers', 'process', '2025-05-29 20:26:54'),
(10, 'audit.vouchers.reject', 'Reject Audit Vouchers', 'Reject vouchers with comments', 'audit.vouchers', 'reject', '2025-05-29 20:26:54'),
(11, 'audit.batches.receive', 'Receive Voucher Batches', 'Receive voucher batches from departments', 'audit.batches', 'receive', '2025-05-29 20:26:54'),
(12, 'audit.batches.dispatch', 'Dispatch Voucher Batches', 'Send vouchers back to departments', 'audit.batches', 'dispatch', '2025-05-29 20:26:54'),
(13, 'department.vouchers.create', 'Create Department Vouchers', 'Create new vouchers', 'department.vouchers', 'create', '2025-05-29 20:26:54'),
(14, 'department.vouchers.view', 'View Department Vouchers', 'View department vouchers', 'department.vouchers', 'view', '2025-05-29 20:26:54'),
(15, 'department.vouchers.edit', 'Edit Department Vouchers', 'Edit voucher information', 'department.vouchers', 'edit', '2025-05-29 20:26:54'),
(16, 'department.vouchers.send', 'Send Vouchers to Audit', 'Send vouchers to audit department', 'department.vouchers', 'send', '2025-05-29 20:26:54'),
(17, 'vouchers.create', 'Create Vouchers', 'Create new vouchers', 'vouchers', 'create', '2025-05-29 20:26:54'),
(18, 'vouchers.view', 'View Vouchers', 'View voucher information', 'vouchers', 'view', '2025-05-29 20:26:54'),
(19, 'vouchers.edit', 'Edit Vouchers', 'Edit voucher information', 'vouchers', 'edit', '2025-05-29 20:26:54'),
(20, 'vouchers.delete', 'Delete Vouchers', 'Delete vouchers', 'vouchers', 'delete', '2025-05-29 20:26:54'),
(21, 'reports.view', 'View Reports', 'View system reports', 'reports', 'view', '2025-05-29 20:26:54'),
(22, 'reports.create', 'Create Reports', 'Create custom reports', 'reports', 'create', '2025-05-29 20:26:54'),
(23, 'reports.export', 'Export Reports', 'Export reports to various formats', 'reports', 'export', '2025-05-29 20:26:54');

-- Table: provisional_cash_records
DROP TABLE IF EXISTS `provisional_cash_records`;
CREATE TABLE `provisional_cash_records` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `voucher_ref` varchar(50) NOT NULL,
  `claimant` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `main_amount` decimal(15,2) NOT NULL,
  `currency` enum('GHS','USD','GBP','EUR') NOT NULL,
  `amount_retired` decimal(15,2) DEFAULT NULL,
  `clearance_remark` enum('CLEARED','REFUNDED TO CHEST','DUE STAFF','RETURNED') DEFAULT NULL,
  `date_retired` varchar(50) DEFAULT NULL,
  `cleared_by` varchar(255) DEFAULT NULL,
  `comment` text,
  `date` varchar(50) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `voucher_id` (`voucher_id`),
  CONSTRAINT `provisional_cash_records_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: provisional_cash_records
INSERT INTO `provisional_cash_records` VALUES
('34783700-2a54-4b1a-b170-bcda57e0907c', 'e929a925-c35b-489d-bc6b-1b994eb2d013', 'FINJUL0003', 'KWEKU', 'START', '8900.00', 'GHS', NULL, NULL, NULL, NULL, NULL, '2025-07-26_20-40-06'),
('a8958e1a-9631-4266-b527-e99759374cd9', 'c9cdab8b-89d2-4801-8440-5aedfb3cd067', 'FINJUL0004', 'ANT', 'TEST', '45000.00', 'GHS', '10000.00', 'REFUNDED TO CHEST', '26-JUL-2025 08:50:18 PM', 'SAMUEL ASIEDU', '', '2025-07-26_20-49-42');

-- Table: resource_locks
DROP TABLE IF EXISTS `resource_locks`;
CREATE TABLE `resource_locks` (
  `id` varchar(36) NOT NULL,
  `resource_type` varchar(50) NOT NULL,
  `resource_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `department` varchar(50) NOT NULL,
  `lock_time` datetime NOT NULL,
  `expiry_time` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_resource` (`resource_type`,`resource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: role_permissions
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` int NOT NULL,
  `permission_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_role_permission` (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: roles
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `description` text,
  `permissions` json DEFAULT NULL,
  `is_system_role` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: roles
INSERT INTO `roles` VALUES
(1, 'SUPER_ADMIN', 'Super Administrator', 'Full system access with all permissions', *, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(2, 'AUDIT_MANAGER', 'Audit Manager', 'Full audit department management access', audit.*,users.view,reports.*, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(3, 'AUDIT_USER', 'Audit User', 'Standard audit user with voucher processing access', audit.vouchers.*,audit.batches.*,reports.view, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(4, 'DEPT_MANAGER', 'Department Manager', 'Department management access', department.*,vouchers.*,users.view, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(5, 'DEPT_USER', 'Department User', 'Standard department user access', vouchers.create,vouchers.view,vouchers.edit, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54'),
(6, 'VIEWER', 'Viewer', 'Read-only access to assigned resources', vouchers.view,reports.view, 1, '2025-05-29 20:26:54', '2025-05-29 20:26:54');

-- Table: system_settings
DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fiscal_year_start` enum('JAN','FEB','MAR','APR','MAY','JUN','JUL','AUG','SEP','OCT','NOV','DEC') DEFAULT 'JAN',
  `fiscal_year_end` enum('JAN','FEB','MAR','APR','MAY','JUN','JUL','AUG','SEP','OCT','NOV','DEC') DEFAULT 'DEC',
  `current_fiscal_year` int NOT NULL,
  `system_time` varchar(50) NOT NULL,
  `auto_backup_enabled` tinyint(1) DEFAULT '1',
  `session_timeout` int DEFAULT '30',
  `last_backup_date` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: system_settings
INSERT INTO `system_settings` VALUES
(1, 'JAN', 'DEC', 2025, '2025-07-12 19:48:10', 1, 30, '2025-07-27 14:24:29'),
(2, 'JAN', 'DEC', 2025, '2025-06-19 14:21:15', 1, 30, NULL);

-- Table: users
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','USER','viewer') NOT NULL,
  `department` varchar(50) NOT NULL,
  `date_created` datetime NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `email` varchar(255) DEFAULT NULL,
  `last_selected_year` int DEFAULT '2025',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: users
INSERT INTO `users` VALUES
('15d83e77-7daa-495b-a1af-38d8d97cae47', 'JAMES NABEL', 'james123', 'USER', 'FINANCE', '2025-07-16 09:14:35', '2025-07-26 16:53:36', 1, NULL, 2025),
('910337af-c186-4026-962d-628a3fe1621c', 'WILLIAM AKUAMOAH', '123', 'USER', 'AUDIT', '2025-07-16 09:16:36', '2025-07-26 23:12:56', 1, NULL, 2025),
('admin-default', 'SYSTEM ADMINISTRATOR', 'enter123', 'admin', 'SYSTEM ADMIN', '2025-06-19 14:21:15', '2025-07-27 14:27:32', 1, NULL, 2025),
('c52a4077-1d14-434f-afd2-55bd05087960', 'FELIX AYISI', '123', 'USER', 'FINANCE', '2025-07-01 09:50:07', '2025-07-27 11:24:05', 1, NULL, 2025),
('f0d94e86-3f5b-4ae8-8bb5-991bb78e4c1d', 'SAMUEL ASIEDU', '123', 'USER', 'AUDIT', '2025-07-01 10:03:02', '2025-07-27 00:28:08', 1, NULL, 2025);

-- Table: voucher_audit_log
DROP TABLE IF EXISTS `voucher_audit_log`;
CREATE TABLE `voucher_audit_log` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `voucher_id` varchar(50) NOT NULL,
  `action` varchar(100) NOT NULL,
  `performed_by` varchar(255) NOT NULL,
  `department` varchar(50) NOT NULL,
  `audit_reason` text,
  `changes_json` text,
  `original_values_json` text,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_performed_by` (`performed_by`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Data for table: voucher_audit_log
INSERT INTO `voucher_audit_log` VALUES
('2d7cbf3f-694e-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'TAX WRONGLY CALCULATED', '{"claimant":"DAN","description":"TEST","amount":"4500.00","preAuditedAmount":"5000.00","taxType":"GOODS 3%","taxAmount":250,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"DAN","description":"TEST","amount":"4500.00","preAuditedAmount":"5000.00","taxType":"SERVICE 7.5%","taxAmount":"600.00","preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-25 11:54:53'),
('a5cefe94-6a7f-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'JUST TRYING', '{"claimant":"TAN","description":"TEST","amount":"4500.00","preAuditedAmount":9500,"taxType":"SERVICE 7.5%","taxAmount":250,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"TAN","description":"test","amount":"4500.00","preAuditedAmount":"8000.00","taxType":null,"taxAmount":null,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-27 00:21:32'),
('b8baf5ec-6a80-11f0-8409-0a0027000013', 'FINJUL0001', 'POST_TRANSACTION_EDIT', 'SAMUEL ASIEDU', 'AUDIT', 'AM TRYING THIS OUT FOR TESTING', '{"claimant":"TAN","description":"TEST","amount":"4500.00","preAuditedAmount":15000,"taxType":"SERVICE 7.5%","taxAmount":500,"preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '{"claimant":"TAN","description":"TEST","amount":"4500.00","preAuditedAmount":"9500.00","taxType":"SERVICE 7.5%","taxAmount":"250.00","preAuditedBy":"SAMUEL ASIEDU","certifiedBy":"WILLIAM AKUAMOAH","currency":"GHS"}', '2025-07-27 00:29:13'),
('f87a2892-694d-11f0-8409-0a0027000013', 'TEST-POST-EDIT-001', 'POST_TRANSACTION_EDIT', 'WILLIAM AKUAMOAH', 'AUDIT', 'Test post-transaction edit', '{"amount":1200,"description":"Updated description via post-transaction edit","claimant":"Updated Claimant Name"}', '{"amount":"1000.00","description":"Test voucher for post-transaction edit","claimant":"Test Claimant"}', '2025-07-25 11:53:25');

-- Table: voucher_batches
DROP TABLE IF EXISTS `voucher_batches`;
CREATE TABLE `voucher_batches` (
  `id` varchar(36) NOT NULL,
  `department` varchar(50) NOT NULL,
  `sent_by` varchar(255) NOT NULL,
  `sent_time` varchar(50) NOT NULL,
  `received` tinyint(1) DEFAULT '0',
  `from_audit` tinyint(1) DEFAULT '0',
  `voucher_ids` text,
  `contains_rejected_vouchers` tinyint(1) DEFAULT '0',
  `rejected_voucher_count` int DEFAULT '0',
  `contains_rejected_copies` tinyint(1) DEFAULT '0',
  `rejected_copy_count` int DEFAULT '0',
  `normal_voucher_count` int DEFAULT '0',
  `contains_resubmissions` tinyint(1) DEFAULT '0',
  `resubmission_count` int DEFAULT '0',
  `contains_returned_vouchers` tinyint(1) DEFAULT '0',
  `returned_voucher_count` int DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: voucher_complete_history
DROP TABLE IF EXISTS `voucher_complete_history`;
undefined;

-- Table: voucher_corrections
DROP TABLE IF EXISTS `voucher_corrections`;
CREATE TABLE `voucher_corrections` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `voucher_id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `corrected_by` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `correction_time` datetime NOT NULL,
  `field_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `old_value` text COLLATE utf8mb4_unicode_ci,
  `new_value` text COLLATE utf8mb4_unicode_ci,
  `reason` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_corrected_by` (`corrected_by`),
  KEY `idx_correction_time` (`correction_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: voucher_department_visibility
DROP TABLE IF EXISTS `voucher_department_visibility`;
CREATE TABLE `voucher_department_visibility` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `department` varchar(50) NOT NULL,
  `visibility_reason` varchar(50) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_voucher_dept` (`voucher_id`,`department`),
  KEY `idx_voucher_department_visibility_voucher_id` (`voucher_id`),
  KEY `idx_voucher_department_visibility_department` (`department`),
  KEY `idx_voucher_department_visibility_created_at` (`created_at`),
  CONSTRAINT `voucher_department_visibility_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: voucher_logs
DROP TABLE IF EXISTS `voucher_logs`;
CREATE TABLE `voucher_logs` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `from_status` varchar(50) NOT NULL,
  `to_status` varchar(50) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `comment` text,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `voucher_id` (`voucher_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `voucher_logs_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `voucher_logs_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: voucher_relationships
DROP TABLE IF EXISTS `voucher_relationships`;
CREATE TABLE `voucher_relationships` (
  `id` int NOT NULL AUTO_INCREMENT,
  `original_voucher_id` varchar(255) NOT NULL,
  `related_voucher_id` varchar(255) NOT NULL,
  `relationship_type` enum('REJECTION_COPY','RESUBMISSION','CORRECTION') NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_original_voucher` (`original_voucher_id`),
  KEY `idx_related_voucher` (`related_voucher_id`),
  KEY `idx_relationship_type` (`relationship_type`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: voucher_sequences
DROP TABLE IF EXISTS `voucher_sequences`;
CREATE TABLE `voucher_sequences` (
  `department` varchar(50) NOT NULL,
  `month_year` varchar(10) NOT NULL,
  `next_number` int NOT NULL DEFAULT '1',
  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`department`),
  UNIQUE KEY `unique_dept_month` (`department`,`month_year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: vouchers
DROP TABLE IF EXISTS `vouchers`;
CREATE TABLE `vouchers` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(50) NOT NULL,
  `date` varchar(50) NOT NULL,
  `claimant` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `currency` enum('GHS','USD','GBP','EUR','CFA') NOT NULL,
  `department` varchar(50) NOT NULL,
  `dispatched_by` varchar(255) DEFAULT NULL,
  `dispatch_time` varchar(50) DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `sent_to_audit` tinyint(1) DEFAULT '0',
  `created_by` varchar(255) NOT NULL,
  `batch_id` varchar(36) DEFAULT NULL,
  `received_by` varchar(255) DEFAULT NULL,
  `receipt_time` varchar(50) DEFAULT NULL,
  `comment` text,
  `tax_type` varchar(50) DEFAULT NULL,
  `tax_details` text,
  `tax_amount` decimal(15,2) DEFAULT NULL,
  `pre_audited_amount` decimal(15,2) DEFAULT NULL,
  `pre_audited_by` varchar(255) DEFAULT NULL,
  `certified_by` varchar(255) DEFAULT NULL,
  `audit_dispatch_time` varchar(50) DEFAULT NULL,
  `audit_dispatched_by` varchar(255) DEFAULT NULL,
  `dispatch_to_on_department` tinyint(1) DEFAULT '0',
  `post_provisional_cash` tinyint(1) DEFAULT '0',
  `dispatched` tinyint(1) DEFAULT '0',
  `dispatch_to_audit_by` varchar(255) DEFAULT NULL,
  `is_returned` tinyint(1) DEFAULT '0',
  `return_comment` text,
  `return_time` varchar(50) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT '0',
  `deletion_time` varchar(50) DEFAULT NULL,
  `rejection_time` varchar(50) DEFAULT NULL,
  `department_receipt_time` varchar(50) DEFAULT NULL,
  `department_received_by` varchar(255) DEFAULT NULL,
  `department_rejected` tinyint(1) DEFAULT '0',
  `rejected_by` varchar(255) DEFAULT NULL,
  `pending_return` tinyint(1) DEFAULT '0',
  `return_initiated_time` varchar(50) DEFAULT NULL,
  `reference_id` varchar(50) DEFAULT NULL,
  `idempotency_key` varchar(255) DEFAULT NULL,
  `flags` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `original_department` varchar(50) DEFAULT NULL,
  `received_by_audit` tinyint(1) DEFAULT '0',
  `work_started` tinyint(1) DEFAULT '0',
  `batch_processed` tinyint(1) DEFAULT '0',
  `batch_processed_time` timestamp NULL DEFAULT NULL,
  `rejection_type` varchar(50) DEFAULT NULL,
  `parent_voucher_id` varchar(255) DEFAULT NULL,
  `version` int NOT NULL DEFAULT '1',
  `is_rejection_copy` tinyint(1) DEFAULT '0',
  `rejection_workflow_stage` enum('INITIAL','REJECTED_DUAL','DISPATCHED','RECEIVED_BY_FINANCE','RESUBMITTED','RESUBMITTED_CERTIFIED','PENDING_DISPATCH','FINANCE_PERMANENT','DISPATCHED_TO_FINANCE') DEFAULT 'INITIAL',
  `correction_count` int DEFAULT '0',
  `last_corrected_by` varchar(255) DEFAULT NULL,
  `last_correction_time` datetime DEFAULT NULL,
  `correction_reason` text,
  `is_corrected` tinyint(1) DEFAULT '0',
  `resubmission_count` int DEFAULT '0',
  `original_rejection_date` datetime DEFAULT NULL COMMENT 'Date when this voucher was first rejected',
  `resubmission_history` json DEFAULT NULL COMMENT 'History of resubmissions with dates and reasons',
  `resubmission_badge` varchar(100) DEFAULT NULL COMMENT 'Badge text to display for resubmitted vouchers',
  `original_rejection_reason` text,
  `resubmission_status` varchar(255) DEFAULT NULL,
  `original_rejected_by` varchar(255) DEFAULT NULL,
  `last_resubmitted_by` varchar(255) DEFAULT NULL,
  `last_resubmission_date` datetime DEFAULT NULL,
  `resubmission_comment` text,
  `is_resubmitted_voucher` tinyint(1) DEFAULT '0',
  `workflow_state` varchar(100) DEFAULT NULL,
  `badge_type` varchar(20) DEFAULT 'NONE',
  `is_copy` tinyint(1) DEFAULT '0',
  `workflow_history` json DEFAULT NULL,
  `rejection_audit_trail` json DEFAULT NULL,
  `resubmission_audit_trail` json DEFAULT NULL,
  `is_rejected` tinyint(1) DEFAULT '0',
  `original_voucher_id` varchar(255) DEFAULT NULL,
  `voucher_type` enum('ORIGINAL','COPY') DEFAULT 'ORIGINAL',
  `is_returned_copy` tinyint(1) DEFAULT '0',
  `returned_by` varchar(255) DEFAULT NULL,
  `is_resubmitted` tinyint(1) DEFAULT '0',
  `is_resubmission` tinyint(1) DEFAULT '0',
  `finance_received` tinyint(1) DEFAULT '0' COMMENT 'Track if Finance has received the voucher from Audit',
  `resubmission_certified_visible_to_finance` tinyint(1) DEFAULT '0',
  `resubmission_tracking_visible_to_audit` tinyint(1) DEFAULT '0',
  `badge_persistence_flags` json DEFAULT NULL,
  `is_returned_voucher` tinyint(1) DEFAULT '0',
  `return_count` int DEFAULT '0',
  `last_return_date` datetime DEFAULT NULL,
  `return_certified_visible_to_finance` tinyint(1) DEFAULT '0',
  `return_audit_visible` tinyint(1) DEFAULT '0',
  `original_return_reason` text,
  `original_returned_by` varchar(255) DEFAULT NULL,
  `last_edit_reason` text,
  `last_edited_by` varchar(255) DEFAULT NULL,
  `last_edit_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `voucher_id` (`voucher_id`),
  KEY `idx_idempotency` (`created_by`,`department`,`idempotency_key`),
  KEY `idx_vouchers_rejection_type` (`rejection_type`),
  KEY `idx_vouchers_parent_voucher_id` (`parent_voucher_id`),
  KEY `idx_vouchers_rejection_copy` (`is_rejection_copy`),
  KEY `idx_vouchers_workflow_stage` (`rejection_workflow_stage`),
  KEY `idx_vouchers_dept_status_rejection` (`department`,`status`,`rejection_type`),
  KEY `idx_vouchers_orig_dept_status` (`original_department`,`status`),
  KEY `idx_vouchers_workflow_stage_dept` (`rejection_workflow_stage`,`department`),
  KEY `idx_vouchers_correction_count` (`correction_count`),
  KEY `idx_vouchers_is_corrected` (`is_corrected`),
  KEY `idx_vouchers_last_correction_time` (`last_correction_time`),
  KEY `idx_vouchers_resubmission_count` (`resubmission_count`),
  KEY `idx_vouchers_original_rejection_date` (`original_rejection_date`),
  KEY `idx_vouchers_workflow_state` (`workflow_state`),
  KEY `idx_vouchers_parent_id` (`parent_voucher_id`),
  KEY `idx_vouchers_workflow_department` (`workflow_state`,`department`),
  KEY `idx_vouchers_workflow_original_dept` (`workflow_state`,`original_department`),
  KEY `idx_vouchers_badge_type` (`badge_type`),
  KEY `idx_vouchers_is_copy` (`is_copy`),
  KEY `idx_vouchers_version` (`version`),
  KEY `idx_vouchers_is_resubmitted` (`is_resubmitted`),
  KEY `idx_vouchers_resubmission_visibility` (`resubmission_certified_visible_to_finance`,`resubmission_tracking_visible_to_audit`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: workflow_audit_log
DROP TABLE IF EXISTS `workflow_audit_log`;
CREATE TABLE `workflow_audit_log` (
  `id` varchar(36) NOT NULL,
  `voucher_id` varchar(36) NOT NULL,
  `from_state` varchar(50) NOT NULL,
  `to_state` varchar(50) NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `copy_id` varchar(36) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_voucher_id` (`voucher_id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_user_id` (`user_id`),
  KEY `copy_id` (`copy_id`),
  CONSTRAINT `workflow_audit_log_ibfk_1` FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `workflow_audit_log_ibfk_2` FOREIGN KEY (`copy_id`) REFERENCES `vouchers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: workflow_transitions
DROP TABLE IF EXISTS `workflow_transitions`;
CREATE TABLE `workflow_transitions` (
  `id` varchar(36) NOT NULL,
  `from_state` varchar(50) NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `to_state` varchar(50) NOT NULL,
  `requires_copy` tinyint(1) DEFAULT '0',
  `copy_state` varchar(50) DEFAULT NULL,
  `badge_type` varchar(20) DEFAULT 'NONE',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_transition` (`from_state`,`event_type`),
  KEY `idx_from_state` (`from_state`),
  KEY `idx_event_type` (`event_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

SET FOREIGN_KEY_CHECKS = 1;
