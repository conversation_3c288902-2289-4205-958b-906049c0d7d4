/**
 * PRODUCTION-READY Configuration Management System
 * Handles environment-specific configurations, secrets, and dynamic updates
 */
export interface ConfigSchema {
    server: {
        port: number;
        host: string;
        environment: 'development' | 'staging' | 'production';
        cors: {
            origin: string | string[];
            credentials: boolean;
        };
        rateLimit: {
            windowMs: number;
            maxRequests: number;
        };
    };
    database: {
        host: string;
        port: number;
        user: string;
        password: string;
        database: string;
        connectionLimit: number;
        timeout: number;
    };
    websocket: {
        port: number;
        pingTimeout: number;
        pingInterval: number;
        transports: string[];
    };
    security: {
        sessionSecret: string;
        sessionTimeout: number;
        bcryptRounds: number;
    };
    logging: {
        level: 'error' | 'warn' | 'info' | 'debug';
        file: boolean;
        console: boolean;
        maxFiles: number;
        maxSize: string;
    };
    features: {
        enableAuditLog: boolean;
        enableNotifications: boolean;
        enableBackup: boolean;
        enableMetrics: boolean;
    };
    business: {
        fiscalYearStart: string;
        defaultCurrency: string;
        supportedCurrencies: string[];
        departments: string[];
        userRoles: string[];
    };
}
export declare class ConfigManager {
    private config;
    private watchers;
    private secrets;
    constructor();
    /**
     * Load configuration from environment and defaults
     */
    private loadConfiguration;
    /**
     * Parse CORS origin configuration
     */
    private parseCorsOrigin;
    /**
     * Load secrets from secure storage or environment
     */
    private loadSecrets;
    /**
     * Validate configuration
     */
    private validateConfiguration;
    /**
     * Get configuration value
     */
    get<K extends keyof ConfigSchema>(key: K): ConfigSchema[K];
    get<K extends keyof ConfigSchema, SK extends keyof ConfigSchema[K]>(key: K, subKey: SK): ConfigSchema[K][SK];
    /**
     * Get secret value
     */
    getSecret(key: string): string | undefined;
    /**
     * Set configuration value (for dynamic updates)
     */
    set<K extends keyof ConfigSchema>(key: K, value: ConfigSchema[K]): void;
    set<K extends keyof ConfigSchema, SK extends keyof ConfigSchema[K]>(key: K, subKey: SK, value: ConfigSchema[K][SK]): void;
    /**
     * Watch for configuration changes
     */
    watch(key: string, callback: (value: any) => void): () => void;
    /**
     * Notify watchers of configuration changes
     */
    private notifyWatchers;
    /**
     * Get all configuration (for debugging - secrets masked)
     */
    getAll(): any;
    /**
     * Reload configuration from environment
     */
    reload(): void;
    /**
     * Detect and notify configuration changes
     */
    private detectAndNotifyChanges;
    /**
     * Export configuration for external services
     */
    export(): {
        database: any;
        server: any;
        websocket: any;
        features: any;
    };
    /**
     * Check if feature is enabled
     */
    isFeatureEnabled(feature: keyof ConfigSchema['features']): boolean;
    /**
     * Get environment
     */
    getEnvironment(): 'development' | 'staging' | 'production';
    /**
     * Check if running in production
     */
    isProduction(): boolean;
    /**
     * Check if running in development
     */
    isDevelopment(): boolean;
}
export declare const configManager: ConfigManager;
