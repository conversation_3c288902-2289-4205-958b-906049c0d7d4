/**
 * ENTERPRISE DATABASE CONFIGURATION
 *
 * Production-ready database configuration that addresses all real-world failure scenarios:
 * - Connection pool exhaustion protection
 * - Network resilience with comprehensive timeouts
 * - Performance optimization settings
 * - Health monitoring configuration
 * - Environment-specific settings
 */
export interface DatabaseConfig {
    host: string;
    port: number;
    user: string;
    password: string;
    database: string;
    connectionLimit: number;
    queueLimit: number;
    acquireTimeout: number;
    timeout: number;
    reconnectTimeout: number;
    maxReconnectAttempts: number;
    reconnectDelay: number;
    healthCheckInterval: number;
    slowQueryThreshold: number;
    charset: string;
    timezone: string;
}
/**
 * Production Database Configuration
 *
 * Optimized for high-availability production environments
 */
export declare const productionDatabaseConfig: DatabaseConfig;
/**
 * Development Database Configuration
 *
 * Optimized for development with faster timeouts and more verbose monitoring
 */
export declare const developmentDatabaseConfig: DatabaseConfig;
/**
 * High-Performance Database Configuration
 *
 * For high-load production environments
 */
export declare const highPerformanceDatabaseConfig: DatabaseConfig;
/**
 * Get database configuration based on environment
 */
export declare function getDatabaseConfig(): DatabaseConfig;
/**
 * Validate database configuration
 */
export declare function validateDatabaseConfig(config: DatabaseConfig): boolean;
/**
 * Database connection string builder for external tools
 */
export declare function buildConnectionString(config: DatabaseConfig): string;
/**
 * Database configuration summary for logging
 */
export declare function getConfigSummary(config: DatabaseConfig): any;
/**
 * Environment-specific database optimizations
 */
export declare const databaseOptimizations: {
    production: {
        enableSSL: boolean;
        enableCompression: boolean;
        enableCaching: boolean;
        logSlowQueries: boolean;
        enableMetrics: boolean;
    };
    development: {
        enableSSL: boolean;
        enableCompression: boolean;
        enableCaching: boolean;
        logSlowQueries: boolean;
        enableMetrics: boolean;
        verboseLogging: boolean;
    };
    testing: {
        enableSSL: boolean;
        enableCompression: boolean;
        enableCaching: boolean;
        logSlowQueries: boolean;
        enableMetrics: boolean;
        fastFailover: boolean;
    };
};
/**
 * Get optimizations for current environment
 */
export declare function getDatabaseOptimizations(): any;
