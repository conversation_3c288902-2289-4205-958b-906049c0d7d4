"use strict";
/**
 * ENTERPRISE DATABASE CONFIGURATION
 *
 * Production-ready database configuration that addresses all real-world failure scenarios:
 * - Connection pool exhaustion protection
 * - Network resilience with comprehensive timeouts
 * - Performance optimization settings
 * - Health monitoring configuration
 * - Environment-specific settings
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseOptimizations = exports.highPerformanceDatabaseConfig = exports.developmentDatabaseConfig = exports.productionDatabaseConfig = void 0;
exports.getDatabaseConfig = getDatabaseConfig;
exports.validateDatabaseConfig = validateDatabaseConfig;
exports.buildConnectionString = buildConnectionString;
exports.getConfigSummary = getConfigSummary;
exports.getDatabaseOptimizations = getDatabaseOptimizations;
/**
 * Production Database Configuration
 *
 * Optimized for high-availability production environments
 */
exports.productionDatabaseConfig = {
    // Connection settings
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'vms@2025@1989',
    database: process.env.DB_NAME || 'vms_production',
    // ENTERPRISE: Pool configuration with overflow protection
    connectionLimit: 50, // Increased from 20 to handle more concurrent users
    queueLimit: 100, // Allow queuing instead of immediate rejection
    acquireTimeout: 30000, // 30 seconds to get a connection
    timeout: 60000, // 60 seconds for query execution
    // ENTERPRISE: Network resilience settings
    reconnectTimeout: 10000, // 10 seconds for reconnection attempts
    maxReconnectAttempts: 10, // More attempts for production
    reconnectDelay: 2000, // 2 seconds base delay
    // ENTERPRISE: Health monitoring
    healthCheckInterval: 30000, // Health check every 30 seconds
    slowQueryThreshold: 5000, // Queries slower than 5 seconds are flagged
    // ENTERPRISE: Performance settings
    charset: 'utf8mb4',
    timezone: '+00:00'
};
/**
 * Development Database Configuration
 *
 * Optimized for development with faster timeouts and more verbose monitoring
 */
exports.developmentDatabaseConfig = {
    // Connection settings
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3307'), // Different port for dev
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'vms@2025@1989',
    database: process.env.DB_NAME || 'vms_development',
    // Development: Smaller pool for local development
    connectionLimit: 10, // Smaller pool for development
    queueLimit: 20, // Smaller queue
    acquireTimeout: 10000, // 10 seconds - faster timeout
    timeout: 30000, // 30 seconds - faster timeout
    // Development: Faster reconnection for quick iteration
    reconnectTimeout: 5000, // 5 seconds
    maxReconnectAttempts: 5, // Fewer attempts
    reconnectDelay: 1000, // 1 second delay
    // Development: More frequent health checks for debugging
    healthCheckInterval: 15000, // Every 15 seconds
    slowQueryThreshold: 2000, // 2 seconds threshold for development
    // Performance settings
    charset: 'utf8mb4',
    timezone: '+00:00'
};
/**
 * High-Performance Database Configuration
 *
 * For high-load production environments
 */
exports.highPerformanceDatabaseConfig = {
    // Connection settings
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'vms@2025@1989',
    database: process.env.DB_NAME || 'vms_production',
    // HIGH-PERFORMANCE: Large pool for high concurrency
    connectionLimit: 100, // Large pool for high load
    queueLimit: 200, // Large queue
    acquireTimeout: 45000, // Extended timeout for high load
    timeout: 90000, // Extended query timeout
    // HIGH-PERFORMANCE: Aggressive reconnection
    reconnectTimeout: 15000, // 15 seconds
    maxReconnectAttempts: 15, // More attempts
    reconnectDelay: 1000, // Fast reconnection
    // HIGH-PERFORMANCE: Frequent monitoring
    healthCheckInterval: 20000, // Every 20 seconds
    slowQueryThreshold: 10000, // 10 seconds threshold for high-performance
    // Performance settings
    charset: 'utf8mb4',
    timezone: '+00:00'
};
/**
 * Get database configuration based on environment
 */
function getDatabaseConfig() {
    const environment = process.env.NODE_ENV || 'development';
    const performanceMode = process.env.DB_PERFORMANCE_MODE || 'standard';
    switch (environment) {
        case 'production':
            if (performanceMode === 'high') {
                return exports.highPerformanceDatabaseConfig;
            }
            return exports.productionDatabaseConfig;
        case 'development':
        case 'dev':
            return exports.developmentDatabaseConfig;
        case 'staging':
        case 'test':
            // Use production config but with development timeouts
            return {
                ...exports.productionDatabaseConfig,
                acquireTimeout: exports.developmentDatabaseConfig.acquireTimeout,
                timeout: exports.developmentDatabaseConfig.timeout,
                healthCheckInterval: exports.developmentDatabaseConfig.healthCheckInterval
            };
        default:
            return exports.developmentDatabaseConfig;
    }
}
/**
 * Validate database configuration
 */
function validateDatabaseConfig(config) {
    try {
        // Validate required fields
        if (!config.host || !config.user || !config.database) {
            throw new Error('Missing required database configuration fields');
        }
        // Validate port
        if (config.port < 1 || config.port > 65535) {
            throw new Error('Invalid database port number');
        }
        // Validate pool settings
        if (config.connectionLimit < 1 || config.connectionLimit > 1000) {
            throw new Error('Connection limit must be between 1 and 1000');
        }
        if (config.queueLimit < 0 || config.queueLimit > 10000) {
            throw new Error('Queue limit must be between 0 and 10000');
        }
        // Validate timeouts
        if (config.acquireTimeout < 1000 || config.acquireTimeout > 300000) {
            throw new Error('Acquire timeout must be between 1-300 seconds');
        }
        if (config.timeout < 1000 || config.timeout > 600000) {
            throw new Error('Query timeout must be between 1-600 seconds');
        }
        // Validate reconnection settings
        if (config.maxReconnectAttempts < 1 || config.maxReconnectAttempts > 100) {
            throw new Error('Max reconnect attempts must be between 1 and 100');
        }
        if (config.reconnectDelay < 100 || config.reconnectDelay > 60000) {
            throw new Error('Reconnect delay must be between 100ms and 60 seconds');
        }
        // Validate monitoring settings
        if (config.healthCheckInterval < 5000 || config.healthCheckInterval > 300000) {
            throw new Error('Health check interval must be between 5-300 seconds');
        }
        if (config.slowQueryThreshold < 100 || config.slowQueryThreshold > 300000) {
            throw new Error('Slow query threshold must be between 100ms and 300 seconds');
        }
        return true;
    }
    catch (error) {
        console.error('Database configuration validation failed:', error?.message || 'Unknown error');
        return false;
    }
}
/**
 * Database connection string builder for external tools
 */
function buildConnectionString(config) {
    return `mysql://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}`;
}
/**
 * Database configuration summary for logging
 */
function getConfigSummary(config) {
    return {
        host: config.host,
        port: config.port,
        database: config.database,
        connectionLimit: config.connectionLimit,
        queueLimit: config.queueLimit,
        acquireTimeout: config.acquireTimeout,
        timeout: config.timeout,
        healthCheckInterval: config.healthCheckInterval,
        slowQueryThreshold: config.slowQueryThreshold
    };
}
/**
 * Environment-specific database optimizations
 */
exports.databaseOptimizations = {
    production: {
        // Production optimizations
        enableSSL: true,
        enableCompression: true,
        enableCaching: true,
        logSlowQueries: true,
        enableMetrics: true
    },
    development: {
        // Development optimizations
        enableSSL: false,
        enableCompression: false,
        enableCaching: false,
        logSlowQueries: true,
        enableMetrics: true,
        verboseLogging: true
    },
    testing: {
        // Testing optimizations
        enableSSL: false,
        enableCompression: false,
        enableCaching: false,
        logSlowQueries: false,
        enableMetrics: false,
        fastFailover: true
    }
};
/**
 * Get optimizations for current environment
 */
function getDatabaseOptimizations() {
    const environment = process.env.NODE_ENV || 'development';
    return exports.databaseOptimizations[environment] || exports.databaseOptimizations.development;
}
//# sourceMappingURL=database.config.js.map