import mysql from 'mysql2/promise';
/**
 * PRODUCTION-READY Database Manager
 * Handles connection pooling, health monitoring, automatic recovery, and transaction management
 */
export declare class DatabaseManager {
    private pool;
    private isConnected;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    private healthCheckInterval;
    private connectionConfig;
    constructor();
    /**
     * Initialize database connection with automatic retry
     */
    initialize(): Promise<void>;
    /**
     * Create connection pool with error handling
     */
    private createPool;
    /**
     * Test database connection
     */
    private testConnection;
    /**
     * Setup database and tables if needed
     */
    private setupDatabase;
    /**
     * Create database if it doesn't exist
     */
    private createDatabaseIfNotExists;
    /**
     * Verify essential tables exist
     */
    private verifyTables;
    /**
     * ENHANCED: Execute query with automatic retry and comprehensive error handling
     */
    query<T = any>(sql: string, params?: any[], retryCount?: number): Promise<T[]>;
    /**
     * ENHANCED: Execute transaction with automatic rollback and comprehensive error handling
     */
    withTransaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>, retryCount?: number): Promise<T>;
    /**
     * Get database health status
     */
    getHealthStatus(): Promise<{
        connected: boolean;
        poolStatus: any;
        uptime: number;
    }>;
    /**
     * CRITICAL FIX: Disabled periodic health checks to prevent infinite loops
     */
    private startHealthCheck;
    /**
     * ENHANCED: Handle connection failures with exponential backoff and circuit breaker
     */
    private handleConnectionFailure;
    /**
     * Check if error is connection-related
     */
    private isConnectionError;
    /**
     * Setup graceful shutdown
     */
    private setupGracefulShutdown;
    /**
     * MONITORING: Get connection pool statistics for production monitoring
     */
    getPoolStats(): {
        error: string;
        totalConnections?: undefined;
        freeConnections?: undefined;
        acquiringConnections?: undefined;
        queuedRequests?: undefined;
        connectionLimit?: undefined;
        queueLimit?: undefined;
    } | {
        totalConnections: any;
        freeConnections: any;
        acquiringConnections: any;
        queuedRequests: any;
        connectionLimit: number;
        queueLimit: number;
        error?: undefined;
    };
    /**
     * MONITORING: Log pool health for production monitoring
     */
    logPoolHealth(): void;
    /**
     * Get connection status
     */
    isHealthy(): boolean;
}
export declare const databaseManager: DatabaseManager;
