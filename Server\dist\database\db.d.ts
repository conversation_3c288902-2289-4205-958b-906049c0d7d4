import * as mysql from 'mysql2/promise';
declare const pool: mysql.Pool;
export declare function initializeDatabase(): Promise<boolean>;
export declare function withTransaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>): Promise<T>;
export declare function query<T = any>(sql: string, params?: any[]): Promise<T[]>;
export declare function getTransaction(): Promise<mysql.PoolConnection>;
export default pool;
