{"version": 3, "file": "year-manager.js", "sourceRoot": "", "sources": ["../../src/database/year-manager.ts"], "names": [], "mappings": ";;;;;;AA8QA,gDAEC;AAGD,wDASC;AA5RD,6DAAmC;AACnC,kDAA4C;AAa5C,MAAM,mBAAmB;IACf,KAAK,GAA4B,IAAI,GAAG,EAAE,CAAC;IAC3C,UAAU,CAAqB;IAEvC;QACE,IAAI,CAAC,UAAU,GAAG;YAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;YACxC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,CAAC;YAC7C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM;YACnC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,eAAe;YACpD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,gBAAgB;YACjD,kBAAkB,EAAE,IAAI;YACxB,eAAe,EAAE,EAAE,EAAE,wBAAwB;YAC7C,UAAU,EAAE,CAAC;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,IAAY;QAClC,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC7C,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,qCAAqC;QACxE,CAAC;QACD,OAAO,OAAO,IAAI,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,IAAY;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;QACjC,CAAC;QAED,gCAAgC;QAChC,MAAM,MAAM,GAAG;YACb,GAAG,IAAI,CAAC,UAAU;YAClB,QAAQ,EAAE,MAAM;SACjB,CAAC;QAEF,MAAM,IAAI,GAAG,iBAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE7B,kBAAM,CAAC,IAAI,CAAC,oCAAoC,IAAI,eAAe,MAAM,GAAG,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,IAAY,EAAE,GAAW,EAAE,SAAgB,EAAE;QAC3D,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAClD,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kBAAM,CAAC,KAAK,CAAC,iCAAiC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,OAAY,EAAE,GAAW,EAAE,SAAgB,EAAE;QAClE,MAAM,IAAI,GAAG,OAAO,EAAE,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC/D,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,IAAY;QACnC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,iCAAiC;YAEhG,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAClC,2EAA2E,EAC3E,CAAC,MAAM,CAAC,CACT,CAAC;YAEF,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kBAAM,CAAC,KAAK,CAAC,0BAA0B,IAAI,mBAAmB,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAU,CAAC;YAEhE,MAAM,KAAK,GAAa,EAAE,CAAC;YAE3B,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;gBAC1B,MAAM,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC;gBAC5B,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC5D,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;oBAClD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;wBACjB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACnB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,8BAA8B;YAC9B,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACjC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1B,CAAC;YAED,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kBAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,2BAA2B;QAChE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,IAAY;QACnC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;YAE9D,kBAAkB;YAClB,MAAM,IAAI,CAAC,OAAO,CAAC,iCAAiC,MAAM,EAAE,CAAC,CAAC;YAE9D,wCAAwC;YACxC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAE7C,yCAAyC;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC3C,MAAM,MAAM,GAAG;gBACb,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,gBAAgB;gBACxD,0BAA0B,EAAE,eAAe,EAAE,YAAY;gBACzD,cAAc,EAAE,iBAAiB;aAClC,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,IAAI,CAAC;oBACH,MAAM,CAAC,aAAa,CAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,qBAAqB,SAAS,IAAI,KAAK,EAAE,CAAU,CAAC;oBAC/F,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC7B,MAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;wBACnD,MAAM,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,kBAAM,CAAC,IAAI,CAAC,wBAAwB,KAAK,YAAY,IAAI,GAAG,EAAE,UAAU,CAAC,CAAC;gBAC5E,CAAC;YACH,CAAC;YAED,wDAAwD;YACxD,MAAM,OAAO,CAAC,OAAO,CAAC,0CAA0C,SAAS,QAAQ,CAAC,CAAC;YAEnF,kBAAM,CAAC,IAAI,CAAC,uCAAuC,MAAM,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kBAAM,CAAC,KAAK,CAAC,uBAAuB,IAAI,YAAY,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAC9E,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,kBAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,IAAY;QAClC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;oBACL,IAAI;oBACJ,YAAY,EAAE,CAAC;oBACf,WAAW,EAAE,CAAC;oBACd,WAAW,EAAE,EAAE;oBACf,QAAQ,EAAE,KAAK;oBACf,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACvC,CAAC;YACJ,CAAC;YAED,yBAAyB;YACzB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;;;;;OAK/C,CAAC,CAAC;YAEH,kBAAkB;YAClB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;;;;;OAK9C,CAAC,CAAC;YAEH,oBAAoB;YACpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;;;OAG/C,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;YACjC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;YAEpD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;;;;OAIjD,EAAE,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAElC,OAAO;gBACL,IAAI;gBACJ,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,CAAC;gBACjD,WAAW,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,IAAI,GAAG,CAAC;gBAC7D,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC;gBACtD,QAAQ,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC;gBACpD,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACzE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kBAAM,CAAC,KAAK,CAAC,qCAAqC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO;gBACL,IAAI;gBACJ,YAAY,EAAE,CAAC;gBACf,WAAW,EAAE,CAAC;gBACd,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,KAAK;gBACf,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACvC,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAED,4BAA4B;AACf,QAAA,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC;AAE7D,wCAAwC;AACxC,SAAgB,kBAAkB,CAAC,OAAY;IAC7C,OAAO,OAAO,EAAE,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;AAC3D,CAAC;AAED,2DAA2D;AAC3D,SAAgB,sBAAsB,CAAC,OAAY;IACjD,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACzC,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAE7C,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;QACzB,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,gBAAgB,CAAC;IACjD,CAAC;IAED,OAAO,OAAO,IAAI,EAAE,CAAC;AACvB,CAAC"}