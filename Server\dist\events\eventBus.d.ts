import { EventEmitter } from 'events';
/**
 * Centralized Event Bus for VMS System
 * Eliminates circular dependencies by providing a single event hub
 */
declare class VMSEventBus extends EventEmitter {
    private static instance;
    private constructor();
    static getInstance(): VMSEventBus;
    emitBatchCreated(batchData: any): void;
    emitBatchUpdated(batchData: any): void;
    emitBatchReceived(batchData: any): void;
    emitNotificationCreated(notificationData: any): void;
    emitVoucherUpdated(voucherData: any): void;
    emitDataUpdate(entityType: string, actionType: string, data: any): void;
}
export declare const eventBus: VMSEventBus;
export interface BatchEvent {
    id: string;
    department: string;
    sent_by: string;
    vouchers: any[];
    voucherIds: string[];
}
export interface NotificationEvent {
    id: string;
    user_id: string;
    message: string;
    is_read: boolean;
    timestamp: string;
    batch_id?: string;
    voucher_id?: string;
    type: string;
    from_audit?: boolean;
}
export interface VoucherEvent {
    id: string;
    voucher_id: string;
    status: string;
    department: string;
}
export {};
