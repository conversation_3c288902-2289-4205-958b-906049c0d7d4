"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.eventBus = void 0;
const events_1 = require("events");
/**
 * Centralized Event Bus for VMS System
 * Eliminates circular dependencies by providing a single event hub
 */
class VMSEventBus extends events_1.EventEmitter {
    static instance;
    constructor() {
        super();
        this.setMaxListeners(50); // Increase limit for production use
        console.log('🎯 VMS Event Bus initialized');
    }
    static getInstance() {
        if (!VMSEventBus.instance) {
            VMSEventBus.instance = new VMSEventBus();
        }
        return VMSEventBus.instance;
    }
    // Batch Events
    emitBatchCreated(batchData) {
        console.log(`📡 Event: Batch created - ${batchData.id}`);
        this.emit('batch:created', batchData);
    }
    emitBatchUpdated(batchData) {
        console.log(`📡 Event: Batch updated - ${batchData.id}`);
        this.emit('batch:updated', batchData);
    }
    emitBatchReceived(batchData) {
        console.log(`📡 Event: Batch received - ${batchData.id}`);
        this.emit('batch:received', batchData);
    }
    // Notification Events
    emitNotificationCreated(notificationData) {
        console.log(`📡 Event: Notification created for ${notificationData.user_id}`);
        this.emit('notification:created', notificationData);
    }
    // Voucher Events
    emitVoucherUpdated(voucherData) {
        console.log(`📡 Event: Voucher updated - ${voucherData.id}`);
        this.emit('voucher:updated', voucherData);
    }
    // Generic data events
    emitDataUpdate(entityType, actionType, data) {
        console.log(`📡 Event: ${entityType} ${actionType} - ${data.id || 'unknown'}`);
        this.emit('data:update', { entityType, actionType, data });
    }
}
// Export singleton instance
exports.eventBus = VMSEventBus.getInstance();
//# sourceMappingURL=eventBus.js.map