/**
 * ZERO-DEPENDENCY EVENT BUS
 * Completely isolated from all other modules to prevent circular dependencies
 */
type EventCallback = (...args: any[]) => void;
declare class SimpleEventBus {
    private static instance;
    private listeners;
    private constructor();
    static getInstance(): SimpleEventBus;
    on(event: string, callback: EventCallback): void;
    emit(event: string, ...args: any[]): void;
    off(event: string, callback: EventCallback): void;
    emitBatchCreated(batchData: any): void;
    emitBatchUpdated(batchData: any): void;
    emitBatchReceived(batchData: any): void;
    emitNotificationCreated(notificationData: any): void;
    emitVoucherUpdated(voucherData: any): void;
}
export declare const simpleEventBus: SimpleEventBus;
export {};
