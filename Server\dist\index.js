"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const http_1 = __importDefault(require("http"));
const path_1 = __importDefault(require("path"));
const socket_io_1 = require("socket.io");
const cors_1 = __importDefault(require("cors"));
const morgan_1 = __importDefault(require("morgan"));
const cookie_parser_1 = __importDefault(require("cookie-parser")); // FIXED: Add cookie parser
const dotenv_1 = __importDefault(require("dotenv"));
// PRODUCTION-GRADE: Enterprise imports with bulletproof error handling
const db_1 = require("./database/db");
const db_2 = __importDefault(require("./database/db"));
const DatabaseManager_1 = require("./database/DatabaseManager");
const logger_1 = require("./utils/logger");
const index_1 = require("./routes/index");
const socketHandlers_1 = require("./socket/socketHandlers");
const ServiceDiscovery_1 = require("./utils/ServiceDiscovery");
const workflow_service_1 = require("./services/workflow-service");
const file_storage_1 = require("./utils/file-storage");
const backup_scheduler_1 = require("./services/backup-scheduler");
// REMOVED: Duplicate system import
// REMOVED: Duplicate system import
// Load environment variables
dotenv_1.default.config();
// PRODUCTION-GRADE: Enterprise configuration with environment detection
const app = (0, express_1.default)();
const PREFERRED_PORT = parseInt(process.env.PREFERRED_PORT || process.env.PORT || '8080');
const NODE_ENV = process.env.NODE_ENV || 'development';
// DYNAMIC CORS: Allow all origins for LAN deployment with service discovery
const corsOrigins = '*'; // Allow all origins for dynamic LAN deployment
// PRODUCTION-GRADE: Enterprise middleware with security hardening
app.use((0, cors_1.default)({
    origin: corsOrigins,
    credentials: true, // FIXED: Enable credentials for session-based auth
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Cache-Control', // FIXED: Add missing cache-control header
        'Pragma',
        'Expires',
        'Accept',
        'Accept-Language',
        'Accept-Encoding'
    ],
    exposedHeaders: ['Set-Cookie'], // FIXED: Expose cookies for session management
    optionsSuccessStatus: 200, // For legacy browser support
    maxAge: 86400 // Cache preflight for 24 hours
}));
// PRODUCTION-GRADE: Complete HTTP-only configuration - NO SECURITY HEADERS
// This is specifically designed for HTTP-only LAN deployment
// DO NOT USE THIS CONFIGURATION FOR INTERNET-FACING SERVERS
// COMPLETELY DISABLE HELMET - No security headers at all
// app.use(helmet()) - DISABLED FOR HTTP-ONLY DEPLOYMENT
// PRODUCTION-GRADE: Aggressive HTTP enforcement middleware
app.use((req, res, next) => {
    // PRODUCTION-SAFE: Remove only HTTPS-enforcing headers
    res.removeHeader('Strict-Transport-Security');
    res.removeHeader('Content-Security-Policy');
    res.removeHeader('Upgrade-Insecure-Requests');
    // PRODUCTION-SAFE: Essential headers only
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'SAMEORIGIN');
    next();
});
app.use((0, morgan_1.default)(NODE_ENV === 'production' ? 'combined' : 'dev', {
    stream: { write: (message) => logger_1.logger.info(message.trim()) }
}));
app.use(express_1.default.json({
    limit: '10mb',
    verify: (req, res, buf) => {
        // Add request validation for production
        if (buf.length > 10 * 1024 * 1024) {
            throw new Error('Request entity too large');
        }
    }
}));
app.use(express_1.default.urlencoded({
    extended: true,
    limit: '10mb',
    parameterLimit: 1000
}));
// FIXED: Add cookie parser for session management
app.use((0, cookie_parser_1.default)());
// PRODUCTION-GRADE: Request tracking and rate limiting
app.use((req, res, next) => {
    req.startTime = Date.now();
    res.on('finish', () => {
        const duration = Date.now() - req.startTime;
        if (duration > 5000) { // Log slow requests
            logger_1.logger.warn(`Slow request: ${req.method} ${req.url} took ${duration}ms`);
        }
    });
    next();
});
// PRODUCTION-GRADE: Serve built frontend static files with HTTP-optimized headers
app.use(express_1.default.static(path_1.default.join(__dirname, '../public'), {
    // Production-grade static file serving
    maxAge: '1d', // Cache static assets for 1 day
    etag: true, // Enable ETags for caching
    lastModified: true, // Enable Last-Modified headers
    index: ['index.html'], // Serve index.html for directory requests
    // HTTP-specific configuration
    setHeaders: (res, path, stat) => {
        // Force HTTP mode for all static assets
        res.removeHeader('Strict-Transport-Security');
        res.removeHeader('Upgrade-Insecure-Requests');
        // Set proper MIME types to prevent browser confusion
        if (path.endsWith('.js')) {
            res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
        }
        else if (path.endsWith('.css')) {
            res.setHeader('Content-Type', 'text/css; charset=utf-8');
        }
        else if (path.endsWith('.html')) {
            res.setHeader('Content-Type', 'text/html; charset=utf-8');
            // Disable caching for HTML files to ensure fresh content
            res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        }
        // Ensure no HTTPS enforcement
        res.setHeader('X-Content-Type-Options', 'nosniff');
    }
}));
// PRODUCTION-GRADE: API Routes with error boundary
app.use('/api', (req, res, next) => {
    // Add API request logging for production monitoring
    logger_1.logger.info(`API Request: ${req.method} ${req.url}`, {
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        timestamp: new Date().toISOString()
    });
    next();
}, index_1.apiRouter);
// PRODUCTION-GRADE: Comprehensive health endpoints for monitoring
app.get('/health', (req, res) => {
    const healthData = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '5.0.0',
        environment: NODE_ENV,
        pid: process.pid,
        platform: process.platform,
        nodeVersion: process.version
    };
    res.json(healthData);
});
app.get('/health/detailed', async (req, res) => {
    try {
        const healthData = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: '5.0.0',
            environment: NODE_ENV,
            database: {
                connected: true, // Will be updated based on actual DB status
                lastCheck: new Date().toISOString()
            },
            websocket: {
                enabled: true,
                connections: 0 // Will be updated with actual connection count
            },
            system: {
                pid: process.pid,
                platform: process.platform,
                nodeVersion: process.version,
                cpuUsage: process.cpuUsage()
            }
        };
        res.json(healthData);
    }
    catch (error) {
        res.status(500).json({
            status: 'unhealthy',
            error: error?.message || 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});
// PRODUCTION-GRADE: Comprehensive error handling with monitoring
app.use((err, req, res, next) => {
    // Log detailed error information for production monitoring
    logger_1.logger.error('Server error occurred:', {
        message: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
    });
    const statusCode = err.status || err.statusCode || 500;
    const errorMessage = NODE_ENV === 'production'
        ? 'Internal Server Error'
        : err.message || 'Internal Server Error';
    // Don't expose sensitive information in production
    const errorResponse = {
        error: errorMessage,
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown',
        ...(NODE_ENV !== 'production' && { stack: err.stack })
    };
    res.status(statusCode).json(errorResponse);
});
// PRODUCTION ARCHITECTURE: Handle SPA routing (must be after API routes)
app.get('*', (req, res) => {
    // Don't serve index.html for API routes
    if (req.path.startsWith('/api')) {
        return res.status(404).json({ error: 'API endpoint not found' });
    }
    // Serve index.html for all other routes (SPA routing)
    res.sendFile(path_1.default.join(__dirname, '../public/index.html'), (err) => {
        if (err) {
            logger_1.logger.error('Error serving index.html:', err);
            res.status(500).json({ error: 'Failed to serve application' });
        }
    });
});
// Create HTTP server
const server = http_1.default.createServer(app);
// PRODUCTION-GRADE: Enterprise server startup with bulletproof error handling
async function startServer() {
    try {
        logger_1.logger.info('🚀 Starting VMS Server - Production Grade Architecture...');
        // FORCE PORT 8080: Use the preferred port directly
        const PORT = PREFERRED_PORT;
        logger_1.logger.info(`🎯 Using forced port: ${PORT}`);
        // PRODUCTION-GRADE: WebSocket with enterprise configuration
        const io = new socket_io_1.Server(server, {
            cors: {
                origin: true, // Allow all origins for single-server deployment
                methods: ['GET', 'POST'],
                credentials: true // Enable credentials for session cookies
            },
            // PRODUCTION: Optimized timeouts for enterprise deployment
            pingTimeout: 60000,
            pingInterval: 25000,
            connectTimeout: 45000,
            // PRODUCTION: Transport configuration for reliability
            transports: ['polling', 'websocket'],
            allowEIO3: true,
            // PRODUCTION: Path and security settings
            path: '/socket.io',
            serveClient: false,
            // PRODUCTION: Performance optimizations
            maxHttpBufferSize: 1e6, // 1MB buffer
            httpCompression: true,
            perMessageDeflate: {
                threshold: 1024,
                concurrencyLimit: 10,
                windowBits: 13
            },
            // PRODUCTION: Connection management
            destroyUpgrade: false,
            destroyUpgradeTimeout: 1000,
            // PRODUCTION: Advanced features for enterprise deployment
            connectionStateRecovery: {
                maxDisconnectionDuration: 2 * 60 * 1000, // 2 minutes
                skipMiddlewares: false, // CRITICAL FIX: Allow middlewares to run for authentication
            },
            // PRODUCTION: Adapter configuration for scaling
            adapter: undefined // Will use default memory adapter for single instance
        });
        // CRITICAL FIX: Use ONLY ONE WebSocket system to prevent duplicate broadcasts
        (0, socketHandlers_1.setIoInstance)(io);
        (0, socketHandlers_1.setupSocketHandlers)(io);
        // DISABLED: Duplicate WebSocket system that causes double notifications
        // // REMOVED: Duplicate system import
        logger_1.logger.info('✅ Single WebSocket system initialized - No duplicates');
        logger_1.logger.info('🎯 Duplicate broadcast prevention: Only socketHandlers active');
        // PRODUCTION-GRADE: Database initialization with enterprise retry logic
        let databaseAvailable = false;
        let retryCount = 0;
        const maxRetries = 5; // Increased for production reliability
        const baseDelay = 2000; // Base delay in milliseconds
        logger_1.logger.info('🔄 Initializing database connection with enterprise retry logic...');
        while (!databaseAvailable && retryCount < maxRetries) {
            try {
                await (0, db_1.initializeDatabase)();
                logger_1.logger.info('✅ Database connected successfully - Production ready');
                databaseAvailable = true;
                // Test database connectivity
                logger_1.logger.info('🔍 Testing database connectivity...');
                // Initialize workflow service
                logger_1.logger.info('🔄 Initializing workflow service...');
                await (0, workflow_service_1.initializeWorkflowService)(db_2.default, // Database pool
                io // WebSocket instance for real-time updates
                );
                logger_1.logger.info('✅ Workflow service initialized successfully');
                // Initialize file storage
                logger_1.logger.info('🔄 Initializing file storage...');
                (0, file_storage_1.initializeStorage)();
                logger_1.logger.info('✅ File storage initialized successfully');
            }
            catch (error) {
                retryCount++;
                const delay = baseDelay * Math.pow(2, retryCount - 1); // Exponential backoff
                logger_1.logger.warn(`⚠️  Database connection attempt ${retryCount}/${maxRetries} failed:`, {
                    error: error.message,
                    code: error.code,
                    errno: error.errno,
                    sqlState: error.sqlState
                });
                if (retryCount < maxRetries) {
                    logger_1.logger.info(`🔄 Retrying database connection in ${delay / 1000} seconds...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                else {
                    logger_1.logger.error('❌ Database connection failed after all retries.');
                    logger_1.logger.error('🔧 Server will continue in degraded mode - WebSocket functionality available');
                    logger_1.logger.error('💡 Please check database configuration and connectivity');
                }
            }
        }
        // CRITICAL FIX: Disabled database health monitoring to prevent infinite loops
        if (databaseAvailable) {
            logger_1.logger.info('📊 Database health monitoring DISABLED to prevent infinite loops');
            // REMOVED: Periodic health checks that were causing database query loops
        }
        // PRODUCTION-GRADE: Server startup with comprehensive monitoring
        // NETWORK FIX: Listen on all interfaces (0.0.0.0) to allow network access
        server.listen(PORT, '0.0.0.0', async () => {
            logger_1.logger.info('🎉 VMS Server - Production Grade Startup Complete!');
            logger_1.logger.info('='.repeat(60));
            logger_1.logger.info(`🚀 Server Status: RUNNING (Port ${PORT})`);
            logger_1.logger.info(`🔌 WebSocket Status: ACTIVE (Enterprise Configuration)`);
            logger_1.logger.info(`💾 Database Status: ${databaseAvailable ? 'CONNECTED' : 'DEGRADED MODE'}`);
            logger_1.logger.info(`🌐 Environment: ${NODE_ENV.toUpperCase()}`);
            logger_1.logger.info(`📊 Process ID: ${process.pid}`);
            logger_1.logger.info('='.repeat(60));
            // PRODUCTION: Database pool monitoring for production load
            if (databaseAvailable) {
                try {
                    // Create DatabaseManager instance for monitoring
                    const dbManager = new DatabaseManager_1.DatabaseManager();
                    await dbManager.initialize();
                    // Log initial pool stats
                    dbManager.logPoolHealth();
                    // Monitor pool health every 5 minutes
                    setInterval(() => {
                        try {
                            dbManager.logPoolHealth();
                        }
                        catch (error) {
                            logger_1.logger.error('❌ Error monitoring DB pool health:', error);
                        }
                    }, 300000); // 5 minutes
                }
                catch (error) {
                    logger_1.logger.warn('⚠️ Could not initialize DB monitoring:', error);
                }
            }
            // PRODUCTION: Service endpoints
            logger_1.logger.info('📡 Service Endpoints:');
            logger_1.logger.info(`   🌐 WebSocket: http://localhost:${PORT}/socket.io/`);
            logger_1.logger.info(`   📊 Health Check: http://localhost:${PORT}/health`);
            logger_1.logger.info(`   📈 Detailed Health: http://localhost:${PORT}/health/detailed`);
            logger_1.logger.info(`   🔧 API Base: http://localhost:${PORT}/api`);
            // PRODUCTION-SAFE: Re-enabled essential tasks with safe intervals
            if (databaseAvailable) {
                try {
                    // Import session cleanup
                    const { scheduleSessionCleanup } = await import('./tasks/session-cleanup.js');
                    // Schedule session cleanup every 10 minutes (safer than every minute)
                    setInterval(async () => {
                        try {
                            const { cleanupStaleSessions } = await import('./tasks/session-cleanup.js');
                            await cleanupStaleSessions();
                        }
                        catch (cleanupError) {
                            logger_1.logger.error('Session cleanup error:', cleanupError);
                        }
                    }, 10 * 60 * 1000); // Every 10 minutes
                    logger_1.logger.info('✅ Session cleanup scheduled (every 10 minutes)');
                    // Run initial cleanup after 30 seconds
                    setTimeout(async () => {
                        try {
                            const { cleanupStaleSessions } = await import('./tasks/session-cleanup.js');
                            await cleanupStaleSessions();
                            logger_1.logger.info('✅ Initial session cleanup completed');
                        }
                        catch (cleanupError) {
                            logger_1.logger.error('Initial session cleanup error:', cleanupError);
                        }
                    }, 30000);
                }
                catch (error) {
                    logger_1.logger.warn('⚠️  Task scheduling failed:', error);
                }
                // PRODUCTION: Start automated backup scheduler (daily backups)
                try {
                    backup_scheduler_1.backupScheduler.start(24); // Backup every 24 hours
                    logger_1.logger.info('✅ Automated backup scheduler started (daily backups)');
                }
                catch (error) {
                    logger_1.logger.warn('⚠️ Backup scheduler failed to start:', error);
                }
            }
            // PRODUCTION-GRADE: Network discovery for enterprise deployment
            try {
                const os = await import('os');
                const networkInterfaces = os.networkInterfaces();
                const lanIPs = Object.values(networkInterfaces)
                    .flat()
                    .filter((iface) => iface.family === 'IPv4' && !iface.internal)
                    .map((iface) => iface.address);
                if (lanIPs.length > 0) {
                    logger_1.logger.info('🌍 Enterprise Network Access:');
                    lanIPs.forEach(ip => {
                        logger_1.logger.info(`   🖥️  VMS System: http://${ip}:${PORT}`);
                        logger_1.logger.info(`   🔌 WebSocket: http://${ip}:${PORT}/socket.io/`);
                    });
                }
                // PRODUCTION: Log system resources
                const memUsage = process.memoryUsage();
                logger_1.logger.info('📊 System Resources:');
                logger_1.logger.info(`   💾 Memory: ${Math.round(memUsage.rss / 1024 / 1024)}MB RSS`);
                logger_1.logger.info(`   🔄 Uptime: ${Math.round(process.uptime())}s`);
            }
            catch (error) {
                logger_1.logger.warn('Could not determine network configuration:', error);
            }
            // SERVICE DISCOVERY: Start automatic server announcement
            try {
                await ServiceDiscovery_1.serviceDiscovery.startAnnouncement(PORT);
                logger_1.logger.info('📡 Service Discovery: ACTIVE - Clients can auto-discover this server');
            }
            catch (error) {
                logger_1.logger.warn('⚠️  Service discovery failed to start:', error);
            }
            logger_1.logger.info('='.repeat(60));
            logger_1.logger.info('🎯 VMS Server - Ready for Production Operations!');
            logger_1.logger.info('='.repeat(60));
        });
    }
    catch (error) {
        logger_1.logger.error('❌ CRITICAL: Server startup failed:', {
            message: error?.message || 'Unknown error',
            stack: error?.stack || 'No stack trace',
            timestamp: new Date().toISOString()
        });
        // PRODUCTION-GRADE: Attempt graceful degradation
        logger_1.logger.info('🔄 Attempting graceful degradation...');
        try {
            // Try to start server in minimal mode on preferred port
            const fallbackPort = PREFERRED_PORT;
            server.listen(fallbackPort, '0.0.0.0', () => {
                logger_1.logger.warn('⚠️  Server started in MINIMAL MODE');
                logger_1.logger.warn('🔧 Limited functionality - please check configuration');
                logger_1.logger.info(`📊 Health endpoint available: http://localhost:${fallbackPort}/health`);
            });
        }
        catch (degradedError) {
            logger_1.logger.error('❌ FATAL: Complete startup failure:', degradedError);
            process.exit(1);
        }
    }
}
// PRODUCTION-GRADE: Enterprise error handling with monitoring
process.on('uncaughtException', (error) => {
    logger_1.logger.error('🚨 CRITICAL: Uncaught Exception detected:', {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        pid: process.pid
    });
    // PRODUCTION: Don't exit immediately, try to handle gracefully
    logger_1.logger.info('🔄 Attempting graceful recovery...');
    setTimeout(() => {
        logger_1.logger.error('💥 FATAL: Recovery failed, exiting...');
        process.exit(1);
    }, 5000);
});
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('🚨 CRITICAL: Unhandled Promise Rejection:', {
        reason: reason,
        promise: promise,
        timestamp: new Date().toISOString(),
        pid: process.pid
    });
    // PRODUCTION: Log but don't exit for unhandled rejections
    logger_1.logger.warn('⚠️  Continuing operation - monitoring for stability');
});
// PRODUCTION-GRADE: Comprehensive graceful shutdown
function gracefulShutdown(signal) {
    logger_1.logger.info(`🛑 ${signal} received - Initiating graceful shutdown...`);
    logger_1.logger.info('📊 Shutdown sequence started');
    // Stop accepting new connections
    server.close(async () => {
        logger_1.logger.info('✅ HTTP server closed');
        try {
            // PRODUCTION: Cleanup tasks
            logger_1.logger.info('🧹 Performing cleanup tasks...');
            // Close database connections if available
            logger_1.logger.info('💾 Closing database connections...');
            // Additional cleanup can be added here
            logger_1.logger.info('🔌 Closing WebSocket connections...');
            // Stop service discovery
            try {
                await ServiceDiscovery_1.serviceDiscovery.stopAnnouncement();
                logger_1.logger.info('📡 Service discovery stopped');
            }
            catch (error) {
                logger_1.logger.warn('⚠️  Error stopping service discovery:', error);
            }
            logger_1.logger.info('✅ Graceful shutdown completed successfully');
            process.exit(0);
        }
        catch (error) {
            logger_1.logger.error('❌ Error during shutdown cleanup:', error);
            process.exit(1);
        }
    });
    // PRODUCTION: Force shutdown after reasonable timeout
    setTimeout(() => {
        logger_1.logger.error('⏰ Shutdown timeout exceeded - forcing exit');
        process.exit(1);
    }, 30000); // 30 seconds for production
}
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
// Start the server
startServer();
//# sourceMappingURL=index.js.map