import { Request, Response, NextFunction } from 'express';
export declare function authenticate(req: Request, res: Response, next: NextFunction): Promise<Response<any, Record<string, any>> | undefined>;
export declare function authorize(roles: string[]): (req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare function attachDatabase(req: Request, res: Response, next: NextFunction): Promise<void>;
declare global {
    namespace Express {
        interface Request {
            user?: any;
            db?: any;
        }
    }
}
