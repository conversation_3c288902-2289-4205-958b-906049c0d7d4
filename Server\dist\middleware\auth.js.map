{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;AAIA,oCA2EC;AAGD,8BAYC;AAGD,wCAgBC;AAhHD,kDAA4C;AAE5C,0EAA0E;AACnE,KAAK,UAAU,YAAY,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;IAChF,IAAI,CAAC;QACH,kBAAM,CAAC,IAAI,CAAC,8BAA8B,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;QAEnE,8DAA8D;QAC9D,wEAAwE;QACxE,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,cAAc,IAAI,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC;QAEvG,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,kBAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YAChE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC;YACH,4DAA4D;YAC5D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;YAEpD,wCAAwC;YACxC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAC1B,iEAAiE,EACjE,CAAC,SAAS,CAAC,CACH,CAAC;YAEX,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,kBAAM,CAAC,IAAI,CAAC,uBAAuB,SAAS,EAAE,CAAC,CAAC;gBAChD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE5B,iDAAiD;YACjD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,EAAE,CAAC;YAC1E,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;YAE/C,IAAI,UAAU,GAAG,MAAM,EAAE,CAAC;gBACxB,qBAAqB;gBACrB,MAAM,KAAK,CAAC,2DAA2D,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;gBACtF,kBAAM,CAAC,IAAI,CAAC,oBAAoB,SAAS,EAAE,CAAC,CAAC;gBAC7C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC,CAAC;YAC5D,CAAC;YAED,uBAAuB;YACvB,MAAM,KAAK,CAAC,+DAA+D,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAE1F,gBAAgB;YAChB,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,kCAAkC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAU,CAAC;YAE1F,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,kBAAM,CAAC,IAAI,CAAC,+BAA+B,SAAS,EAAE,CAAC,CAAC;gBACxD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,8BAA8B;YAC9B,GAAG,CAAC,IAAI,GAAG;gBACT,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,SAAS,EAAE,SAAS;aACrB,CAAC;YAEF,kBAAM,CAAC,KAAK,CAAC,2CAA2C,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACrE,IAAI,EAAE,CAAC;QAET,CAAC;QAAC,OAAO,OAAO,EAAE,CAAC;YACjB,kBAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC,CAAC;QACjE,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AAED,sCAAsC;AACtC,SAAgB,SAAS,CAAC,KAAe;IACvC,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC;AAED,mDAAmD;AAC5C,KAAK,UAAU,cAAc,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;IAClF,IAAI,CAAC;QACH,gCAAgC;QAChC,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAEpE,GAAG,CAAC,EAAE,GAAG;YACP,OAAO,EAAE,KAAK,EAAE,0DAA0D;YAC1E,KAAK,EAAE,KAAK;YACZ,cAAc,EAAE,cAAc;SAC/B,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC"}