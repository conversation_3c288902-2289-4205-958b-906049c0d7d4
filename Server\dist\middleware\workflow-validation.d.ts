/**
 * Workflow Validation Middleware
 * Validates workflow state transitions before execution
 */
import { Request, Response, NextFunction } from 'express';
export interface WorkflowRequest extends Request {
    voucher?: any;
    transition?: any;
    db?: any;
    user?: any;
}
/**
 * Validate workflow transition request
 */
export declare function validateWorkflowTransition(req: WorkflowRequest, res: Response, next: NextFunction): Promise<Response<any, Record<string, any>> | undefined>;
/**
 * Validate voucher data integrity
 */
export declare function validateVoucherIntegrity(req: Request, res: Response, next: NextFunction): Promise<Response<any, Record<string, any>> | undefined>;
declare global {
    namespace Express {
        interface Request {
            voucher?: any;
            transition?: any;
        }
    }
}
