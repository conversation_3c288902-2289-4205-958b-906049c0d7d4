"use strict";
/**
 * Workflow Validation Middleware
 * Validates workflow state transitions before execution
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateWorkflowTransition = validateWorkflowTransition;
exports.validateVoucherIntegrity = validateVoucherIntegrity;
const VoucherWorkflowStateMachine_1 = require("../workflow/VoucherWorkflowStateMachine");
/**
 * Validate workflow transition request
 */
async function validateWorkflowTransition(req, res, next) {
    try {
        const { voucherId, event, metadata } = req.body;
        const userId = req.user?.id;
        const userDepartment = req.user?.department;
        // Validate required fields
        if (!voucherId) {
            return res.status(400).json({
                success: false,
                error: 'Voucher ID is required'
            });
        }
        if (!event) {
            return res.status(400).json({
                success: false,
                error: 'Workflow event is required'
            });
        }
        if (!userId) {
            return res.status(401).json({
                success: false,
                error: 'User authentication required'
            });
        }
        // Validate event type
        if (!Object.values(VoucherWorkflowStateMachine_1.WorkflowEvent).includes(event)) {
            return res.status(400).json({
                success: false,
                error: `Invalid workflow event: ${event}`
            });
        }
        // Get current voucher state
        const [vouchers] = await req.db.execute('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Voucher not found'
            });
        }
        const voucher = vouchers[0];
        const currentState = voucher.workflow_state;
        // Validate state transition
        const transition = VoucherWorkflowStateMachine_1.VoucherWorkflowStateMachine.getTransition(currentState, event);
        if (!transition) {
            return res.status(400).json({
                success: false,
                error: `Invalid transition from ${currentState} with event ${event}`
            });
        }
        // Validate user permissions
        const hasPermission = await validateUserPermission(voucher, event, userDepartment, userId, req.db);
        if (!hasPermission.allowed) {
            return res.status(403).json({
                success: false,
                error: hasPermission.reason || 'Permission denied'
            });
        }
        // Validate business rules
        const businessValidation = await validateBusinessRules(voucher, event, metadata, req.db);
        if (!businessValidation.valid) {
            return res.status(400).json({
                success: false,
                error: businessValidation.reason || 'Business rule validation failed'
            });
        }
        // Attach validated data to request
        req.voucher = voucher;
        req.transition = transition;
        req.body.originalDepartment = voucher.original_department;
        next();
    }
    catch (error) {
        console.error('Workflow validation error:', error);
        res.status(500).json({
            success: false,
            error: 'Workflow validation failed'
        });
    }
}
/**
 * Validate user permissions for workflow action
 */
async function validateUserPermission(voucher, event, userDepartment, userId, db) {
    // Department-based permissions
    const auditEvents = [
        VoucherWorkflowStateMachine_1.WorkflowEvent.RECEIVE_FROM_FINANCE,
        VoucherWorkflowStateMachine_1.WorkflowEvent.START_WORK,
        VoucherWorkflowStateMachine_1.WorkflowEvent.CERTIFY_VOUCHER,
        VoucherWorkflowStateMachine_1.WorkflowEvent.REJECT_VOUCHER,
        VoucherWorkflowStateMachine_1.WorkflowEvent.RETURN_VOUCHER,
        VoucherWorkflowStateMachine_1.WorkflowEvent.DISPATCH_TO_FINANCE
    ];
    const financeEvents = [
        VoucherWorkflowStateMachine_1.WorkflowEvent.CREATE_VOUCHER,
        VoucherWorkflowStateMachine_1.WorkflowEvent.SUBMIT_TO_AUDIT,
        VoucherWorkflowStateMachine_1.WorkflowEvent.RESUBMIT_FROM_REJECTED,
    ];
    if (userDepartment === 'AUDIT' && !auditEvents.includes(event)) {
        return {
            allowed: false,
            reason: 'Audit users cannot perform this action'
        };
    }
    if (userDepartment !== 'AUDIT' && !financeEvents.includes(event)) {
        return {
            allowed: false,
            reason: 'Finance users cannot perform this action'
        };
    }
    // Finance users can only act on their department's vouchers
    if (userDepartment !== 'AUDIT' && voucher.original_department !== userDepartment) {
        return {
            allowed: false,
            reason: 'You can only act on vouchers from your department'
        };
    }
    // Check if user is authorized for specific actions
    if (event === VoucherWorkflowStateMachine_1.WorkflowEvent.DISPATCH_TO_FINANCE) {
        // Only senior audit staff can dispatch
        const [userRoles] = await db.execute('SELECT role FROM users WHERE id = ?', [userId]);
        if (userRoles.length === 0 || !['AUDIT_MANAGER', 'AUDIT_SENIOR'].includes(userRoles[0].role)) {
            return {
                allowed: false,
                reason: 'Only senior audit staff can dispatch vouchers'
            };
        }
    }
    return { allowed: true };
}
/**
 * Validate business rules for workflow transition
 */
async function validateBusinessRules(voucher, event, metadata, db) {
    // Rule 1: Cannot work on vouchers that are already being processed by someone else
    if (event === VoucherWorkflowStateMachine_1.WorkflowEvent.START_WORK) {
        const [activeWork] = await db.execute(`
      SELECT user_id FROM voucher_work_sessions 
      WHERE voucher_id = ? AND is_active = TRUE AND user_id != ?
    `, [voucher.id, metadata?.userId]);
        if (activeWork.length > 0) {
            return {
                valid: false,
                reason: 'Voucher is currently being processed by another user'
            };
        }
    }
    // Rule 2: Cannot reject/return without reason
    if ([VoucherWorkflowStateMachine_1.WorkflowEvent.REJECT_VOUCHER, VoucherWorkflowStateMachine_1.WorkflowEvent.RETURN_VOUCHER].includes(event)) {
        if (!metadata?.reason || metadata.reason.trim().length < 10) {
            return {
                valid: false,
                reason: 'Rejection/return reason must be at least 10 characters'
            };
        }
    }
    // Rule 3: Cannot dispatch empty batches
    if (event === VoucherWorkflowStateMachine_1.WorkflowEvent.DISPATCH_TO_FINANCE) {
        if (!metadata?.batchId) {
            return {
                valid: false,
                reason: 'Voucher must be assigned to a batch before dispatch'
            };
        }
        const [batchVouchers] = await db.execute(`
      SELECT COUNT(*) as count FROM batch_vouchers 
      WHERE batch_id = ? AND voucher_id != ?
    `, [metadata.batchId, voucher.id]);
        if (batchVouchers[0].count === 0) {
            return {
                valid: false,
                reason: 'Cannot dispatch single voucher batch'
            };
        }
    }
    // Rule 4: Cannot resubmit without addressing previous issues
    if ([VoucherWorkflowStateMachine_1.WorkflowEvent.RESUBMIT_FROM_REJECTED].includes(event)) {
        if (!metadata?.changesDescription || metadata.changesDescription.trim().length < 20) {
            return {
                valid: false,
                reason: 'Please describe the changes made to address previous issues (minimum 20 characters)'
            };
        }
    }
    // Rule 5: Validate voucher amount limits
    if (event === VoucherWorkflowStateMachine_1.WorkflowEvent.CERTIFY_VOUCHER) {
        const amount = parseFloat(voucher.amount || '0');
        if (amount > 1000000) { // 1 million limit
            // Check if user has high-value approval authority
            const [userAuth] = await db.execute(`
        SELECT can_approve_high_value FROM users WHERE id = ?
      `, [metadata?.userId]);
            if (userAuth.length === 0 || !userAuth[0].can_approve_high_value) {
                return {
                    valid: false,
                    reason: 'High-value vouchers require special approval authority'
                };
            }
        }
    }
    // Rule 6: Check for duplicate submissions
    if (event === VoucherWorkflowStateMachine_1.WorkflowEvent.SUBMIT_TO_AUDIT) {
        const [duplicates] = await db.execute(`
      SELECT COUNT(*) as count FROM vouchers 
      WHERE voucher_id = ? AND id != ? AND deleted = FALSE
    `, [voucher.voucher_id, voucher.id]);
        if (duplicates[0].count > 0) {
            return {
                valid: false,
                reason: 'Duplicate voucher ID detected'
            };
        }
    }
    return { valid: true };
}
/**
 * Validate voucher data integrity
 */
async function validateVoucherIntegrity(req, res, next) {
    try {
        const { voucherId } = req.params;
        const [vouchers] = await req.db.execute(`
      SELECT 
        v.*,
        COUNT(bv.batch_id) as batch_count,
        MAX(wal.timestamp) as last_transition
      FROM vouchers v
      LEFT JOIN batch_vouchers bv ON v.id = bv.voucher_id
      LEFT JOIN workflow_audit_log wal ON v.id = wal.voucher_id
      WHERE v.id = ? AND v.deleted = FALSE
      GROUP BY v.id
    `, [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Voucher not found'
            });
        }
        const voucher = vouchers[0];
        // Check for data consistency issues
        const issues = [];
        // Check workflow state consistency
        if (!Object.values(VoucherWorkflowStateMachine_1.WorkflowState).includes(voucher.workflow_state)) {
            issues.push('Invalid workflow state');
        }
        // Check department consistency
        if (voucher.workflow_state.startsWith('AUDIT_') && voucher.department !== 'AUDIT') {
            issues.push('Department mismatch with workflow state');
        }
        // Check copy consistency
        if (voucher.is_copy && !voucher.parent_voucher_id) {
            issues.push('Copy voucher missing parent reference');
        }
        if (issues.length > 0) {
            return res.status(400).json({
                success: false,
                error: 'Voucher data integrity issues',
                issues
            });
        }
        req.voucher = voucher;
        next();
    }
    catch (error) {
        console.error('Voucher integrity validation error:', error);
        res.status(500).json({
            success: false,
            error: 'Voucher integrity validation failed'
        });
    }
}
//# sourceMappingURL=workflow-validation.js.map