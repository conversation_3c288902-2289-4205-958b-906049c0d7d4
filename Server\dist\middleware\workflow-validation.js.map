{"version": 3, "file": "workflow-validation.js", "sourceRoot": "", "sources": ["../../src/middleware/workflow-validation.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAeH,gEA8GC;AAkLD,4DAiEC;AA7WD,yFAAoH;AASpH;;GAEG;AACI,KAAK,UAAU,0BAA0B,CAC9C,GAAoB,EACpB,GAAa,EACb,IAAkB;IAElB,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAChD,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QAC5B,MAAM,cAAc,GAAG,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC;QAE5C,2BAA2B;QAC3B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wBAAwB;aAChC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,4BAA4B;aACpC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,8BAA8B;aACtC,CAAC,CAAC;QACL,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,2CAAa,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAClD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B,KAAK,EAAE;aAC1C,CAAC,CAAC;QACL,CAAC;QAED,4BAA4B;QAC5B,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,OAAO,CACrC,yDAAyD,EACzD,CAAC,SAAS,CAAC,CACZ,CAAC;QAEF,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,mBAAmB;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5B,MAAM,YAAY,GAAG,OAAO,CAAC,cAA+B,CAAC;QAE7D,4BAA4B;QAC5B,MAAM,UAAU,GAAG,yDAA2B,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAClF,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B,YAAY,eAAe,KAAK,EAAE;aACrE,CAAC,CAAC;QACL,CAAC;QAED,4BAA4B;QAC5B,MAAM,aAAa,GAAG,MAAM,sBAAsB,CAChD,OAAO,EACP,KAAK,EACL,cAAc,EACd,MAAM,EACN,GAAG,CAAC,EAAE,CACP,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC3B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,aAAa,CAAC,MAAM,IAAI,mBAAmB;aACnD,CAAC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,MAAM,kBAAkB,GAAG,MAAM,qBAAqB,CACpD,OAAO,EACP,KAAK,EACL,QAAQ,EACR,GAAG,CAAC,EAAE,CACP,CAAC;QAEF,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kBAAkB,CAAC,MAAM,IAAI,iCAAiC;aACtE,CAAC,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;QACtB,GAAG,CAAC,UAAU,GAAG,UAAU,CAAC;QAC5B,GAAG,CAAC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,mBAAmB,CAAC;QAE1D,IAAI,EAAE,CAAC;IAET,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,4BAA4B;SACpC,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,sBAAsB,CACnC,OAAY,EACZ,KAAoB,EACpB,cAAsB,EACtB,MAAc,EACd,EAAO;IAGP,+BAA+B;IAC/B,MAAM,WAAW,GAAG;QAClB,2CAAa,CAAC,oBAAoB;QAClC,2CAAa,CAAC,UAAU;QACxB,2CAAa,CAAC,eAAe;QAC7B,2CAAa,CAAC,cAAc;QAC5B,2CAAa,CAAC,cAAc;QAC5B,2CAAa,CAAC,mBAAmB;KAClC,CAAC;IAEF,MAAM,aAAa,GAAG;QACpB,2CAAa,CAAC,cAAc;QAC5B,2CAAa,CAAC,eAAe;QAC7B,2CAAa,CAAC,sBAAsB;KAErC,CAAC;IAEF,IAAI,cAAc,KAAK,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QAC/D,OAAO;YACL,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,wCAAwC;SACjD,CAAC;IACJ,CAAC;IAED,IAAI,cAAc,KAAK,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACjE,OAAO;YACL,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,0CAA0C;SACnD,CAAC;IACJ,CAAC;IAED,4DAA4D;IAC5D,IAAI,cAAc,KAAK,OAAO,IAAI,OAAO,CAAC,mBAAmB,KAAK,cAAc,EAAE,CAAC;QACjF,OAAO;YACL,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,mDAAmD;SAC5D,CAAC;IACJ,CAAC;IAED,mDAAmD;IACnD,IAAI,KAAK,KAAK,2CAAa,CAAC,mBAAmB,EAAE,CAAC;QAChD,uCAAuC;QACvC,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,EAAE,CAAC,OAAO,CAClC,qCAAqC,EACrC,CAAC,MAAM,CAAC,CACT,CAAC;QAEF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7F,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,+CAA+C;aACxD,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAC3B,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAClC,OAAY,EACZ,KAAoB,EACpB,QAAa,EACb,EAAO;IAGP,mFAAmF;IACnF,IAAI,KAAK,KAAK,2CAAa,CAAC,UAAU,EAAE,CAAC;QACvC,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC;;;KAGrC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;QAEnC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,sDAAsD;aAC/D,CAAC;QACJ,CAAC;IACH,CAAC;IAED,8CAA8C;IAC9C,IAAI,CAAC,2CAAa,CAAC,cAAc,EAAE,2CAAa,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACjF,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC5D,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,wDAAwD;aACjE,CAAC;QACJ,CAAC;IACH,CAAC;IAED,wCAAwC;IACxC,IAAI,KAAK,KAAK,2CAAa,CAAC,mBAAmB,EAAE,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACvB,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,qDAAqD;aAC9D,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,aAAa,CAAC,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC;;;KAGxC,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAEnC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,sCAAsC;aAC/C,CAAC;QACJ,CAAC;IACH,CAAC;IAED,6DAA6D;IAC7D,IAAI,CAAC,2CAAa,CAAC,sBAAsB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QAC3D,IAAI,CAAC,QAAQ,EAAE,kBAAkB,IAAI,QAAQ,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACpF,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,qFAAqF;aAC9F,CAAC;QACJ,CAAC;IACH,CAAC;IAED,yCAAyC;IACzC,IAAI,KAAK,KAAK,2CAAa,CAAC,eAAe,EAAE,CAAC;QAC5C,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC;QAEjD,IAAI,MAAM,GAAG,OAAO,EAAE,CAAC,CAAC,kBAAkB;YACxC,kDAAkD;YAClD,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC;;OAEnC,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;YAEvB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,sBAAsB,EAAE,CAAC;gBACjE,OAAO;oBACL,KAAK,EAAE,KAAK;oBACZ,MAAM,EAAE,wDAAwD;iBACjE,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,0CAA0C;IAC1C,IAAI,KAAK,KAAK,2CAAa,CAAC,eAAe,EAAE,CAAC;QAC5C,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC;;;KAGrC,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAErC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,+BAA+B;aACxC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AACzB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,wBAAwB,CAC5C,GAAY,EACZ,GAAa,EACb,IAAkB;IAElB,IAAI,CAAC;QACH,MAAM,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAEjC,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;;;;KAUvC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;QAEhB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,mBAAmB;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE5B,oCAAoC;QACpC,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,2CAAa,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YACnE,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAED,+BAA+B;QAC/B,IAAI,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,UAAU,KAAK,OAAO,EAAE,CAAC;YAClF,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAClD,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,+BAA+B;gBACtC,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAED,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;QACtB,IAAI,EAAE,CAAC;IAET,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,qCAAqC;SAC7C,CAAC,CAAC;IACL,CAAC;AACH,CAAC"}