"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.adminRouter = void 0;
const express_1 = __importDefault(require("express"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
const year_rollover_service_js_1 = require("../services/year-rollover-service.js");
const backup_scheduler_js_1 = require("../services/backup-scheduler.js");
exports.adminRouter = express_1.default.Router();
// Apply authentication middleware to all routes
exports.adminRouter.use(auth_js_1.authenticate);
// Apply authorization middleware to all routes
exports.adminRouter.use((0, auth_js_1.authorize)(['admin']));
// Get system settings
exports.adminRouter.get('/settings', async (req, res) => {
    try {
        const settings = await (0, db_js_1.query)('SELECT * FROM system_settings LIMIT 1');
        if (settings.length === 0) {
            // Create default settings if none exist
            const currentYear = new Date().getFullYear();
            await (0, db_js_1.query)(`INSERT INTO system_settings (
          fiscal_year_start, fiscal_year_end, current_fiscal_year, system_time, auto_backup_enabled, session_timeout
        ) VALUES (?, ?, ?, ?, ?, ?)`, ['JAN', 'DEC', currentYear, new Date().toISOString(), true, 30]);
            const newSettings = await (0, db_js_1.query)('SELECT * FROM system_settings LIMIT 1');
            res.json(newSettings[0]);
        }
        else {
            res.json(settings[0]);
        }
    }
    catch (error) {
        logger_js_1.logger.error('Get system settings error:', error);
        res.status(500).json({ error: 'Failed to get system settings' });
    }
});
// Update system settings
exports.adminRouter.put('/settings', async (req, res) => {
    try {
        const { fiscalYearStart, fiscalYearEnd, currentFiscalYear, systemTime, autoBackupEnabled, sessionTimeout } = req.body;
        // Check if settings exist
        const settings = await (0, db_js_1.query)('SELECT * FROM system_settings LIMIT 1');
        if (settings.length === 0) {
            // Create settings if none exist
            await (0, db_js_1.query)(`INSERT INTO system_settings (
          fiscal_year_start, fiscal_year_end, current_fiscal_year, system_time, auto_backup_enabled, session_timeout
        ) VALUES (?, ?, ?, ?, ?, ?)`, [
                fiscalYearStart || 'JAN',
                fiscalYearEnd || 'DEC',
                currentFiscalYear || new Date().getFullYear(),
                systemTime || new Date().toISOString(),
                autoBackupEnabled !== undefined ? autoBackupEnabled : true,
                sessionTimeout || 30
            ]);
        }
        else {
            // Update existing settings
            let updateQuery = 'UPDATE system_settings SET ';
            const updateParams = [];
            const updates = [];
            if (fiscalYearStart !== undefined) {
                updates.push('fiscal_year_start = ?');
                updateParams.push(fiscalYearStart);
            }
            if (fiscalYearEnd !== undefined) {
                updates.push('fiscal_year_end = ?');
                updateParams.push(fiscalYearEnd);
            }
            if (currentFiscalYear !== undefined) {
                updates.push('current_fiscal_year = ?');
                updateParams.push(currentFiscalYear);
            }
            if (systemTime !== undefined) {
                updates.push('system_time = ?');
                updateParams.push(systemTime);
            }
            if (autoBackupEnabled !== undefined) {
                updates.push('auto_backup_enabled = ?');
                updateParams.push(autoBackupEnabled);
            }
            if (sessionTimeout !== undefined) {
                updates.push('session_timeout = ?');
                updateParams.push(sessionTimeout);
            }
            // If no updates, return early
            if (updates.length === 0) {
                return res.status(400).json({ error: 'No updates provided' });
            }
            updateQuery += updates.join(', ') + ' WHERE id = ?';
            updateParams.push(settings[0].id);
            await (0, db_js_1.query)(updateQuery, updateParams);
        }
        // Get updated settings
        const updatedSettings = await (0, db_js_1.query)('SELECT * FROM system_settings LIMIT 1');
        res.json(updatedSettings[0]);
    }
    catch (error) {
        logger_js_1.logger.error('Update system settings error:', error);
        res.status(500).json({ error: 'Failed to update system settings' });
    }
});
// Backup database
exports.adminRouter.post('/backup', async (req, res) => {
    try {
        // Create backups directory if it doesn't exist
        const backupsDir = path_1.default.join(process.cwd(), 'backups');
        if (!fs_1.default.existsSync(backupsDir)) {
            fs_1.default.mkdirSync(backupsDir);
        }
        // Generate backup filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFilename = `vms_backup_${timestamp}.sql`;
        const backupPath = path_1.default.join(backupsDir, backupFilename);
        // Get database connection config
        const dbConfig = {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '3306'),
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || 'vms@2025@1989',
            database: process.env.DB_NAME || 'vms_production',
        };
        // PRODUCTION-GRADE: Node.js-based backup (no external dependencies)
        try {
            // Create database connection for backup
            const mysql = require('mysql2/promise');
            const connection = await mysql.createConnection(dbConfig);
            // Get all table names
            const [tables] = await connection.execute('SHOW TABLES');
            const tableNames = tables.map((row) => Object.values(row)[0]);
            let backupContent = `-- VMS Production Database Backup\n`;
            backupContent += `-- Generated: ${new Date().toISOString()}\n`;
            backupContent += `-- Database: ${dbConfig.database}\n\n`;
            backupContent += `SET FOREIGN_KEY_CHECKS = 0;\n\n`;
            // Backup each table
            for (const tableName of tableNames) {
                // Get table structure
                const [createTable] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
                const createStatement = createTable[0]['Create Table'];
                backupContent += `-- Table: ${tableName}\n`;
                backupContent += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
                backupContent += `${createStatement};\n\n`;
                // Get table data
                const [rows] = await connection.execute(`SELECT * FROM \`${tableName}\``);
                if (rows.length > 0) {
                    backupContent += `-- Data for table: ${tableName}\n`;
                    backupContent += `INSERT INTO \`${tableName}\` VALUES\n`;
                    const values = rows.map(row => {
                        const escapedValues = Object.values(row).map(value => {
                            if (value === null)
                                return 'NULL';
                            if (typeof value === 'string') {
                                // Handle datetime strings that might be in ISO format
                                if (value.includes('T') && value.includes('Z')) {
                                    try {
                                        // Convert ISO datetime to MySQL format
                                        const date = new Date(value);
                                        // Check if the date is valid
                                        if (isNaN(date.getTime())) {
                                            // If invalid date, treat as regular string
                                            return `'${value.replace(/'/g, "''")}'`;
                                        }
                                        return `'${date.toISOString().slice(0, 19).replace('T', ' ')}'`;
                                    }
                                    catch (error) {
                                        // If any error occurs, treat as regular string
                                        return `'${value.replace(/'/g, "''")}'`;
                                    }
                                }
                                return `'${value.replace(/'/g, "''")}'`;
                            }
                            if (value instanceof Date) {
                                try {
                                    // Check if the date is valid before converting
                                    if (isNaN(value.getTime())) {
                                        return 'NULL';
                                    }
                                    return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
                                }
                                catch (error) {
                                    return 'NULL';
                                }
                            }
                            return value;
                        });
                        return `(${escapedValues.join(', ')})`;
                    });
                    backupContent += values.join(',\n') + ';\n\n';
                }
            }
            backupContent += `SET FOREIGN_KEY_CHECKS = 1;\n`;
            // Write backup file
            fs_1.default.writeFileSync(backupPath, backupContent, 'utf8');
            // Verify backup file was created and has content
            const stats = fs_1.default.statSync(backupPath);
            if (stats.size === 0) {
                throw new Error('Backup file is empty');
            }
            await connection.end();
            // Update last backup date in system settings
            await (0, db_js_1.query)('UPDATE system_settings SET last_backup_date = ? WHERE id = 1', [new Date().toISOString()]);
            logger_js_1.logger.info(`✅ Backup created successfully: ${backupFilename} (${stats.size} bytes)`);
            res.json({
                message: 'Backup created successfully',
                filename: backupFilename,
                path: backupPath,
                size: stats.size,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            logger_js_1.logger.error('❌ Backup creation failed:', error);
            // Clean up failed backup file
            if (fs_1.default.existsSync(backupPath)) {
                fs_1.default.unlinkSync(backupPath);
            }
            res.status(500).json({
                error: 'Failed to create backup',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    catch (error) {
        logger_js_1.logger.error('Backup error:', error);
        res.status(500).json({ error: 'Failed to create backup' });
    }
});
// Restore database from backup
exports.adminRouter.post('/restore', async (req, res) => {
    try {
        const { filename } = req.body;
        if (!filename) {
            return res.status(400).json({ error: 'Backup filename is required' });
        }
        // Check if backup file exists
        const backupPath = path_1.default.join(process.cwd(), 'backups', filename);
        if (!fs_1.default.existsSync(backupPath)) {
            return res.status(404).json({ error: 'Backup file not found' });
        }
        // Get database connection config
        const dbConfig = {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '3306'),
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || 'vms@2025@1989',
            database: process.env.DB_NAME || 'vms_production',
        };
        // PRODUCTION-GRADE: Node.js-based restore with validation
        try {
            // Validate backup file
            const backupContent = fs_1.default.readFileSync(backupPath, 'utf8');
            if (!backupContent.includes('-- VMS Production Database Backup')) {
                return res.status(400).json({ error: 'Invalid backup file format' });
            }
            // Create database connection for restore
            const mysql = require('mysql2/promise');
            const connection = await mysql.createConnection(dbConfig);
            // Split backup into individual statements
            const statements = backupContent
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
            logger_js_1.logger.info(`🔄 Starting restore process: ${statements.length} statements to execute`);
            // Execute statements in transaction for safety
            await connection.beginTransaction();
            try {
                let executedCount = 0;
                for (const statement of statements) {
                    if (statement.trim()) {
                        await connection.execute(statement);
                        executedCount++;
                    }
                }
                await connection.commit();
                await connection.end();
                logger_js_1.logger.info(`✅ Database restored successfully: ${executedCount} statements executed`);
                res.json({
                    message: 'Database restored successfully',
                    statementsExecuted: executedCount,
                    timestamp: new Date().toISOString()
                });
            }
            catch (execError) {
                await connection.rollback();
                await connection.end();
                throw execError;
            }
        }
        catch (error) {
            logger_js_1.logger.error('❌ Restore failed:', error);
            res.status(500).json({
                error: 'Failed to restore backup',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
    catch (error) {
        logger_js_1.logger.error('Restore error:', error);
        res.status(500).json({ error: 'Failed to restore backup' });
    }
});
// Get list of available backups
exports.adminRouter.get('/backups', (req, res) => {
    try {
        const backupsDir = path_1.default.join(process.cwd(), 'backups');
        // Create directory if it doesn't exist
        if (!fs_1.default.existsSync(backupsDir)) {
            fs_1.default.mkdirSync(backupsDir);
            return res.json([]);
        }
        // Read directory
        const files = fs_1.default.readdirSync(backupsDir)
            .filter(file => file.endsWith('.sql'))
            .map(file => {
            const stats = fs_1.default.statSync(path_1.default.join(backupsDir, file));
            return {
                filename: file,
                size: stats.size,
                created: stats.mtime
            };
        })
            .sort((a, b) => b.created.getTime() - a.created.getTime()); // Sort by date, newest first
        res.json(files);
    }
    catch (error) {
        logger_js_1.logger.error('Get backups error:', error);
        res.status(500).json({ error: 'Failed to get backups' });
    }
});
// Get audit logs
exports.adminRouter.get('/audit-logs', async (req, res) => {
    try {
        const { limit = 100, offset = 0, userId, action, resourceType, startDate, endDate } = req.query;
        // Build query
        let sql = 'SELECT * FROM audit_logs';
        const params = [];
        const conditions = [];
        if (userId) {
            conditions.push('user_id = ?');
            params.push(userId);
        }
        if (action) {
            conditions.push('action = ?');
            params.push(action);
        }
        if (resourceType) {
            conditions.push('resource_type = ?');
            params.push(resourceType);
        }
        if (startDate) {
            conditions.push('timestamp >= ?');
            params.push(startDate);
        }
        if (endDate) {
            conditions.push('timestamp <= ?');
            params.push(endDate);
        }
        if (conditions.length > 0) {
            sql += ' WHERE ' + conditions.join(' AND ');
        }
        sql += ' ORDER BY timestamp DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit));
        params.push(parseInt(offset));
        const logs = await (0, db_js_1.query)(sql, params);
        // Get total count for pagination
        let countSql = 'SELECT COUNT(*) as count FROM audit_logs';
        if (conditions.length > 0) {
            countSql += ' WHERE ' + conditions.join(' AND ');
        }
        const countResult = await (0, db_js_1.query)(countSql, params.slice(0, -2));
        const totalCount = countResult[0].count;
        res.json({
            logs,
            pagination: {
                total: totalCount,
                limit: parseInt(limit),
                offset: parseInt(offset),
                pages: Math.ceil(totalCount / parseInt(limit))
            }
        });
    }
    catch (error) {
        logger_js_1.logger.error('Get audit logs error:', error);
        res.status(500).json({ error: 'Failed to get audit logs' });
    }
});
// Clear local storage (for testing/development)
exports.adminRouter.post('/clear-storage', (req, res) => {
    try {
        // This is a server-side endpoint, so we can't directly clear browser localStorage
        // Instead, we'll return instructions for the client to clear its localStorage
        res.json({
            message: 'To clear localStorage, execute the following in your browser console: localStorage.clear()'
        });
    }
    catch (error) {
        logger_js_1.logger.error('Clear storage error:', error);
        res.status(500).json({ error: 'Failed to clear storage' });
    }
});
// Clear all resource locks (for testing/development)
exports.adminRouter.post('/clear-locks', (req, res) => {
    try {
        // Import the socket handlers to access the lock clearing function
        const { clearAllResourceLocks } = require('../socket/socketHandlers');
        const clearedCount = clearAllResourceLocks();
        logger_js_1.logger.info(`Admin manually cleared ${clearedCount} resource locks`);
        res.json({
            message: `Successfully cleared ${clearedCount} resource locks`,
            clearedCount
        });
    }
    catch (error) {
        logger_js_1.logger.error('Clear locks error:', error);
        res.status(500).json({ error: 'Failed to clear resource locks' });
    }
});
// Year Rollover Management Endpoints
// Get year rollover status
exports.adminRouter.get('/year-rollover/status', async (req, res) => {
    try {
        const status = await year_rollover_service_js_1.yearRolloverService.getRolloverStatus();
        res.json(status);
    }
    catch (error) {
        logger_js_1.logger.error('Get rollover status error:', error);
        res.status(500).json({ error: 'Failed to get rollover status' });
    }
});
// Trigger manual year rollover
exports.adminRouter.post('/year-rollover/trigger', async (req, res) => {
    try {
        const { targetYear } = req.body;
        if (!targetYear || !Number.isInteger(targetYear)) {
            return res.status(400).json({ error: 'Valid target year is required' });
        }
        const success = await year_rollover_service_js_1.yearRolloverService.triggerManualRollover(targetYear);
        if (success) {
            res.json({
                success: true,
                message: `Year rollover to ${targetYear} completed successfully`
            });
        }
        else {
            res.status(500).json({ error: 'Year rollover failed' });
        }
    }
    catch (error) {
        logger_js_1.logger.error('Manual rollover error:', error);
        res.status(500).json({ error: 'Failed to trigger year rollover' });
    }
});
// Backup Scheduler Management Endpoints
// Get backup scheduler status
exports.adminRouter.get('/backup-schedule', (req, res) => {
    try {
        const status = backup_scheduler_js_1.backupScheduler.getStatus();
        res.json(status);
    }
    catch (error) {
        logger_js_1.logger.error('Get backup schedule error:', error);
        res.status(500).json({ error: 'Failed to get backup schedule' });
    }
});
// Set daily backup schedule
exports.adminRouter.post('/backup-schedule', (req, res) => {
    try {
        const { time, enabled } = req.body;
        if (!enabled) {
            backup_scheduler_js_1.backupScheduler.stop();
            res.json({ message: 'Backup scheduling disabled' });
            return;
        }
        if (!time) {
            return res.status(400).json({ error: 'Time is required when enabling backup schedule' });
        }
        backup_scheduler_js_1.backupScheduler.scheduleDaily(time);
        res.json({
            message: 'Backup schedule updated successfully',
            scheduledTime: time,
            nextBackup: backup_scheduler_js_1.backupScheduler.getStatus().nextBackup
        });
    }
    catch (error) {
        logger_js_1.logger.error('Set backup schedule error:', error);
        res.status(500).json({
            error: 'Failed to set backup schedule',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Trigger manual backup
exports.adminRouter.post('/backup-manual', async (req, res) => {
    try {
        const success = await backup_scheduler_js_1.backupScheduler.triggerManualBackup();
        if (success) {
            res.json({ message: 'Manual backup completed successfully' });
        }
        else {
            res.status(500).json({ error: 'Manual backup failed' });
        }
    }
    catch (error) {
        logger_js_1.logger.error('Manual backup error:', error);
        res.status(500).json({
            error: 'Failed to perform manual backup',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// PRODUCTION FIX: Clear stale department locks
exports.adminRouter.post('/clear-stale-locks', async (req, res) => {
    try {
        const { department } = req.body;
        // Import the department locks management functions
        const { releaseDepartmentLock, departmentLocks } = require('../socket/socketHandlers');
        let clearedLocks = 0;
        const clearedDepartments = [];
        if (department) {
            // Clear specific department lock
            const lockExists = departmentLocks.has(department);
            if (lockExists) {
                departmentLocks.delete(department);
                clearedLocks = 1;
                clearedDepartments.push(department);
                logger_js_1.logger.info(`🔧 ADMIN: Manually cleared stale lock for ${department}`);
            }
        }
        else {
            // Clear all department locks
            clearedLocks = departmentLocks.size;
            for (const dept of departmentLocks.keys()) {
                clearedDepartments.push(dept);
            }
            departmentLocks.clear();
            logger_js_1.logger.info(`🔧 ADMIN: Manually cleared all ${clearedLocks} stale locks`);
        }
        res.json({
            success: true,
            message: `Cleared ${clearedLocks} stale lock(s)`,
            clearedDepartments,
            clearedCount: clearedLocks
        });
    }
    catch (error) {
        logger_js_1.logger.error('Failed to clear stale locks:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
//# sourceMappingURL=admin.js.map