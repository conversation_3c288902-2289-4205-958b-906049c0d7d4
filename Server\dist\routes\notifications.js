"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationRouter = void 0;
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
exports.notificationRouter = express_1.default.Router();
// Apply authentication middleware to all routes
exports.notificationRouter.use(auth_js_1.authenticate);
// Get all notifications for the current user
exports.notificationRouter.get('/', async (req, res) => {
    try {
        let notifications;
        if (req.user.department === 'SYSTEM ADMIN') {
            // Admin sees all notifications
            notifications = await (0, db_js_1.query)('SELECT * FROM notifications ORDER BY timestamp DESC');
        }
        else if (req.user.department === 'AUDIT') {
            // Audit sees notifications for Audit and from departments
            notifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE user_id = ? OR user_id = ? OR from_audit = FALSE ORDER BY timestamp DESC', [req.user.id, 'AUDIT']);
        }
        else {
            // Departments see their own notifications and from Audit
            notifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE user_id = ? OR user_id = ? OR (from_audit = TRUE AND user_id = ?) ORDER BY timestamp DESC', [req.user.id, req.user.department, req.user.department]);
        }
        res.json(notifications);
    }
    catch (error) {
        logger_js_1.logger.error('Get notifications error:', error);
        res.status(500).json({ error: 'Failed to get notifications' });
    }
});
// Get unread notifications count
exports.notificationRouter.get('/unread/count', async (req, res) => {
    try {
        let count;
        if (req.user.department === 'SYSTEM ADMIN') {
            // Admin sees all notifications
            const result = await (0, db_js_1.query)('SELECT COUNT(*) as count FROM notifications WHERE is_read = FALSE');
            count = result[0].count;
        }
        else if (req.user.department === 'AUDIT') {
            // Audit sees notifications for Audit and from departments
            const result = await (0, db_js_1.query)('SELECT COUNT(*) as count FROM notifications WHERE is_read = FALSE AND (user_id = ? OR user_id = ? OR from_audit = FALSE)', [req.user.id, 'AUDIT']);
            count = result[0].count;
        }
        else {
            // Departments see their own notifications and from Audit
            const result = await (0, db_js_1.query)('SELECT COUNT(*) as count FROM notifications WHERE is_read = FALSE AND (user_id = ? OR user_id = ? OR (from_audit = TRUE AND user_id = ?))', [req.user.id, req.user.department, req.user.department]);
            count = result[0].count;
        }
        res.json({ count });
    }
    catch (error) {
        logger_js_1.logger.error('Get unread notifications count error:', error);
        res.status(500).json({ error: 'Failed to get unread notifications count' });
    }
});
// Mark notification as read
exports.notificationRouter.put('/:id/read', async (req, res) => {
    try {
        const notificationId = req.params.id;
        // Get notification
        const notifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE id = ?', [notificationId]);
        if (notifications.length === 0) {
            return res.status(404).json({ error: 'Notification not found' });
        }
        const notification = notifications[0];
        // Check if user has access to this notification
        if (req.user.department !== 'SYSTEM ADMIN' &&
            notification.user_id !== req.user.id &&
            notification.user_id !== req.user.department &&
            !(req.user.department === 'AUDIT' && notification.from_audit === false) &&
            !(notification.from_audit === true && notification.user_id === req.user.department)) {
            return res.status(403).json({ error: 'Access denied' });
        }
        // Mark as read
        await (0, db_js_1.query)('UPDATE notifications SET is_read = TRUE WHERE id = ?', [notificationId]);
        // Get updated notification
        const updatedNotifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE id = ?', [notificationId]);
        res.json(updatedNotifications[0]);
    }
    catch (error) {
        logger_js_1.logger.error('Mark notification as read error:', error);
        res.status(500).json({ error: 'Failed to mark notification as read' });
    }
});
// Mark all notifications as read
exports.notificationRouter.put('/read/all', async (req, res) => {
    try {
        if (req.user.department === 'SYSTEM ADMIN') {
            // Admin marks all notifications as read
            await (0, db_js_1.query)('UPDATE notifications SET is_read = TRUE');
        }
        else if (req.user.department === 'AUDIT') {
            // Audit marks notifications for Audit and from departments as read
            await (0, db_js_1.query)('UPDATE notifications SET is_read = TRUE WHERE user_id = ? OR user_id = ? OR from_audit = FALSE', [req.user.id, 'AUDIT']);
        }
        else {
            // Departments mark their own notifications and from Audit as read
            await (0, db_js_1.query)('UPDATE notifications SET is_read = TRUE WHERE user_id = ? OR user_id = ? OR (from_audit = TRUE AND user_id = ?)', [req.user.id, req.user.department, req.user.department]);
        }
        res.json({ message: 'All notifications marked as read' });
    }
    catch (error) {
        logger_js_1.logger.error('Mark all notifications as read error:', error);
        res.status(500).json({ error: 'Failed to mark all notifications as read' });
    }
});
// Create notification
exports.notificationRouter.post('/', async (req, res) => {
    try {
        const { userId, message, voucherId, batchId, type, fromAudit = false } = req.body;
        // Validate required fields
        if (!userId || !message || !type) {
            return res.status(400).json({ error: 'User ID, message, and type are required' });
        }
        // Create notification
        const id = (0, uuid_1.v4)();
        await (0, db_js_1.query)(`INSERT INTO notifications (
        id, user_id, message, is_read, timestamp, voucher_id, batch_id, type, from_audit
      ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?, ?)`, [id, userId, message, false, voucherId || null, batchId || null, type, fromAudit]);
        // Get created notification
        const notifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE id = ?', [id]);
        res.status(201).json(notifications[0]);
    }
    catch (error) {
        logger_js_1.logger.error('Create notification error:', error);
        res.status(500).json({ error: 'Failed to create notification' });
    }
});
// Delete notification
exports.notificationRouter.delete('/:id', async (req, res) => {
    try {
        const notificationId = req.params.id;
        // Get notification
        const notifications = await (0, db_js_1.query)('SELECT * FROM notifications WHERE id = ?', [notificationId]);
        if (notifications.length === 0) {
            return res.status(404).json({ error: 'Notification not found' });
        }
        const notification = notifications[0];
        // Check if user has access to delete this notification
        if (req.user.department !== 'SYSTEM ADMIN' &&
            notification.user_id !== req.user.id &&
            notification.user_id !== req.user.department) {
            return res.status(403).json({ error: 'Access denied' });
        }
        // Delete notification
        await (0, db_js_1.query)('DELETE FROM notifications WHERE id = ?', [notificationId]);
        res.json({ message: 'Notification deleted successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Delete notification error:', error);
        res.status(500).json({ error: 'Failed to delete notification' });
    }
});
//# sourceMappingURL=notifications.js.map