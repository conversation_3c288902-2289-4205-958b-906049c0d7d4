"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_js_1 = require("../database/db.js");
const logger_js_1 = require("../utils/logger.js");
const auth_js_1 = require("../middleware/auth.js");
const router = express_1.default.Router();
// Apply authentication middleware to all routes
router.use(auth_js_1.authenticate);
// Admin access check middleware
function requireAdminAccess(req, res, next) {
    if (req.user.department === 'SYSTEM ADMIN' || req.user.role === 'admin') {
        next();
    }
    else {
        res.status(403).json({ error: 'Admin access required' });
    }
}
// Get pending password change requests (admin only)
router.get('/', async (req, res) => {
    try {
        const requests = await (0, db_js_1.query)(`
      SELECT
        pcr.*,
        u.name as current_user_name,
        u.department as current_user_department
      FROM password_change_requests pcr
      LEFT JOIN users u ON pcr.user_id = u.id
      WHERE pcr.status = 'PENDING'
      ORDER BY pcr.requested_at DESC
    `);
        res.json(requests);
    }
    catch (error) {
        logger_js_1.logger.error('Error fetching password change requests:', error);
        res.status(500).json({ error: 'Failed to fetch password change requests' });
    }
});
// Approve password change request (admin only)
router.put('/:requestId/approve', requireAdminAccess, async (req, res) => {
    try {
        const { requestId } = req.params;
        // Use authenticated user's information
        const adminId = req.user.id;
        const adminName = req.user.name;
        // Get the request
        const requests = await (0, db_js_1.query)('SELECT * FROM password_change_requests WHERE id = ? AND status = "PENDING"', [requestId]);
        if (requests.length === 0) {
            return res.status(404).json({ error: 'Password change request not found or already processed' });
        }
        const request = requests[0];
        // Update user's password
        await (0, db_js_1.query)('UPDATE users SET password = ? WHERE id = ?', [request.new_password_hash, request.user_id]);
        // Mark request as approved
        await (0, db_js_1.query)(`UPDATE password_change_requests 
       SET status = 'APPROVED', processed_at = NOW(), processed_by = ?, admin_notes = ?
       WHERE id = ?`, [adminName, `Approved by ${adminName}`, requestId]);
        logger_js_1.logger.info(`Password change request approved: ${requestId} by admin ${adminName}`);
        res.json({
            success: true,
            message: 'Password change request approved successfully'
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error approving password change request:', error);
        res.status(500).json({ error: 'Failed to approve password change request' });
    }
});
// Reject password change request (admin only)
router.put('/:requestId/reject', requireAdminAccess, async (req, res) => {
    try {
        const { requestId } = req.params;
        const { reason } = req.body;
        // Use authenticated user's information
        const adminId = req.user.id;
        const adminName = req.user.name;
        // Get the request
        const requests = await (0, db_js_1.query)('SELECT * FROM password_change_requests WHERE id = ? AND status = "PENDING"', [requestId]);
        if (requests.length === 0) {
            return res.status(404).json({ error: 'Password change request not found or already processed' });
        }
        // Mark request as rejected
        await (0, db_js_1.query)(`UPDATE password_change_requests 
       SET status = 'REJECTED', processed_at = NOW(), processed_by = ?, admin_notes = ?
       WHERE id = ?`, [adminName, reason || `Rejected by ${adminName}`, requestId]);
        logger_js_1.logger.info(`Password change request rejected: ${requestId} by admin ${adminName}`);
        res.json({
            success: true,
            message: 'Password change request rejected successfully'
        });
    }
    catch (error) {
        logger_js_1.logger.error('Error rejecting password change request:', error);
        res.status(500).json({ error: 'Failed to reject password change request' });
    }
});
exports.default = router;
//# sourceMappingURL=password-change-requests.js.map