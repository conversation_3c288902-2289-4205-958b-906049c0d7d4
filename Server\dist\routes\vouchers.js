"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.voucherRouter = void 0;
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
const socketHandlers_js_1 = require("../socket/socketHandlers.js");
const legacy_voucher_status_compat_js_1 = require("../utils/legacy-voucher-status-compat.js");
const badgeStateManager_js_1 = require("../utils/badgeStateManager.js");
const returnStateManager_1 = require("../utils/returnStateManager");
exports.voucherRouter = express_1.default.Router();
/**
 * Helper function to determine workflow state from status and voucher properties
 */
function determineWorkflowStateFromStatus(status, voucher) {
    const { department, original_department, received_by_audit, work_started, dispatched, audit_dispatched_by } = voucher;
    // CRITICAL FIX: Audit Department States (check first)
    if (department === 'AUDIT') {
        switch (status) {
            case 'AUDIT: PROCESSING':
                if (work_started) {
                    return 'AUDIT_PENDING_DISPATCH';
                }
                else {
                    return 'AUDIT_NEW';
                }
            case 'VOUCHER REJECTED':
                return 'AUDIT_PENDING_DISPATCH_REJECTED';
            case 'VOUCHER RETURNED':
                return 'AUDIT_PENDING_DISPATCH_RETURNED';
            case 'VOUCHER CERTIFIED':
                return 'AUDIT_DISPATCHED';
        }
    }
    // Finance Department States
    if (department !== 'AUDIT' && original_department !== 'AUDIT') {
        switch (status) {
            case 'PENDING SUBMISSION':
                return 'FINANCE_PENDING';
            case 'PENDING RECEIPT':
                return 'FINANCE_PROCESSING';
            case 'VOUCHER CERTIFIED':
                return 'FINANCE_CERTIFIED';
            case 'VOUCHER REJECTED':
                return 'FINANCE_REJECTED';
            case 'VOUCHER RETURNED':
                return 'FINANCE_RETURNED';
        }
    }
    // Finance receiving certified voucher back from Audit
    if (department === 'FINANCE' && status === 'VOUCHER CERTIFIED' && voucher.department_received_by) {
        return 'FINANCE_CERTIFIED';
    }
    // Audit Department States - check if voucher is currently in audit workflow
    if (department === 'AUDIT' || (original_department === 'AUDIT' && received_by_audit)) {
        switch (status) {
            case 'AUDIT: PROCESSING':
                if (received_by_audit && !work_started) {
                    return 'AUDIT_NEW';
                }
                else if (received_by_audit && work_started && !dispatched) {
                    return 'AUDIT_PENDING_DISPATCH';
                }
                else if (dispatched || audit_dispatched_by) {
                    return 'AUDIT_DISPATCHED';
                }
                break;
            case 'VOUCHER REJECTED':
                if (received_by_audit && !work_started) {
                    return 'AUDIT_PENDING_DISPATCH_REJECTED';
                }
                else if (received_by_audit && work_started && !dispatched) {
                    return 'AUDIT_PENDING_DISPATCH_REJECTED';
                }
                else if (dispatched || audit_dispatched_by) {
                    return 'FINANCE_REJECTED';
                }
                break;
            case 'VOUCHER CERTIFIED':
                return 'AUDIT_DISPATCHED';
            case 'VOUCHER REJECTED':
                return 'AUDIT_REJECTED';
            case 'VOUCHER RETURNED':
                return 'AUDIT_RETURNED';
        }
    }
    return null;
}
// Apply authentication middleware to all routes
exports.voucherRouter.use(auth_js_1.authenticate);
// Removed seeding endpoint - fixing root causes instead
// Get all vouchers - Updated for new Department Voucher Hub architecture
exports.voucherRouter.get('/', async (req, res) => {
    try {
        const { department } = req.query;
        const timestamp = new Date().toISOString();
        logger_js_1.logger.info(`GET /vouchers request at ${timestamp} by user ${req.user.name} (${req.user.department}), query department: ${department || 'none'}`);
        let vouchers;
        if (department) {
            // NEW ARCHITECTURE: Return ALL vouchers related to the department
            // Let client-side filtering handle the specific tab logic
            logger_js_1.logger.info(`Fetching vouchers for specified department: ${department} (NEW ARCHITECTURE)`);
            if (department === 'AUDIT') {
                // AUDIT sees: vouchers currently in AUDIT + permanent records + returned copies
                vouchers = await (0, db_js_1.query)(`
          SELECT * FROM vouchers
          WHERE (
            department = 'AUDIT' OR
            original_department = 'AUDIT' OR

            is_rejection_copy = 1 OR
            status IN ('PENDING RECEIPT', 'AUDIT: PROCESSING', 'VOUCHER CERTIFIED', 'VOUCHER REJECTED')
          ) AND (deleted IS NULL OR deleted = FALSE)
          ORDER BY created_at DESC
        `);
            }
            else {
                // OTHER DEPARTMENTS see: vouchers that belong to them + dispatched vouchers + returned vouchers
                // CRITICAL FIX: Exclude return copies from Finance queries
                vouchers = await (0, db_js_1.query)(`
          SELECT * FROM vouchers
          WHERE (
            department = ? OR
            original_department = ? OR
            (original_department = ? AND status IN ('AUDIT: PROCESSING', 'VOUCHER CERTIFIED', 'VOUCHER REJECTED'))
          ) AND (deleted IS NULL OR deleted = FALSE)
          AND (is_returned_copy IS NULL OR is_returned_copy = 0)
          ORDER BY created_at DESC
        `, [department, department, department]);
            }
        }
        else if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
            // Audit and Admin see all vouchers
            logger_js_1.logger.info(`Fetching all vouchers for ${req.user.department} user`);
            vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE (deleted IS NULL OR deleted = FALSE) ORDER BY created_at DESC');
        }
        else {
            // Other departments - use same logic as department-specific query
            logger_js_1.logger.info(`Fetching vouchers for user's department: ${req.user.department} (NEW ARCHITECTURE)`);
            vouchers = await (0, db_js_1.query)(`
        SELECT * FROM vouchers
        WHERE (
          department = ? OR
          original_department = ? OR
          (original_department = ? AND status IN ('AUDIT: PROCESSING', 'VOUCHER CERTIFIED', 'VOUCHER REJECTED'))
        ) AND (deleted IS NULL OR deleted = FALSE)
        AND (is_returned_copy IS NULL OR is_returned_copy = 0)
        ORDER BY created_at DESC
      `, [req.user.department, req.user.department, req.user.department]);
        }
        // Log the number of vouchers found
        logger_js_1.logger.info(`Found ${vouchers.length} vouchers for request from ${req.user.name} (${req.user.department})`);
        // This transformation is now done later in the code
        // If no vouchers found, check if there should be any
        if (vouchers.length === 0) {
            const deptToCheck = department || req.user.department;
            if (deptToCheck !== 'AUDIT' && deptToCheck !== 'SYSTEM ADMIN') {
                // Check if there are any vouchers for this department at all
                const voucherCount = await (0, db_js_1.query)('SELECT COUNT(*) as count FROM vouchers WHERE department = ?', [deptToCheck]);
                if (voucherCount[0].count > 0) {
                    logger_js_1.logger.warn(`No non-deleted vouchers found for department ${deptToCheck}, but ${voucherCount[0].count} total vouchers exist`);
                }
            }
        }
        // Check for gaps in voucher IDs
        if (vouchers.length > 0) {
            // Group vouchers by department
            const vouchersByDept = {};
            for (const voucher of vouchers) {
                if (!vouchersByDept[voucher.department]) {
                    vouchersByDept[voucher.department] = [];
                }
                vouchersByDept[voucher.department].push(voucher);
            }
            // Check each department for gaps
            for (const [dept, deptVouchers] of Object.entries(vouchersByDept)) {
                // Group by month prefix
                const vouchersByPrefix = {};
                for (const voucher of deptVouchers) {
                    if (!voucher.voucher_id)
                        continue;
                    const prefix = voucher.voucher_id.substring(0, 3);
                    if (!vouchersByPrefix[prefix]) {
                        vouchersByPrefix[prefix] = [];
                    }
                    vouchersByPrefix[prefix].push(voucher.voucher_id);
                }
                // Check each prefix group for gaps
                for (const [prefix, ids] of Object.entries(vouchersByPrefix)) {
                    if (ids.length <= 1)
                        continue;
                    // Extract numeric parts
                    const numericParts = ids.map((id) => parseInt(id.substring(3)));
                    numericParts.sort((a, b) => a - b);
                    // Check for gaps
                    for (let i = 1; i < numericParts.length; i++) {
                        if (numericParts[i] - numericParts[i - 1] > 1) {
                            logger_js_1.logger.warn(`Gap detected in ${dept} voucher sequence: ${prefix}${numericParts[i - 1].toString().padStart(5, '0')} -> ${prefix}${numericParts[i].toString().padStart(5, '0')}`);
                        }
                    }
                }
            }
        }
        // Log the first few vouchers for debugging
        if (vouchers.length > 0) {
            const sampleVouchers = vouchers.slice(0, 3).map(v => `${v.voucher_id} (${v.id}): ${v.status}`);
            logger_js_1.logger.info(`Sample vouchers: ${sampleVouchers.join(', ')}`);
        }
        // FIXED: Transform database fields to client-compatible format
        const transformedVouchersForDept = vouchers.map(voucher => {
            // Parse flags JSON safely
            let flags = {};
            try {
                if (typeof voucher.flags === 'string') {
                    flags = JSON.parse(voucher.flags);
                }
                else if (typeof voucher.flags === 'object' && voucher.flags !== null) {
                    flags = voucher.flags;
                }
            }
            catch (error) {
                logger_js_1.logger.warn(`Failed to parse flags for voucher ${voucher.id}:`, error);
                flags = {};
            }
            return {
                // Core voucher data
                id: voucher.id,
                date: voucher.date,
                claimant: voucher.claimant,
                description: voucher.description,
                amount: voucher.amount,
                currency: voucher.currency,
                department: voucher.department,
                status: voucher.status,
                comment: voucher.comment,
                // Map snake_case to camelCase for client compatibility
                voucherId: voucher.voucher_id,
                originalDepartment: voucher.original_department, // 🔧 CRITICAL FIX: Add missing originalDepartment transformation
                createdBy: voucher.created_by,
                dispatchedBy: voucher.dispatched_by,
                dispatchTime: voucher.dispatch_time,
                sentToAudit: Boolean(voucher.sent_to_audit),
                batchId: voucher.batch_id,
                receivedBy: voucher.received_by,
                receivedByAudit: Boolean(voucher.received_by_audit), // 🔧 CRITICAL FIX: Add missing receivedByAudit transformation
                receiptTime: voucher.receipt_time,
                taxType: voucher.tax_type,
                taxDetails: voucher.tax_details,
                taxAmount: voucher.tax_amount,
                preAuditedAmount: voucher.pre_audited_amount,
                preAuditedBy: voucher.pre_audited_by,
                certifiedBy: voucher.certified_by,
                auditDispatchTime: voucher.audit_dispatch_time,
                auditDispatchedBy: voucher.audit_dispatched_by,
                dispatchToOnDepartment: Boolean(voucher.dispatch_to_on_department),
                postProvisionalCash: Boolean(voucher.post_provisional_cash),
                dispatched: Boolean(voucher.dispatched),
                dispatchToAuditBy: voucher.dispatch_to_audit_by,
                deleted: Boolean(voucher.deleted),
                deletionTime: voucher.deletion_time,
                rejectionTime: voucher.rejection_time,
                departmentReceiptTime: voucher.department_receipt_time,
                departmentReceivedBy: voucher.department_received_by,
                departmentRejected: Boolean(voucher.department_rejected),
                rejectedBy: voucher.rejected_by,
                referenceId: voucher.reference_id,
                idempotencyKey: voucher.idempotency_key,
                workStarted: Boolean(voucher.work_started), // CRITICAL FIX: Map workStarted field
                createdAt: voucher.created_at,
                // DUAL-TAB WORKFLOW: Transform dual-tab fields
                rejectionType: voucher.rejection_type,
                isRejectionCopy: Boolean(voucher.is_rejection_copy),
                rejectionWorkflowStage: voucher.rejection_workflow_stage,
                parentVoucherId: voucher.parent_voucher_id,
                // RETURN VOUCHER WORKFLOW: Transform return copy fields
                isReturnedCopy: Boolean(voucher.is_returned_copy),
                is_returned_copy: voucher.is_returned_copy,
                // RETURN COMMENT MAPPING: Map comment to returnComment for returned vouchers
                returnComment: voucher.status === 'VOUCHER RETURNED' ? voucher.comment : undefined,
                // RETURN TIME MAPPING: Map return_time to returnTime for returned vouchers
                returnTime: voucher.return_time,
                returnedBy: voucher.returned_by,
                // NEW ARCHITECTURE: Add fields expected by client-side filtering
                dispatchedAt: voucher.dispatch_time || voucher.audit_dispatch_time,
                // WORKFLOW STATE MIGRATION: Include workflow state
                workflow_state: voucher.workflow_state,
                // REQUIREMENT 3: Original rejection reason fields for batch receiving display
                original_rejection_reason: voucher.original_rejection_reason,
                originalRejectionReason: voucher.original_rejection_reason,
                original_rejected_by: voucher.original_rejected_by,
                originalRejectedBy: voucher.original_rejected_by,
                original_rejection_date: voucher.original_rejection_date,
                originalRejectionDate: voucher.original_rejection_date,
                // Ensure flags are accessible
                flags: flags
            };
        });
        // DEBUG: Log sample voucher data to understand the date issue
        if (transformedVouchersForDept.length > 0) {
            const sampleVoucher = transformedVouchersForDept[0];
            logger_js_1.logger.info(`🔍 DEBUG - Sample voucher data:`, {
                voucherId: sampleVoucher.voucherId,
                receiptTime: sampleVoucher.receiptTime,
                receivedBy: sampleVoucher.receivedBy,
                receivedByAudit: sampleVoucher.receivedByAudit,
                status: sampleVoucher.status
            });
        }
        res.json(transformedVouchersForDept);
    }
    catch (error) {
        logger_js_1.logger.error('Get vouchers error:', error);
        res.status(500).json({ error: 'Failed to get vouchers' });
    }
});
// Get voucher by ID
exports.voucherRouter.get('/:id', async (req, res) => {
    try {
        const voucherId = req.params.id;
        // Get voucher
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }
        const voucher = vouchers[0];
        // Check if user has access to this voucher
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && voucher.department !== req.user.department) {
            return res.status(403).json({ error: 'Access denied' });
        }
        // FIXED: Transform database fields to client-compatible format
        const transformedVoucher = {
            ...voucher,
            // Map snake_case to camelCase for client compatibility
            voucherId: voucher.voucher_id,
            originalDepartment: voucher.original_department, // CRITICAL FIX: Include original department
            createdBy: voucher.created_by,
            dispatchedBy: voucher.dispatched_by,
            dispatchTime: voucher.dispatch_time,
            sentToAudit: Boolean(voucher.sent_to_audit),
            batchId: voucher.batch_id,
            receivedBy: voucher.received_by,
            receivedByAudit: Boolean(voucher.received_by_audit), // CRITICAL FIX: Include audit receipt status
            receiptTime: voucher.receipt_time,
            taxType: voucher.tax_type,
            taxDetails: voucher.tax_details,
            taxAmount: voucher.tax_amount,
            preAuditedAmount: voucher.pre_audited_amount,
            preAuditedBy: voucher.pre_audited_by,
            certifiedBy: voucher.certified_by,
            auditDispatchTime: voucher.audit_dispatch_time,
            auditDispatchedBy: voucher.audit_dispatched_by,
            dispatchToOnDepartment: Boolean(voucher.dispatch_to_on_department),
            postProvisionalCash: Boolean(voucher.post_provisional_cash),
            dispatched: Boolean(voucher.dispatched),
            dispatchToAuditBy: voucher.dispatch_to_audit_by,
            deleted: Boolean(voucher.deleted),
            deletionTime: voucher.deletion_time,
            rejectionTime: voucher.rejection_time,
            departmentReceiptTime: voucher.department_receipt_time,
            departmentReceivedBy: voucher.department_received_by,
            departmentRejected: Boolean(voucher.department_rejected),
            rejectedBy: voucher.rejected_by,
            referenceId: voucher.reference_id,
            idempotencyKey: voucher.idempotency_key,
            workStarted: Boolean(voucher.work_started), // CRITICAL FIX: Map workStarted field
            createdAt: voucher.created_at,
            // DUAL-TAB WORKFLOW: Transform dual-tab fields
            rejectionType: voucher.rejection_type,
            isRejectionCopy: Boolean(voucher.is_rejection_copy),
            rejectionWorkflowStage: voucher.rejection_workflow_stage,
            parentVoucherId: voucher.parent_voucher_id,
            // RETURN VOUCHER WORKFLOW: Transform return copy fields
            isReturnedCopy: Boolean(voucher.is_returned_copy),
            is_returned_copy: voucher.is_returned_copy,
            // RETURN COMMENT MAPPING: Map comment to returnComment for returned vouchers
            returnComment: voucher.status === 'VOUCHER RETURNED' ? voucher.comment : undefined,
            // RETURN TIME MAPPING: Map return_time to returnTime for returned vouchers
            returnTime: voucher.return_time,
            returnedBy: voucher.returned_by,
            // RESUBMISSION FIELDS: Critical for badge persistence
            isResubmitted: Boolean(voucher.is_resubmitted),
            is_resubmitted: voucher.is_resubmitted,
            resubmission_certified_visible_to_finance: voucher.resubmission_certified_visible_to_finance,
            resubmission_tracking_visible_to_audit: voucher.resubmission_tracking_visible_to_audit,
            // REQUIREMENT 3: Original rejection reason fields for batch receiving display
            original_rejection_reason: voucher.original_rejection_reason,
            originalRejectionReason: voucher.original_rejection_reason,
            original_rejected_by: voucher.original_rejected_by,
            originalRejectedBy: voucher.original_rejected_by,
            original_rejection_date: voucher.original_rejection_date,
            originalRejectionDate: voucher.original_rejection_date
        };
        res.json(transformedVoucher);
    }
    catch (error) {
        logger_js_1.logger.error('Get voucher error:', error);
        res.status(500).json({ error: 'Failed to get voucher' });
    }
});
// OLD generateVoucherId function REMOVED to prevent conflicts
// Now using department-specific voucher ID generation inline in the POST endpoint
// Create voucher with comprehensive duplicate prevention
exports.voucherRouter.post('/', async (req, res) => {
    try {
        const { claimant, description, amount, currency, department, taxType, taxDetails, taxAmount, comment, idempotencyKey } = req.body;
        // Validate required fields
        if (!claimant || !description || !amount || !currency || !department) {
            return res.status(400).json({ error: 'Missing required fields' });
        }
        // CRITICAL FIX: Enhanced duplicate prevention
        // Check for recent duplicate submissions (same user, description, amount within 5 minutes)
        const recentDuplicates = await (0, db_js_1.query)(`
      SELECT * FROM vouchers
      WHERE created_by = ?
        AND description = ?
        AND amount = ?
        AND department = ?
        AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        AND deleted = FALSE
      ORDER BY created_at DESC
      LIMIT 1
    `, [req.user.id, description, amount, department]);
        if (recentDuplicates.length > 0) {
            logger_js_1.logger.warn(`Duplicate voucher creation blocked: ${description} by ${req.user.name}`);
            return res.status(200).json({
                ...recentDuplicates[0],
                message: 'Voucher already exists - returning existing voucher'
            });
        }
        // CORRECT PERMISSION LOGIC: Enhanced permission check with detailed logging
        logger_js_1.logger.info('🔐 VMSPROD VOUCHER CREATION - Permission check:', {
            userDepartment: req.user.department,
            requestedDepartment: department,
            userRole: req.user.role,
            userName: req.user.name,
            isAudit: req.user.department === 'AUDIT',
            isSystemAdmin: req.user.department === 'SYSTEM ADMIN',
            departmentMatch: department === req.user.department,
            isUserRole: req.user.role === 'USER',
            isViewerRole: req.user.role === 'VIEWER'
        });
        // CORRECT PERMISSION LOGIC:
        // 1. VIEWER role cannot create vouchers (only view)
        // 2. USER role can create vouchers for their own department
        // 3. SYSTEM ADMIN can create vouchers for any department
        // 4. AUDIT does NOT create vouchers - they receive and process them
        const canCreateVoucher = (req.user.role === 'USER' && department === req.user.department || // USER role for own department
            req.user.department === 'SYSTEM ADMIN' // SYSTEM ADMIN for any department
        );
        // Block VIEWER role completely
        if (req.user.role === 'VIEWER') {
            logger_js_1.logger.warn('🚫 VMSPROD VOUCHER CREATION - Access denied for VIEWER role:', {
                userDepartment: req.user.department,
                requestedDepartment: department,
                userRole: req.user.role,
                reason: 'VIEWER role cannot create vouchers'
            });
            return res.status(403).json({
                error: 'Access denied. VIEWER role cannot create vouchers.',
                details: {
                    userDepartment: req.user.department,
                    requestedDepartment: department,
                    userRole: req.user.role
                }
            });
        }
        // Block AUDIT department from creating vouchers
        if (req.user.department === 'AUDIT') {
            logger_js_1.logger.warn('🚫 VMSPROD VOUCHER CREATION - Access denied for AUDIT department:', {
                userDepartment: req.user.department,
                requestedDepartment: department,
                userRole: req.user.role,
                reason: 'AUDIT department does not create vouchers - they receive and process them'
            });
            return res.status(403).json({
                error: 'Access denied. AUDIT department does not create vouchers.',
                details: {
                    userDepartment: req.user.department,
                    requestedDepartment: department,
                    userRole: req.user.role
                }
            });
        }
        // Check general permission
        if (!canCreateVoucher) {
            logger_js_1.logger.warn('🚫 VMSPROD VOUCHER CREATION - Access denied:', {
                userDepartment: req.user.department,
                requestedDepartment: department,
                userRole: req.user.role,
                reason: 'User does not have permission to create vouchers for this department'
            });
            return res.status(403).json({
                error: 'Access denied. You can only create vouchers for your own department.',
                details: {
                    userDepartment: req.user.department,
                    requestedDepartment: department,
                    userRole: req.user.role
                }
            });
        }
        logger_js_1.logger.info('✅ VMSPROD VOUCHER CREATION - Permission granted for user:', {
            userName: req.user.name,
            userDepartment: req.user.department,
            userRole: req.user.role,
            targetDepartment: department
        });
        // PRODUCTION: Enhanced idempotency protection
        if (idempotencyKey) {
            const existingVouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE created_by = ? AND department = ? AND idempotency_key = ? AND deleted = FALSE', [req.user.id, department, idempotencyKey]);
            if (existingVouchers.length > 0) {
                logger_js_1.logger.info(`Duplicate voucher creation attempt blocked for user ${req.user.id} with key ${idempotencyKey}`);
                return res.status(200).json({
                    ...existingVouchers[0],
                    message: 'Voucher already exists with this idempotency key'
                });
            }
        }
        // SIMPLE: Use transaction for atomicity - existing duplicate prevention is sufficient
        const connection = await (0, db_js_1.getTransaction)();
        try {
            // PERMANENT FIX: Generate department-specific voucher ID within the transaction
            const now = new Date();
            const month = now.toLocaleString('en-US', { month: 'short' }).toUpperCase();
            // Create unique department prefix mapping
            const deptPrefixMap = {
                'FINANCE': 'FIN',
                'MINISTRIES': 'MIN',
                'PENSIONS': 'PEN',
                'PENTMEDIA': 'PMD', // Unique prefix for PENTMEDIA
                'MISSIONS': 'MIS',
                'PENTSOS': 'PSO', // Unique prefix for PENTSOS
                'AUDIT': 'AUD',
                'SYSTEM ADMIN': 'SYS'
            };
            const deptPrefix = deptPrefixMap[department] || department.substring(0, 3).toUpperCase();
            // CRITICAL FIX: Use a more robust approach to get the next voucher number
            // Count existing vouchers GLOBALLY for this prefix and month to avoid duplicates
            // when vouchers are transferred between departments
            const [countResult] = await connection.query(`SELECT COUNT(*) as voucher_count
         FROM vouchers
         WHERE voucher_id LIKE ?
         FOR UPDATE`, [`${deptPrefix}${month}%`]);
            // Calculate the next voucher number based on count (ensures no gaps)
            const nextNumber = (countResult[0]?.voucher_count || 0) + 1;
            let voucherId = `${deptPrefix}${month}${nextNumber.toString().padStart(4, '0')}`;
            // SAFETY CHECK: Verify this voucher ID doesn't already exist
            const [existingCheck] = await connection.query('SELECT COUNT(*) as exists_count FROM vouchers WHERE voucher_id = ?', [voucherId]);
            if (existingCheck[0]?.exists_count > 0) {
                // If somehow the ID exists, use MAX approach as fallback
                // CRITICAL FIX: Check GLOBALLY across all departments
                const [maxResult] = await connection.query(`SELECT MAX(CAST(SUBSTRING(voucher_id, 7) AS UNSIGNED)) as max_count
           FROM vouchers
           WHERE voucher_id LIKE ?`, [`${deptPrefix}${month}%`]);
                const fallbackNumber = (maxResult[0]?.max_count || 0) + 1;
                const fallbackVoucherId = `${deptPrefix}${month}${fallbackNumber.toString().padStart(4, '0')}`;
                logger_js_1.logger.warn(`Voucher ID collision detected. Using fallback: ${voucherId} → ${fallbackVoucherId}`);
                voucherId = fallbackVoucherId;
            }
            // Log the voucher ID generation
            logger_js_1.logger.info(`Generated department-specific voucher ID: ${voucherId} for department: ${department} (prefix: ${deptPrefix})`);
            // Create voucher
            const id = (0, uuid_1.v4)();
            const date = new Date().toLocaleString('en-US', {
                day: '2-digit',
                month: 'long',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            }).toUpperCase();
            // PERMANENT SOLUTION: Insert voucher with correct PENDING status for immediate display
            await connection.query(`INSERT INTO vouchers (
          id, voucher_id, date, claimant, description, amount, currency, department,
          original_department, dispatched_by, dispatch_time, status, sent_to_audit, created_by,
          tax_type, tax_details, tax_amount, comment, idempotency_key, deleted, workflow_state
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
                id, voucherId, date, claimant, description, amount, currency, department,
                department, // Set original_department same as department initially
                '', '', legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.PENDING, false, req.user.name,
                taxType || null, taxDetails || null, taxAmount || null, comment || null, idempotencyKey || null, false,
                'FINANCE_PENDING' // Set initial workflow state
            ]);
            // Commit the transaction
            await connection.commit();
            // Get created voucher
            const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [id]);
            const createdVoucher = vouchers[0];
            // Log the created voucher for debugging
            logger_js_1.logger.info(`Created voucher with ID: ${id}, Voucher ID: ${voucherId}`);
            console.log('Created voucher:', createdVoucher);
            // Ensure the voucher_id is properly set in the response
            if (!createdVoucher.voucher_id) {
                createdVoucher.voucher_id = voucherId;
            }
            // CRITICAL FIX: Format voucher for client and broadcast
            const clientFormattedVoucher = {
                ...createdVoucher,
                // Ensure client-compatible field names
                voucherId: createdVoucher.voucher_id,
                createdBy: createdVoucher.created_by,
                dispatchedBy: createdVoucher.dispatched_by || '',
                sentToAudit: Boolean(createdVoucher.sent_to_audit),
                dispatched: Boolean(createdVoucher.dispatched),
                lastUpdated: new Date().toISOString()
            };
            (0, socketHandlers_js_1.broadcastVoucherUpdate)('created', clientFormattedVoucher);
            res.status(201).json(createdVoucher);
        }
        catch (error) {
            // Rollback the transaction on error
            await connection.rollback();
            throw error;
        }
        finally {
            // Release the connection
            connection.release();
        }
    }
    catch (error) {
        logger_js_1.logger.error('Create voucher error:', error);
        res.status(500).json({ error: 'Failed to create voucher' });
    }
});
// Update voucher
exports.voucherRouter.put('/:id', async (req, res) => {
    try {
        const voucherId = req.params.id;
        // Get voucher
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }
        const voucher = vouchers[0];
        // Check if user has access to update this voucher
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && voucher.department !== req.user.department) {
            return res.status(403).json({ error: 'Access denied' });
        }
        // Define field mapping from frontend camelCase to database snake_case
        const fieldMapping = {
            // Basic fields
            'voucherId': 'voucher_id',
            'voucher_id': 'voucher_id',
            'claimant': 'claimant',
            'description': 'description',
            'amount': 'amount',
            'currency': 'currency',
            'department': 'original_department',
            'status': 'status',
            'comment': 'comment',
            'date': 'date',
            // CRITICAL FIX: Add original rejection reason fields
            'original_rejection_reason': 'original_rejection_reason',
            'originalRejectionReason': 'original_rejection_reason',
            'original_rejected_by': 'original_rejected_by',
            'originalRejectedBy': 'original_rejected_by',
            'original_rejection_date': 'original_rejection_date',
            'originalRejectionDate': 'original_rejection_date',
            // Audit fields
            'preAuditedAmount': 'pre_audited_amount',
            'pre_audited_amount': 'pre_audited_amount',
            'preAuditedBy': 'pre_audited_by',
            'pre_audited_by': 'pre_audited_by',
            'certifiedBy': 'certified_by',
            'certified_by': 'certified_by',
            'taxType': 'tax_type',
            'tax_type': 'tax_type',
            'taxDetails': 'tax_details',
            'tax_details': 'tax_details',
            'taxAmount': 'tax_amount',
            'tax_amount': 'tax_amount',
            'certified': 'certified',
            'receivedByAudit': 'received_by_audit',
            'received_by_audit': 'received_by_audit',
            'certificationDate': 'certification_date',
            'certification_date': 'certification_date',
            // Dispatch fields
            'dispatchedBy': 'dispatched_by',
            'dispatched_by': 'dispatched_by',
            'dispatchTime': 'dispatch_time',
            'dispatch_time': 'dispatch_time',
            'dispatched': 'dispatched',
            'dispatchToAuditBy': 'dispatch_to_audit_by',
            'dispatch_to_audit_by': 'dispatch_to_audit_by',
            'auditDispatchTime': 'audit_dispatch_time',
            'audit_dispatch_time': 'audit_dispatch_time',
            'auditDispatchedBy': 'audit_dispatched_by',
            'audit_dispatched_by': 'audit_dispatched_by',
            // Receipt fields
            'receivedBy': 'received_by',
            'received_by': 'received_by',
            'receiptTime': 'receipt_time',
            'receipt_time': 'receipt_time',
            'departmentReceiptTime': 'department_receipt_time',
            'department_receipt_time': 'department_receipt_time',
            'departmentReceivedBy': 'department_received_by',
            'department_received_by': 'department_received_by',
            // Rejection fields
            'rejectedBy': 'rejected_by',
            'rejected_by': 'rejected_by',
            'rejectionTime': 'rejection_time',
            'rejection_time': 'rejection_time',
            'departmentRejected': 'department_rejected',
            'department_rejected': 'department_rejected',
            'rejectionReason': 'rejection_reason',
            'rejection_reason': 'rejection_reason',
            // Resubmission fields
            'isResubmitted': 'is_resubmitted',
            'is_resubmitted': 'is_resubmitted',
            'resubmissionDate': 'last_resubmission_date',
            'lastResubmissionDate': 'last_resubmission_date',
            'last_resubmission_date': 'last_resubmission_date',
            'resubmission_certified_visible_to_finance': 'resubmission_certified_visible_to_finance',
            'resubmission_tracking_visible_to_audit': 'resubmission_tracking_visible_to_audit',
            // Lock fields
            'isLocked': 'is_locked',
            'is_locked': 'is_locked',
            'lockedBy': 'locked_by',
            'locked_by': 'locked_by',
            'lockTime': 'lock_time',
            'lock_time': 'lock_time',
            'lastUpdatedBy': 'last_updated_by',
            'last_updated_by': 'last_updated_by',
            // Other fields
            'sentToAudit': 'sent_to_audit',
            'sent_to_audit': 'sent_to_audit',
            'batchId': 'batch_id',
            'batch_id': 'batch_id',
            'createdBy': 'created_by',
            'created_by': 'created_by',
            'deleted': 'deleted',
            'deletionTime': 'deletion_time',
            'deletion_time': 'deletion_time',
            'postProvisionalCash': 'post_provisional_cash',
            'post_provisional_cash': 'post_provisional_cash',
            'dispatchToOnDepartment': 'dispatch_to_on_department',
            'dispatch_to_on_department': 'dispatch_to_on_department',
            'referenceId': 'reference_id',
            'reference_id': 'reference_id',
            'version': 'version',
            'flags': 'flags',
            'idempotencyKey': 'idempotency_key',
            'idempotency_key': 'idempotency_key',
            'workStarted': 'work_started',
            'work_started': 'work_started',
            // Note: final_status, batch_processed, and batch_processed_time fields removed
            // as they don't exist in the database schema
        };
        // Build update query
        let updateQuery = 'UPDATE vouchers SET ';
        const updateParams = [];
        const updates = [];
        // DEBUGGING: Log all incoming fields for troubleshooting
        logger_js_1.logger.info(`🔍 VOUCHER UPDATE DEBUG: Incoming fields for voucher ${voucherId}:`, Object.keys(req.body));
        logger_js_1.logger.info(`🔍 VOUCHER UPDATE DEBUG: Full request body:`, req.body);
        // Process each field in the request body
        for (const [key, value] of Object.entries(req.body)) {
            // Skip fields that shouldn't be updated directly
            if (key === 'id' || key === 'lastUpdated') {
                logger_js_1.logger.info(`🔍 VOUCHER UPDATE DEBUG: Skipping field: ${key}`);
                continue;
            }
            // Get the correct database column name
            const columnName = fieldMapping[key];
            if (columnName) {
                updates.push(`${columnName} = ?`);
                updateParams.push(value);
                logger_js_1.logger.info(`🔍 VOUCHER UPDATE DEBUG: Mapped field: ${key} → ${columnName} = ${value}`);
            }
            else {
                // Log unknown fields but don't fail
                logger_js_1.logger.warn(`🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: ${key} = ${value}`);
            }
        }
        // If no updates, return early with detailed error
        if (updates.length === 0) {
            logger_js_1.logger.error(`🔍 VOUCHER UPDATE DEBUG: No valid updates for voucher ${voucherId}. Received fields: ${Object.keys(req.body).join(', ')}`);
            return res.status(400).json({
                error: 'No valid updates provided',
                receivedFields: Object.keys(req.body),
                validFields: Object.keys(fieldMapping)
            });
        }
        // WORKFLOW STATE MIGRATION: Add workflow state update if status or workflow-related fields are being changed
        if (req.body.status || req.body.department || req.body.receivedByAudit || req.body.workStarted || req.body.dispatched || req.body.auditDispatchedBy || req.body.departmentReceivedBy) {
            // PRODUCTION FIX: Handle audit_dispatched_by field correctly based on workflow state
            let auditDispatchedByValue = voucher.audit_dispatched_by;
            // If moving to PENDING DISPATCH (workStarted=true, dispatched=false), clear audit_dispatched_by
            if (req.body.workStarted === true && req.body.dispatched === false) {
                auditDispatchedByValue = null;
                logger_js_1.logger.info(`🔄 WORKFLOW FIX: Clearing audit_dispatched_by for voucher ${voucherId} moving to PENDING DISPATCH`);
            }
            else if (req.body.auditDispatchedBy !== undefined) {
                auditDispatchedByValue = req.body.auditDispatchedBy;
            }
            // Create updated voucher object for workflow state determination
            const updatedVoucherForWorkflow = {
                ...voucher,
                status: req.body.status || voucher.status,
                department: req.body.department || voucher.department,
                received_by_audit: req.body.receivedByAudit !== undefined ? req.body.receivedByAudit : voucher.received_by_audit,
                work_started: req.body.workStarted !== undefined ? req.body.workStarted : voucher.work_started,
                dispatched: req.body.dispatched !== undefined ? req.body.dispatched : voucher.dispatched,
                audit_dispatched_by: auditDispatchedByValue,
                department_received_by: req.body.departmentReceivedBy || voucher.department_received_by
            };
            const workflowState = determineWorkflowStateFromStatus(updatedVoucherForWorkflow.status, updatedVoucherForWorkflow);
            if (workflowState) {
                updates.push('workflow_state = ?');
                updateParams.push(workflowState);
                logger_js_1.logger.info(`🔄 Updating workflow_state to: ${workflowState} for voucher ${voucherId}`);
            }
            // PRODUCTION FIX: Ensure audit_dispatched_by is updated in the database
            if (auditDispatchedByValue !== voucher.audit_dispatched_by) {
                updates.push('audit_dispatched_by = ?');
                updateParams.push(auditDispatchedByValue);
                logger_js_1.logger.info(`🔄 WORKFLOW FIX: Setting audit_dispatched_by to: ${auditDispatchedByValue} for voucher ${voucherId}`);
            }
        }
        updateQuery += updates.join(', ') + ' WHERE id = ?';
        updateParams.push(voucherId);
        // Log the update query for debugging
        logger_js_1.logger.info(`Updating voucher ${voucherId} with query: ${updateQuery}`);
        logger_js_1.logger.info(`Update parameters:`, updateParams);
        // Execute update
        await (0, db_js_1.query)(updateQuery, updateParams);
        // Get updated voucher
        const updatedVouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
        const updatedVoucher = updatedVouchers[0];
        // Transform voucher for client compatibility before broadcasting
        const transformedVoucher = {
            ...updatedVoucher,
            voucherId: updatedVoucher.voucher_id,
            originalDepartment: updatedVoucher.original_department,
            createdBy: updatedVoucher.created_by,
            dispatchedBy: updatedVoucher.dispatched_by,
            dispatchTime: updatedVoucher.dispatch_time,
            sentToAudit: Boolean(updatedVoucher.sent_to_audit),
            batchId: updatedVoucher.batch_id,
            receivedBy: updatedVoucher.received_by,
            receivedByAudit: Boolean(updatedVoucher.received_by_audit),
            receiptTime: updatedVoucher.receipt_time,
            taxType: updatedVoucher.tax_type,
            taxDetails: updatedVoucher.tax_details,
            taxAmount: updatedVoucher.tax_amount,
            preAuditedAmount: updatedVoucher.pre_audited_amount,
            preAuditedBy: updatedVoucher.pre_audited_by,
            certifiedBy: updatedVoucher.certified_by,
            auditDispatchTime: updatedVoucher.audit_dispatch_time,
            auditDispatchedBy: updatedVoucher.audit_dispatched_by,
            dispatchToOnDepartment: Boolean(updatedVoucher.dispatch_to_on_department),
            postProvisionalCash: Boolean(updatedVoucher.post_provisional_cash),
            dispatched: Boolean(updatedVoucher.dispatched),
            dispatchToAuditBy: updatedVoucher.dispatch_to_audit_by,
            workStarted: Boolean(updatedVoucher.work_started),
            deleted: Boolean(updatedVoucher.deleted),
            deletionTime: updatedVoucher.deletion_time,
            rejectionTime: updatedVoucher.rejection_time,
            departmentReceiptTime: updatedVoucher.department_receipt_time,
            departmentReceivedBy: updatedVoucher.department_received_by,
            departmentRejected: Boolean(updatedVoucher.department_rejected),
            rejectedBy: updatedVoucher.rejected_by,
            referenceId: updatedVoucher.reference_id,
            idempotencyKey: updatedVoucher.idempotency_key,
            createdAt: updatedVoucher.created_at,
            // RESUBMISSION FIELDS: Critical for badge persistence
            isResubmitted: Boolean(updatedVoucher.is_resubmitted),
            is_resubmitted: updatedVoucher.is_resubmitted,
            resubmission_certified_visible_to_finance: updatedVoucher.resubmission_certified_visible_to_finance,
            resubmission_tracking_visible_to_audit: updatedVoucher.resubmission_tracking_visible_to_audit
        };
        // PRODUCTION FIX: Use only one broadcast method to prevent duplicates
        (0, socketHandlers_js_1.broadcastVoucherUpdate)('updated', transformedVoucher);
        res.json(updatedVoucher);
    }
    catch (error) {
        logger_js_1.logger.error('Update voucher error:', error);
        res.status(500).json({ error: 'Failed to update voucher' });
    }
});
// Delete voucher (soft delete)
exports.voucherRouter.delete('/:id', async (req, res) => {
    try {
        const voucherId = req.params.id;
        // Get voucher
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }
        const voucher = vouchers[0];
        // Check if user has access to delete this voucher
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && voucher.department !== req.user.department) {
            return res.status(403).json({ error: 'Access denied' });
        }
        // Soft delete
        await (0, db_js_1.query)('UPDATE vouchers SET deleted = TRUE, deletion_time = ? WHERE id = ?', [new Date().toISOString(), voucherId]);
        // PRODUCTION FIX: Use only one broadcast method to prevent duplicates
        (0, socketHandlers_js_1.broadcastVoucherUpdate)('deleted', { id: voucherId, voucher_id: voucher.voucher_id });
        res.json({ message: 'Voucher deleted successfully' });
    }
    catch (error) {
        logger_js_1.logger.error('Delete voucher error:', error);
        res.status(500).json({ error: 'Failed to delete voucher' });
    }
});
// POST-TRANSACTION EDIT: Allow Audit users to edit dispatched vouchers
exports.voucherRouter.put('/:id/post-transaction-edit', auth_js_1.authenticate, async (req, res) => {
    try {
        const voucherId = req.params.id;
        const { auditReason, ...editData } = req.body;
        // SECURITY: Only Audit users can perform post-transaction edits
        if (req.user.department !== 'AUDIT') {
            return res.status(403).json({
                error: 'Access denied. Only Audit users can perform post-transaction edits.'
            });
        }
        // VALIDATION: Require audit reason
        if (!auditReason || !auditReason.trim()) {
            return res.status(400).json({
                error: 'Audit reason is required for post-transaction edits.'
            });
        }
        // Get the voucher to edit
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }
        const voucher = vouchers[0];
        // BUSINESS RULE: Only allow editing of dispatched/certified vouchers
        if (voucher.status !== 'VOUCHER CERTIFIED') {
            return res.status(400).json({
                error: 'Post-transaction edits can only be made to certified vouchers.'
            });
        }
        // Define field mapping from frontend camelCase to database snake_case
        const editableFields = {
            // Basic editable fields
            'claimant': 'claimant',
            'description': 'description',
            'amount': 'amount',
            'preAuditedAmount': 'pre_audited_amount',
            'certifiedAmount': 'pre_audited_amount', // Alias for certified amount
            'taxType': 'tax_type',
            'taxAmount': 'tax_amount',
            'preAuditedBy': 'pre_audited_by',
            'certifiedBy': 'certified_by',
            // Currency is editable but should match amount
            'currency': 'currency'
        };
        // NON-EDITABLE FIELDS (for security)
        const nonEditableFields = [
            'voucherId', 'voucher_id', 'id', 'date', 'dispatchTime', 'dispatch_time',
            'created_at', 'created_by', 'status', 'department', 'original_department',
            'dispatchedBy', 'audit_dispatched_by', 'auditDispatchedBy' // Dispatch info is historical
        ];
        // Validate that no non-editable fields are being modified
        for (const field of nonEditableFields) {
            if (editData.hasOwnProperty(field)) {
                return res.status(400).json({
                    error: `Field '${field}' is not editable in post-transaction edits.`
                });
            }
        }
        // Build the update query
        const updateFields = [];
        const updateValues = [];
        for (const [frontendField, dbField] of Object.entries(editableFields)) {
            if (editData.hasOwnProperty(frontendField)) {
                updateFields.push(`${dbField} = ?`);
                // Convert undefined to null for MySQL compatibility
                const value = editData[frontendField] === undefined ? null : editData[frontendField];
                updateValues.push(value);
            }
        }
        // Add edit history fields to the update
        updateFields.push('last_edit_reason = ?', 'last_edited_by = ?', 'last_edit_time = NOW()');
        updateValues.push(auditReason.trim(), req.user.name);
        if (updateFields.length === 3) { // Only edit history fields, no actual data changes
            return res.status(400).json({ error: 'No valid fields to update' });
        }
        updateValues.push(voucherId); // For WHERE clause
        // Execute the update
        const updateQuery = `
      UPDATE vouchers
      SET ${updateFields.join(', ')}
      WHERE id = ? AND deleted = FALSE
    `;
        await (0, db_js_1.query)(updateQuery, updateValues);
        // Log the post-transaction edit
        const auditLogData = {
            voucher_id: voucher.voucher_id,
            action: 'POST_TRANSACTION_EDIT',
            performed_by: req.user.name,
            department: req.user.department,
            audit_reason: auditReason.trim(),
            changes: editData,
            timestamp: new Date().toISOString(),
            original_values: Object.keys(editData).reduce((acc, key) => {
                const dbField = editableFields[key];
                if (dbField && voucher[dbField] !== undefined) {
                    acc[key] = voucher[dbField];
                }
                return acc;
            }, {})
        };
        // Insert audit log
        await (0, db_js_1.query)(`
      INSERT INTO voucher_audit_log (
        voucher_id, action, performed_by, department, audit_reason,
        changes_json, original_values_json, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
            voucher.voucher_id,
            auditLogData.action,
            auditLogData.performed_by,
            auditLogData.department,
            auditLogData.audit_reason,
            JSON.stringify(auditLogData.changes),
            JSON.stringify(auditLogData.original_values),
            new Date(auditLogData.timestamp).toISOString().slice(0, 19).replace('T', ' ')
        ]);
        // Get the updated voucher
        const updatedVouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
        const updatedVoucher = updatedVouchers[0];
        // Broadcast the update to all connected clients
        (0, socketHandlers_js_1.broadcastVoucherUpdate)('post_transaction_edit', {
            id: voucherId,
            voucher_id: voucher.voucher_id,
            changes: editData,
            updated_voucher: updatedVoucher
        });
        logger_js_1.logger.info(`✅ POST-TRANSACTION EDIT: ${voucher.voucher_id} edited by ${req.user.name}`, {
            voucherId: voucher.voucher_id,
            editedBy: req.user.name,
            changes: editData
        });
        res.json({
            message: 'Post-transaction edit completed successfully',
            voucher: updatedVoucher,
            audit_log: auditLogData
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const errorStack = error instanceof Error ? error.stack : '';
        logger_js_1.logger.error('POST-TRANSACTION EDIT ERROR:', {
            error: errorMessage,
            stack: errorStack,
            voucherId: req.params.id,
            editData: req.body,
            user: req.user?.username
        });
        res.status(500).json({
            error: 'Failed to perform post-transaction edit',
            details: errorMessage
        });
    }
});
// Send voucher to audit
exports.voucherRouter.post('/:id/send-to-audit', async (req, res) => {
    try {
        const voucherId = req.params.id;
        const connection = await (0, db_js_1.getTransaction)();
        try {
            // Get voucher with row lock
            const [vouchers] = await connection.query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE FOR UPDATE', [voucherId]);
            if (!vouchers || vouchers.length === 0) {
                await connection.rollback();
                return res.status(404).json({ error: 'Voucher not found' });
            }
            const voucher = vouchers[0];
            // Check if user has access to send this voucher to audit
            if (req.user.department !== 'SYSTEM ADMIN' && voucher.department !== req.user.department) {
                await connection.rollback();
                return res.status(403).json({ error: 'Access denied' });
            }
            // RESUBMISSION FIX: Detect if this is a re-submitted voucher
            // A voucher is a resubmission if:
            // 1. It has been rejected before (has rejected_by field)
            // 2. It's currently in PENDING SUBMISSION (was added back)
            // 3. It's being sent to audit again
            // DEBUG: Log voucher details for resubmission detection
            logger_js_1.logger.info(`🔍 RESUBMISSION DEBUG - Voucher ${voucher.voucher_id}:`, {
                voucherId: voucher.voucher_id,
                status: voucher.status,
                rejected_by: voucher.rejected_by || 'NULL',
                rejection_time: voucher.rejection_time || 'NULL',
                comment: voucher.comment || 'NULL'
            });
            const condition1 = voucher.rejected_by && voucher.rejected_by.trim() !== '';
            const condition2 = voucher.rejection_time;
            const condition3 = voucher.status === 'VOUCHER REJECTED';
            const condition4 = voucher.status === 'VOUCHER RETURNED';
            logger_js_1.logger.info(`🧪 RESUBMISSION CONDITIONS - ${voucher.voucher_id}:`, {
                condition1_rejected_by: condition1,
                condition2_rejection_time: condition2,
                condition3_status_rejected: condition3,
                condition4_status_returned: condition4
            });
            const isResubmission = condition1 || condition2 || condition3 || condition4;
            logger_js_1.logger.info(`🎯 RESUBMISSION RESULT - ${voucher.voucher_id}: ${isResubmission ? 'YES' : 'NO'}`);
            // Determine the appropriate status and workflow state
            const targetStatus = isResubmission ? 'RE-SUBMISSION' : legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.PENDING_RECEIPT;
            const targetWorkflowState = isResubmission ? 'FINANCE_RESUBMISSION' : 'FINANCE_PROCESSING';
            // Get the flags that should be set for the new status
            const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: targetStatus });
            // FIXED: Validate the status transition with the flags that WILL BE SET during transition
            const currentFlags = voucher.flags ? JSON.parse(voucher.flags) : {};
            const flagsAfterTransition = {
                ...currentFlags,
                sentToAudit: true,
                isResubmission: isResubmission // Add resubmission flag
            };
            const { isValid, reason } = (0, legacy_voucher_status_compat_js_1.isValidStatusTransition)(voucher.status, targetStatus, req.user.role, flagsAfterTransition // Use flags that will exist after transition
            );
            if (!isValid) {
                await connection.rollback();
                logger_js_1.logger.warn(`Invalid status transition attempted: ${voucher.status} -> ${targetStatus}: ${reason}`);
                return res.status(400).json({
                    error: `Invalid status transition: ${reason}`
                });
            }
            // PRODUCTION FIX: Update workflow state using the workflow state machine
            const currentWorkflowState = voucher.workflow_state || 'FINANCE_PENDING';
            const newWorkflowState = targetWorkflowState;
            // PRODUCTION FIX: Create a batch for this voucher automatically
            const { v4: uuidv4 } = require('uuid');
            const batchId = uuidv4();
            // Create batch for audit department with resubmission info
            await connection.query(`INSERT INTO voucher_batches (
          id, department, sent_by, sent_time, received, from_audit,
          contains_rejected_vouchers, rejected_voucher_count,
          contains_rejected_copies, rejected_copy_count, normal_voucher_count,
          contains_resubmissions, resubmission_count
        ) VALUES (?, ?, ?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [batchId, 'AUDIT', req.user.name, false, false, false, 0, false, 0, isResubmission ? 0 : 1, isResubmission, isResubmission ? 1 : 0]);
            // CRITICAL FIX: Link the voucher to the batch in batch_vouchers table
            try {
                await connection.query('INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)', [batchId, voucherId]);
                logger_js_1.logger.info(`✅ Successfully linked voucher ${voucherId} to batch ${batchId}`);
            }
            catch (batchLinkError) {
                logger_js_1.logger.error(`❌ Failed to link voucher ${voucherId} to batch ${batchId}:`, batchLinkError);
                throw batchLinkError;
            }
            // CRITICAL FIX: Update voucher with proper status, workflow_state, batch_id, synchronized flags, sent_to_audit flag, AND reference_id for offset logic
            await connection.query('UPDATE vouchers SET status = ?, workflow_state = ?, batch_id = ?, flags = ?, dispatch_to_audit_by = ?, dispatch_time = NOW(), sent_to_audit = TRUE, reference_id = ? WHERE id = ?', [targetStatus, newWorkflowState, batchId, JSON.stringify(flags), req.user.name, voucher.voucher_id, voucherId]);
            // UNIFIED BADGE SYSTEM: Use BadgeStateManager for resubmission state
            if (isResubmission) {
                await badgeStateManager_js_1.BadgeStateManager.setResubmissionState(connection, voucherId, true, {
                    incrementCount: true
                });
                // RETURN VOUCHER RESUBMISSION: Handle returned voucher resubmissions
                const isReturnedResubmission = voucher.is_returned_voucher === 1 || voucher.is_returned_voucher === true;
                if (isReturnedResubmission) {
                    await returnStateManager_1.ReturnStateManager.setReturnState(connection, voucherId, true, {
                        preserveReturnReason: true
                    });
                }
            }
            // Log the status and workflow state change
            logger_js_1.logger.info(`Created batch ${batchId} and changed voucher ${voucherId} status to ${targetStatus} and workflow_state from ${currentWorkflowState} to ${newWorkflowState} (sent to audit by ${req.user.name}) ${isResubmission ? '[RESUBMISSION]' : ''}`);
            // Create notification for audit department
            const notificationId = uuidv4();
            // Get all users with AUDIT department
            const auditUsers = await connection.query('SELECT id FROM users WHERE department = "AUDIT" AND is_active = 1');
            // REAL-TIME FIX: Broadcast batch notification to Audit department immediately
            (0, socketHandlers_js_1.broadcastToAuditDepartment)('new_batch_notification', {
                batchId,
                department: req.user.department,
                voucherCount: 1,
                sentBy: req.user.name,
                timestamp: Date.now(),
                message: `New voucher ${voucher.voucher_id} from ${req.user.department}`
            });
            // Create notifications for audit users
            const notificationMessage = isResubmission
                ? `Re-submitted voucher ${voucher.voucher_id} received from ${voucher.department}`
                : `New voucher ${voucher.voucher_id} received from ${voucher.department}`;
            const notificationType = isResubmission ? 'RESUBMISSION' : 'NEW_VOUCHER';
            if (auditUsers[0].length === 0) {
                await connection.query(`INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`, [
                    notificationId,
                    'AUDIT',
                    notificationMessage,
                    false,
                    voucherId,
                    notificationType
                ]);
            }
            else {
                for (const auditUser of auditUsers[0]) {
                    await connection.query(`INSERT INTO notifications (
              id, user_id, message, is_read, timestamp, voucher_id, type
            ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`, [
                        uuidv4(),
                        auditUser.id,
                        notificationMessage,
                        false,
                        voucherId,
                        notificationType
                    ]);
                }
            }
            // Get updated voucher
            const [updatedVouchers] = await connection.query('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
            const updatedVoucher = updatedVouchers[0];
            // CRITICAL FIX: Validate that updatedVoucher exists
            if (!updatedVoucher) {
                throw new Error(`Failed to retrieve updated voucher data for ID: ${voucherId}`);
            }
            // Transform voucher for client compatibility before broadcasting
            const transformedVoucher = {
                ...updatedVoucher,
                voucherId: updatedVoucher.voucher_id,
                originalDepartment: updatedVoucher.original_department,
                createdBy: updatedVoucher.created_by,
                dispatchedBy: updatedVoucher.dispatched_by,
                dispatchTime: updatedVoucher.dispatch_time,
                sentToAudit: Boolean(updatedVoucher.sent_to_audit),
                batchId: updatedVoucher.batch_id,
                receivedBy: updatedVoucher.received_by,
                receivedByAudit: Boolean(updatedVoucher.received_by_audit),
                receiptTime: updatedVoucher.receipt_time,
                taxType: updatedVoucher.tax_type,
                taxDetails: updatedVoucher.tax_details,
                taxAmount: updatedVoucher.tax_amount,
                preAuditedAmount: updatedVoucher.pre_audited_amount,
                preAuditedBy: updatedVoucher.pre_audited_by,
                certifiedBy: updatedVoucher.certified_by,
                auditDispatchTime: updatedVoucher.audit_dispatch_time,
                auditDispatchedBy: updatedVoucher.audit_dispatched_by,
                dispatchToOnDepartment: Boolean(updatedVoucher.dispatch_to_on_department),
                postProvisionalCash: Boolean(updatedVoucher.post_provisional_cash),
                dispatched: Boolean(updatedVoucher.dispatched),
                dispatchToAuditBy: updatedVoucher.dispatch_to_audit_by,
                workStarted: Boolean(updatedVoucher.work_started),
                deleted: Boolean(updatedVoucher.deleted),
                deletionTime: updatedVoucher.deletion_time,
                rejectionTime: updatedVoucher.rejection_time,
                departmentReceiptTime: updatedVoucher.department_receipt_time,
                departmentReceivedBy: updatedVoucher.department_received_by,
                departmentRejected: Boolean(updatedVoucher.department_rejected),
                rejectedBy: updatedVoucher.rejected_by,
                referenceId: updatedVoucher.reference_id,
                idempotencyKey: updatedVoucher.idempotency_key,
                createdAt: updatedVoucher.created_at
            };
            // PRODUCTION FIX: Use only one broadcast method to prevent duplicates
            (0, socketHandlers_js_1.broadcastVoucherUpdate)('updated', transformedVoucher);
            // Broadcast notification
            const notification = {
                id: notificationId,
                user_id: 'AUDIT',
                message: `New voucher ${voucher.voucher_id} received from ${voucher.department}`,
                is_read: false,
                timestamp: new Date().toISOString(),
                voucher_id: voucherId,
                type: 'NEW_VOUCHER'
            };
            // PRODUCTION FIX: Use only one broadcast method to prevent duplicates
            (0, socketHandlers_js_1.broadcastNotificationUpdate)('created', notification);
            await connection.commit();
            res.json(updatedVoucher);
        }
        catch (error) {
            await connection.rollback();
            throw error;
        }
        finally {
            connection.release();
        }
    }
    catch (error) {
        logger_js_1.logger.error('Send voucher to audit error:', error);
        res.status(500).json({ error: 'Failed to send voucher to audit' });
    }
});
// Certify voucher
exports.voucherRouter.post('/:id/certify', async (req, res) => {
    try {
        const voucherId = req.params.id;
        // Get voucher
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }
        const voucher = vouchers[0];
        // Check if user has access to certify this voucher
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
            return res.status(403).json({ error: 'Access denied' });
        }
        // Get the flags that should be set for the new status
        const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED });
        // Validate the status transition
        if (!(0, legacy_voucher_status_compat_js_1.isValidStatusTransition)(voucher.status, legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED, req.user.role, voucher)) {
            logger_js_1.logger.warn(`Invalid status transition attempted: ${voucher.status} -> ${legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED}`);
            return res.status(400).json({
                error: `Invalid status transition from ${voucher.status} to ${legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED}`
            });
        }
        // Update voucher with proper status and synchronized flags
        // PRODUCTION FIX: Update database columns to match flags for proper filtering
        // OFFSET LOGIC FIX: Set reference_id to enable offset logic for removing original voucher from Processing tab
        const currentTime = new Date().toISOString();
        await (0, db_js_1.query)(`UPDATE vouchers SET
        status = ?,
        flags = ?,
        sent_to_audit = TRUE,
        dispatched = TRUE,
        certified_by = ?,
        audit_dispatch_time = ?,
        reference_id = ?
      WHERE id = ?`, [
            legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED,
            JSON.stringify(flags),
            req.user.name,
            currentTime,
            voucher.voucher_id, // CRITICAL FIX: Set reference_id to original voucher_id for offset logic
            voucherId
        ]);
        // Log the status change
        logger_js_1.logger.info(`Changed voucher ${voucherId} status to ${legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED} (certified by ${req.user.name})`);
        console.log(`Changed voucher ${voucherId} status to ${legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED} (certified by ${req.user.name})`);
        // Create notification for the department
        await (0, db_js_1.query)(`INSERT INTO notifications (
        id, user_id, message, is_read, timestamp, voucher_id, type, from_audit
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [
            (0, uuid_1.v4)(),
            voucher.department,
            `Voucher ${voucher.voucher_id} certified by Audit`,
            false,
            currentTime,
            voucherId,
            'VOUCHER_CERTIFIED',
            true
        ]);
        // Log the certification
        logger_js_1.logger.info(`Voucher ${voucherId} (${voucher.voucher_id}) certified by ${req.user.name}`);
        console.log(`Voucher ${voucherId} (${voucher.voucher_id}) certified by ${req.user.name}`);
        // Get updated voucher
        const updatedVouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
        const updatedVoucher = updatedVouchers[0];
        // PRODUCTION FIX: Broadcast voucher update to prevent frontend spinning
        (0, socketHandlers_js_1.broadcastVoucherUpdate)('updated', updatedVoucher);
        // BIDIRECTIONAL REAL-TIME FIX: Notify original department that their voucher was certified
        if (voucher.original_department && voucher.original_department !== 'AUDIT') {
            (0, socketHandlers_js_1.broadcastToDepartment)(voucher.original_department, 'new_batch_notification', {
                batchId: null, // Individual voucher certification
                department: 'AUDIT',
                voucherCount: 1,
                sentBy: req.user.name,
                timestamp: Date.now(),
                message: `Voucher ${voucher.voucher_id} has been CERTIFIED by Audit`
            });
            logger_js_1.logger.info(`📢 REAL-TIME: Notified ${voucher.original_department} that voucher ${voucher.voucher_id} was certified`);
        }
        // CRITICAL FIX: Broadcast voucher status update for real-time status changes
        (0, socketHandlers_js_1.broadcastVoucherUpdate)('updated', {
            id: updatedVoucher.id,
            voucher_id: updatedVoucher.voucher_id,
            status: updatedVoucher.status,
            department: updatedVoucher.department,
            original_department: updatedVoucher.original_department,
            workflow_state: updatedVoucher.workflow_state,
            certified_by: updatedVoucher.certified_by,
            audit_dispatch_time: updatedVoucher.audit_dispatch_time
        });
        logger_js_1.logger.info(`📢 REAL-TIME: Broadcasted voucher status update for ${updatedVoucher.voucher_id}: CERTIFIED`);
        res.json(updatedVoucher);
    }
    catch (error) {
        logger_js_1.logger.error('Certify voucher error:', error);
        res.status(500).json({ error: 'Failed to certify voucher' });
    }
});
// Reject voucher
exports.voucherRouter.post('/:id/reject', async (req, res) => {
    try {
        const voucherId = req.params.id;
        const { comment } = req.body;
        // Get voucher
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }
        const voucher = vouchers[0];
        // Check if user has access to reject this voucher
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
            return res.status(403).json({ error: 'Access denied' });
        }
        // SIMPLIFIED REJECTION WORKFLOW - CORRECT IMPLEMENTATION
        // =====================================================
        const currentTime = new Date().toISOString();
        const rejectionComment = comment || 'No comment provided';
        // Validate the status transition
        if (!(0, legacy_voucher_status_compat_js_1.isValidStatusTransition)(voucher.status, legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED, req.user.role, voucher)) {
            logger_js_1.logger.warn(`Invalid status transition attempted: ${voucher.status} -> ${legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED}`);
            return res.status(400).json({
                error: `Invalid status transition from ${voucher.status} to ${legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED}`
            });
        }
        // STEP 1: Update ORIGINAL voucher - goes to PENDING DISPATCH tab (ready to send back to Finance)
        console.log(`Rejecting voucher ${voucher.voucher_id} - original goes to PENDING DISPATCH`);
        const { flags: rejectedFlags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED });
        await (0, db_js_1.query)(`
      UPDATE vouchers SET
        status = ?,
        flags = ?,
        rejected_by = ?,
        rejection_time = NOW(),
        comment = ?,
        work_started = 1,
        department = 'AUDIT'
      WHERE id = ?
    `, [
            legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED,
            JSON.stringify(rejectedFlags),
            req.user.name,
            rejectionComment,
            voucherId
        ]);
        // STEP 2: Create COPY for Finance Voucher Hub REJECTED tab (Audit Dashboard)
        console.log(`Creating copy for Finance Voucher Hub REJECTED tab`);
        const copyId = (0, uuid_1.v4)();
        const { flags: copyFlags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({
            status: legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED
        });
        await (0, db_js_1.query)(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, flags,
        created_by, created_at, sent_to_audit, received_by_audit, work_started,
        parent_voucher_id, is_rejection_copy,
        rejected_by, rejection_time, comment
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
            copyId,
            voucher.voucher_id + '-COPY', // Copy for Finance Voucher Hub
            voucher.date,
            voucher.claimant,
            voucher.description,
            voucher.amount,
            voucher.currency,
            'FINANCE', // Goes to Finance Voucher Hub REJECTED tab (in Audit Dashboard)
            voucher.original_department,
            legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED,
            JSON.stringify(copyFlags),
            voucher.created_by,
            voucher.created_at,
            true, // sent_to_audit
            true, // received_by_audit
            true, // work_started
            voucherId, // Link to original voucher
            true, // is_rejection_copy
            req.user.name,
            currentTime,
            rejectionComment
        ]);
        console.log(`✅ Simplified rejection completed for ${voucher.voucher_id}:`);
        console.log(`   - Original voucher: ${voucherId} (PENDING DISPATCH tab)`);
        console.log(`   - Copy: ${copyId} (Finance Voucher Hub REJECTED tab)`);
        // Log the status change
        logger_js_1.logger.info(`Changed voucher ${voucherId} status to ${legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED} (rejected by ${req.user.name})`);
        console.log(`Changed voucher ${voucherId} status to ${legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED} (rejected by ${req.user.name})`);
        // Create notification for department
        const notificationId = (0, uuid_1.v4)();
        await (0, db_js_1.query)(`INSERT INTO notifications (
        id, user_id, message, is_read, timestamp, voucher_id, type, from_audit
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [
            notificationId,
            voucher.department,
            `Voucher ${voucher.voucher_id} rejected by Audit`,
            false,
            currentTime,
            voucherId,
            'VOUCHER_REJECTED',
            true
        ]);
        // Log the rejection
        logger_js_1.logger.info(`Voucher ${voucherId} (${voucher.voucher_id}) rejected by ${req.user.name} with dual-tab workflow`);
        console.log(`Voucher ${voucherId} (${voucher.voucher_id}) rejected by ${req.user.name} with comment: ${rejectionComment}`);
        // Get updated permanent record
        const updatedVouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
        const updatedVoucher = updatedVouchers[0];
        // Broadcast updates for both records
        (0, socketHandlers_js_1.broadcastVoucherUpdate)('updated', updatedVoucher);
        // Get and broadcast copy
        const copies = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [copyId]);
        if (copies.length > 0) {
            (0, socketHandlers_js_1.broadcastVoucherUpdate)('created', copies[0]);
        }
        // BIDIRECTIONAL REAL-TIME FIX: Notify original department that their voucher was rejected
        if (voucher.original_department && voucher.original_department !== 'AUDIT') {
            (0, socketHandlers_js_1.broadcastToDepartment)(voucher.original_department, 'new_batch_notification', {
                batchId: null, // Individual voucher rejection
                department: 'AUDIT',
                voucherCount: 1,
                sentBy: req.user.name,
                timestamp: Date.now(),
                message: `Voucher ${voucher.voucher_id} has been REJECTED by Audit`
            });
            logger_js_1.logger.info(`📢 REAL-TIME: Notified ${voucher.original_department} that voucher ${voucher.voucher_id} was rejected`);
        }
        // CRITICAL FIX: Broadcast voucher status update for real-time status changes
        (0, socketHandlers_js_1.broadcastVoucherUpdate)('updated', {
            id: updatedVoucher.id,
            voucher_id: updatedVoucher.voucher_id,
            status: updatedVoucher.status,
            department: updatedVoucher.department,
            original_department: updatedVoucher.original_department,
            workflow_state: updatedVoucher.workflow_state,
            rejected_by: updatedVoucher.rejected_by,
            rejection_time: updatedVoucher.rejection_time,
            comment: updatedVoucher.comment
        });
        logger_js_1.logger.info(`📢 REAL-TIME: Broadcasted voucher status update for ${updatedVoucher.voucher_id}: REJECTED`);
        res.json({
            originalVoucher: updatedVoucher,
            copy: copies.length > 0 ? copies[0] : null,
            message: 'Voucher rejected - original goes to PENDING DISPATCH, copy to Finance Voucher Hub'
        });
    }
    catch (error) {
        logger_js_1.logger.error('Reject voucher error:', error);
        res.status(500).json({ error: 'Failed to reject voucher' });
    }
});
// Get blacklisted voucher IDs
exports.voucherRouter.get('/blacklist/ids', async (req, res) => {
    try {
        const blacklistedIds = await (0, db_js_1.query)('SELECT * FROM blacklisted_voucher_ids');
        res.json(blacklistedIds);
    }
    catch (error) {
        logger_js_1.logger.error('Get blacklisted voucher IDs error:', error);
        res.status(500).json({ error: 'Failed to get blacklisted voucher IDs' });
    }
});
// Add voucher ID to blacklist
exports.voucherRouter.post('/blacklist/ids', async (req, res) => {
    try {
        const { voucherId } = req.body;
        if (!voucherId) {
            return res.status(400).json({ error: 'Voucher ID is required' });
        }
        // Check if already blacklisted
        const existingIds = await (0, db_js_1.query)('SELECT * FROM blacklisted_voucher_ids WHERE voucher_id = ?', [voucherId]);
        if (existingIds.length > 0) {
            return res.status(409).json({ error: 'Voucher ID already blacklisted' });
        }
        // Add to blacklist
        const id = (0, uuid_1.v4)();
        await (0, db_js_1.query)('INSERT INTO blacklisted_voucher_ids (id, voucher_id) VALUES (?, ?)', [id, voucherId]);
        res.status(201).json({
            id,
            voucherId
        });
    }
    catch (error) {
        logger_js_1.logger.error('Add to blacklist error:', error);
        res.status(500).json({ error: 'Failed to add voucher ID to blacklist' });
    }
});
// Return voucher endpoint - PHASE 1 IMPLEMENTATION
exports.voucherRouter.post('/:id/return', async (req, res) => {
    try {
        const voucherId = req.params.id;
        const { returnReason, dispatcher } = req.body;
        // Validate required fields
        if (!returnReason || !returnReason.trim()) {
            return res.status(400).json({ error: 'Return reason is required' });
        }
        if (!dispatcher || !dispatcher.trim()) {
            return res.status(400).json({ error: 'Dispatcher is required' });
        }
        // PRODUCTION FIX: Use withTransaction helper for proper transaction management
        const result = await (0, db_js_1.withTransaction)(async (connection) => {
            // Get voucher with row lock
            const [vouchers] = await connection.query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE FOR UPDATE', [voucherId]);
            if (!vouchers || vouchers.length === 0) {
                await connection.rollback();
                return res.status(404).json({ error: 'Voucher not found' });
            }
            const voucher = vouchers[0];
            // Check if user has access to return this voucher (only Audit can return)
            if (req.user.department !== 'AUDIT') {
                await connection.rollback();
                return res.status(403).json({ error: 'Only Audit department can return vouchers' });
            }
            // Check if voucher is in NEW VOUCHERS state (AUDIT_NEW)
            if (voucher.workflow_state !== 'AUDIT_NEW') {
                await connection.rollback();
                return res.status(400).json({
                    error: 'Voucher can only be returned from NEW VOUCHERS tab',
                    currentState: voucher.workflow_state
                });
            }
            // STEP 1: Create permanent copy voucher in AUDIT RETURNED tab ONLY
            const copyId = (0, uuid_1.v4)();
            // Get source voucher data first - CRITICAL FIX: Use correct array destructuring for connection.query
            const [sourceVouchers] = await connection.query('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
            const source = sourceVouchers[0];
            // CRITICAL FIX: Validate that source voucher exists
            if (!source) {
                throw new Error(`Failed to retrieve source voucher data for ID: ${voucherId}`);
            }
            logger_js_1.logger.info(`🔍 RETURN DEBUG: Source voucher data:`, {
                voucherId: source.voucher_id,
                createdBy: source.created_by,
                createdAt: source.created_at,
                date: source.date
            });
            // CRITICAL FIX: Create copy with correct original_department, not hardcoded 'FINANCE'
            // This ensures the copy reflects the voucher's actual origin department
            const originalDept = voucher.original_department || voucher.department;
            // CRITICAL FIX: Ensure date is never null - use current date if source date is null
            const validDate = source.date || new Date().toISOString().split('T')[0];
            // CRITICAL FIX: Validate other required fields to prevent null constraint violations
            const validVoucherId = source.voucher_id || `UNKNOWN-${Date.now()}`;
            const validClaimant = source.claimant || 'Unknown Claimant';
            const validDescription = source.description || 'No Description';
            const validAmount = source.amount || 0;
            const validCurrency = source.currency || 'GHS';
            const validCreatedBy = source.created_by || 'System';
            const validCreatedAt = source.created_at || new Date().toISOString();
            await connection.query(`INSERT INTO vouchers (
          id, voucher_id, date, claimant, description, amount, currency,
          department, original_department, status, workflow_state,
          created_by, created_at, sent_to_audit, received_by_audit,
          comment, returned_by, return_time, is_returned_copy,
          parent_voucher_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'AUDIT', ?, ?, 'AUDIT_RETURNED_COPY', ?, ?, true, true, ?, ?, NOW(), true, ?)`, [
                copyId,
                validVoucherId + '-RETURN-COPY',
                validDate,
                validClaimant,
                'RETURN COPY: ' + validDescription,
                validAmount,
                validCurrency,
                originalDept,
                legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED,
                validCreatedBy,
                validCreatedAt,
                returnReason.trim(),
                req.user.name,
                voucherId
            ]);
            // CRITICAL FIX: Create permanent copy for ORIGINAL department's Voucher Hub RETURNED tab
            // This ensures returned vouchers appear in the correct department hub (MISSIONS, PENSIONS, etc.)
            const departmentHubCopyId = (0, uuid_1.v4)();
            await connection.query(`INSERT INTO vouchers (
          id, voucher_id, date, claimant, description, amount, currency,
          department, original_department, status, workflow_state,
          created_by, created_at, sent_to_audit, received_by_audit,
          comment, returned_by, return_time, is_returned_copy,
          parent_voucher_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, true, true, ?, ?, NOW(), true, ?)`, [
                departmentHubCopyId,
                validVoucherId + `-${originalDept}-RETURN-COPY`,
                validDate, // CRITICAL FIX: Use the same validDate to prevent null date errors
                validClaimant,
                `${originalDept} RETURN COPY: ` + validDescription,
                validAmount,
                validCurrency,
                originalDept,
                originalDept,
                legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED,
                `${originalDept}_RETURNED_COPY`,
                validCreatedBy,
                validCreatedAt,
                returnReason.trim(),
                req.user.name,
                voucherId
            ]);
            // STEP 2: Update original voucher to AUDIT_PENDING_DISPATCH_RETURNED state
            await connection.query(`UPDATE vouchers SET
          status = ?,
          workflow_state = 'AUDIT_PENDING_DISPATCH_RETURNED',
          comment = ?,
          returned_by = ?,
          return_time = NOW(),
          is_returned_voucher = 1
        WHERE id = ?`, [legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED, returnReason.trim(), req.user.name, voucherId]);
            // STEP 3: Set return state using ReturnStateManager
            await returnStateManager_1.ReturnStateManager.setReturnState(connection, voucherId, true, {
                incrementCount: true,
                preserveReturnReason: true
            });
            // CRITICAL FIX: Create batch for voucher's ORIGINAL department, not hardcoded FINANCE
            // This ensures returned vouchers go to the correct department (MISSIONS, PENSIONS, etc.)
            const targetDepartment = voucher.original_department || voucher.department;
            const batchId = (0, uuid_1.v4)();
            await connection.query(`INSERT INTO voucher_batches (
          id, department, sent_by, sent_time, received, from_audit,
          contains_returned_vouchers, returned_voucher_count
        ) VALUES (?, ?, ?, NOW(), ?, ?, ?, ?)`, [batchId, targetDepartment, dispatcher, false, true, true, 1]);
            // STEP 5: Link voucher to batch
            await connection.query('INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)', [batchId, voucherId]);
            // STEP 6: Update voucher with batch information
            await connection.query(`UPDATE vouchers SET
          batch_id = ?,
          audit_dispatch_time = NOW(),
          audit_dispatched_by = ?
        WHERE id = ?`, [batchId, dispatcher, voucherId]);
            await connection.commit();
            // Get updated voucher for response
            const updatedVouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
            const updatedVoucher = updatedVouchers[0];
            // CRITICAL FIX: Validate that updatedVoucher exists before using it
            if (!updatedVoucher) {
                throw new Error(`Failed to retrieve updated voucher data for ID: ${voucherId}`);
            }
            // Broadcast updates with complete voucher data for proper client handling
            (0, socketHandlers_js_1.broadcastVoucherUpdate)('returned', {
                ...updatedVoucher,
                id: voucherId,
                voucher_id: voucher.voucher_id,
                status: legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED,
                workflow_state: 'AUDIT_PENDING_DISPATCH_RETURNED',
                returned_by: req.user.name,
                return_reason: returnReason.trim(),
                comment: returnReason.trim(),
                batch_id: batchId
            });
            // CRITICAL FIX: Also broadcast the return copy creation for real-time updates
            const returnCopyData = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [copyId]);
            if (returnCopyData && returnCopyData.length > 0) {
                const returnCopy = returnCopyData[0];
                (0, socketHandlers_js_1.broadcastVoucherUpdate)('created', {
                    ...returnCopy,
                    id: copyId,
                    voucher_id: returnCopy.voucher_id,
                    voucherId: returnCopy.voucher_id,
                    status: legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED,
                    workflow_state: 'AUDIT_RETURNED_COPY',
                    is_returned_copy: true,
                    isReturnedCopy: true,
                    comment: returnReason.trim(),
                    returnComment: returnReason.trim(),
                    returnTime: returnCopyData[0].return_time,
                    returnedBy: returnCopyData[0].returned_by,
                    return_time: returnCopyData[0].return_time,
                    returned_by: returnCopyData[0].returned_by
                });
            }
            // CRITICAL FIX: Also broadcast the Department Hub copy creation for real-time updates
            const departmentHubCopyData = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [departmentHubCopyId]);
            if (departmentHubCopyData && departmentHubCopyData.length > 0) {
                const copyData = departmentHubCopyData[0];
                (0, socketHandlers_js_1.broadcastVoucherUpdate)('created', {
                    ...copyData,
                    id: departmentHubCopyId,
                    voucher_id: copyData.voucher_id,
                    voucherId: copyData.voucher_id,
                    status: legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_RETURNED,
                    workflow_state: `${originalDept}_RETURNED_COPY`,
                    is_returned_copy: true,
                    isReturnedCopy: true,
                    comment: returnReason.trim(),
                    returnComment: returnReason.trim(),
                    returnTime: copyData.return_time,
                    returnedBy: copyData.returned_by,
                    return_time: copyData.return_time,
                    returned_by: copyData.returned_by
                });
            }
            // Broadcast batch notification
            (0, socketHandlers_js_1.broadcastNotificationUpdate)('BATCH_CREATED', {
                department: targetDepartment,
                batchId,
                message: `New returned voucher batch from ${req.user.name}`,
                timestamp: new Date().toISOString()
            });
            // BIDIRECTIONAL REAL-TIME FIX: Notify original department that their voucher was returned
            if (voucher.original_department && voucher.original_department !== 'AUDIT') {
                (0, socketHandlers_js_1.broadcastToDepartment)(voucher.original_department, 'new_batch_notification', {
                    batchId,
                    department: 'AUDIT',
                    voucherCount: 1,
                    sentBy: req.user.name,
                    timestamp: Date.now(),
                    message: `Voucher ${voucher.voucher_id} has been RETURNED by Audit`
                });
                logger_js_1.logger.info(`📢 REAL-TIME: Notified ${voucher.original_department} that voucher ${voucher.voucher_id} was returned`);
            }
            // CRITICAL FIX: Broadcast voucher status update for real-time status changes
            (0, socketHandlers_js_1.broadcastVoucherUpdate)('updated', {
                id: updatedVoucher.id,
                voucher_id: updatedVoucher.voucher_id,
                status: updatedVoucher.status,
                department: updatedVoucher.department,
                original_department: updatedVoucher.original_department,
                workflow_state: updatedVoucher.workflow_state,
                returned_by: updatedVoucher.returned_by,
                return_time: updatedVoucher.return_time,
                comment: updatedVoucher.comment,
                batch_id: updatedVoucher.batch_id
            });
            logger_js_1.logger.info(`📢 REAL-TIME: Broadcasted voucher status update for ${updatedVoucher.voucher_id}: RETURNED`);
            logger_js_1.logger.info(`✅ RETURN VOUCHER: ${voucher.voucher_id} returned by ${req.user.name} to ${targetDepartment}`);
            // Return the result data from withTransaction
            return {
                message: `Voucher returned to ${targetDepartment} successfully`,
                voucher: updatedVoucher,
                copyId,
                batchId
            };
        }); // End of withTransaction
        // Send response with result from transaction
        res.json(result);
    }
    catch (error) {
        logger_js_1.logger.error('Return voucher error:', error);
        res.status(500).json({ error: 'Failed to return voucher' });
    }
});
// FEATURE 1: Edit voucher in PENDING tab (for rejected/returned vouchers)
exports.voucherRouter.put('/:id/edit-pending', async (req, res) => {
    try {
        const { id: voucherId } = req.params;
        const { claimant, description, amount } = req.body;
        const userId = req.user?.id;
        const userName = req.user?.name;
        const userDepartment = req.user?.department;
        logger_js_1.logger.info(`🔧 EDIT PENDING VOUCHER: ${userName} (${userDepartment}) editing voucher ${voucherId}`);
        // Validate required fields
        if (!claimant || !description || !amount) {
            return res.status(400).json({ error: 'Claimant, description, and amount are required' });
        }
        if (amount <= 0) {
            return res.status(400).json({ error: 'Amount must be greater than 0' });
        }
        // Get voucher and verify it's editable (rejected/returned vouchers in PENDING tab)
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }
        const voucher = vouchers[0];
        // Verify user has permission to edit this voucher
        if (voucher.department !== userDepartment) {
            return res.status(403).json({ error: 'You can only edit vouchers from your own department' });
        }
        // Verify voucher is editable (rejected/returned vouchers)
        const isEditable = (voucher.status === 'VOUCHER REJECTED' ||
            voucher.is_returned ||
            voucher.return_comment ||
            (voucher.comment && (voucher.comment.includes('REJECTED') || voucher.comment.includes('RETURNED'))));
        if (!isEditable) {
            return res.status(400).json({ error: 'Only rejected or returned vouchers can be edited' });
        }
        // Update voucher with new data
        await (0, db_js_1.query)(`UPDATE vouchers
       SET claimant = ?, description = ?, amount = ?, updated_at = NOW()
       WHERE id = ?`, [claimant.trim(), description.trim(), amount, voucherId]);
        // Get updated voucher
        const updatedVouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
        const updatedVoucher = updatedVouchers[0];
        logger_js_1.logger.info(`✅ VOUCHER EDITED: ${voucherId} by ${userName} - Claimant: ${claimant}, Amount: ${amount}`);
        res.json({
            message: 'Voucher updated successfully',
            voucher: updatedVoucher
        });
    }
    catch (error) {
        logger_js_1.logger.error('Edit pending voucher error:', error);
        res.status(500).json({ error: 'Failed to update voucher' });
    }
});
// FEATURE 2: Toggle voucher hold status (for NEW VOUCHER tab in audit)
exports.voucherRouter.put('/:id/toggle-hold', async (req, res) => {
    try {
        const { id: voucherId } = req.params;
        const { holdComment, isOnHold } = req.body;
        const userId = req.user?.id;
        const userName = req.user?.name;
        const userDepartment = req.user?.department;
        logger_js_1.logger.info(`🔄 TOGGLE VOUCHER HOLD: ${userName} (${userDepartment}) ${isOnHold ? 'putting on hold' : 'removing hold'} voucher ${voucherId}`);
        // Only audit users can toggle hold status
        if (userDepartment !== 'AUDIT') {
            return res.status(403).json({ error: 'Only audit users can toggle voucher hold status' });
        }
        // Validate hold comment when putting on hold
        if (isOnHold && !holdComment?.trim()) {
            return res.status(400).json({ error: 'Hold comment is required when putting voucher on hold' });
        }
        // Get voucher
        const vouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
        if (vouchers.length === 0) {
            return res.status(404).json({ error: 'Voucher not found' });
        }
        const voucher = vouchers[0];
        // Update voucher hold status
        await (0, db_js_1.query)(`UPDATE vouchers
       SET is_on_hold = ?, hold_comment = ?, hold_by = ?, hold_time = ?
       WHERE id = ?`, [
            isOnHold,
            isOnHold ? holdComment.trim() : null,
            isOnHold ? userName : null,
            isOnHold ? new Date() : null,
            voucherId
        ]);
        // Get updated voucher
        const updatedVouchers = await (0, db_js_1.query)('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
        const updatedVoucher = updatedVouchers[0];
        logger_js_1.logger.info(`✅ VOUCHER HOLD TOGGLED: ${voucherId} by ${userName} - ${isOnHold ? 'ON HOLD' : 'RESUMED'}`);
        res.json({
            message: `Voucher ${isOnHold ? 'put on hold' : 'removed from hold'} successfully`,
            voucher: updatedVoucher
        });
    }
    catch (error) {
        logger_js_1.logger.error('Toggle voucher hold error:', error);
        res.status(500).json({ error: 'Failed to toggle voucher hold status' });
    }
});
// Export the router
exports.default = exports.voucherRouter;
//# sourceMappingURL=vouchers.js.map