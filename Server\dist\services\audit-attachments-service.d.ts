export interface AuditAttachment {
    id: string;
    voucher_id: string;
    original_filename: string;
    stored_filename: string;
    file_path: string;
    file_size: number;
    mime_type: string;
    uploaded_by: string;
    uploaded_at: string;
    is_active: boolean;
    uploader_name?: string;
}
export declare class AuditAttachmentsService {
    /**
     * Upload and attach file to voucher
     */
    static uploadAttachment(voucherId: string, file: any, uploadedBy: string, voucherData: {
        claimant: string;
        description: string;
    }): Promise<AuditAttachment>;
    /**
     * Get all attachments for a voucher
     */
    static getVoucherAttachments(voucherId: string): Promise<AuditAttachment[]>;
    /**
     * Get single attachment by ID
     */
    static getAttachmentById(attachmentId: string): Promise<AuditAttachment | null>;
    /**
     * Delete attachment (soft delete)
     */
    static deleteAttachment(attachmentId: string, deletedBy: string): Promise<boolean>;
}
