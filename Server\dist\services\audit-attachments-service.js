"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditAttachmentsService = void 0;
const uuid_1 = require("uuid");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const db_js_1 = require("../database/db.js");
const logger_js_1 = require("../utils/logger.js");
const file_storage_js_1 = require("../utils/file-storage.js");
class AuditAttachmentsService {
    /**
     * Upload and attach file to voucher
     */
    static async uploadAttachment(voucherId, file, uploadedBy, voucherData) {
        try {
            // Validate file
            const validation = (0, file_storage_js_1.validateFile)(file);
            if (!validation.isValid) {
                throw new Error(validation.error);
            }
            // Generate unique ID and filenames
            const attachmentId = (0, uuid_1.v4)();
            const originalExtension = path_1.default.extname(file.originalname);
            const storedFilename = (0, file_storage_js_1.generateStoredFilename)(voucherData.claimant, voucherData.description, originalExtension);
            // Ensure unique filename (add counter if exists)
            const voucherStoragePath = (0, file_storage_js_1.getVoucherStoragePath)(voucherId);
            (0, file_storage_js_1.ensureDirectoryExists)(voucherStoragePath);
            let finalStoredFilename = storedFilename;
            let counter = 1;
            while (fs_1.default.existsSync(path_1.default.join(voucherStoragePath, finalStoredFilename))) {
                const nameWithoutExt = path_1.default.parse(storedFilename).name;
                const ext = path_1.default.parse(storedFilename).ext;
                finalStoredFilename = `${nameWithoutExt}_${counter}${ext}`;
                counter++;
            }
            // Save file to storage
            const filePath = path_1.default.join(voucherStoragePath, finalStoredFilename);
            await fs_1.default.promises.writeFile(filePath, file.buffer);
            // Save metadata to database
            const attachmentData = {
                id: attachmentId,
                voucher_id: voucherId,
                original_filename: file.originalname,
                stored_filename: finalStoredFilename,
                file_path: filePath,
                file_size: file.size,
                mime_type: file.mimetype,
                uploaded_by: uploadedBy
            };
            await (0, db_js_1.query)(`
        INSERT INTO audit_voucher_attachments 
        (id, voucher_id, original_filename, stored_filename, file_path, file_size, mime_type, uploaded_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
                attachmentData.id,
                attachmentData.voucher_id,
                attachmentData.original_filename,
                attachmentData.stored_filename,
                attachmentData.file_path,
                attachmentData.file_size,
                attachmentData.mime_type,
                attachmentData.uploaded_by
            ]);
            logger_js_1.logger.info(`Audit attachment uploaded: ${finalStoredFilename} for voucher ${voucherId}`);
            return {
                ...attachmentData,
                uploaded_at: new Date().toISOString(),
                is_active: true
            };
        }
        catch (error) {
            logger_js_1.logger.error('Error uploading audit attachment:', error);
            throw error;
        }
    }
    /**
     * Get all attachments for a voucher
     */
    static async getVoucherAttachments(voucherId) {
        try {
            const attachments = await (0, db_js_1.query)(`
        SELECT 
          ava.*,
          u.name as uploader_name
        FROM audit_voucher_attachments ava
        LEFT JOIN users u ON ava.uploaded_by = u.id
        WHERE ava.voucher_id = ? AND ava.is_active = TRUE
        ORDER BY ava.uploaded_at DESC
      `, [voucherId]);
            return attachments;
        }
        catch (error) {
            logger_js_1.logger.error('Error fetching voucher attachments:', error);
            throw error;
        }
    }
    /**
     * Get single attachment by ID
     */
    static async getAttachmentById(attachmentId) {
        try {
            const attachments = await (0, db_js_1.query)(`
        SELECT 
          ava.*,
          u.name as uploader_name
        FROM audit_voucher_attachments ava
        LEFT JOIN users u ON ava.uploaded_by = u.id
        WHERE ava.id = ? AND ava.is_active = TRUE
      `, [attachmentId]);
            return attachments.length > 0 ? attachments[0] : null;
        }
        catch (error) {
            logger_js_1.logger.error('Error fetching attachment by ID:', error);
            throw error;
        }
    }
    /**
     * Delete attachment (soft delete)
     */
    static async deleteAttachment(attachmentId, deletedBy) {
        try {
            await (0, db_js_1.query)(`
        UPDATE audit_voucher_attachments 
        SET is_active = FALSE 
        WHERE id = ?
      `, [attachmentId]);
            logger_js_1.logger.info(`Audit attachment deleted: ${attachmentId} by ${deletedBy}`);
            return true;
        }
        catch (error) {
            logger_js_1.logger.error('Error deleting audit attachment:', error);
            throw error;
        }
    }
}
exports.AuditAttachmentsService = AuditAttachmentsService;
//# sourceMappingURL=audit-attachments-service.js.map