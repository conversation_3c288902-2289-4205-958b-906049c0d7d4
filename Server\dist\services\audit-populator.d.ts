/**
 * Audit Log Populator Service
 * Populates audit logs with real system activities for production-level monitoring
 */
export declare class AuditPopulator {
    /**
     * Populate audit logs with recent system activities
     */
    static populateAuditLogs(): Promise<void>;
    /**
     * Populate login/logout activities from active_sessions
     */
    private static populateLoginActivities;
    /**
     * Populate voucher-related activities
     */
    private static populateVoucherActivities;
    /**
     * Populate batch-related activities
     */
    private static populateBatchActivities;
    /**
     * Populate system-level activities
     */
    private static populateSystemActivities;
    /**
     * Populate user management activities
     */
    private static populateUserManagementActivities;
    /**
     * Create an audit log entry
     */
    private static createAuditLog;
    /**
     * Clean up old audit logs (keep last 30 days)
     */
    static cleanupOldLogs(): Promise<void>;
}
