export interface AuditLogEntry {
    id: string;
    timestamp: string;
    user: {
        id: string;
        name: string;
        department: string;
    };
    action: string;
    description: string;
    resourceType: string;
    resourceId?: string;
    details: object;
    ipAddress: string;
    userAgent: string;
    severity: 'INFO' | 'WARNING' | 'ERROR';
}
export declare class AuditService {
    static ensureAuditTable(): Promise<void>;
    static logActivity(params: {
        userId: string;
        userName: string;
        department: string;
        action: string;
        description: string;
        resourceType: string;
        resourceId?: string;
        details?: object;
        ipAddress: string;
        userAgent: string;
        severity?: 'INFO' | 'WARNING' | 'ERROR';
    }): Promise<AuditLogEntry>;
    static getAuditLogs(filters?: {
        startDate?: string;
        endDate?: string;
        userId?: string;
        department?: string;
        action?: string;
        severity?: string;
        limit?: number;
        offset?: number;
    }): Promise<any[]>;
    static getAuditStats(timeframe?: 'today' | 'week' | 'month'): Promise<any>;
    static getRecentActivities(limit?: number): Promise<any[]>;
    static logLogin(userId: string, userName: string, department: string, ipAddress: string, userAgent: string): Promise<AuditLogEntry>;
    static logLogout(userId: string, userName: string, department: string, ipAddress: string, userAgent: string): Promise<AuditLogEntry>;
    static logVoucherCreate(userId: string, userName: string, department: string, voucherId: string, amount: number, ipAddress: string, userAgent: string): Promise<AuditLogEntry>;
    static logVoucherDispatch(userId: string, userName: string, department: string, voucherCount: number, targetDept: string, ipAddress: string, userAgent: string): Promise<AuditLogEntry>;
    static logVoucherCertify(userId: string, userName: string, department: string, voucherId: string, ipAddress: string, userAgent: string): Promise<AuditLogEntry>;
    static logBackupCreate(userId: string, userName: string, department: string, backupType: string, ipAddress: string, userAgent: string): Promise<AuditLogEntry>;
    static logSystemError(userId: string, userName: string, department: string, error: string, ipAddress: string, userAgent: string): Promise<AuditLogEntry>;
}
export default AuditService;
