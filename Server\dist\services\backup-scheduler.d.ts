/**
 * PRODUCTION-GRADE: Automated backup scheduling service
 * Simple, reliable, no external dependencies
 */
export declare class BackupScheduler {
    private backupInterval;
    private dailyBackupTimeout;
    private isBackupInProgress;
    private scheduledTime;
    /**
     * Start automated backup scheduling
     * @param intervalHours - Hours between backups (default: 24 hours)
     */
    start(intervalHours?: number): void;
    /**
     * Schedule daily backup at specific time
     * @param time - Time in HH:MM format (24-hour)
     */
    scheduleDaily(time: string): void;
    /**
     * Stop automated backup scheduling
     */
    stop(): void;
    /**
     * Perform automated backup
     */
    private performAutomatedBackup;
    /**
     * Clean up old automated backups (keep last 7)
     */
    private cleanupOldBackups;
    /**
     * Trigger manual backup
     */
    triggerManualBackup(): Promise<boolean>;
    /**
     * Get backup status
     */
    getStatus(): {
        isScheduled: boolean;
        isBackupInProgress: boolean;
        scheduledTime: string | null;
        nextBackup: string;
    };
}
export declare const backupScheduler: BackupScheduler;
