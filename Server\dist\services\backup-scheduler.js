"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.backupScheduler = exports.BackupScheduler = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const logger_js_1 = require("../utils/logger.js");
const db_js_1 = require("../database/db.js");
/**
 * PRODUCTION-GRADE: Automated backup scheduling service
 * Simple, reliable, no external dependencies
 */
class BackupScheduler {
    backupInterval = null;
    dailyBackupTimeout = null;
    isBackupInProgress = false;
    scheduledTime = null; // Format: "HH:MM"
    /**
     * Start automated backup scheduling
     * @param intervalHours - Hours between backups (default: 24 hours)
     */
    start(intervalHours = 24) {
        // Clear any existing interval
        this.stop();
        const intervalMs = intervalHours * 60 * 60 * 1000; // Convert to milliseconds
        logger_js_1.logger.info(`🕒 Starting automated backup scheduler (every ${intervalHours} hours)`);
        // Schedule regular backups
        this.backupInterval = setInterval(async () => {
            await this.performAutomatedBackup();
        }, intervalMs);
        // Perform initial backup after 5 minutes (give system time to stabilize)
        setTimeout(async () => {
            await this.performAutomatedBackup();
        }, 5 * 60 * 1000);
    }
    /**
     * Schedule daily backup at specific time
     * @param time - Time in HH:MM format (24-hour)
     */
    scheduleDaily(time) {
        // Validate time format
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (!timeRegex.test(time)) {
            throw new Error('Invalid time format. Use HH:MM (24-hour format)');
        }
        this.stop();
        this.scheduledTime = time;
        const [hours, minutes] = time.split(':').map(Number);
        const scheduleNextBackup = () => {
            const now = new Date();
            const scheduledDate = new Date();
            scheduledDate.setHours(hours, minutes, 0, 0);
            // If the scheduled time has passed today, schedule for tomorrow
            if (scheduledDate <= now) {
                scheduledDate.setDate(scheduledDate.getDate() + 1);
            }
            const msUntilBackup = scheduledDate.getTime() - now.getTime();
            logger_js_1.logger.info(`🕒 Next automated backup scheduled for: ${scheduledDate.toLocaleString()}`);
            this.dailyBackupTimeout = setTimeout(async () => {
                await this.performAutomatedBackup();
                // Schedule the next backup for tomorrow
                scheduleNextBackup();
            }, msUntilBackup);
        };
        scheduleNextBackup();
        logger_js_1.logger.info(`✅ Daily backup scheduled at ${time} every day`);
    }
    /**
     * Stop automated backup scheduling
     */
    stop() {
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
            this.backupInterval = null;
        }
        if (this.dailyBackupTimeout) {
            clearTimeout(this.dailyBackupTimeout);
            this.dailyBackupTimeout = null;
        }
        this.scheduledTime = null;
        logger_js_1.logger.info('🛑 Automated backup scheduler stopped');
    }
    /**
     * Perform automated backup
     */
    async performAutomatedBackup() {
        if (this.isBackupInProgress) {
            logger_js_1.logger.warn('⚠️ Backup already in progress, skipping scheduled backup');
            return false;
        }
        this.isBackupInProgress = true;
        try {
            logger_js_1.logger.info('🔄 Starting automated backup...');
            // Create backups directory if it doesn't exist
            const backupsDir = path_1.default.join(process.cwd(), 'backups');
            if (!fs_1.default.existsSync(backupsDir)) {
                fs_1.default.mkdirSync(backupsDir, { recursive: true });
            }
            // Generate backup filename
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupFilename = `vms_auto_backup_${timestamp}.sql`;
            const backupPath = path_1.default.join(backupsDir, backupFilename);
            // Database configuration
            const dbConfig = {
                host: process.env.DB_HOST || 'localhost',
                port: parseInt(process.env.DB_PORT || '3306'),
                user: process.env.DB_USER || 'root',
                password: process.env.DB_PASSWORD || 'vms@2025@1989',
                database: process.env.DB_NAME || 'vms_production',
            };
            // Create database connection for backup
            const mysql = require('mysql2/promise');
            const connection = await mysql.createConnection(dbConfig);
            // Get all table names
            const [tables] = await connection.execute('SHOW TABLES');
            const tableNames = tables.map((row) => Object.values(row)[0]);
            let backupContent = `-- VMS Production Database Backup (AUTOMATED)\n`;
            backupContent += `-- Generated: ${new Date().toISOString()}\n`;
            backupContent += `-- Database: ${dbConfig.database}\n`;
            backupContent += `-- Type: Automated Backup\n\n`;
            backupContent += `SET FOREIGN_KEY_CHECKS = 0;\n\n`;
            // Backup each table
            for (const tableName of tableNames) {
                // Get table structure
                const [createTable] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
                const createStatement = createTable[0]['Create Table'];
                backupContent += `-- Table: ${tableName}\n`;
                backupContent += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
                backupContent += `${createStatement};\n\n`;
                // Get table data
                const [rows] = await connection.execute(`SELECT * FROM \`${tableName}\``);
                if (rows.length > 0) {
                    backupContent += `-- Data for table: ${tableName}\n`;
                    backupContent += `INSERT INTO \`${tableName}\` VALUES\n`;
                    const values = rows.map(row => {
                        const escapedValues = Object.values(row).map(value => {
                            if (value === null)
                                return 'NULL';
                            if (typeof value === 'string') {
                                // Handle datetime strings that might be in ISO format
                                if (value.includes('T') && value.includes('Z')) {
                                    try {
                                        // Convert ISO datetime to MySQL format
                                        const date = new Date(value);
                                        // Check if the date is valid
                                        if (isNaN(date.getTime())) {
                                            // If invalid date, treat as regular string
                                            return `'${value.replace(/'/g, "''")}'`;
                                        }
                                        return `'${date.toISOString().slice(0, 19).replace('T', ' ')}'`;
                                    }
                                    catch (error) {
                                        // If any error occurs, treat as regular string
                                        return `'${value.replace(/'/g, "''")}'`;
                                    }
                                }
                                return `'${value.replace(/'/g, "''")}'`;
                            }
                            if (value instanceof Date) {
                                try {
                                    // Check if the date is valid before converting
                                    if (isNaN(value.getTime())) {
                                        return 'NULL';
                                    }
                                    return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
                                }
                                catch (error) {
                                    return 'NULL';
                                }
                            }
                            return value;
                        });
                        return `(${escapedValues.join(', ')})`;
                    });
                    backupContent += values.join(',\n') + ';\n\n';
                }
            }
            backupContent += `SET FOREIGN_KEY_CHECKS = 1;\n`;
            // Write backup file
            fs_1.default.writeFileSync(backupPath, backupContent, 'utf8');
            // Verify backup file was created and has content
            const stats = fs_1.default.statSync(backupPath);
            if (stats.size === 0) {
                throw new Error('Backup file is empty');
            }
            await connection.end();
            // Update last backup date in system settings
            await (0, db_js_1.query)('UPDATE system_settings SET last_backup_date = ? WHERE id = 1', [new Date().toISOString()]);
            // Clean up old backups (keep last 7 automated backups)
            await this.cleanupOldBackups(backupsDir);
            logger_js_1.logger.info(`✅ Automated backup completed: ${backupFilename} (${stats.size} bytes)`);
            return true;
        }
        catch (error) {
            logger_js_1.logger.error('❌ Automated backup failed:', error);
            return false;
        }
        finally {
            this.isBackupInProgress = false;
        }
    }
    /**
     * Clean up old automated backups (keep last 7)
     */
    async cleanupOldBackups(backupsDir) {
        try {
            const files = fs_1.default.readdirSync(backupsDir)
                .filter(file => file.startsWith('vms_auto_backup_') && file.endsWith('.sql'))
                .map(file => ({
                name: file,
                path: path_1.default.join(backupsDir, file),
                stats: fs_1.default.statSync(path_1.default.join(backupsDir, file))
            }))
                .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime()); // Sort by date, newest first
            // Keep only the 7 most recent automated backups
            const filesToDelete = files.slice(7);
            for (const file of filesToDelete) {
                fs_1.default.unlinkSync(file.path);
                logger_js_1.logger.info(`🗑️ Cleaned up old backup: ${file.name}`);
            }
            if (filesToDelete.length > 0) {
                logger_js_1.logger.info(`✅ Backup cleanup completed: removed ${filesToDelete.length} old backups`);
            }
        }
        catch (error) {
            logger_js_1.logger.warn('⚠️ Backup cleanup failed:', error);
        }
    }
    /**
     * Trigger manual backup
     */
    async triggerManualBackup() {
        return await this.performAutomatedBackup();
    }
    /**
     * Get backup status
     */
    getStatus() {
        const isScheduled = this.backupInterval !== null || this.dailyBackupTimeout !== null;
        let nextBackup = 'Not scheduled';
        if (this.scheduledTime && this.dailyBackupTimeout) {
            const now = new Date();
            const [hours, minutes] = this.scheduledTime.split(':').map(Number);
            const scheduledDate = new Date();
            scheduledDate.setHours(hours, minutes, 0, 0);
            if (scheduledDate <= now) {
                scheduledDate.setDate(scheduledDate.getDate() + 1);
            }
            nextBackup = scheduledDate.toLocaleString();
        }
        else if (this.backupInterval) {
            nextBackup = 'Interval-based';
        }
        return {
            isScheduled,
            isBackupInProgress: this.isBackupInProgress,
            scheduledTime: this.scheduledTime,
            nextBackup
        };
    }
}
exports.BackupScheduler = BackupScheduler;
// Export singleton instance
exports.backupScheduler = new BackupScheduler();
//# sourceMappingURL=backup-scheduler.js.map