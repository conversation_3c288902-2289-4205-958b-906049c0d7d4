/**
 * Enterprise Monitoring Service
 * Advanced monitoring and alerting for production-level admin dashboard
 */
export declare class EnterpriseMonitoring {
    /**
     * Get comprehensive system metrics for enterprise dashboard
     */
    static getSystemMetrics(): Promise<any>;
    /**
     * Get database performance metrics
     */
    private static getDatabaseMetrics;
    /**
     * Get system performance metrics
     */
    private static getPerformanceMetrics;
    /**
     * Get business intelligence metrics
     */
    private static getBusinessMetrics;
    /**
     * Get security and audit metrics
     */
    private static getSecurityMetrics;
    /**
     * Get user activity metrics
     */
    private static getUserActivityMetrics;
    /**
     * Calculate overall system status
     */
    private static calculateOverallStatus;
    /**
     * Get system alerts
     */
    static getSystemAlerts(): Promise<any[]>;
}
