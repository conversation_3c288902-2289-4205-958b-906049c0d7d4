/**
 * Workflow Service Initialization
 * Sets up the workflow manager and related services
 */
import { VoucherWorkflowManager } from '../workflow/VoucherWorkflowManager';
/**
 * Initialize workflow service
 */
export declare function initializeWorkflowService(db: any, eventPublisher: any): Promise<VoucherWorkflowManager>;
/**
 * Get workflow manager instance
 */
export declare function getWorkflowManager(): VoucherWorkflowManager;
/**
 * Workflow service health check
 */
export declare function checkWorkflowServiceHealth(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: any;
}>;
