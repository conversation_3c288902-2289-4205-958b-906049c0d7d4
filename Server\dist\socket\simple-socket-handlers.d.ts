/**
 * BULLETPROOF WebSocket Handlers
 *
 * Simplified, reliable WebSocket handling for VMS
 * - No complex dependencies
 * - Robust error handling
 * - Simple connection management
 */
import { Server as SocketIOServer } from 'socket.io';
export declare function setIoInstance(ioInstance: SocketIOServer): void;
export declare function getIoInstance(): SocketIOServer | null;
export declare function setupSocketHandlers(ioInstance: SocketIOServer): void;
export declare function sendToUser(userId: string, event: string, data: any): boolean;
export declare function sendToDepartment(department: string, event: string, data: any): boolean;
export declare function broadcast(event: string, data: any): boolean;
export declare function getConnectionStats(): {
    total: number;
    byDepartment: Record<string, number>;
    authenticated: number;
};
