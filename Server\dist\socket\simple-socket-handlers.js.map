{"version": 3, "file": "simple-socket-handlers.js", "sourceRoot": "", "sources": ["../../src/socket/simple-socket-handlers.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAiBH,sCAGC;AAED,sCAEC;AAED,kDAaC;AAsJD,gCAyBC;AAED,4CAWC;AAED,8BAWC;AAED,gDAiBC;AAhQD,kDAA4C;AAE5C,6BAA6B;AAC7B,MAAM,WAAW,GAAG,IAAI,GAAG,EAOvB,CAAC;AAEL,IAAI,EAAE,GAA0B,IAAI,CAAC;AAErC,SAAgB,aAAa,CAAC,UAA0B;IACtD,EAAE,GAAG,UAAU,CAAC;IAChB,kBAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;AAC1C,CAAC;AAED,SAAgB,aAAa;IAC3B,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAgB,mBAAmB,CAAC,UAA0B;IAC5D,EAAE,GAAG,UAAU,CAAC;IAEhB,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAc,EAAE,EAAE;QACrC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;QACvB,kBAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,kBAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAc;IACtC,MAAM,YAAY,GAAG,MAAM,CAAC,EAAE,CAAC;IAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAEvB,mBAAmB;IACnB,WAAW,CAAC,GAAG,CAAC,YAAY,EAAE;QAC5B,EAAE,EAAE,YAAY;QAChB,WAAW,EAAE,GAAG;QAChB,YAAY,EAAE,GAAG;KAClB,CAAC,CAAC;IAEH,kBAAM,CAAC,IAAI,CAAC,2BAA2B,YAAY,EAAE,CAAC,CAAC;IAEvD,iBAAiB;IACjB,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE;QACjC,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC;YAE3D,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACzB,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAC;gBAClE,OAAO;YACT,CAAC;YAED,yBAAyB;YACzB,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACjD,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;gBAC3B,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAC/B,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC;gBACnC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvC,CAAC;YAED,mCAAmC;YACnC,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACxB,kBAAM,CAAC,IAAI,CAAC,gCAAgC,YAAY,MAAM,UAAU,EAAE,CAAC,CAAC;YAC9E,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;gBAC3B,OAAO,EAAE,IAAI;gBACb,YAAY;gBACZ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;YAEH,kBAAM,CAAC,IAAI,CAAC,2BAA2B,YAAY,KAAK,QAAQ,MAAM,UAAU,GAAG,CAAC,CAAC;QAEvF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kBAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,EAAE;QACpC,IAAI,CAAC;YACH,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC;YAElC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;gBACjE,OAAO;YACT,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAExB,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACjD,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC;gBACnC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvC,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,UAAU;gBACV,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC,CAAC;YAEH,kBAAM,CAAC,IAAI,CAAC,gCAAgC,YAAY,MAAM,UAAU,EAAE,CAAC,CAAC;QAE9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kBAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,YAAY;IACZ,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;QACrB,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAC/C,cAAc,CAAC,YAAY,CAAC,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kBAAM,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACrC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,+BAA+B;IAC/B,MAAM,CAAC,EAAE,CAAC,yBAAyB,EAAE,CAAC,IAAI,EAAE,EAAE;QAC5C,IAAI,CAAC;YACH,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC;YAElD,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC,CAAC;gBACjF,OAAO;YACT,CAAC;YAED,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACxC,cAAc,CAAC,YAAY,CAAC,CAAC;YAE7B,kBAAM,CAAC,KAAK,CAAC,mBAAmB,UAAU,KAAK,KAAK,EAAE,CAAC,CAAC;QAE1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kBAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,gBAAgB;IAChB,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;QACjC,mBAAmB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,iBAAiB;IACjB,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;QAC3B,kBAAM,CAAC,KAAK,CAAC,oBAAoB,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;QAChB,cAAc,CAAC,YAAY,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,cAAc,CAAC,YAAoB;IAC1C,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACjD,IAAI,UAAU,EAAE,CAAC;QACf,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvC,CAAC;AACH,CAAC;AAED,SAAS,mBAAmB,CAAC,YAAoB,EAAE,MAAc;IAC/D,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAEjD,IAAI,UAAU,EAAE,CAAC;QACf,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACjC,kBAAM,CAAC,IAAI,CAAC,8BAA8B,YAAY,KAAK,MAAM,GAAG,CAAC,CAAC;IACxE,CAAC;AACH,CAAC;AAED,yCAAyC;AACzC,SAAgB,UAAU,CAAC,MAAc,EAAE,KAAa,EAAE,IAAS;IACjE,IAAI,CAAC,EAAE;QAAE,OAAO,KAAK,CAAC;IAEtB,IAAI,CAAC;QACH,qCAAqC;QACrC,IAAI,IAAI,GAAG,KAAK,CAAC;QACjB,KAAK,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,WAAW,EAAE,CAAC;YACjD,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAChD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;oBACzB,IAAI,GAAG,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,kBAAM,CAAC,KAAK,CAAC,2BAA2B,MAAM,KAAK,KAAK,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC3C,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,SAAgB,gBAAgB,CAAC,UAAkB,EAAE,KAAa,EAAE,IAAS;IAC3E,IAAI,CAAC,EAAE;QAAE,OAAO,KAAK,CAAC;IAEtB,IAAI,CAAC;QACH,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACpC,kBAAM,CAAC,KAAK,CAAC,iCAAiC,UAAU,KAAK,KAAK,EAAE,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACjD,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,SAAgB,SAAS,CAAC,KAAa,EAAE,IAAS;IAChD,IAAI,CAAC,EAAE;QAAE,OAAO,KAAK,CAAC;IAEtB,IAAI,CAAC;QACH,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrB,kBAAM,CAAC,KAAK,CAAC,sBAAsB,KAAK,EAAE,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QACxC,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,SAAgB,kBAAkB;IAChC,MAAM,KAAK,GAAG;QACZ,KAAK,EAAE,WAAW,CAAC,IAAI;QACvB,YAAY,EAAE,EAA4B;QAC1C,aAAa,EAAE,CAAC;KACjB,CAAC;IAEF,KAAK,MAAM,UAAU,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;QAC9C,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;YACtB,KAAK,CAAC,aAAa,EAAE,CAAC;QACxB,CAAC;QACD,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YAC1B,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,gCAAgC;AAChC,WAAW,CAAC,GAAG,EAAE;IACf,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY;IAE3C,KAAK,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,WAAW,EAAE,CAAC;QACjD,IAAI,GAAG,GAAG,UAAU,CAAC,YAAY,GAAG,OAAO,EAAE,CAAC;YAC5C,MAAM,MAAM,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;YACD,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC7B,kBAAM,CAAC,IAAI,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;AACH,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,qBAAqB"}