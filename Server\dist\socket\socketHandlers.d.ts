import { Server } from 'socket.io';
export declare function setupSocketHandlers(io: Server): void;
export declare function setIoInstance(io: Server): void;
declare function clearAllDepartmentLocks(): number;
export declare function broadcastUserUpdate(type: string, userData: any): void;
export declare function broadcastRegistrationUpdate(type: string, registrationData: any): void;
export declare function broadcastVoucherUpdate(type: string, voucherData: any): void;
export declare function broadcastBatchUpdate(type: string, batchData: any): void;
export declare function broadcastNotificationUpdate(type: string, notificationData: any): void;
export declare function broadcastToAuditDepartment(event: string, data: any): void;
export declare function broadcastToDepartment(department: string, event: string, data: any): void;
export declare function broadcastDataUpdate(entityType: string, actionType: string, data: any): void;
declare function releaseUserLocksOnLogout(userId: string): number;
export { clearAllDepartmentLocks, releaseUserLocksOnLogout };
