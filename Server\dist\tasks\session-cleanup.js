"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupStaleSessions = cleanupStaleSessions;
exports.scheduleSessionCleanup = scheduleSessionCleanup;
const db_js_1 = require("../database/db.js");
const logger_js_1 = require("../utils/logger.js");
const session_deduplication_js_1 = require("../utils/session-deduplication.js");
/**
 * Cleans up stale sessions in the database
 * - Marks sessions as inactive if they've been inactive for more than 15 minutes
 * - Runs on a schedule (called from server.ts)
 */
async function cleanupStaleSessions() {
    try {
        // PRODUCTION-SAFE: Conservative cleanup thresholds
        const INACTIVITY_THRESHOLD = 60; // 60 minutes for inactive sessions
        const OLD_SESSION_THRESHOLD = 24 * 60; // 24 hours for old sessions
        // Step 1: Mark inactive sessions (60+ minutes of inactivity)
        const staleResult = await (0, db_js_1.query)(`UPDATE active_sessions
       SET is_active = FALSE, session_end = NOW()
       WHERE is_active = TRUE
       AND TIMESTAMPDIFF(MINUTE, last_activity, NOW()) >= ?`, [INACTIVITY_THRESHOLD]);
        if (staleResult.affectedRows > 0) {
            logger_js_1.logger.info(`Marked ${staleResult.affectedRows} sessions as inactive due to 60+ minutes inactivity`);
        }
        // Step 2: Delete very old inactive sessions (24+ hours old)
        const deleteResult = await (0, db_js_1.query)(`DELETE FROM active_sessions
       WHERE is_active = FALSE
       AND TIMESTAMPDIFF(MINUTE, session_end, NOW()) >= ?`, [OLD_SESSION_THRESHOLD]);
        if (deleteResult.affectedRows > 0) {
            logger_js_1.logger.info(`Deleted ${deleteResult.affectedRows} old inactive sessions (24+ hours old)`);
        }
        // Step 3: SINGLE SESSION FIX - Deduplicate user sessions
        await (0, session_deduplication_js_1.deduplicateUserSessions)();
        // Step 4: Get current session statistics
        const stats = await (0, session_deduplication_js_1.getSessionStats)();
        logger_js_1.logger.debug(`Session stats: ${stats.activeSessions} active, ${stats.totalSessions - stats.activeSessions} inactive, ${stats.totalSessions} total`);
        if (stats.duplicateUsers > 0) {
            logger_js_1.logger.warn(`⚠️  Still found ${stats.duplicateUsers} users with duplicate sessions after cleanup`);
        }
        else {
            logger_js_1.logger.info(`✅ All users have unique sessions (${stats.uniqueActiveUsers} unique active users)`);
        }
    }
    catch (error) {
        logger_js_1.logger.error('Error cleaning up stale sessions:', error);
    }
}
/**
 * Schedule the cleanup task to run every minute
 */
function scheduleSessionCleanup() {
    // Run immediately on startup
    cleanupStaleSessions();
    // Then schedule to run every minute
    setInterval(cleanupStaleSessions, 60 * 1000);
    logger_js_1.logger.info('Session cleanup task scheduled');
}
//# sourceMappingURL=session-cleanup.js.map