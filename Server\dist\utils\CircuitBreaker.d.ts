/**
 * PRODUCTION-READY Circuit Breaker Pattern
 * Prevents cascading failures and provides graceful degradation
 */
export declare enum CircuitState {
    CLOSED = "CLOSED",// Normal operation
    OPEN = "OPEN",// Circuit is open, requests fail fast
    HALF_OPEN = "HALF_OPEN"
}
export interface CircuitBreakerOptions {
    failureThreshold: number;
    recoveryTimeout: number;
    monitoringPeriod: number;
    expectedErrors?: string[];
}
export interface CircuitBreakerStats {
    state: CircuitState;
    failureCount: number;
    successCount: number;
    lastFailureTime: number | null;
    nextAttemptTime: number | null;
}
export declare class CircuitBreaker {
    private state;
    private failureCount;
    private successCount;
    private lastFailureTime;
    private nextAttemptTime;
    private readonly options;
    constructor(options?: Partial<CircuitBreakerOptions>);
    /**
     * Execute a function with circuit breaker protection
     */
    execute<T>(fn: () => Promise<T>): Promise<T>;
    /**
     * Handle successful execution
     */
    private onSuccess;
    /**
     * Handle failed execution
     */
    private onFailure;
    /**
     * Check if error should count towards failure threshold
     */
    private shouldCountFailure;
    /**
     * Open the circuit breaker
     */
    private open;
    /**
     * Reset the circuit breaker to closed state
     */
    private reset;
    /**
     * Check if we should attempt to reset the circuit
     */
    private shouldAttemptReset;
    /**
     * Get current circuit breaker statistics
     */
    getStats(): CircuitBreakerStats;
    /**
     * Check if circuit is healthy
     */
    isHealthy(): boolean;
    /**
     * Force circuit to open (for testing or manual intervention)
     */
    forceOpen(): void;
    /**
     * Force circuit to close (for testing or manual intervention)
     */
    forceClose(): void;
}
/**
 * Circuit Breaker Manager for managing multiple circuit breakers
 */
export declare class CircuitBreakerManager {
    private breakers;
    /**
     * Get or create a circuit breaker for a service
     */
    getBreaker(serviceName: string, options?: Partial<CircuitBreakerOptions>): CircuitBreaker;
    /**
     * Execute function with circuit breaker protection
     */
    execute<T>(serviceName: string, fn: () => Promise<T>, options?: Partial<CircuitBreakerOptions>): Promise<T>;
    /**
     * Get health status of all circuit breakers
     */
    getHealthStatus(): Record<string, CircuitBreakerStats>;
    /**
     * Reset all circuit breakers
     */
    resetAll(): void;
    /**
     * Get list of unhealthy services
     */
    getUnhealthyServices(): string[];
}
export declare const circuitBreakerManager: CircuitBreakerManager;
