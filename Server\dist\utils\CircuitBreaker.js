"use strict";
/**
 * PRODUCTION-READY Circuit Breaker Pattern
 * Prevents cascading failures and provides graceful degradation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.circuitBreakerManager = exports.CircuitBreakerManager = exports.CircuitBreaker = exports.CircuitState = void 0;
var CircuitState;
(function (CircuitState) {
    CircuitState["CLOSED"] = "CLOSED";
    CircuitState["OPEN"] = "OPEN";
    CircuitState["HALF_OPEN"] = "HALF_OPEN"; // Testing if service is back
})(CircuitState || (exports.CircuitState = CircuitState = {}));
class CircuitBreaker {
    state = CircuitState.CLOSED;
    failureCount = 0;
    successCount = 0;
    lastFailureTime = null;
    nextAttemptTime = null;
    options;
    constructor(options = {}) {
        this.options = {
            failureThreshold: options.failureThreshold || 5,
            recoveryTimeout: options.recoveryTimeout || 60000, // 1 minute
            monitoringPeriod: options.monitoringPeriod || 120000, // 2 minutes
            expectedErrors: options.expectedErrors || []
        };
    }
    /**
     * Execute a function with circuit breaker protection
     */
    async execute(fn) {
        if (this.state === CircuitState.OPEN) {
            if (this.shouldAttemptReset()) {
                this.state = CircuitState.HALF_OPEN;
            }
            else {
                throw new Error(`Circuit breaker is OPEN. Next attempt at ${new Date(this.nextAttemptTime)}`);
            }
        }
        try {
            const result = await fn();
            this.onSuccess();
            return result;
        }
        catch (error) {
            this.onFailure(error);
            throw error;
        }
    }
    /**
     * Handle successful execution
     */
    onSuccess() {
        this.successCount++;
        if (this.state === CircuitState.HALF_OPEN) {
            this.reset();
        }
    }
    /**
     * Handle failed execution
     */
    onFailure(error) {
        // Only count expected errors
        if (this.shouldCountFailure(error)) {
            this.failureCount++;
            this.lastFailureTime = Date.now();
            if (this.state === CircuitState.HALF_OPEN) {
                this.open();
            }
            else if (this.failureCount >= this.options.failureThreshold) {
                this.open();
            }
        }
    }
    /**
     * Check if error should count towards failure threshold
     */
    shouldCountFailure(error) {
        if (this.options.expectedErrors.length === 0) {
            return true; // Count all errors if no specific errors defined
        }
        return this.options.expectedErrors.some(expectedError => error.message?.includes(expectedError) ||
            error.code === expectedError ||
            error.name === expectedError);
    }
    /**
     * Open the circuit breaker
     */
    open() {
        this.state = CircuitState.OPEN;
        this.nextAttemptTime = Date.now() + this.options.recoveryTimeout;
    }
    /**
     * Reset the circuit breaker to closed state
     */
    reset() {
        this.state = CircuitState.CLOSED;
        this.failureCount = 0;
        this.successCount = 0;
        this.lastFailureTime = null;
        this.nextAttemptTime = null;
    }
    /**
     * Check if we should attempt to reset the circuit
     */
    shouldAttemptReset() {
        return this.nextAttemptTime !== null && Date.now() >= this.nextAttemptTime;
    }
    /**
     * Get current circuit breaker statistics
     */
    getStats() {
        return {
            state: this.state,
            failureCount: this.failureCount,
            successCount: this.successCount,
            lastFailureTime: this.lastFailureTime,
            nextAttemptTime: this.nextAttemptTime
        };
    }
    /**
     * Check if circuit is healthy
     */
    isHealthy() {
        return this.state === CircuitState.CLOSED;
    }
    /**
     * Force circuit to open (for testing or manual intervention)
     */
    forceOpen() {
        this.open();
    }
    /**
     * Force circuit to close (for testing or manual intervention)
     */
    forceClose() {
        this.reset();
    }
}
exports.CircuitBreaker = CircuitBreaker;
/**
 * Circuit Breaker Manager for managing multiple circuit breakers
 */
class CircuitBreakerManager {
    breakers = new Map();
    /**
     * Get or create a circuit breaker for a service
     */
    getBreaker(serviceName, options) {
        if (!this.breakers.has(serviceName)) {
            this.breakers.set(serviceName, new CircuitBreaker(options));
        }
        return this.breakers.get(serviceName);
    }
    /**
     * Execute function with circuit breaker protection
     */
    async execute(serviceName, fn, options) {
        const breaker = this.getBreaker(serviceName, options);
        return breaker.execute(fn);
    }
    /**
     * Get health status of all circuit breakers
     */
    getHealthStatus() {
        const status = {};
        for (const [serviceName, breaker] of this.breakers) {
            status[serviceName] = breaker.getStats();
        }
        return status;
    }
    /**
     * Reset all circuit breakers
     */
    resetAll() {
        for (const breaker of this.breakers.values()) {
            breaker.forceClose();
        }
    }
    /**
     * Get list of unhealthy services
     */
    getUnhealthyServices() {
        const unhealthy = [];
        for (const [serviceName, breaker] of this.breakers) {
            if (!breaker.isHealthy()) {
                unhealthy.push(serviceName);
            }
        }
        return unhealthy;
    }
}
exports.CircuitBreakerManager = CircuitBreakerManager;
// Singleton instance
exports.circuitBreakerManager = new CircuitBreakerManager();
//# sourceMappingURL=CircuitBreaker.js.map