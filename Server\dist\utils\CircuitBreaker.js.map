{"version": 3, "file": "CircuitBreaker.js", "sourceRoot": "", "sources": ["../../src/utils/CircuitBreaker.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,IAAY,YAIX;AAJD,WAAY,YAAY;IACtB,iCAAiB,CAAA;IACjB,6BAAa,CAAA;IACb,uCAAuB,CAAA,CAAC,6BAA6B;AACvD,CAAC,EAJW,YAAY,4BAAZ,YAAY,QAIvB;AAiBD,MAAa,cAAc;IACjB,KAAK,GAAiB,YAAY,CAAC,MAAM,CAAC;IAC1C,YAAY,GAAW,CAAC,CAAC;IACzB,YAAY,GAAW,CAAC,CAAC;IACzB,eAAe,GAAkB,IAAI,CAAC;IACtC,eAAe,GAAkB,IAAI,CAAC;IAC7B,OAAO,CAAwB;IAEhD,YAAY,UAA0C,EAAE;QACtD,IAAI,CAAC,OAAO,GAAG;YACb,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,CAAC;YAC/C,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,KAAK,EAAE,WAAW;YAC9D,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,MAAM,EAAE,YAAY;YAClE,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,EAAE;SAC7C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAI,EAAoB;QACnC,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC;YACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;gBAC9B,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,4CAA4C,IAAI,IAAI,CAAC,IAAI,CAAC,eAAgB,CAAC,EAAE,CAAC,CAAC;YACjG,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,EAAE,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACtB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,SAAS;QACf,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,KAAU;QAC1B,6BAA6B;QAC7B,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAElC,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,SAAS,EAAE,CAAC;gBAC1C,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAC9D,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,KAAU;QACnC,IAAI,IAAI,CAAC,OAAO,CAAC,cAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC,CAAC,iDAAiD;QAChE,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,cAAe,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CACvD,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC;YACtC,KAAK,CAAC,IAAI,KAAK,aAAa;YAC5B,KAAK,CAAC,IAAI,KAAK,aAAa,CAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,IAAI;QACV,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;IACnE,CAAC;IAED;;OAEG;IACK,KAAK;QACX,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,OAAO,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,eAAe,CAAC;IAC7E,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,MAAM,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,SAAS;QACP,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,KAAK,EAAE,CAAC;IACf,CAAC;CACF;AA7ID,wCA6IC;AAED;;GAEG;AACH,MAAa,qBAAqB;IACxB,QAAQ,GAAgC,IAAI,GAAG,EAAE,CAAC;IAE1D;;OAEG;IACH,UAAU,CAAC,WAAmB,EAAE,OAAwC;QACtE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAI,WAAmB,EAAE,EAAoB,EAAE,OAAwC;QAClG,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACtD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,eAAe;QACb,MAAM,MAAM,GAAwC,EAAE,CAAC;QAEvD,KAAK,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnD,MAAM,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC3C,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,OAAO,CAAC,UAAU,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,MAAM,SAAS,GAAa,EAAE,CAAC;QAE/B,KAAK,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnD,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;gBACzB,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAzDD,sDAyDC;AAED,qBAAqB;AACR,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC"}