/**
 * Production-Grade Event Deduplication System
 * Prevents infinite loops and duplicate event processing
 */
interface EventKey {
    type: string;
    entityId: string;
    userId?: string;
    timestamp?: number;
}
export declare class EventDeduplicator {
    private static instance;
    private eventCache;
    private readonly maxAge;
    private readonly maxDuplicates;
    private readonly cleanupInterval;
    private cleanupTimer?;
    private constructor();
    static getInstance(): EventDeduplicator;
    /**
     * Check if event should be processed (not a duplicate)
     */
    shouldProcess(eventKey: EventKey): boolean;
    /**
     * Mark event as processed (for tracking)
     */
    markProcessed(eventKey: EventKey): void;
    /**
     * Generate unique key for event
     */
    private generateKey;
    /**
     * Start cleanup timer
     */
    private startCleanup;
    /**
     * Clean up old events
     */
    private cleanup;
    /**
     * Get current cache stats
     */
    getStats(): {
        totalEvents: number;
        oldestEvent: number;
        newestEvent: number;
    };
    /**
     * Clear all events (for testing)
     */
    clear(): void;
    /**
     * Shutdown cleanup
     */
    shutdown(): void;
}
export declare const eventDeduplicator: EventDeduplicator;
export {};
