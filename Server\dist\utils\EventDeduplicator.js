"use strict";
/**
 * Production-Grade Event Deduplication System
 * Prevents infinite loops and duplicate event processing
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.eventDeduplicator = exports.EventDeduplicator = void 0;
const logger_js_1 = require("./logger.js");
class EventDeduplicator {
    static instance;
    eventCache = new Map();
    maxAge = 30000; // 30 seconds
    maxDuplicates = 3; // Max 3 identical events
    cleanupInterval = 60000; // Cleanup every minute
    cleanupTimer;
    constructor() {
        this.startCleanup();
    }
    static getInstance() {
        if (!EventDeduplicator.instance) {
            EventDeduplicator.instance = new EventDeduplicator();
        }
        return EventDeduplicator.instance;
    }
    /**
     * Check if event should be processed (not a duplicate)
     */
    shouldProcess(eventKey) {
        const key = this.generateKey(eventKey);
        const now = Date.now();
        const existing = this.eventCache.get(key);
        if (!existing) {
            // First time seeing this event
            this.eventCache.set(key, {
                key,
                timestamp: now,
                count: 1,
                lastSeen: now
            });
            return true;
        }
        // Check if event is too old (reset counter)
        if (now - existing.timestamp > this.maxAge) {
            this.eventCache.set(key, {
                key,
                timestamp: now,
                count: 1,
                lastSeen: now
            });
            return true;
        }
        // Check if we've seen too many duplicates
        if (existing.count >= this.maxDuplicates) {
            logger_js_1.logger.warn(`🚫 Event deduplication: Blocking duplicate event`, {
                eventType: eventKey.type,
                entityId: eventKey.entityId,
                count: existing.count,
                age: now - existing.timestamp
            });
            return false;
        }
        // Update existing record
        existing.count++;
        existing.lastSeen = now;
        this.eventCache.set(key, existing);
        return true;
    }
    /**
     * Mark event as processed (for tracking)
     */
    markProcessed(eventKey) {
        const key = this.generateKey(eventKey);
        const existing = this.eventCache.get(key);
        if (existing) {
            existing.lastSeen = Date.now();
            this.eventCache.set(key, existing);
        }
    }
    /**
     * Generate unique key for event
     */
    generateKey(eventKey) {
        const parts = [
            eventKey.type,
            eventKey.entityId,
            eventKey.userId || 'system'
        ];
        return parts.join(':');
    }
    /**
     * Start cleanup timer
     */
    startCleanup() {
        this.cleanupTimer = setInterval(() => {
            this.cleanup();
        }, this.cleanupInterval);
    }
    /**
     * Clean up old events
     */
    cleanup() {
        const now = Date.now();
        let cleaned = 0;
        for (const [key, record] of this.eventCache.entries()) {
            if (now - record.lastSeen > this.maxAge * 2) {
                this.eventCache.delete(key);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            logger_js_1.logger.debug(`🧹 Event deduplicator cleaned ${cleaned} old events`);
        }
    }
    /**
     * Get current cache stats
     */
    getStats() {
        const now = Date.now();
        let oldest = now;
        let newest = 0;
        for (const record of this.eventCache.values()) {
            if (record.timestamp < oldest)
                oldest = record.timestamp;
            if (record.lastSeen > newest)
                newest = record.lastSeen;
        }
        return {
            totalEvents: this.eventCache.size,
            oldestEvent: oldest === now ? 0 : now - oldest,
            newestEvent: newest === 0 ? 0 : now - newest
        };
    }
    /**
     * Clear all events (for testing)
     */
    clear() {
        this.eventCache.clear();
        logger_js_1.logger.info('🧹 Event deduplicator cache cleared');
    }
    /**
     * Shutdown cleanup
     */
    shutdown() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = undefined;
        }
        this.eventCache.clear();
    }
}
exports.EventDeduplicator = EventDeduplicator;
// Export singleton instance
exports.eventDeduplicator = EventDeduplicator.getInstance();
//# sourceMappingURL=EventDeduplicator.js.map