/**
 * PRODUCTION-READY Health Monitoring System
 * Monitors system health, performance metrics, and provides alerting
 */
export interface HealthCheck {
    name: string;
    check: () => Promise<HealthStatus>;
    interval: number;
    timeout: number;
    critical: boolean;
}
export interface HealthStatus {
    healthy: boolean;
    message: string;
    details?: any;
    timestamp: number;
    responseTime: number;
}
export interface SystemHealth {
    overall: 'healthy' | 'degraded' | 'unhealthy';
    checks: Record<string, HealthStatus>;
    uptime: number;
    timestamp: number;
    version: string;
}
export interface PerformanceMetrics {
    cpu: {
        usage: number;
        loadAverage: number[];
    };
    memory: {
        used: number;
        free: number;
        total: number;
        percentage: number;
    };
    requests: {
        total: number;
        successful: number;
        failed: number;
        averageResponseTime: number;
    };
    database: {
        connections: number;
        queries: number;
        averageQueryTime: number;
    };
}
export declare class HealthMonitor {
    private checks;
    private results;
    private intervals;
    private startTime;
    private requestMetrics;
    private queryMetrics;
    constructor();
    /**
     * Register a health check
     */
    registerCheck(check: HealthCheck): void;
    /**
     * Remove a health check
     */
    unregisterCheck(name: string): void;
    /**
     * Get current system health status
     */
    getSystemHealth(): Promise<SystemHealth>;
    /**
     * Get performance metrics
     */
    getPerformanceMetrics(): PerformanceMetrics;
    /**
     * Record request metrics
     */
    recordRequest(responseTime: number, success: boolean): void;
    /**
     * Record database query metrics
     */
    recordQuery(responseTime: number): void;
    /**
     * Register default health checks
     */
    private registerDefaultChecks;
    /**
     * Start monitoring intervals for a specific check
     */
    private startCheckInterval;
    /**
     * Start initial monitoring
     */
    private startMonitoring;
    /**
     * Create a timeout promise
     */
    private timeout;
    /**
     * Calculate CPU usage percentage
     */
    private calculateCpuUsage;
    /**
     * Calculate average from array of numbers
     */
    private calculateAverage;
    /**
     * Shutdown monitoring
     */
    shutdown(): void;
}
export declare const healthMonitor: HealthMonitor;
