{"version": 3, "file": "HealthMonitor.js", "sourceRoot": "", "sources": ["../../src/utils/HealthMonitor.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,2CAAqC;AACrC,uEAAiE;AACjE,2DAA4D;AAkD5D,MAAa,aAAa;IAChB,MAAM,GAA6B,IAAI,GAAG,EAAE,CAAC;IAC7C,OAAO,GAA8B,IAAI,GAAG,EAAE,CAAC;IAC/C,SAAS,GAAgC,IAAI,GAAG,EAAE,CAAC;IACnD,SAAS,GAAW,IAAI,CAAC,GAAG,EAAE,CAAC;IAC/B,cAAc,GAAG;QACvB,KAAK,EAAE,CAAC;QACR,UAAU,EAAE,CAAC;QACb,MAAM,EAAE,CAAC;QACT,aAAa,EAAE,EAAc;KAC9B,CAAC;IACM,YAAY,GAAG;QACrB,KAAK,EAAE,CAAC;QACR,aAAa,EAAE,EAAc;KAC9B,CAAC;IAEF;QACE,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,KAAkB;QAC9B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAY;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,QAAQ,EAAE,CAAC;YACb,aAAa,CAAC,QAAQ,CAAC,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,MAAM,MAAM,GAAiC,EAAE,CAAC;QAChD,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,oCAAoC;QACpC,KAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,aAAa,EAAE,CAAC;gBAChB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACpC,IAAI,KAAK,EAAE,QAAQ,EAAE,CAAC;oBACpB,gBAAgB,EAAE,CAAC;gBACrB,CAAC;YACH,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,OAA6C,CAAC;QAClD,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO,GAAG,WAAW,CAAC;QACxB,CAAC;aAAM,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,GAAG,UAAU,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,SAAS,CAAC;QACtB,CAAC;QAED,OAAO;YACL,OAAO;YACP,MAAM;YACN,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS;YACnC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,OAAO;SACpD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAEpC,OAAO;YACL,GAAG,EAAE;gBACH,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;gBACvC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,qBAAqB;aAC7C;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ,CAAC,QAAQ;gBACvB,IAAI,EAAE,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,QAAQ;gBAC5C,KAAK,EAAE,QAAQ,CAAC,SAAS;gBACzB,UAAU,EAAE,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG;aAC3D;YACD,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK;gBAChC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU;gBAC1C,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;gBAClC,mBAAmB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;aAC9E;YACD,QAAQ,EAAE;gBACR,WAAW,EAAE,CAAC,EAAE,2CAA2C;gBAC3D,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;gBAChC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;aACzE;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,YAAoB,EAAE,OAAgB;QAClD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAErD,qCAAqC;QACrC,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YACpD,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,YAAoB;QAC9B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEnD,kCAAkC;QAClC,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YAClD,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,wBAAwB;QACxB,IAAI,CAAC,aAAa,CAAC;YACjB,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,KAAK,IAAI,EAAE;gBAChB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,MAAM,oCAAe,CAAC,eAAe,EAAE,CAAC;oBACvD,OAAO;wBACL,OAAO,EAAE,MAAM,CAAC,SAAS;wBACzB,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,4BAA4B;wBAChF,OAAO,EAAE,MAAM;wBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;wBACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;qBACrC,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,0BAA0B,KAAK,EAAE,OAAO,IAAI,eAAe,EAAE;wBACtE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;wBACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;qBACrC,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,QAAQ,EAAE,KAAK,EAAE,aAAa;YAC9B,OAAO,EAAE,IAAI,EAAI,YAAY;YAC7B,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,aAAa,CAAC;YACjB,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,KAAK,IAAI,EAAE;gBAChB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACvC,MAAM,kBAAkB,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;gBAE1E,MAAM,OAAO,GAAG,kBAAkB,GAAG,EAAE,CAAC,CAAC,8BAA8B;gBAEvE,OAAO;oBACL,OAAO;oBACP,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,4BAA4B;oBAC1E,OAAO,EAAE;wBACP,QAAQ,EAAE,QAAQ,CAAC,QAAQ;wBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;wBAC7B,UAAU,EAAE,kBAAkB;qBAC/B;oBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACrC,CAAC;YACJ,CAAC;YACD,QAAQ,EAAE,KAAK,EAAE,WAAW;YAC5B,OAAO,EAAE,IAAI,EAAI,WAAW;YAC5B,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,CAAC,aAAa,CAAC;YACjB,IAAI,EAAE,kBAAkB;YACxB,KAAK,EAAE,KAAK,IAAI,EAAE;gBAChB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,iBAAiB,GAAG,yCAAqB,CAAC,oBAAoB,EAAE,CAAC;gBACvE,MAAM,OAAO,GAAG,iBAAiB,CAAC,MAAM,KAAK,CAAC,CAAC;gBAE/C,OAAO;oBACL,OAAO;oBACP,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,4BAA4B;oBAC/G,OAAO,EAAE;wBACP,iBAAiB;wBACjB,WAAW,EAAE,yCAAqB,CAAC,eAAe,EAAE;qBACrD;oBACD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACrC,CAAC;YACJ,CAAC;YACD,QAAQ,EAAE,KAAK,EAAE,aAAa;YAC9B,OAAO,EAAE,IAAI,EAAI,WAAW;YAC5B,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;QAEH,0CAA0C;QAC1C,IAAI,CAAC,aAAa,CAAC;YACjB,IAAI,EAAE,YAAY;YAClB,KAAK,EAAE,KAAK,IAAI,EAAE;gBAChB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,IAAI,CAAC;oBACH,qDAAqD;oBACrD,uDAAuD;oBAEvD,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,wBAAwB;wBACjC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;wBAC5B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;wBACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;qBACrC,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,4BAA4B,KAAK,EAAE,OAAO,IAAI,eAAe,EAAE;wBACxE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;wBACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;qBACrC,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,QAAQ,EAAE,MAAM,EAAE,YAAY;YAC9B,OAAO,EAAE,IAAI,EAAK,YAAY;YAC9B,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,KAAkB;QAC3C,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YACtC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;oBAChC,KAAK,CAAC,KAAK,EAAE;oBACb,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;iBAC5B,CAAC,CAAC;gBAEH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAErC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACtC,kBAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;gBACtE,CAAC;YACH,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,MAAM,YAAY,GAAiB;oBACjC,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,kCAAkC,KAAK,EAAE,OAAO,IAAI,eAAe,EAAE;oBAC9E,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,YAAY,EAAE,KAAK,CAAC,OAAO;iBAC5B,CAAC;gBAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;gBAC3C,kBAAM,CAAC,KAAK,CAAC,wBAAwB,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEnB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,6BAA6B;QAC7B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;YACzC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,OAAO,CAAC,EAAU;QACxB,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;YAC/B,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,QAAyB;QACjD,mCAAmC;QACnC,OAAO,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,wBAAwB;IAC9E,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAiB;QACxC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACnC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;CACF;AA/UD,sCA+UC;AAED,qBAAqB;AACR,QAAA,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC"}