/**
 * Dynamic Port Manager
 * Automatically finds available ports and handles conflicts
 */
export declare class PortManager {
    private static instance;
    private usedPorts;
    private preferredPorts;
    private portRange;
    private constructor();
    static getInstance(): PortManager;
    /**
     * Find an available port starting from preferred ports
     */
    findAvailablePort(preferredPort?: number): Promise<number>;
    /**
     * Check if a port is available
     */
    private isPortAvailable;
    /**
     * Release a port when no longer needed
     */
    releasePort(port: number): void;
    /**
     * Get all currently used ports
     */
    getUsedPorts(): number[];
    /**
     * Check if port is in use by this manager
     */
    isPortInUse(port: number): boolean;
    /**
     * Find multiple available ports
     */
    findMultiplePorts(count: number): Promise<number[]>;
    /**
     * Set custom port range
     */
    setPortRange(min: number, max: number): void;
    /**
     * Add preferred ports
     */
    addPreferredPorts(ports: number[]): void;
    /**
     * Get port allocation summary
     */
    getPortSummary(): {
        usedPorts: number[];
        preferredPorts: number[];
        portRange: {
            min: number;
            max: number;
        };
        availableInRange: number;
    };
}
/**
 * Singleton instance
 */
export declare const portManager: PortManager;
