/**
 * PRODUCTION-READY Retry Manager with Exponential Backoff
 * Handles transient failures with intelligent retry strategies
 */
export interface RetryOptions {
    maxAttempts: number;
    baseDelay: number;
    maxDelay: number;
    backoffFactor: number;
    jitter: boolean;
    retryableErrors?: string[];
    onRetry?: (attempt: number, error: any) => void;
}
export interface RetryResult<T> {
    success: boolean;
    result?: T;
    error?: any;
    attempts: number;
    totalTime: number;
}
export declare class RetryManager {
    protected readonly defaultOptions: RetryOptions;
    /**
     * Execute function with retry logic
     */
    execute<T>(fn: () => Promise<T>, options?: Partial<RetryOptions>): Promise<T>;
    /**
     * Execute with detailed result information
     */
    executeWithResult<T>(fn: () => Promise<T>, options?: Partial<RetryOptions>): Promise<RetryResult<T>>;
    /**
     * Check if error should trigger a retry
     */
    private isRetryableError;
    /**
     * Calculate delay with exponential backoff and jitter
     */
    private calculateDelay;
    /**
     * Sleep for specified milliseconds
     */
    private sleep;
    /**
     * Create a retryable version of a function
     */
    wrap<T extends (...args: any[]) => Promise<any>>(fn: T, options?: Partial<RetryOptions>): T;
}
/**
 * Specialized retry managers for different scenarios
 */
export declare class DatabaseRetryManager extends RetryManager {
    constructor();
}
export declare class APIRetryManager extends RetryManager {
    constructor();
}
export declare class WebSocketRetryManager extends RetryManager {
    constructor();
}
export declare const retryManager: RetryManager;
export declare const databaseRetryManager: DatabaseRetryManager;
export declare const apiRetryManager: APIRetryManager;
export declare const webSocketRetryManager: WebSocketRetryManager;
