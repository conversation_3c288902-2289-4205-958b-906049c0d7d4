"use strict";
/**
 * PRODUCTION-READY Retry Manager with Exponential Backoff
 * Handles transient failures with intelligent retry strategies
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.webSocketRetryManager = exports.apiRetryManager = exports.databaseRetryManager = exports.retryManager = exports.WebSocketRetryManager = exports.APIRetryManager = exports.DatabaseRetryManager = exports.RetryManager = void 0;
class RetryManager {
    defaultOptions = {
        maxAttempts: 3,
        baseDelay: 1000, // 1 second
        maxDelay: 30000, // 30 seconds
        backoffFactor: 2,
        jitter: true,
        retryableErrors: [
            'ECONNRESET',
            'ENOTFOUND',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'PROTOCOL_CONNECTION_LOST',
            'ER_LOCK_WAIT_TIMEOUT',
            'ER_LOCK_DEADLOCK'
        ]
    };
    /**
     * Execute function with retry logic
     */
    async execute(fn, options = {}) {
        const opts = { ...this.defaultOptions, ...options };
        const startTime = Date.now();
        let lastError;
        for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
            try {
                const result = await fn();
                return result;
            }
            catch (error) {
                lastError = error;
                // Check if error is retryable
                if (!this.isRetryableError(error, opts.retryableErrors)) {
                    throw error;
                }
                // Don't retry on last attempt
                if (attempt === opts.maxAttempts) {
                    break;
                }
                // Calculate delay with exponential backoff and jitter
                const delay = this.calculateDelay(attempt, opts);
                // Call retry callback if provided
                if (opts.onRetry) {
                    opts.onRetry(attempt, error);
                }
                // Wait before retrying
                await this.sleep(delay);
            }
        }
        // All attempts failed
        throw lastError;
    }
    /**
     * Execute with detailed result information
     */
    async executeWithResult(fn, options = {}) {
        const opts = { ...this.defaultOptions, ...options };
        const startTime = Date.now();
        let lastError;
        for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
            try {
                const result = await fn();
                return {
                    success: true,
                    result,
                    attempts: attempt,
                    totalTime: Date.now() - startTime
                };
            }
            catch (error) {
                lastError = error;
                if (!this.isRetryableError(error, opts.retryableErrors) || attempt === opts.maxAttempts) {
                    break;
                }
                const delay = this.calculateDelay(attempt, opts);
                if (opts.onRetry) {
                    opts.onRetry(attempt, error);
                }
                await this.sleep(delay);
            }
        }
        return {
            success: false,
            error: lastError,
            attempts: opts.maxAttempts,
            totalTime: Date.now() - startTime
        };
    }
    /**
     * Check if error should trigger a retry
     */
    isRetryableError(error, retryableErrors) {
        if (!retryableErrors || retryableErrors.length === 0) {
            return true; // Retry all errors if no specific errors defined
        }
        return retryableErrors.some(retryableError => error.message?.includes(retryableError) ||
            error.code === retryableError ||
            error.name === retryableError ||
            error.errno === retryableError);
    }
    /**
     * Calculate delay with exponential backoff and jitter
     */
    calculateDelay(attempt, options) {
        // Exponential backoff: baseDelay * (backoffFactor ^ (attempt - 1))
        let delay = options.baseDelay * Math.pow(options.backoffFactor, attempt - 1);
        // Cap at maximum delay
        delay = Math.min(delay, options.maxDelay);
        // Add jitter to prevent thundering herd problem
        if (options.jitter) {
            // Add random jitter of ±25%
            const jitterRange = delay * 0.25;
            const jitter = (Math.random() - 0.5) * 2 * jitterRange;
            delay += jitter;
        }
        return Math.max(0, Math.floor(delay));
    }
    /**
     * Sleep for specified milliseconds
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Create a retryable version of a function
     */
    wrap(fn, options = {}) {
        const retryManager = this;
        return (async (...args) => {
            return retryManager.execute(() => fn(...args), options);
        });
    }
}
exports.RetryManager = RetryManager;
/**
 * Specialized retry managers for different scenarios
 */
class DatabaseRetryManager extends RetryManager {
    constructor() {
        super();
        // Override default options for database operations
        Object.assign(this.defaultOptions, {
            maxAttempts: 5,
            baseDelay: 500,
            maxDelay: 10000,
            retryableErrors: [
                'PROTOCOL_CONNECTION_LOST',
                'ECONNRESET',
                'ENOTFOUND',
                'ECONNREFUSED',
                'ETIMEDOUT',
                'ER_LOCK_WAIT_TIMEOUT',
                'ER_LOCK_DEADLOCK',
                'ER_QUERY_INTERRUPTED'
            ]
        });
    }
}
exports.DatabaseRetryManager = DatabaseRetryManager;
class APIRetryManager extends RetryManager {
    constructor() {
        super();
        // Override default options for API calls
        Object.assign(this.defaultOptions, {
            maxAttempts: 3,
            baseDelay: 1000,
            maxDelay: 15000,
            retryableErrors: [
                'ECONNRESET',
                'ENOTFOUND',
                'ECONNREFUSED',
                'ETIMEDOUT',
                'NETWORK_ERROR',
                'TIMEOUT'
            ]
        });
    }
}
exports.APIRetryManager = APIRetryManager;
class WebSocketRetryManager extends RetryManager {
    constructor() {
        super();
        // Override default options for WebSocket connections
        Object.assign(this.defaultOptions, {
            maxAttempts: 10,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffFactor: 1.5, // Slower backoff for WebSocket
            retryableErrors: [
                'ECONNRESET',
                'ENOTFOUND',
                'ECONNREFUSED',
                'ETIMEDOUT',
                'WS_CONNECTION_FAILED',
                'WS_DISCONNECTED'
            ]
        });
    }
}
exports.WebSocketRetryManager = WebSocketRetryManager;
// Singleton instances
exports.retryManager = new RetryManager();
exports.databaseRetryManager = new DatabaseRetryManager();
exports.apiRetryManager = new APIRetryManager();
exports.webSocketRetryManager = new WebSocketRetryManager();
//# sourceMappingURL=RetryManager.js.map