{"version": 3, "file": "RetryManager.js", "sourceRoot": "", "sources": ["../../src/utils/RetryManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAoBH,MAAa,YAAY;IACJ,cAAc,GAAiB;QAChD,WAAW,EAAE,CAAC;QACd,SAAS,EAAE,IAAI,EAAO,WAAW;QACjC,QAAQ,EAAE,KAAK,EAAO,aAAa;QACnC,aAAa,EAAE,CAAC;QAChB,MAAM,EAAE,IAAI;QACZ,eAAe,EAAE;YACf,YAAY;YACZ,WAAW;YACX,cAAc;YACd,WAAW;YACX,0BAA0B;YAC1B,sBAAsB;YACtB,kBAAkB;SACnB;KACF,CAAC;IAEF;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,EAAoB,EACpB,UAAiC,EAAE;QAEnC,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,SAAc,CAAC;QAEnB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC;YAC7D,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,EAAE,EAAE,CAAC;gBAC1B,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,CAAC;gBAElB,8BAA8B;gBAC9B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;oBACxD,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,8BAA8B;gBAC9B,IAAI,OAAO,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjC,MAAM;gBACR,CAAC;gBAED,sDAAsD;gBACtD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAEjD,kCAAkC;gBAClC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACjB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC/B,CAAC;gBAED,uBAAuB;gBACvB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,sBAAsB;QACtB,MAAM,SAAS,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACrB,EAAoB,EACpB,UAAiC,EAAE;QAEnC,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,SAAc,CAAC;QAEnB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,EAAE,CAAC;YAC7D,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,EAAE,EAAE,CAAC;gBAC1B,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM;oBACN,QAAQ,EAAE,OAAO;oBACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBAClC,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,CAAC;gBAElB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,OAAO,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;oBACxF,MAAM;gBACR,CAAC;gBAED,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAEjD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACjB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC/B,CAAC;gBAED,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,IAAI,CAAC,WAAW;YAC1B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;SAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAU,EAAE,eAA0B;QAC7D,IAAI,CAAC,eAAe,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC,CAAC,iDAAiD;QAChE,CAAC;QAED,OAAO,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAC3C,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,cAAc,CAAC;YACvC,KAAK,CAAC,IAAI,KAAK,cAAc;YAC7B,KAAK,CAAC,IAAI,KAAK,cAAc;YAC7B,KAAK,CAAC,KAAK,KAAK,cAAc,CAC/B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,OAAe,EAAE,OAAqB;QAC3D,mEAAmE;QACnE,IAAI,KAAK,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAE7E,uBAAuB;QACvB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE1C,gDAAgD;QAChD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,4BAA4B;YAC5B,MAAM,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC;YACjC,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;YACvD,KAAK,IAAI,MAAM,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,IAAI,CACF,EAAK,EACL,UAAiC,EAAE;QAEnC,MAAM,YAAY,GAAG,IAAI,CAAC;QAE1B,OAAO,CAAC,KAAK,EAAE,GAAG,IAAW,EAAE,EAAE;YAC/B,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC,CAAM,CAAC;IACV,CAAC;CACF;AArKD,oCAqKC;AAED;;GAEG;AACH,MAAa,oBAAqB,SAAQ,YAAY;IACpD;QACE,KAAK,EAAE,CAAC;QACR,mDAAmD;QACnD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;YACjC,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,GAAG;YACd,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE;gBACf,0BAA0B;gBAC1B,YAAY;gBACZ,WAAW;gBACX,cAAc;gBACd,WAAW;gBACX,sBAAsB;gBACtB,kBAAkB;gBAClB,sBAAsB;aACvB;SACF,CAAC,CAAC;IACL,CAAC;CACF;AApBD,oDAoBC;AAED,MAAa,eAAgB,SAAQ,YAAY;IAC/C;QACE,KAAK,EAAE,CAAC;QACR,yCAAyC;QACzC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;YACjC,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE;gBACf,YAAY;gBACZ,WAAW;gBACX,cAAc;gBACd,WAAW;gBACX,eAAe;gBACf,SAAS;aACV;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAlBD,0CAkBC;AAED,MAAa,qBAAsB,SAAQ,YAAY;IACrD;QACE,KAAK,EAAE,CAAC;QACR,qDAAqD;QACrD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;YACjC,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,GAAG,EAAE,+BAA+B;YACnD,eAAe,EAAE;gBACf,YAAY;gBACZ,WAAW;gBACX,cAAc;gBACd,WAAW;gBACX,sBAAsB;gBACtB,iBAAiB;aAClB;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAnBD,sDAmBC;AAED,sBAAsB;AACT,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAClC,QAAA,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AAClD,QAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AACxC,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC"}