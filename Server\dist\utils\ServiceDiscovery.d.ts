export interface ServiceInfo {
    serviceName: string;
    host: string;
    port: number;
    version: string;
    timestamp: number;
    capabilities: string[];
}
/**
 * Service Discovery System
 * Handles automatic server announcement and client discovery
 */
export declare class ServiceDiscovery {
    private static instance;
    private broadcastSocket;
    private discoverySocket;
    private serviceInfo;
    private broadcastInterval;
    private isRunning;
    private readonly BROADCAST_PORT;
    private readonly DISCOVERY_PORT;
    private readonly BROADCAST_INTERVAL;
    private readonly SERVICE_NAME;
    private constructor();
    static getInstance(): ServiceDiscovery;
    /**
     * Start service announcement
     */
    startAnnouncement(port: number): Promise<void>;
    /**
     * Stop service announcement
     */
    stopAnnouncement(): Promise<void>;
    /**
     * Send discovery response to specific client
     */
    private sendDiscoveryResponse;
    /**
     * Start periodic service announcements
     */
    private startPeriodicAnnouncements;
    /**
     * Broadcast service information
     */
    private broadcastServiceInfo;
    /**
     * Get local IP addresses
     */
    private getLocalIPAddresses;
    /**
     * Get broadcast addresses for all network interfaces
     */
    private getBroadcastAddresses;
    /**
     * Calculate broadcast address from IP and netmask
     */
    private calculateBroadcastAddress;
    /**
     * Get current service information
     */
    getServiceInfo(): ServiceInfo | null;
    /**
     * Check if service discovery is running
     */
    isServiceRunning(): boolean;
    /**
     * Update service port (when port changes dynamically)
     */
    updateServicePort(newPort: number): void;
}
/**
 * Singleton instance
 */
export declare const serviceDiscovery: ServiceDiscovery;
