"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceDiscovery = exports.ServiceDiscovery = void 0;
const dgram_1 = __importDefault(require("dgram"));
const os_1 = __importDefault(require("os"));
const logger_js_1 = require("./logger.js");
/**
 * Service Discovery System
 * Handles automatic server announcement and client discovery
 */
class ServiceDiscovery {
    static instance;
    broadcastSocket = null;
    discoverySocket = null;
    serviceInfo = null;
    broadcastInterval = null;
    isRunning = false;
    // Configuration
    BROADCAST_PORT = 45454;
    DISCOVERY_PORT = 45455;
    BROADCAST_INTERVAL = 5000; // 5 seconds
    SERVICE_NAME = 'VMS-Server';
    constructor() { }
    static getInstance() {
        if (!ServiceDiscovery.instance) {
            ServiceDiscovery.instance = new ServiceDiscovery();
        }
        return ServiceDiscovery.instance;
    }
    /**
     * Start service announcement
     */
    async startAnnouncement(port) {
        if (this.isRunning) {
            logger_js_1.logger.warn('🔊 Service discovery already running');
            return;
        }
        try {
            // Get local IP addresses
            const localIPs = this.getLocalIPAddresses();
            this.serviceInfo = {
                serviceName: this.SERVICE_NAME,
                host: localIPs[0] || 'localhost',
                port: port,
                version: '3.0.0',
                timestamp: Date.now(),
                capabilities: ['voucher-management', 'real-time-updates', 'multi-user']
            };
            // Create broadcast socket
            this.broadcastSocket = dgram_1.default.createSocket('udp4');
            this.broadcastSocket.bind(() => {
                this.broadcastSocket.setBroadcast(true);
                logger_js_1.logger.info(`📡 Service discovery broadcast started on port ${this.BROADCAST_PORT}`);
            });
            // Create discovery response socket
            this.discoverySocket = dgram_1.default.createSocket('udp4');
            this.discoverySocket.bind(this.DISCOVERY_PORT, () => {
                logger_js_1.logger.info(`🔍 Service discovery listener started on port ${this.DISCOVERY_PORT}`);
            });
            // Handle discovery requests
            this.discoverySocket.on('message', (msg, rinfo) => {
                try {
                    const request = JSON.parse(msg.toString());
                    if (request.type === 'VMS_DISCOVERY_REQUEST') {
                        this.sendDiscoveryResponse(rinfo.address, rinfo.port);
                    }
                }
                catch (error) {
                    // Ignore invalid messages
                }
            });
            // Start periodic announcements
            this.startPeriodicAnnouncements();
            this.isRunning = true;
            logger_js_1.logger.info(`🎯 VMS Service Discovery started - Broadcasting on all network interfaces`);
            logger_js_1.logger.info(`📊 Service Info: ${JSON.stringify(this.serviceInfo, null, 2)}`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to start service discovery:', error);
            throw error;
        }
    }
    /**
     * Stop service announcement
     */
    async stopAnnouncement() {
        if (!this.isRunning)
            return;
        try {
            // Stop periodic announcements
            if (this.broadcastInterval) {
                clearInterval(this.broadcastInterval);
                this.broadcastInterval = null;
            }
            // Close sockets
            if (this.broadcastSocket) {
                this.broadcastSocket.close();
                this.broadcastSocket = null;
            }
            if (this.discoverySocket) {
                this.discoverySocket.close();
                this.discoverySocket = null;
            }
            this.isRunning = false;
            logger_js_1.logger.info('🔇 Service discovery stopped');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error stopping service discovery:', error);
        }
    }
    /**
     * Send discovery response to specific client
     */
    sendDiscoveryResponse(clientIP, clientPort) {
        if (!this.serviceInfo || !this.broadcastSocket)
            return;
        const response = {
            type: 'VMS_DISCOVERY_RESPONSE',
            service: this.serviceInfo,
            localIPs: this.getLocalIPAddresses()
        };
        const message = Buffer.from(JSON.stringify(response));
        this.broadcastSocket.send(message, clientPort, clientIP, (error) => {
            if (error) {
                logger_js_1.logger.error(`❌ Failed to send discovery response to ${clientIP}:${clientPort}:`, error);
            }
            else {
                logger_js_1.logger.info(`📤 Sent discovery response to ${clientIP}:${clientPort}`);
            }
        });
    }
    /**
     * Start periodic service announcements
     */
    startPeriodicAnnouncements() {
        this.broadcastInterval = setInterval(() => {
            this.broadcastServiceInfo();
        }, this.BROADCAST_INTERVAL);
        // Send initial announcement
        this.broadcastServiceInfo();
    }
    /**
     * Broadcast service information
     */
    broadcastServiceInfo() {
        if (!this.serviceInfo || !this.broadcastSocket)
            return;
        const announcement = {
            type: 'VMS_SERVICE_ANNOUNCEMENT',
            service: {
                ...this.serviceInfo,
                timestamp: Date.now() // Update timestamp
            },
            localIPs: this.getLocalIPAddresses()
        };
        const message = Buffer.from(JSON.stringify(announcement));
        // Broadcast to all network interfaces
        const broadcastAddresses = this.getBroadcastAddresses();
        broadcastAddresses.forEach(address => {
            this.broadcastSocket.send(message, this.BROADCAST_PORT, address, (error) => {
                if (error) {
                    logger_js_1.logger.debug(`❌ Broadcast failed to ${address}:`, error.message);
                }
                else {
                    logger_js_1.logger.debug(`📡 Broadcasted to ${address}:${this.BROADCAST_PORT}`);
                }
            });
        });
    }
    /**
     * Get local IP addresses
     */
    getLocalIPAddresses() {
        const interfaces = os_1.default.networkInterfaces();
        const addresses = [];
        for (const name of Object.keys(interfaces)) {
            const nets = interfaces[name];
            if (!nets)
                continue;
            for (const net of nets) {
                // Skip internal and non-IPv4 addresses
                if (net.family === 'IPv4' && !net.internal) {
                    addresses.push(net.address);
                }
            }
        }
        return addresses;
    }
    /**
     * Get broadcast addresses for all network interfaces
     */
    getBroadcastAddresses() {
        const interfaces = os_1.default.networkInterfaces();
        const broadcasts = ['***************']; // Global broadcast
        for (const name of Object.keys(interfaces)) {
            const nets = interfaces[name];
            if (!nets)
                continue;
            for (const net of nets) {
                if (net.family === 'IPv4' && !net.internal && net.netmask) {
                    // Calculate broadcast address
                    const broadcast = this.calculateBroadcastAddress(net.address, net.netmask);
                    if (broadcast && !broadcasts.includes(broadcast)) {
                        broadcasts.push(broadcast);
                    }
                }
            }
        }
        return broadcasts;
    }
    /**
     * Calculate broadcast address from IP and netmask
     */
    calculateBroadcastAddress(ip, netmask) {
        try {
            const ipParts = ip.split('.').map(Number);
            const maskParts = netmask.split('.').map(Number);
            const broadcast = ipParts.map((part, index) => {
                return part | (255 - maskParts[index]);
            });
            return broadcast.join('.');
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Get current service information
     */
    getServiceInfo() {
        return this.serviceInfo;
    }
    /**
     * Check if service discovery is running
     */
    isServiceRunning() {
        return this.isRunning;
    }
    /**
     * Update service port (when port changes dynamically)
     */
    updateServicePort(newPort) {
        if (this.serviceInfo) {
            this.serviceInfo.port = newPort;
            this.serviceInfo.timestamp = Date.now();
            logger_js_1.logger.info(`🔄 Service port updated to: ${newPort}`);
        }
    }
}
exports.ServiceDiscovery = ServiceDiscovery;
/**
 * Singleton instance
 */
exports.serviceDiscovery = ServiceDiscovery.getInstance();
//# sourceMappingURL=ServiceDiscovery.js.map