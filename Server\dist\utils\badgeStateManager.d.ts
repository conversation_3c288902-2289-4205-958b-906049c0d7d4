/**
 * Badge State Manager
 * Centralized backend logic for managing voucher badge states
 * Ensures consistent badge persistence across all workflow transitions
 */
export interface VoucherBadgeState {
    is_resubmitted: boolean;
    resubmission_certified_visible_to_finance: boolean;
    resubmission_tracking_visible_to_audit: boolean;
    last_resubmission_date: Date | null;
    resubmission_count: number;
    badge_persistence_flags: any;
}
export declare class BadgeStateManager {
    /**
     * Determines if a voucher should be marked as a resubmission
     */
    static isResubmissionVoucher(voucher: any): boolean;
    /**
     * Sets the resubmission flag and related fields for a voucher
     * REQUIREMENT 3: Enhanced to preserve original rejection reasons
     */
    static setResubmissionState(connection: any, voucherId: string, isResubmission: boolean, options?: {
        incrementCount?: boolean;
        setCertifiedVisibility?: boolean;
        setAuditVisibility?: boolean;
        preserveRejectionReason?: boolean;
    }): Promise<void>;
    /**
     * REQUIREMENT 3: Preserves original rejection reason for resubmissions
     * ENHANCED: Now finds rejection reason from copy vouchers if main voucher comment is null
     */
    static preserveOriginalRejectionReason(connection: any, voucherId: string): Promise<void>;
    /**
     * Ensures badge persistence for certified returns
     * These should appear in both Finance CERTIFIED and Audit DISPATCHED tabs
     */
    static ensureCertifiedResubmissionVisibility(connection: any, voucherId: string, isCertified: boolean): Promise<void>;
    /**
     * Gets the current badge state for a voucher
     */
    static getBadgeState(connection: any, voucherId: string): Promise<VoucherBadgeState | null>;
    /**
     * Clears all resubmission flags (used when voucher is reset)
     */
    static clearResubmissionState(connection: any, voucherId: string): Promise<void>;
}
