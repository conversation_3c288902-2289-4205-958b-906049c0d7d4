{"version": 3, "file": "badgeStateManager.js", "sourceRoot": "", "sources": ["../../src/utils/badgeStateManager.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,4CAAyC;AAYzC,MAAa,iBAAiB;IAC5B;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,OAAY;QACvC,qBAAqB;QACrB,MAAM,mBAAmB,GAAG,OAAO,CAAC,cAAc,KAAK,CAAC,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,CAAC;QAC5F,MAAM,qBAAqB,GAAG,OAAO,CAAC,MAAM,KAAK,eAAe,CAAC;QACjE,MAAM,uBAAuB,GAAG,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC,cAAc,CAAC;YACjD,OAAO,CAAC,cAAc,KAAK,uBAAuB,CAAC;QAClF,MAAM,mBAAmB,GAAG,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;QAErF,OAAO,mBAAmB,IAAI,qBAAqB,IAAI,uBAAuB,IAAI,mBAAmB,CAAC;IACxG,CAAC;IAID;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAC/B,UAAe,EACf,SAAiB,EACjB,cAAuB,EACvB,UAKI,EAAE;QAEN,MAAM,EACJ,cAAc,GAAG,KAAK,EACtB,sBAAsB,GAAG,KAAK,EAC9B,kBAAkB,GAAG,KAAK,EAC1B,uBAAuB,GAAG,IAAI,EAC/B,GAAG,OAAO,CAAC;QAEZ,IAAI,CAAC;YACH,6FAA6F;YAC7F,IAAI,cAAc,IAAI,uBAAuB,EAAE,CAAC;gBAC9C,MAAM,IAAI,CAAC,+BAA+B,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,WAAW,GAAG;;;mCAGW,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;OAC7D,CAAC;YACF,IAAI,YAAY,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5C,4CAA4C;YAC5C,IAAI,cAAc,IAAI,cAAc,EAAE,CAAC;gBACrC,WAAW,IAAI,4DAA4D,CAAC;YAC9E,CAAC;YAED,oCAAoC;YACpC,IAAI,sBAAsB,EAAE,CAAC;gBAC3B,WAAW,IAAI,iDAAiD,CAAC;gBACjE,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC;YAED,IAAI,kBAAkB,EAAE,CAAC;gBACvB,WAAW,IAAI,8CAA8C,CAAC;gBAC9D,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC;YAED,WAAW,IAAI,eAAe,CAAC;YAC/B,YAAY,CAAC,IAAI,CAAC,SAAgB,CAAC,CAAC;YAEpC,MAAM,UAAU,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAElD,eAAM,CAAC,IAAI,CAAC,sCAAsC,cAAc,gBAAgB,SAAS,EAAE,CAAC,CAAC;QAC/F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kDAAkD,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YACpF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,UAAe,EAAE,SAAiB;QAC7E,IAAI,CAAC;YACH,wDAAwD;YACxD,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;OAKzC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAEhB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,YAAY,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE5B,gFAAgF;YAChF,IAAI,CAAC,OAAO,CAAC,yBAAyB,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBAC9D,IAAI,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC;gBAEtC,gFAAgF;gBAChF,IAAI,CAAC,eAAe,IAAI,eAAe,KAAK,MAAM,IAAI,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;oBACpF,eAAM,CAAC,IAAI,CAAC,gCAAgC,SAAS,sDAAsD,CAAC,CAAC;oBAE7G,MAAM,CAAC,YAAY,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;WAM7C,EAAE,CAAC,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;oBAE/B,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5B,eAAe,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;wBAC1C,eAAM,CAAC,IAAI,CAAC,2DAA2D,eAAe,GAAG,CAAC,CAAC;oBAC7F,CAAC;gBACH,CAAC;gBAED,wDAAwD;gBACxD,6FAA6F;gBAC7F,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;SAWtB,EAAE;oBACD,eAAe,IAAI,kCAAkC;oBACrD,eAAe;oBACf,SAAS;iBACV,CAAC,CAAC;gBAEH,eAAM,CAAC,IAAI,CAAC,qEAAqE,SAAS,MAAM,eAAe,IAAI,kCAAkC,GAAG,CAAC,CAAC;YAC5J,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4DAA4D,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAMD;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,qCAAqC,CAChD,UAAe,EACf,SAAiB,EACjB,WAAoB;QAEpB,IAAI,CAAC;YACH,kCAAkC;YAClC,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CACvC,0DAA0D,EAC1D,CAAC,SAAS,CAAC,CACZ,CAAC;YAEF,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,YAAY,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,KAAK,CAAC,CAAC;YAEpD,IAAI,cAAc,IAAI,WAAW,EAAE,CAAC;gBAClC,mDAAmD;gBACnD,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;;SActB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;gBAEhB,eAAM,CAAC,IAAI,CAAC,2EAA2E,SAAS,EAAE,CAAC,CAAC;YACtG,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kEAAkE,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YACpG,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAID;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,UAAe,EAAE,SAAiB;QAC3D,IAAI,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;OAWzC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAEhB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,OAAO;gBACL,cAAc,EAAE,OAAO,CAAC,cAAc,KAAK,CAAC;gBAC5C,yCAAyC,EAAE,OAAO,CAAC,yCAAyC,KAAK,CAAC;gBAClG,sCAAsC,EAAE,OAAO,CAAC,sCAAsC,KAAK,CAAC;gBAC5F,sBAAsB,EAAE,OAAO,CAAC,sBAAsB;gBACtD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,IAAI,CAAC;gBAEnD,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;aACzD,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,UAAe,EAAE,SAAiB;QACpE,IAAI,CAAC;YACH,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;;;OAQtB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAEhB,eAAM,CAAC,IAAI,CAAC,0DAA0D,SAAS,EAAE,CAAC,CAAC;QACrF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mDAAmD,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YACrF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CAGF;AAzQD,8CAyQC"}