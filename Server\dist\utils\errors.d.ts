export declare class DatabaseError extends Error {
    code: string;
    constructor(code: string, message: string);
}
export declare const ErrorCodes: {
    readonly VOUCHER_NOT_FOUND: "VOUCHER_NOT_FOUND";
    readonly INVALID_STATUS_TRANSITION: "INVALID_STATUS_TRANSITION";
    readonly UPDATE_FAILED: "UPDATE_FAILED";
    readonly TRANSACTION_FAILED: "TRANSACTION_FAILED";
    readonly VALIDATION_FAILED: "VALIDATION_FAILED";
    readonly DB_CONNECTION_ERROR: "DB_CONNECTION_ERROR";
    readonly USER_NOT_FOUND: "USER_NOT_FOUND";
    readonly UNKNOWN_ERROR: "UNKNOWN_ERROR";
    readonly INSERT_FAILED: "INSERT_FAILED";
    readonly DELETE_FAILED: "DELETE_FAILED";
};
export type ErrorCode = keyof typeof ErrorCodes;
