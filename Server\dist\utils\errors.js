"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorCodes = exports.DatabaseError = void 0;
class DatabaseError extends Error {
    code;
    constructor(code, message) {
        super(message);
        this.code = code;
        this.name = 'DatabaseError';
        // Maintains proper stack trace for where error was thrown
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, DatabaseError);
        }
    }
}
exports.DatabaseError = DatabaseError;
exports.ErrorCodes = {
    VOUCHER_NOT_FOUND: 'VOUCHER_NOT_FOUND',
    INVALID_STATUS_TRANSITION: 'INVALID_STATUS_TRANSITION',
    UPDATE_FAILED: 'UPDATE_FAILED',
    TRANSACTION_FAILED: 'TRANSACTION_FAILED',
    VALIDATION_FAILED: 'VALIDATION_FAILED',
    DB_CONNECTION_ERROR: 'DB_CONNECTION_ERROR',
    USER_NOT_FOUND: 'USER_NOT_FOUND',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
    INSERT_FAILED: 'INSERT_FAILED',
    DELETE_FAILED: 'DELETE_FAILED'
};
//# sourceMappingURL=errors.js.map