export declare const STORAGE_CONFIG: {
    baseDir: string;
    auditAttachmentsDir: string;
    allowedMimeTypes: string[];
    allowedExtensions: string[];
    maxFileSize: number;
    maxFilenameLength: number;
};
/**
 * Initialize storage directories
 */
export declare function initializeStorage(): void;
/**
 * Get storage path for a voucher
 */
export declare function getVoucherStoragePath(voucherId: string): string;
/**
 * Sanitize filename for safe storage
 */
export declare function sanitizeFilename(filename: string): string;
/**
 * Generate stored filename from voucher data
 */
export declare function generateStoredFilename(claimant: string, description: string, originalExtension: string): string;
/**
 * Validate file type and size
 */
export declare function validateFile(file: any): {
    isValid: boolean;
    error?: string;
};
/**
 * Ensure directory exists
 */
export declare function ensureDirectoryExists(dirPath: string): void;
