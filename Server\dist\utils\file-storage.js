"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.STORAGE_CONFIG = void 0;
exports.initializeStorage = initializeStorage;
exports.getVoucherStoragePath = getVoucherStoragePath;
exports.sanitizeFilename = sanitizeFilename;
exports.generateStoredFilename = generateStoredFilename;
exports.validateFile = validateFile;
exports.ensureDirectoryExists = ensureDirectoryExists;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const logger_js_1 = require("./logger.js");
// File storage configuration
exports.STORAGE_CONFIG = {
    baseDir: path_1.default.join(process.cwd(), '..', 'uploads'),
    auditAttachmentsDir: 'audit-attachments',
    allowedMimeTypes: ['application/pdf', 'image/jpeg'],
    allowedExtensions: ['.pdf', '.jpg', '.jpeg'],
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFilenameLength: 200
};
/**
 * Initialize storage directories
 */
function initializeStorage() {
    try {
        const currentYear = new Date().getFullYear().toString();
        const auditDir = path_1.default.join(exports.STORAGE_CONFIG.baseDir, exports.STORAGE_CONFIG.auditAttachmentsDir, currentYear);
        // Create directories if they don't exist
        if (!fs_1.default.existsSync(exports.STORAGE_CONFIG.baseDir)) {
            fs_1.default.mkdirSync(exports.STORAGE_CONFIG.baseDir, { recursive: true });
            logger_js_1.logger.info('Created base uploads directory');
        }
        if (!fs_1.default.existsSync(auditDir)) {
            fs_1.default.mkdirSync(auditDir, { recursive: true });
            logger_js_1.logger.info(`Created audit attachments directory for ${currentYear}`);
        }
        logger_js_1.logger.info('File storage initialized successfully');
    }
    catch (error) {
        logger_js_1.logger.error('Failed to initialize file storage:', error);
        throw error;
    }
}
/**
 * Get storage path for a voucher
 */
function getVoucherStoragePath(voucherId) {
    const currentYear = new Date().getFullYear().toString();
    return path_1.default.join(exports.STORAGE_CONFIG.baseDir, exports.STORAGE_CONFIG.auditAttachmentsDir, currentYear, `voucher-${voucherId}`);
}
/**
 * Sanitize filename for safe storage
 */
function sanitizeFilename(filename) {
    // Remove or replace unsafe characters
    return filename
        .replace(/[<>:"/\\|?*]/g, '_') // Replace unsafe chars with underscore
        .replace(/\s+/g, '_') // Replace spaces with underscore
        .replace(/_{2,}/g, '_') // Replace multiple underscores with single
        .substring(0, exports.STORAGE_CONFIG.maxFilenameLength); // Limit length
}
/**
 * Generate stored filename from voucher data
 */
function generateStoredFilename(claimant, description, originalExtension) {
    const sanitizedClaimant = sanitizeFilename(claimant || 'Unknown_Claimant');
    const sanitizedDescription = sanitizeFilename(description || 'Document');
    return `${sanitizedClaimant}_${sanitizedDescription}${originalExtension}`;
}
/**
 * Validate file type and size
 */
function validateFile(file) {
    // Check file size
    if (file.size > exports.STORAGE_CONFIG.maxFileSize) {
        return {
            isValid: false,
            error: `File size exceeds maximum limit of ${exports.STORAGE_CONFIG.maxFileSize / (1024 * 1024)}MB`
        };
    }
    // Check MIME type
    if (!exports.STORAGE_CONFIG.allowedMimeTypes.includes(file.mimetype)) {
        return {
            isValid: false,
            error: 'Only PDF and JPG files are allowed'
        };
    }
    // Check file extension
    const extension = path_1.default.extname(file.originalname).toLowerCase();
    if (!exports.STORAGE_CONFIG.allowedExtensions.includes(extension)) {
        return {
            isValid: false,
            error: 'Only .pdf, .jpg, and .jpeg files are allowed'
        };
    }
    return { isValid: true };
}
/**
 * Ensure directory exists
 */
function ensureDirectoryExists(dirPath) {
    if (!fs_1.default.existsSync(dirPath)) {
        fs_1.default.mkdirSync(dirPath, { recursive: true });
    }
}
//# sourceMappingURL=file-storage.js.map