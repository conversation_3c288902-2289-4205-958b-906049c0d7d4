/**
 * Legacy Voucher Status Compatibility Layer
 * Temporary compatibility layer for legacy code during migration
 * TODO: Remove this file once all legacy code is migrated to workflow state machine
 */
import { WorkflowState } from '../workflow/VoucherWorkflowStateMachine';
export declare const VOUCHER_STATUSES: {
    PENDING: "PENDING SUBMISSION";
    PENDING_RECEIPT: "PENDING RECEIPT";
    VOUCHER_PROCESSING: "VOUCHER PROCESSING";
    AUDIT_PROCESSING: "AUDIT: PROCESSING";
    VOUCHER_CERTIFIED: "VOUCHER CERTIFIED";
    VOUCHER_REJECTED: "VOUCHER REJECTED";
    VOUCHER_RETURNED: "VOUCHER RETURNED";
    PENDING_DISPATCH: "PENDING DISPATCH";
    DISPATCHED: "DISPATCHED";
};
export declare function synchronizeVoucherFlags(options: {
    status: string;
}): {
    flags: {
        isNew: boolean;
        isPendingDispatch: boolean;
        isDispatched: boolean;
        isCertified: boolean;
        isRejected: boolean;
        isProcessing: boolean;
    };
};
export declare function isValidStatusTransition(fromStatus: string, toStatus: string, userRole?: string, voucher?: any): {
    isValid: boolean;
    reason?: string;
};
export declare function isValidStatusTransition_old(fromStatus: string, toStatus: string, userRole?: string, voucher?: any): boolean;
export declare function mapLegacyStatusToWorkflowState(legacyStatus: string): WorkflowState;
export declare function mapWorkflowStateToLegacyStatus(workflowState: WorkflowState): string;
export declare function updateVoucherWithBothSystems(db: any, voucherId: string, workflowState: WorkflowState, additionalFields?: Record<string, any>): Promise<void>;
export declare function warnLegacyUsage(functionName: string, location: string): void;
