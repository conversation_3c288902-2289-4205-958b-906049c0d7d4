/**
 * Hash a password (bcrypt-compatible API)
 * @param password The plain text password to hash
 * @param saltRounds Ignored, kept for API compatibility with bcrypt
 * @returns A Promise that resolves to the hashed password
 */
export declare function hash(password: string, saltRounds: number): Promise<string>;
/**
 * Compare a password with a hash (bcrypt-compatible API)
 * @param password The plain text password to check
 * @param hashedPassword The hashed password to compare against
 * @returns A Promise that resolves to true if the password matches, false otherwise
 */
export declare function compare(password: string, hashedPassword: string): Promise<boolean>;
declare const _default: {
    hash: typeof hash;
    compare: typeof compare;
    hashSync: (password: string, saltRounds: number) => string;
    compareSync: (password: string, hashedPassword: string) => boolean;
};
export default _default;
