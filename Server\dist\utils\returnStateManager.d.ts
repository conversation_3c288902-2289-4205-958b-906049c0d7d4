/**
 * Return State Manager
 * Centralized backend logic for managing return voucher states
 * Mirrors the BadgeStateManager pattern for consistency
 */
export declare class ReturnStateManager {
    /**
     * Determines if a voucher should be marked as a return voucher
     */
    static isReturnVoucher(voucher: any): boolean;
    /**
     * Sets the return flag and related fields for a voucher
     * Mirrors the resubmission state management pattern
     */
    static setReturnState(connection: any, voucherId: string, isReturn: boolean, options?: {
        incrementCount?: boolean;
        setCertifiedVisibility?: boolean;
        setAuditVisibility?: boolean;
        preserveReturnReason?: boolean;
    }): Promise<void>;
    /**
     * Preserves original return reason for return vouchers
     * ENHANCED: Handles resubmission scenarios and finds reasons from copy vouchers
     */
    static preserveOriginalReturnReason(connection: any, voucherId: string): Promise<void>;
    /**
     * Ensures return voucher visibility in Finance RETURNED tab
     * Mirrors the certified resubmission visibility pattern
     * ENHANCED: Now supports dual-tab visibility for certified returned vouchers
     */
    static ensureCertifiedReturnVisibility(connection: any, voucherId: string, visible: boolean): Promise<void>;
    /**
     * Clears all return flags (used when voucher is reset)
     */
    static clearReturnState(connection: any, voucherId: string): Promise<void>;
    /**
     * Ensures return voucher resubmission visibility in Finance CERTIFIED tab
     * Mirrors the certified resubmission visibility pattern for returned vouchers
     */
    static ensureCertifiedReturnResubmissionVisibility(connection: any, voucherId: string, visible: boolean): Promise<void>;
    /**
     * Gets return voucher metadata for display
     */
    static getReturnMetadata(connection: any, voucherId: string): Promise<any>;
    /**
     * PHASE 2 FIX: Complete returned voucher resubmission workflow
     * Handles the full cycle: returned → resubmitted → certified
     * Ensures proper badge persistence and dual-tab visibility
     */
    static completeCertifiedReturnWorkflow(connection: any, voucherId: string): Promise<void>;
}
