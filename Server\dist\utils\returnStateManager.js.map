{"version": 3, "file": "returnStateManager.js", "sourceRoot": "", "sources": ["../../src/utils/returnStateManager.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,4CAAyC;AAEzC,MAAa,kBAAkB;IAC7B;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,OAAY;QACjC,qBAAqB;QACrB,MAAM,aAAa,GAAG,OAAO,CAAC,mBAAmB,KAAK,CAAC,IAAI,OAAO,CAAC,mBAAmB,KAAK,IAAI,CAAC;QAChG,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,KAAK,kBAAkB,CAAC;QAC9D,MAAM,iBAAiB,GAAG,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC,UAAU,CAAC;YAC7C,OAAO,CAAC,cAAc,KAAK,kBAAkB;YAC7C,OAAO,CAAC,cAAc,KAAK,qBAAqB;YAChD,OAAO,CAAC,cAAc,KAAK,iCAAiC,CAAC;QACtF,MAAM,gBAAgB,GAAG,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC;QAElF,OAAO,aAAa,IAAI,eAAe,IAAI,iBAAiB,IAAI,gBAAgB,CAAC;IACnF,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CACzB,UAAe,EACf,SAAiB,EACjB,QAAiB,EACjB,UAKI,EAAE;QAEN,MAAM,EACJ,cAAc,GAAG,KAAK,EACtB,sBAAsB,GAAG,KAAK,EAC9B,kBAAkB,GAAG,KAAK,EAC1B,oBAAoB,GAAG,IAAI,EAC5B,GAAG,OAAO,CAAC;QAEZ,eAAM,CAAC,IAAI,CAAC,qDAAqD,SAAS,OAAO,QAAQ,EAAE,CAAC,CAAC;QAE7F,IAAI,CAAC;YACH,qEAAqE;YACrE,IAAI,QAAQ,IAAI,oBAAoB,EAAE,CAAC;gBACrC,MAAM,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,WAAW,GAAG;;;6BAGK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;OACjD,CAAC;YACF,IAAI,YAAY,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtC,sCAAsC;YACtC,IAAI,cAAc,IAAI,QAAQ,EAAE,CAAC;gBAC/B,WAAW,IAAI,gDAAgD,CAAC;YAClE,CAAC;YAED,oCAAoC;YACpC,IAAI,sBAAsB,EAAE,CAAC;gBAC3B,WAAW,IAAI,2CAA2C,CAAC;gBAC3D,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC;YAED,IAAI,kBAAkB,EAAE,CAAC;gBACvB,WAAW,IAAI,4BAA4B,CAAC;gBAC5C,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC;YAED,WAAW,IAAI,eAAe,CAAC;YAC/B,YAAY,CAAC,IAAI,CAAC,SAAgB,CAAC,CAAC;YAEpC,MAAM,UAAU,CAAC,KAAK,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;YAElD,eAAM,CAAC,IAAI,CAAC,6DAA6D,SAAS,8BAA8B,sBAAsB,EAAE,CAAC,CAAC;QAC5I,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,UAAe,EAAE,SAAiB;QAC1E,IAAI,CAAC;YACH,oDAAoD;YACpD,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;OAMzC,EAAE,CAAC,SAAgB,CAAC,CAAC,CAAC;YAEvB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,eAAM,CAAC,IAAI,CAAC,4BAA4B,SAAS,2CAA2C,CAAC,CAAC;gBAC9F,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE5B,+DAA+D;YAC/D,IAAI,OAAO,CAAC,sBAAsB,EAAE,CAAC;gBACnC,eAAM,CAAC,IAAI,CAAC,yEAAyE,SAAS,EAAE,CAAC,CAAC;gBAClG,OAAO;YACT,CAAC;YAED,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC;YAEnC,kFAAkF;YAClF,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBAChD,eAAM,CAAC,IAAI,CAAC,iCAAiC,SAAS,6CAA6C,CAAC,CAAC;gBAErG,MAAM,CAAC,YAAY,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;SAU7C,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;gBAEpC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;oBACvC,eAAM,CAAC,IAAI,CAAC,2DAA2D,YAAY,GAAG,CAAC,CAAC;gBAC1F,CAAC;YACH,CAAC;YAED,6CAA6C;YAC7C,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC/C,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;SAMtB,EAAE;oBACD,YAAY,CAAC,IAAI,EAAE;oBACnB,OAAO,CAAC,WAAW;oBACnB,YAAY,CAAC,IAAI,EAAE;oBACnB,SAAgB;iBACjB,CAAC,CAAC;gBAEH,eAAM,CAAC,IAAI,CAAC,iEAAiE,SAAS,MAAM,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACtH,CAAC;iBAAM,CAAC;gBACN,qCAAqC;gBACrC,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;SAMtB,EAAE,CAAC,SAAgB,CAAC,CAAC,CAAC;gBAEvB,eAAM,CAAC,IAAI,CAAC,0DAA0D,SAAS,EAAE,CAAC,CAAC;YACrF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yDAAyD,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAC1C,UAAe,EACf,SAAiB,EACjB,OAAgB;QAEhB,IAAI,CAAC;YACH,4FAA4F;YAC5F,IAAI,OAAO,EAAE,CAAC;gBACZ,sCAAsC;gBACtC,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CACvC,+DAA+D,EAC/D,CAAC,SAAS,CAAC,CACZ,CAAC;gBAEF,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1B,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,YAAY,CAAC,CAAC;gBACpD,CAAC;gBAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC5B,MAAM,iBAAiB,GAAG,OAAO,CAAC,mBAAmB,KAAK,CAAC,CAAC;gBAE5D,IAAI,iBAAiB,EAAE,CAAC;oBACtB,0FAA0F;oBAC1F,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;WAetB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;oBAEhB,eAAM,CAAC,IAAI,CAAC,wEAAwE,SAAS,EAAE,CAAC,CAAC;gBACnG,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,sCAAsC;gBACtC,MAAM,UAAU,CAAC,KAAK,CACpB,0EAA0E,EAC1E,CAAC,CAAC,EAAE,SAAgB,CAAC,CACtB,CAAC;gBAEF,eAAM,CAAC,IAAI,CAAC,oEAAoE,SAAS,EAAE,CAAC,CAAC;YAC/F,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2DAA2D,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,UAAe,EAAE,SAAiB;QAC9D,IAAI,CAAC;YACH,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;;OAOtB,EAAE,CAAC,SAAgB,CAAC,CAAC,CAAC;YAEvB,eAAM,CAAC,IAAI,CAAC,qDAAqD,SAAS,EAAE,CAAC,CAAC;QAChF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6CAA6C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,2CAA2C,CACtD,UAAe,EACf,SAAiB,EACjB,OAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,UAAU,CAAC,KAAK,CACpB,0EAA0E,EAC1E,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,SAAgB,CAAC,CACpC,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,oFAAoF,SAAS,OAAO,OAAO,EAAE,CAAC,CAAC;QAC7H,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wEAAwE,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1G,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,UAAe,EAAE,SAAiB;QAC/D,IAAI,CAAC;YACH,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;OAWzC,EAAE,CAAC,SAAgB,CAAC,CAAC,CAAC;YAEvB,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+CAA+C,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAC1C,UAAe,EACf,SAAiB;QAEjB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,gEAAgE,SAAS,EAAE,CAAC,CAAC;YAEzF,4BAA4B;YAC5B,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC,qCAAqC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAE9F,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,WAAW,SAAS,YAAY,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE5B,8CAA8C;YAC9C,MAAM,iBAAiB,GAAG,CACxB,OAAO,CAAC,mBAAmB,KAAK,CAAC;gBACjC,OAAO,CAAC,MAAM,KAAK,mBAAmB,CACvC,CAAC;YAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACvB,eAAM,CAAC,IAAI,CAAC,uBAAuB,SAAS,oEAAoE,CAAC,CAAC;gBAClH,OAAO;YACT,CAAC;YAED,qEAAqE;YACrE,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BtB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAEhB,eAAM,CAAC,IAAI,CAAC,8DAA8D,SAAS,gCAAgC,CAAC,CAAC;QACvH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qEAAqE,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YACvG,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA7WD,gDA6WC"}