/**
 * Clean up duplicate sessions for all users
 * Keeps only the most recent active session per user
 */
export declare function deduplicateUserSessions(): Promise<void>;
/**
 * Get session statistics for monitoring
 */
export declare function getSessionStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    uniqueActiveUsers: number;
    duplicateUsers: number;
}>;
/**
 * Clean up sessions for a specific user (useful for logout)
 */
export declare function cleanupUserSessions(userId: string, keepMostRecent?: boolean): Promise<number>;
