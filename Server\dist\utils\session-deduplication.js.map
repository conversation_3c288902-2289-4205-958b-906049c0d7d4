{"version": 3, "file": "session-deduplication.js", "sourceRoot": "", "sources": ["../../src/utils/session-deduplication.ts"], "names": [], "mappings": ";;AAOA,0DAuDC;AAKD,0CA2CC;AAKD,kDA0CC;AA7JD,6CAA0C;AAC1C,2CAAqC;AAErC;;;GAGG;AACI,KAAK,UAAU,uBAAuB;IAC3C,IAAI,CAAC;QACH,kBAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAE5D,2CAA2C;QAC3C,MAAM,cAAc,GAAG,MAAM,IAAA,aAAK,EAAC;;;;;;;KAOlC,CAAU,CAAC;QAEZ,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,kBAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC7C,OAAO;QACT,CAAC;QAED,kBAAM,CAAC,IAAI,CAAC,YAAY,cAAc,CAAC,MAAM,gCAAgC,CAAC,CAAC;QAE/E,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;YAClC,+DAA+D;YAC/D,MAAM,YAAY,GAAG,MAAM,IAAA,aAAK,EAAC;;;;;OAKhC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAU,CAAC;YAE5B,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,oDAAoD;gBACpD,MAAM,oBAAoB,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,UAAU,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAEvD,MAAM,IAAA,aAAK,EAAC;;;yBAGK,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;SACnD,EAAE,UAAU,CAAC,CAAC;gBAEf,YAAY,IAAI,oBAAoB,CAAC,MAAM,CAAC;gBAE5C,kBAAM,CAAC,IAAI,CAAC,cAAc,oBAAoB,CAAC,MAAM,gCAAgC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YACzG,CAAC;QACH,CAAC;QAED,kBAAM,CAAC,IAAI,CAAC,6CAA6C,YAAY,qBAAqB,CAAC,CAAC;IAE9F,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,eAAe;IAMnC,IAAI,CAAC;QACH,qBAAqB;QACrB,MAAM,CAAC,WAAW,CAAC,GAAG,MAAM,IAAA,aAAK,EAAC,+CAA+C,CAAU,CAAC;QAE5F,sBAAsB;QACtB,MAAM,CAAC,YAAY,CAAC,GAAG,MAAM,IAAA,aAAK,EAAC,sEAAsE,CAAU,CAAC;QAEpH,0BAA0B;QAC1B,MAAM,CAAC,YAAY,CAAC,GAAG,MAAM,IAAA,aAAK,EAAC;;;;KAIlC,CAAU,CAAC;QAEZ,oCAAoC;QACpC,MAAM,cAAc,GAAG,MAAM,IAAA,aAAK,EAAC;;;;;;;;;KASlC,CAAU,CAAC;QAEZ,OAAO;YACL,aAAa,EAAE,WAAW,CAAC,KAAK;YAChC,cAAc,EAAE,YAAY,CAAC,KAAK;YAClC,iBAAiB,EAAE,YAAY,CAAC,KAAK;YACrC,cAAc,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC;SAC9C,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,mBAAmB,CAAC,MAAc,EAAE,iBAA0B,IAAI;IACtF,IAAI,CAAC;QACH,IAAI,cAAc,EAAE,CAAC;YACnB,iCAAiC;YACjC,MAAM,YAAY,GAAG,MAAM,IAAA,aAAK,EAAC;;;;;OAKhC,EAAE,CAAC,MAAM,CAAC,CAAU,CAAC;YAEtB,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,4CAA4C;gBAC5C,MAAM,oBAAoB,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,UAAU,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAEvD,MAAM,IAAA,aAAK,EAAC;;;yBAGK,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;SACnD,EAAE,UAAU,CAAC,CAAC;gBAEf,kBAAM,CAAC,IAAI,CAAC,WAAW,oBAAoB,CAAC,MAAM,gCAAgC,MAAM,EAAE,CAAC,CAAC;gBAC5F,OAAO,oBAAoB,CAAC,MAAM,CAAC;YACrC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,wCAAwC;YACxC,MAAM,MAAM,GAAG,MAAM,IAAA,aAAK,EAAC;;;;OAI1B,EAAE,CAAC,MAAM,CAAC,CAAQ,CAAC;YAEpB,kBAAM,CAAC,IAAI,CAAC,qCAAqC,MAAM,EAAE,CAAC,CAAC;YAC3D,OAAO,MAAM,CAAC,YAAY,IAAI,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAM,CAAC,KAAK,CAAC,uCAAuC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QACtE,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC"}