/**
 * PRODUCTION-READY WebSocket Manager
 * Handles connection reliability, automatic reconnection, and message queuing
 */
import { Server as SocketIOServer } from 'socket.io';
export interface WebSocketConnection {
    id: string;
    userId?: string;
    department?: string;
    connectedAt: number;
    lastActivity: number;
    isAuthenticated: boolean;
    metadata: Record<string, any>;
}
export interface QueuedMessage {
    id: string;
    targetUserId?: string;
    targetDepartment?: string;
    event: string;
    data: any;
    priority: 'low' | 'normal' | 'high' | 'critical';
    createdAt: number;
    expiresAt: number;
    retryCount: number;
    maxRetries: number;
}
export declare class WebSocketManager {
    private io;
    private connections;
    private userConnections;
    private departmentConnections;
    private messageQueue;
    private heartbeatInterval;
    private queueProcessorInterval;
    private connectionCleanupInterval;
    constructor();
    /**
     * Initialize WebSocket server with production settings
     */
    initialize(io: SocketIOServer): void;
    /**
     * Setup WebSocket event handlers
     */
    private setupEventHandlers;
    /**
     * Handle new WebSocket connection
     */
    private handleConnection;
    /**
     * Setup socket-specific event handlers
     */
    private setupSocketHandlers;
    /**
     * Handle user authentication
     */
    private handleAuthentication;
    /**
     * Handle connection disconnection
     */
    private handleDisconnection;
    /**
     * Send message with reliability features
     */
    sendMessage(target: {
        userId?: string;
        department?: string;
        socketId?: string;
    }, event: string, data: any, options?: {
        priority?: 'low' | 'normal' | 'high' | 'critical';
        persistent?: boolean;
        ttl?: number;
        requireAck?: boolean;
    }): Promise<boolean>;
    /**
     * Internal message sending logic
     */
    private doSendMessage;
    /**
     * LAN-OPTIMIZED: Simplified message handling - no queuing needed for local network
     * In LAN environment, users are either connected or completely offline
     */
    private queueMessage;
    /**
     * LAN-OPTIMIZED: No message delivery needed - real-time updates handle everything
     */
    private deliverPendingMessages;
    /**
     * Get connection statistics
     */
    getConnectionStats(): {
        totalConnections: number;
        authenticatedConnections: number;
        departmentBreakdown: Record<string, number>;
        queuedMessages: number;
    };
    /**
     * Helper methods for connection tracking
     */
    private addUserConnection;
    private removeUserConnection;
    private addDepartmentConnection;
    private removeDepartmentConnection;
    private updateActivity;
    private updateConnectionQuality;
    private handleMessageAcknowledgment;
    /**
     * Start heartbeat mechanism
     */
    private startHeartbeat;
    /**
     * Start queue processor
     */
    private startQueueProcessor;
    /**
     * Process message queues
     */
    private processMessageQueues;
    /**
     * Start connection cleanup
     */
    private startConnectionCleanup;
    /**
     * Clean up disconnected connections
     */
    private cleanupConnections;
    /**
     * Shutdown WebSocket manager
     */
    shutdown(): void;
}
export declare const webSocketManager: WebSocketManager;
