/**
 * VMS Voucher Workflow Manager
 * Handles atomic state transitions and voucher copy management
 */
import { WorkflowState, WorkflowEvent, VoucherWorkflowContext } from './VoucherWorkflowStateMachine';
export interface VoucherWorkflowResult {
    success: boolean;
    voucher: any;
    copy?: any;
    previousState: WorkflowState;
    newState: WorkflowState;
    affectedTabs: string[];
    error?: string;
}
export interface WorkflowEventData {
    type: WorkflowEvent;
    voucherId: string;
    context: VoucherWorkflowContext;
    data?: Record<string, any>;
}
export declare class VoucherWorkflowManager {
    private db;
    private eventPublisher;
    private auditLogger;
    constructor(db: any, eventPublisher: any, auditLogger: any);
    /**
     * Execute workflow transition atomically with production-grade error recovery
     */
    transitionVoucher(eventData: WorkflowEventData): Promise<VoucherWorkflowResult>;
    /**
     * Create voucher copy for rejection/return flows
     */
    private createVoucherCopy;
    /**
     * Update voucher state atomically
     */
    private updateVoucherState;
    /**
     * Get voucher by ID
     */
    private getVoucherById;
    /**
     * Determine department based on workflow state
     */
    private getDepartmentForState;
    /**
     * Get affected tabs for real-time updates
     */
    private getAffectedTabs;
    /**
     * Log workflow transition for audit trail
     */
    private logWorkflowTransition;
    /**
     * Publish real-time workflow event
     */
    private publishWorkflowEvent;
    /**
     * Execute function within database transaction
     */
    private withTransaction;
    /**
     * Generate unique ID
     */
    private generateId;
    /**
     * Enhanced voucher retrieval with retry logic
     */
    private getVoucherByIdWithRetry;
    /**
     * Handle workflow errors with recovery mechanisms
     */
    private handleWorkflowError;
    /**
     * Attempt to recover from invalid state transitions
     */
    private attemptStateRecovery;
    /**
     * Attempt to recover missing vouchers
     */
    private attemptVoucherRecovery;
}
