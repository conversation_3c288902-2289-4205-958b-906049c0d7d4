{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🎯 Using forced port: 8080","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔧 Setting up Socket.IO middleware and handlers...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Starting workflow state migration...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"No vouchers need workflow state migration","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Users already exist (10 users found)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔄 Initializing workflow service...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Initializing workflow service...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔄 Initializing file storage...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"File storage initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ File storage initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🌐 Environment: DEVELOPMENT","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📊 Process ID: 21252","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"New database connection established: 518","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database connection test successful","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database 'vms_production' verified","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Essential database tables verified","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database setup verification complete","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Health checks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database Manager initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Session cleanup scheduled (every 10 minutes)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🛑 Automated backup scheduler stopped","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🕒 Starting automated backup scheduler (every 24 hours)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Automated backup scheduler started (daily backups)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🖥️  VMS System: http://************:8080","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🖥️  VMS System: http://10.25.41.234:8080","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🔌 WebSocket: http://10.25.41.234:8080/socket.io/","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   💾 Memory: 65MB RSS","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🔄 Uptime: 3s","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1754064653797,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-01 16:11:23"}
{"level":"info","message":"✅ No duplicate sessions found","service":"vms-server","timestamp":"2025-08-01 16:11:23"}
{"level":"info","message":"✅ All users have unique sessions (2 unique active users)","service":"vms-server","timestamp":"2025-08-01 16:11:23"}
{"level":"info","message":"✅ Initial session cleanup completed","service":"vms-server","timestamp":"2025-08-01 16:11:23"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:11:35.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:35"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 44.858 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Socket P4wtzNXhxmc9iwMhAAAB is in rooms: P4wtzNXhxmc9iwMhAAAB, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"🔍 Checking for year rollover...","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"🗓️ Year rollover monitoring started - checking daily","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"📅 Current date: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"📊 Calendar year: 2025","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"📈 Calculated fiscal year: 2025","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"⚙️ Configured fiscal year: 2025","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:11:53.468Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:53"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:11:53"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 24.450 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:53"}
{"level":"info","message":"User disconnected: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) has no remaining connections","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"\u001b[0mGET /audit-dashboard \u001b[36m304\u001b[0m 2.803 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"Cleared socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) - session remains active","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"Broadcasting 0 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"📢 Broadcasting user_left for active user: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-01T16:11:56.547Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 3.371 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 3.441 ms - 288\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Socket 0oQi_0imDVyJ6fUTAAAE is in rooms: 0oQi_0imDVyJ6fUTAAAE, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/me","service":"vms-server","timestamp":"2025-08-01T16:11:57.274Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /me","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/auth/me \u001b[36m304\u001b[0m 16.143 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 2.641 ms - 288\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"✅ REAL-TIME: Confirmed department join for EMMANUEL AMOAKOH in AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Connected clients after join: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754064717313","service":"vms-server","timestamp":"2025-08-01T16:11:57.340Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754064717313","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users?_t=1754064717315","service":"vms-server","timestamp":"2025-08-01T16:11:57.348Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754064717315","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"GET /vouchers request at 2025-08-01T16:11:57.363Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754064717315 \u001b[32m200\u001b[0m 36.139 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Found 2 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Sample vouchers: MISAUG0002 (5010d7f3-60b3-4921-8613-3ebe1ff99457): AUDIT: PROCESSING, MISAUG0001 (5ca8b4ff-9693-4f09-93c0-1fcb68fd21c4): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-01 16:04:50","receivedBy":"EMMANUEL AMOAKOH","receivedByAudit":true,"service":"vms-server","status":"AUDIT: PROCESSING","timestamp":"2025-08-01 16:11:57","voucherId":"MISAUG0002"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754064717313 \u001b[32m200\u001b[0m 75.417 ms - 3265\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users?_t=1754064717393","service":"vms-server","timestamp":"2025-08-01T16:11:57.429Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754064717393","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754064717416","service":"vms-server","timestamp":"2025-08-01T16:11:57.433Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754064717416","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"GET /vouchers request at 2025-08-01T16:11:57.444Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754064717393 \u001b[32m200\u001b[0m 25.689 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Found 2 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Sample vouchers: MISAUG0002 (5010d7f3-60b3-4921-8613-3ebe1ff99457): AUDIT: PROCESSING, MISAUG0001 (5ca8b4ff-9693-4f09-93c0-1fcb68fd21c4): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-01 16:04:50","receivedBy":"EMMANUEL AMOAKOH","receivedByAudit":true,"service":"vms-server","status":"AUDIT: PROCESSING","timestamp":"2025-08-01 16:11:57","voucherId":"MISAUG0002"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754064717416 \u001b[32m200\u001b[0m 39.328 ms - 3265\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:11:57.477Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[32m200\u001b[0m 33.779 ms - 8051\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-01T16:11:57.524Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 26.909 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-01T16:11:57.564Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 12.405 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:11:57.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:11:57.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 19.218 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 35.404 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?timestamp=1754064720312","service":"vms-server","timestamp":"2025-08-01T16:12:00.315Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?timestamp=1754064720312","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"GET /vouchers request at 2025-08-01T16:12:00.325Z by user EMMANUEL AMOAKOH (AUDIT), query department: none","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"Fetching all vouchers for AUDIT user","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"Found 2 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"Sample vouchers: MISAUG0002 (5010d7f3-60b3-4921-8613-3ebe1ff99457): AUDIT: PROCESSING, MISAUG0001 (5ca8b4ff-9693-4f09-93c0-1fcb68fd21c4): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-01 16:04:50","receivedBy":"EMMANUEL AMOAKOH","receivedByAudit":true,"service":"vms-server","status":"AUDIT: PROCESSING","timestamp":"2025-08-01 16:12:00","voucherId":"MISAUG0002"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?timestamp=1754064720312 \u001b[32m200\u001b[0m 17.600 ms - 3265\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"🔄 LOCK REQUEST: Re-authenticated EMMANUEL AMOAKOH (AUDIT) from current session","service":"vms-server","timestamp":"2025-08-01 16:12:02"}
{"level":"info","message":"✅ AUDIT EXCLUSIVE ACCESS: EMMANUEL AMOAKOH accessing MISSIONS voucher hub","service":"vms-server","timestamp":"2025-08-01 16:12:02"}
{"level":"info","message":"🔒 ISOLATED LOCK REQUEST: EMMANUEL AMOAKOH (AUDIT) requesting voucher hub lock: AUDIT-VOUCHER-HUB-MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:02"}
{"level":"info","message":"🔒 ISOLATED ACCESS: VOUCHER HUB lock acquired - AUDIT-VOUCHER-HUB-MISSIONS by EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:12:02"}
{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-08-01 16:12:20"}
{"level":"info","message":"🎯 Using forced port: 8080","service":"vms-server","timestamp":"2025-08-01 16:12:20"}
{"level":"info","message":"🔧 Setting up Socket.IO middleware and handlers...","service":"vms-server","timestamp":"2025-08-01 16:12:20"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-08-01 16:12:20"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-08-01 16:12:20"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-08-01 16:12:20"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-08-01 16:12:20"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-08-01 16:12:20"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-08-01 16:12:20"}
{"level":"info","message":"Starting workflow state migration...","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"No vouchers need workflow state migration","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Users already exist (10 users found)","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🔄 Initializing workflow service...","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Initializing workflow service...","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🔄 Initializing file storage...","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"File storage initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"✅ File storage initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🌐 Environment: DEVELOPMENT","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"📊 Process ID: 31328","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"New database connection established: 523","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Database connection test successful","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Database 'vms_production' verified","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Essential database tables verified","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Database setup verification complete","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Health checks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Database Manager initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"✅ Session cleanup scheduled (every 10 minutes)","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🛑 Automated backup scheduler stopped","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🕒 Starting automated backup scheduler (every 24 hours)","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"✅ Automated backup scheduler started (daily backups)","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"   🖥️  VMS System: http://************:8080","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"   🖥️  VMS System: http://10.25.41.234:8080","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"   🔌 WebSocket: http://10.25.41.234:8080/socket.io/","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"   💾 Memory: 63MB RSS","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"   🔄 Uptime: 1s","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1754064741207,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"level":"info","message":"Socket 71EuaDuy3GImlAJCAAAB is in rooms: 71EuaDuy3GImlAJCAAAB, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-01 16:12:21"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:12:27.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:12:27"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:12:27"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 26.118 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:27"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 5.874 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"ip":"***********","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-01T16:12:28.118Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 3.874 ms - 185\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.779 ms - 288\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"ip":"***********","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754064749632","service":"vms-server","timestamp":"2025-08-01T16:12:28.177Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754064749632 \u001b[32m200\u001b[0m 10.894 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"ip":"***********","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-01T16:12:28.205Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"WebSocket connection from *********** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: CHARIS (MISSIONS)","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"🔌 WebSocket connection established: CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) from MISSIONS [Auth: YES]","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"🚀 Setting up authenticated connection for CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) from MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"User CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) joined room: department:MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"User CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) joined personal room: user:7e3826ee-dfce-4c9a-9cca-251721fe0ca0","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"Broadcasting 1 connected users for department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"ip":"***********","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754064749682","service":"vms-server","timestamp":"2025-08-01T16:12:28.228Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754064749682 \u001b[32m200\u001b[0m 5.980 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"Updated existing session with socket info for CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0)","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"Total connected clients: 2, 1 in department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"Socket EKEtLRZ1rYau_EqJAAAE is in rooms: EKEtLRZ1rYau_EqJAAAE, department:MISSIONS, user:7e3826ee-dfce-4c9a-9cca-251721fe0ca0","service":"vms-server","timestamp":"2025-08-01 16:12:28"}
{"level":"info","message":"User disconnected: CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) from department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"User CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) has no remaining connections","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"Cleared socket info for CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) - session remains active","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"Broadcasting 0 connected users for department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"📢 Broadcasting user_left for active user: CHARIS (MISSIONS)","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 0 in department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"\u001b[0mGET / \u001b[32m200\u001b[0m 36.733 ms - 1469\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 2.711 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"ip":"***********","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-01T16:12:31.276Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 3.790 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 2.361 ms - 288\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"ip":"***********","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754064752746","service":"vms-server","timestamp":"2025-08-01T16:12:31.287Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754064752746 \u001b[32m200\u001b[0m 9.878 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"ip":"***********","level":"info","message":"API Request: GET /login-updates/user-updates","service":"vms-server","timestamp":"2025-08-01T16:12:31.297Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"SSE connection established for login updates. Total connections: 1","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"WebSocket connection from *********** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"ip":"***********","level":"info","message":"API Request: GET /auth/users-by-department?_t=1754064752770","service":"vms-server","timestamp":"2025-08-01T16:12:31.313Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: CHARIS (MISSIONS)","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"🔌 WebSocket connection established: CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) from MISSIONS [Auth: YES]","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"🚀 Setting up authenticated connection for CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) from MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"User CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) joined room: department:MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"User CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) joined personal room: user:7e3826ee-dfce-4c9a-9cca-251721fe0ca0","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"Broadcasting 1 connected users for department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"Fetched 10 users for login dropdown","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"\u001b[0mGET /api/auth/users-by-department?_t=1754064752770 \u001b[32m200\u001b[0m 17.000 ms - 889\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"Updated existing session with socket info for CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0)","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"Total connected clients: 2, 1 in department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"level":"info","message":"Socket FuJBn7Dym_BqXTPEAAAH is in rooms: FuJBn7Dym_BqXTPEAAAH, department:MISSIONS, user:7e3826ee-dfce-4c9a-9cca-251721fe0ca0","service":"vms-server","timestamp":"2025-08-01 16:12:31"}
{"ip":"***********","level":"info","message":"API Request: POST /auth/login","service":"vms-server","timestamp":"2025-08-01T16:12:40.508Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Successful login: CHARIS (MISSIONS) - Role: USER","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Audit logged: CHARIS logged in to MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Created new session for user CHARIS (session: 2bfe9942-e501-4862-8917-14bc083456fd)","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"\u001b[0mPOST /api/auth/login \u001b[32m200\u001b[0m 102.554 ms - 259\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.724 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: GET /vouchers?department=MISSIONS&timestamp=1754064762048","service":"vms-server","timestamp":"2025-08-01T16:12:40.595Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=MISSIONS&timestamp=1754064762048","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"GET /vouchers request at 2025-08-01T16:12:40.601Z by user CHARIS (MISSIONS), query department: MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Fetching vouchers for specified department: MISSIONS (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Found 0 vouchers for request from CHARIS (MISSIONS)","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=MISSIONS&timestamp=1754064762048 \u001b[32m200\u001b[0m 20.249 ms - 2\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"User disconnected: CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) from department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"User CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) has no remaining connections","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Cleared socket info for CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) - session remains active","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Broadcasting 0 connected users for department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"📢 Broadcasting user_left for active user: CHARIS (MISSIONS)","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"After disconnect - Total connected clients: 1, 0 in department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"\u001b[0mGET /api/login-updates/user-updates \u001b[32m200\u001b[0m 2.704 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-01T16:12:40.752Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"WebSocket connection from *********** - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: CHARIS (MISSIONS)","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"🔌 WebSocket connection established: CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) from MISSIONS [Auth: YES]","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"🚀 Setting up authenticated connection for CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) from MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"User CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) joined room: department:MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"User CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0) joined personal room: user:7e3826ee-dfce-4c9a-9cca-251721fe0ca0","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Broadcasting 1 connected users for department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 52.997 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-01T16:12:40.811Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Updated existing session with socket info for CHARIS (7e3826ee-dfce-4c9a-9cca-251721fe0ca0)","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Total connected clients: 2, 1 in department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Socket d7ZLOsiRe3S-Pw8BAAAK is in rooms: d7ZLOsiRe3S-Pw8BAAAK, department:MISSIONS, user:7e3826ee-dfce-4c9a-9cca-251721fe0ca0","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 22.901 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:12:40.864Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:12:40.866Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.116 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:12:40.874Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[32m200\u001b[0m 18.124 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:12:40.886Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.351 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:12:40.891Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.042 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:12:40.896Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:12:40.901Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.894 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[36m304\u001b[0m 21.117 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[32m200\u001b[0m 48.800 ms - 8051\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"ip":"***********","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:12:40.928Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 7.350 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:40"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-01 16:12:51"}
{"level":"info","message":"🔍 Found 1 users with duplicate sessions","service":"vms-server","timestamp":"2025-08-01 16:12:51"}
{"level":"info","message":"🧹 Cleaned 1 duplicate sessions for user CHARIS","service":"vms-server","timestamp":"2025-08-01 16:12:51"}
{"level":"info","message":"✅ Session deduplication complete: cleaned 1 duplicate sessions","service":"vms-server","timestamp":"2025-08-01 16:12:51"}
{"level":"info","message":"✅ All users have unique sessions (2 unique active users)","service":"vms-server","timestamp":"2025-08-01 16:12:51"}
{"level":"info","message":"✅ Initial session cleanup completed","service":"vms-server","timestamp":"2025-08-01 16:12:51"}
{"ip":"***********","level":"info","message":"API Request: POST /vouchers","service":"vms-server","timestamp":"2025-08-01T16:12:53.460Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /","service":"vms-server","timestamp":"2025-08-01 16:12:53"}
{"departmentMatch":true,"isAudit":false,"isSystemAdmin":false,"isUserRole":true,"isViewerRole":false,"level":"info","message":"🔐 VMSPROD VOUCHER CREATION - Permission check:","requestedDepartment":"MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:53","userDepartment":"MISSIONS","userName":"CHARIS","userRole":"USER"}
{"level":"info","message":"✅ VMSPROD VOUCHER CREATION - Permission granted for user:","service":"vms-server","targetDepartment":"MISSIONS","timestamp":"2025-08-01 16:12:53","userDepartment":"MISSIONS","userName":"CHARIS","userRole":"USER"}
{"level":"info","message":"Generated department-specific voucher ID: MISAUG0003 for department: MISSIONS (prefix: MIS)","service":"vms-server","timestamp":"2025-08-01 16:12:53"}
{"level":"info","message":"Created voucher with ID: 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2, Voucher ID: MISAUG0003","service":"vms-server","timestamp":"2025-08-01 16:12:53"}
{"level":"info","message":"Broadcasting voucher update for 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 to department MISSIONS (original: MISSIONS)","service":"vms-server","timestamp":"2025-08-01 16:12:53"}
{"level":"info","message":"📢 REAL-TIME: Broadcasted to current department: MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:53"}
{"level":"info","message":"✅ REAL-TIME: Successfully broadcasted voucher update for 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 to all relevant departments","service":"vms-server","timestamp":"2025-08-01 16:12:53"}
{"level":"info","message":"\u001b[0mPOST /api/vouchers \u001b[32m201\u001b[0m 154.804 ms - 2477\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:53"}
{"level":"info","message":"🔄 LOCK REQUEST: Re-authenticated CHARIS (MISSIONS) from current session","service":"vms-server","timestamp":"2025-08-01 16:12:57"}
{"level":"info","message":"🔒 ISOLATED LOCK REQUEST: CHARIS (MISSIONS) requesting dashboard lock: DEPARTMENT-DASHBOARD-MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:57"}
{"level":"info","message":"🔒 ISOLATED ACCESS: DEPARTMENT DASHBOARD lock acquired - DEPARTMENT-DASHBOARD-MISSIONS by CHARIS (MISSIONS)","service":"vms-server","timestamp":"2025-08-01 16:12:57"}
{"ip":"***********","level":"info","message":"API Request: POST /batches","service":"vms-server","timestamp":"2025-08-01T16:12:57.715Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /","service":"vms-server","timestamp":"2025-08-01 16:12:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:12:57.952Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:12:57"}
{"level":"info","message":"📦 Batch created: fb69625a-ca9d-479d-9a33-f52993065a95 - Contains 0 rejected + 0 resubmission voucher(s)","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 94.152 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"comment":"NULL","level":"info","message":"🔍 RESUBMISSION DEBUG - Voucher MISAUG0003:","rejected_by":"NULL","rejection_time":"NULL","service":"vms-server","status":"PENDING SUBMISSION","timestamp":"2025-08-01 16:12:58","voucherId":"MISAUG0003"}
{"condition1_rejected_by":null,"condition2_rejection_time":null,"condition3_status_rejected":false,"level":"info","message":"🧪 RESUBMISSION CONDITIONS - MISAUG0003:","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"🎯 RESUBMISSION RESULT - MISAUG0003: NO","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"📥 NORMAL: Sending 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 FROM MISSIONS TO AUDIT - Status: PENDING RECEIPT","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"📤 Creating individual notifications for AUDIT department users","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"✅ Created 2 individual notifications for AUDIT department users","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"🔍 AUDIT BROADCAST DEBUG: Found 1 connected Audit clients","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"   - EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) - Socket: 71EuaDuy3GImlAJCAAAB","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"🏠 AUDIT ROOM DEBUG: Room 'department:AUDIT' has 1 members","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"batchId":"fb69625a-ca9d-479d-9a33-f52993065a95","department":"MISSIONS","level":"info","message":"New batch received from MISSIONS","notifications":[{"id":"eb60c3ec-6d1c-46f4-a1ee-79aebc424cfb","status":"pending","userId":"8aa62d84-53de-4e65-8f7f-bb8e87e92276","userName":"SELORM"},{"id":"0e671c41-3351-459d-99ba-ca7dc58257a4","status":"pending","userId":"d97a34d9-af6d-4026-a5e8-00db2d7a6a98","userName":"EMMANUEL AMOAKOH"}],"sentBy":"CHARIS","service":"vms-server","timestamp":1754064778363,"voucherCount":1}
{"level":"info","message":"📡 FALLBACK: Direct emit to EMMANUEL AMOAKOH","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"📢 REAL-TIME: Broadcasted new_batch_notification to Audit department for batch fb69625a-ca9d-479d-9a33-f52993065a95 (2 users)","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"✅ Batch fb69625a-ca9d-479d-9a33-f52993065a95 completed with metadata: 1 normal, 0 rejected","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:12:58.375Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /notifications","service":"vms-server","timestamp":"2025-08-01T16:12:58.377Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"📡 Emitted batch creation event for fb69625a-ca9d-479d-9a33-f52993065a95","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"✅ Successfully processed batch fb69625a-ca9d-479d-9a33-f52993065a95 with 1 vouchers","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"\u001b[0mPOST /api/batches \u001b[32m201\u001b[0m 673.457 ms - 3149\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"ip":"***********","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:12:58.399Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[32m200\u001b[0m 51.682 ms - 11969\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:12:58.429Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[32m200\u001b[0m 43.042 ms - 11969\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"\u001b[0mGET /api/notifications \u001b[32m200\u001b[0m 71.874 ms - 2275\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"🔓 NAVIGATION RELEASE: EMMANUEL AMOAKOH (AUDIT) releasing lock for AUDIT-MISSIONS due to navigation","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /notifications","service":"vms-server","timestamp":"2025-08-01T16:12:58.452Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"🔓 NAVIGATION RELEASE: EMMANUEL AMOAKOH (AUDIT) releasing lock for AUDIT-MISSIONS due to navigation","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 34.668 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"\u001b[0mGET /api/notifications \u001b[36m304\u001b[0m 15.446 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"ip":"***********","level":"info","message":"API Request: GET /vouchers?department=MISSIONS&timestamp=1754064780427","service":"vms-server","timestamp":"2025-08-01T16:12:58.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=MISSIONS&timestamp=1754064780427","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"GET /vouchers request at 2025-08-01T16:12:58.971Z by user CHARIS (MISSIONS), query department: MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"Fetching vouchers for specified department: MISSIONS (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-01 16:12:58"}
{"level":"info","message":"Found 1 vouchers for request from CHARIS (MISSIONS)","service":"vms-server","timestamp":"2025-08-01 16:12:59"}
{"level":"info","message":"Sample vouchers: MISAUG0003 (966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2): PENDING RECEIPT","service":"vms-server","timestamp":"2025-08-01 16:12:59"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":false,"service":"vms-server","status":"PENDING RECEIPT","timestamp":"2025-08-01 16:12:59","voucherId":"MISAUG0003"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=MISSIONS&timestamp=1754064780427 \u001b[32m200\u001b[0m 46.843 ms - 1612\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:59"}
{"ip":"***********","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:12:59.025Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:12:59"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 30.779 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:59"}
{"level":"info","message":"🔓 DEPARTMENT LOCK RELEASE: CHARIS (MISSIONS) releasing lock for MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:59"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?timestamp=1754064787650","service":"vms-server","timestamp":"2025-08-01T16:13:07.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?timestamp=1754064787650","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"GET /vouchers request at 2025-08-01T16:13:07.659Z by user EMMANUEL AMOAKOH (AUDIT), query department: none","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"Fetching all vouchers for AUDIT user","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"Found 3 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"Sample vouchers: MISAUG0003 (966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2): PENDING RECEIPT, MISAUG0002 (5010d7f3-60b3-4921-8613-3ebe1ff99457): AUDIT: PROCESSING, MISAUG0001 (5ca8b4ff-9693-4f09-93c0-1fcb68fd21c4): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":null,"receivedBy":null,"receivedByAudit":false,"service":"vms-server","status":"PENDING RECEIPT","timestamp":"2025-08-01 16:13:07","voucherId":"MISAUG0003"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?timestamp=1754064787650 \u001b[32m200\u001b[0m 18.339 ms - 4876\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches/fb69625a-ca9d-479d-9a33-f52993065a95","service":"vms-server","timestamp":"2025-08-01T16:13:07.668Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /fb69625a-ca9d-479d-9a33-f52993065a95","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"Fetching batch: fb69625a-ca9d-479d-9a33-f52993065a95","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"Step 1: Fetching batch from database","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"Step 2: Batch found - MISSIONS from CHARIS","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"Step 3: Fetching vouchers in batch","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"Step 4: Found 1 vouchers in batch","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"Batch fb69625a-ca9d-479d-9a33-f52993065a95 fetched successfully with 1 vouchers","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"\u001b[0mGET /api/batches/fb69625a-ca9d-479d-9a33-f52993065a95 \u001b[32m200\u001b[0m 16.338 ms - 3888\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:07"}
{"level":"info","message":"🔄 LOCK REQUEST: Re-authenticated EMMANUEL AMOAKOH (AUDIT) from current session","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"✅ AUDIT EXCLUSIVE ACCESS: EMMANUEL AMOAKOH accessing AUDIT voucher hub","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔒 ISOLATED LOCK REQUEST: EMMANUEL AMOAKOH (AUDIT) requesting voucher hub lock: AUDIT-VOUCHER-HUB-AUDIT","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔒 ISOLATED ACCESS: VOUCHER HUB lock acquired - AUDIT-VOUCHER-HUB-AUDIT by EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"ip":"127.0.0.1","level":"info","message":"API Request: POST /batches/fb69625a-ca9d-479d-9a33-f52993065a95/receive","service":"vms-server","timestamp":"2025-08-01T16:13:09.840Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: POST /fb69625a-ca9d-479d-9a33-f52993065a95/receive","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔄 BATCH RECEIVE: Starting batch receive for fb69625a-ca9d-479d-9a33-f52993065a95 by EMMANUEL AMOAKOH","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔄 BATCH RECEIVE: Processing 1 accepted, 0 rejected vouchers","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔄 BATCH RECEIVE: Transaction started for batch fb69625a-ca9d-479d-9a33-f52993065a95","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔄 BATCH RECEIVE: Fetching batch details...","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔄 BATCH RECEIVE: Batch found, proceeding with processing...","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔒 BATCH CLAIMED: Batch fb69625a-ca9d-479d-9a33-f52993065a95 claimed by EMMANUEL AMOAKOH, notifications marked as read for all users","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔍 AUDIT BROADCAST DEBUG: Found 1 connected Audit clients","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"   - EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) - Socket: 71EuaDuy3GImlAJCAAAB","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🏠 AUDIT ROOM DEBUG: Room 'department:AUDIT' has 1 members","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"batchId":"fb69625a-ca9d-479d-9a33-f52993065a95","claimedBy":"EMMANUEL AMOAKOH","claimedById":"d97a34d9-af6d-4026-a5e8-00db2d7a6a98","level":"info","message":"Batch fb69625a-ca9d-479d-9a33-f52993065a95 is now being processed by EMMANUEL AMOAKOH","service":"vms-server","timestamp":1754064789862}
{"level":"info","message":"📡 FALLBACK: Direct emit to EMMANUEL AMOAKOH","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"📢 REAL-TIME: Broadcasted batch claim notification to all audit users","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"currentFlags":{"isCertified":false,"isDispatched":false,"isNew":false,"isPendingDispatch":false,"isProcessing":false,"isRejected":false},"currentStatus":"PENDING RECEIPT","level":"info","message":"🔍 VALIDATION DEBUG: Checking transition for voucher 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2:","newStatus":"AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:13:09","userDepartment":"AUDIT","userName":"EMMANUEL AMOAKOH","userRole":"USER"}
{"level":"info","message":"🔄 BATCH RECEIVE: Bypassing status validation for system operation PENDING RECEIPT -> AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:13:09","userDepartment":"AUDIT","userName":"EMMANUEL AMOAKOH","userRole":"USER","voucherId":"966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2"}
{"level":"info","message":"🔄 AUDIT ACCEPTANCE: Changing voucher 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 status: PENDING RECEIPT → AUDIT: PROCESSING (received by EMMANUEL AMOAKOH)","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔄 AUDIT ACCEPTANCE: Setting received_by_audit = true for voucher 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔄 AUDIT RECEIVING: Voucher 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 - isResubmitted: null, workflow_state: AUDIT_NEW","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:13:09.875Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"Broadcasting voucher update for undefined to department undefined (original: undefined)","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"✅ REAL-TIME: Successfully broadcasted voucher update for undefined to all relevant departments","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"📢 REAL-TIME: Broadcasted voucher status update for undefined: AUDIT: PROCESSING (received by Audit)","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"✅ AUDIT PROCESSING: Successfully updated voucher 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 with status AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"warn","message":"⚠️ Duplicate return batch prevented - existing batch found for department MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 16.003 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /notifications","service":"vms-server","timestamp":"2025-08-01T16:13:09.894Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"🔄 BATCH RECEIVE: Transaction completed successfully in 48ms","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"✅ BATCH RECEIVE: Successfully completed batch fb69625a-ca9d-479d-9a33-f52993065a95 in 50ms","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"\u001b[0mPOST /api/batches/fb69625a-ca9d-479d-9a33-f52993065a95/receive \u001b[32m200\u001b[0m 64.176 ms - 3186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"\u001b[0mGET /api/notifications \u001b[32m200\u001b[0m 12.933 ms - 2548\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:13:09.907Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /notifications","service":"vms-server","timestamp":"2025-08-01T16:13:09.910Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"\u001b[0mGET /api/notifications \u001b[36m304\u001b[0m 10.871 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[32m200\u001b[0m 21.695 ms - 12030\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:09"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:13:10.903Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:13:10"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:13:10.907Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 7.133 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:10"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:13:10.911Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 9.929 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:10"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:13:10"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[32m200\u001b[0m 27.759 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:10"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754064791922","service":"vms-server","timestamp":"2025-08-01T16:13:11.919Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754064791922","service":"vms-server","timestamp":"2025-08-01 16:13:11"}
{"level":"info","message":"GET /vouchers request at 2025-08-01T16:13:11.936Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-01 16:13:11"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-01 16:13:11"}
{"level":"info","message":"Found 3 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:13:11"}
{"level":"info","message":"Sample vouchers: MISAUG0003 (966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2): AUDIT: PROCESSING, MISAUG0002 (5010d7f3-60b3-4921-8613-3ebe1ff99457): AUDIT: PROCESSING, MISAUG0001 (5ca8b4ff-9693-4f09-93c0-1fcb68fd21c4): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:13:11"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-01 16:13:09","receivedBy":"EMMANUEL AMOAKOH","receivedByAudit":true,"service":"vms-server","status":"AUDIT: PROCESSING","timestamp":"2025-08-01 16:13:11","voucherId":"MISAUG0003"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754064791922 \u001b[32m200\u001b[0m 29.566 ms - 4898\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:11"}
{"level":"info","message":"🔓 DEPARTMENT LOCK RELEASE: EMMANUEL AMOAKOH (AUDIT) releasing lock for AUDIT-AUDIT","service":"vms-server","timestamp":"2025-08-01 16:13:11"}
{"level":"info","message":"🔄 LOCK REQUEST: Re-authenticated EMMANUEL AMOAKOH (AUDIT) from current session","service":"vms-server","timestamp":"2025-08-01 16:13:13"}
{"level":"info","message":"✅ AUDIT EXCLUSIVE ACCESS: EMMANUEL AMOAKOH accessing MISSIONS voucher hub","service":"vms-server","timestamp":"2025-08-01 16:13:13"}
{"level":"info","message":"🔒 ISOLATED LOCK REQUEST: EMMANUEL AMOAKOH (AUDIT) requesting voucher hub lock: AUDIT-VOUCHER-HUB-MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:13:13"}
{"level":"info","message":"🔒 ISOLATED ACCESS: VOUCHER HUB lock acquired - AUDIT-VOUCHER-HUB-MISSIONS by EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:13:13"}
{"level":"info","message":"🔍 Checking for year rollover...","service":"vms-server","timestamp":"2025-08-01 16:13:20"}
{"level":"info","message":"🗓️ Year rollover monitoring started - checking daily","service":"vms-server","timestamp":"2025-08-01 16:13:20"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-01 16:13:20"}
{"level":"info","message":"📅 Current date: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-01 16:13:20"}
{"level":"info","message":"📊 Calendar year: 2025","service":"vms-server","timestamp":"2025-08-01 16:13:20"}
{"level":"info","message":"📈 Calculated fiscal year: 2025","service":"vms-server","timestamp":"2025-08-01 16:13:20"}
{"level":"info","message":"⚙️ Configured fiscal year: 2025","service":"vms-server","timestamp":"2025-08-01 16:13:20"}
{"level":"error","message":"❌ BATCH RECEIVE: Timeout after 15 seconds for batch fb69625a-ca9d-479d-9a33-f52993065a95","service":"vms-server","timestamp":"2025-08-01 16:13:24"}
{"ip":"127.0.0.1","level":"info","message":"API Request: PUT /vouchers/966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2/toggle-hold","service":"vms-server","timestamp":"2025-08-01T16:13:25.112Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: PUT /966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2/toggle-hold","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔄 TOGGLE VOUCHER HOLD: EMMANUEL AMOAKOH (AUDIT) putting on hold voucher 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"✅ VOUCHER HOLD TOGGLED: 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 by EMMANUEL AMOAKOH - ON HOLD","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"\u001b[0mPUT /api/vouchers/966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2/toggle-hold \u001b[32m200\u001b[0m 29.196 ms - 2781\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"ip":"127.0.0.1","level":"info","message":"API Request: PUT /vouchers/966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2","service":"vms-server","timestamp":"2025-08-01T16:13:25.145Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: PUT /966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"0":"id","1":"voucher_id","10":"status","100":"hold_by","101":"hold_time","11":"sent_to_audit","12":"created_by","13":"batch_id","14":"received_by","15":"receipt_time","16":"comment","17":"tax_type","18":"tax_details","19":"tax_amount","2":"date","20":"pre_audited_amount","21":"pre_audited_by","22":"certified_by","23":"audit_dispatch_time","24":"audit_dispatched_by","25":"dispatch_to_on_department","26":"post_provisional_cash","27":"dispatched","28":"dispatch_to_audit_by","29":"is_returned","3":"claimant","30":"return_comment","31":"return_time","32":"deleted","33":"deletion_time","34":"rejection_time","35":"department_receipt_time","36":"department_received_by","37":"department_rejected","38":"rejected_by","39":"pending_return","4":"description","40":"return_initiated_time","41":"reference_id","42":"idempotency_key","43":"flags","44":"created_at","45":"original_department","46":"received_by_audit","47":"work_started","48":"batch_processed","49":"batch_processed_time","5":"amount","50":"rejection_type","51":"parent_voucher_id","52":"version","53":"is_rejection_copy","54":"rejection_workflow_stage","55":"correction_count","56":"last_corrected_by","57":"last_correction_time","58":"correction_reason","59":"is_corrected","6":"currency","60":"resubmission_count","61":"original_rejection_date","62":"resubmission_history","63":"resubmission_badge","64":"original_rejection_reason","65":"resubmission_status","66":"original_rejected_by","67":"last_resubmitted_by","68":"last_resubmission_date","69":"resubmission_comment","7":"department","70":"is_resubmitted_voucher","71":"workflow_state","72":"badge_type","73":"is_copy","74":"workflow_history","75":"rejection_audit_trail","76":"resubmission_audit_trail","77":"is_rejected","78":"original_voucher_id","79":"voucher_type","8":"dispatched_by","80":"is_returned_copy","81":"returned_by","82":"is_resubmitted","83":"is_resubmission","84":"finance_received","85":"resubmission_certified_visible_to_finance","86":"resubmission_tracking_visible_to_audit","87":"badge_persistence_flags","88":"is_returned_voucher","89":"return_count","9":"dispatch_time","90":"last_return_date","91":"return_certified_visible_to_finance","92":"return_audit_visible","93":"original_return_reason","94":"original_returned_by","95":"last_edit_reason","96":"last_edited_by","97":"last_edit_time","98":"is_on_hold","99":"hold_comment","level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Incoming fields for voucher 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2:","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"amount":"67000.00","audit_dispatch_time":null,"audit_dispatched_by":null,"badge_persistence_flags":null,"badge_type":"NONE","batch_id":"fb69625a-ca9d-479d-9a33-f52993065a95","batch_processed":0,"batch_processed_time":null,"certified_by":null,"claimant":"derek","comment":"null","correction_count":0,"correction_reason":null,"created_at":"2025-08-01T16:12:53.000Z","created_by":"CHARIS","currency":"GHS","date":"AUGUST 01, 2025 AT 04:12 PM","deleted":0,"deletion_time":null,"department":"AUDIT","department_receipt_time":null,"department_received_by":null,"department_rejected":0,"description":"TEST","dispatch_time":"2025-08-01 16:12:58","dispatch_to_audit_by":"CHARIS","dispatch_to_on_department":0,"dispatched":false,"dispatched_by":"","finance_received":0,"flags":{"isCertified":false,"isDispatched":false,"isNew":false,"isPendingDispatch":false,"isProcessing":true,"isRejected":false},"hold_by":"EMMANUEL AMOAKOH","hold_comment":"TESTING THE SYSTEM","hold_time":"2025-08-01T16:13:25.000Z","id":"966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2","idempotency_key":"MISSIONS-1754064774920-dutncd5lt","is_copy":0,"is_corrected":0,"is_on_hold":1,"is_rejected":0,"is_rejection_copy":0,"is_resubmission":0,"is_resubmitted":0,"is_resubmitted_voucher":0,"is_returned":0,"is_returned_copy":0,"is_returned_voucher":0,"last_corrected_by":null,"last_correction_time":null,"last_edit_reason":null,"last_edit_time":null,"last_edited_by":null,"last_resubmission_date":null,"last_resubmitted_by":null,"last_return_date":null,"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Full request body:","original_department":"MISSIONS","original_rejected_by":null,"original_rejection_date":null,"original_rejection_reason":null,"original_return_reason":null,"original_returned_by":null,"original_voucher_id":null,"parent_voucher_id":null,"pending_return":0,"post_provisional_cash":0,"pre_audited_amount":null,"pre_audited_by":null,"receipt_time":"2025-08-01 16:13:09","received_by":"EMMANUEL AMOAKOH","received_by_audit":1,"reference_id":null,"rejected_by":null,"rejection_audit_trail":null,"rejection_time":null,"rejection_type":null,"rejection_workflow_stage":"INITIAL","resubmission_audit_trail":null,"resubmission_badge":null,"resubmission_certified_visible_to_finance":0,"resubmission_comment":null,"resubmission_count":0,"resubmission_history":null,"resubmission_status":null,"resubmission_tracking_visible_to_audit":0,"return_audit_visible":0,"return_certified_visible_to_finance":0,"return_comment":null,"return_count":0,"return_initiated_time":null,"return_time":null,"returned_by":null,"sent_to_audit":1,"service":"vms-server","status":"AUDIT: PROCESSING","tax_amount":null,"tax_details":null,"tax_type":null,"timestamp":"2025-08-01 16:13:25","version":1,"voucher_id":"MISAUG0003","voucher_type":"ORIGINAL","work_started":0,"workflow_history":null,"workflow_state":"AUDIT_NEW"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Skipping field: id","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: voucher_id → voucher_id = MISAUG0003","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: date → date = AUGUST 01, 2025 AT 04:12 PM","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: claimant → claimant = derek","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: description → description = TEST","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: amount → amount = 67000.00","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: currency → currency = GHS","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: department → original_department = AUDIT","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: dispatched_by → dispatched_by = ","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: dispatch_time → dispatch_time = 2025-08-01 16:12:58","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: status → status = AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: sent_to_audit → sent_to_audit = 1","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: created_by → created_by = CHARIS","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: batch_id → batch_id = fb69625a-ca9d-479d-9a33-f52993065a95","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: received_by → received_by = EMMANUEL AMOAKOH","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: receipt_time → receipt_time = 2025-08-01 16:13:09","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: comment → comment = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: tax_type → tax_type = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: tax_details → tax_details = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: tax_amount → tax_amount = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: pre_audited_amount → pre_audited_amount = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: pre_audited_by → pre_audited_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: certified_by → certified_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: audit_dispatch_time → audit_dispatch_time = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: audit_dispatched_by → audit_dispatched_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: dispatch_to_on_department → dispatch_to_on_department = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: post_provisional_cash → post_provisional_cash = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: dispatched → dispatched = false","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: dispatch_to_audit_by → dispatch_to_audit_by = CHARIS","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: is_returned = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: return_comment = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: return_time = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: deleted → deleted = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: deletion_time → deletion_time = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: rejection_time → rejection_time = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: department_receipt_time → department_receipt_time = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: department_received_by → department_received_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: department_rejected → department_rejected = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: rejected_by → rejected_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: pending_return = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: return_initiated_time = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: reference_id → reference_id = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: idempotency_key → idempotency_key = MISSIONS-1754064774920-dutncd5lt","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: flags → flags = [object Object]","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: created_at = 2025-08-01T16:12:53.000Z","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: original_department = MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: received_by_audit → received_by_audit = 1","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: work_started → work_started = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: batch_processed = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: batch_processed_time = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: rejection_type = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: parent_voucher_id = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: version → version = 1","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: is_rejection_copy = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: rejection_workflow_stage = INITIAL","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: correction_count = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: last_corrected_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: last_correction_time = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: correction_reason = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: is_corrected = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: resubmission_count = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: original_rejection_date → original_rejection_date = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: resubmission_history = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: resubmission_badge = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: original_rejection_reason → original_rejection_reason = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: resubmission_status = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: original_rejected_by → original_rejected_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: last_resubmitted_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: last_resubmission_date → last_resubmission_date = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: resubmission_comment = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: is_resubmitted_voucher = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: workflow_state = AUDIT_NEW","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: badge_type = NONE","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: is_copy = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: workflow_history = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: rejection_audit_trail = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: resubmission_audit_trail = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: is_rejected = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: original_voucher_id = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: voucher_type = ORIGINAL","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: is_returned_copy = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: returned_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: is_resubmitted → is_resubmitted = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: is_resubmission = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: finance_received = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: resubmission_certified_visible_to_finance → resubmission_certified_visible_to_finance = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔍 VOUCHER UPDATE DEBUG: Mapped field: resubmission_tracking_visible_to_audit → resubmission_tracking_visible_to_audit = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: badge_persistence_flags = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: is_returned_voucher = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: return_count = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: last_return_date = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: return_certified_visible_to_finance = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: return_audit_visible = 0","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: original_return_reason = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: original_returned_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: last_edit_reason = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: last_edited_by = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: last_edit_time = null","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: is_on_hold = 1","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: hold_comment = TESTING THE SYSTEM","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: hold_by = EMMANUEL AMOAKOH","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"warn","message":"🔍 VOUCHER UPDATE DEBUG: Unknown field in voucher update: hold_time = 2025-08-01T16:13:25.000Z","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"🔄 Updating workflow_state to: AUDIT_NEW for voucher 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"Updating voucher 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 with query: UPDATE vouchers SET voucher_id = ?, date = ?, claimant = ?, description = ?, amount = ?, currency = ?, original_department = ?, dispatched_by = ?, dispatch_time = ?, status = ?, sent_to_audit = ?, created_by = ?, batch_id = ?, received_by = ?, receipt_time = ?, comment = ?, tax_type = ?, tax_details = ?, tax_amount = ?, pre_audited_amount = ?, pre_audited_by = ?, certified_by = ?, audit_dispatch_time = ?, audit_dispatched_by = ?, dispatch_to_on_department = ?, post_provisional_cash = ?, dispatched = ?, dispatch_to_audit_by = ?, deleted = ?, deletion_time = ?, rejection_time = ?, department_receipt_time = ?, department_received_by = ?, department_rejected = ?, rejected_by = ?, reference_id = ?, idempotency_key = ?, flags = ?, received_by_audit = ?, work_started = ?, version = ?, original_rejection_date = ?, original_rejection_reason = ?, original_rejected_by = ?, last_resubmission_date = ?, is_resubmitted = ?, resubmission_certified_visible_to_finance = ?, resubmission_tracking_visible_to_audit = ?, workflow_state = ? WHERE id = ?","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"0":"MISAUG0003","1":"AUGUST 01, 2025 AT 04:12 PM","10":1,"11":"CHARIS","12":"fb69625a-ca9d-479d-9a33-f52993065a95","13":"EMMANUEL AMOAKOH","14":"2025-08-01 16:13:09","15":"null","16":null,"17":null,"18":null,"19":null,"2":"derek","20":null,"21":null,"22":null,"23":null,"24":0,"25":0,"26":false,"27":"CHARIS","28":0,"29":null,"3":"TEST","30":null,"31":null,"32":null,"33":0,"34":null,"35":null,"36":"MISSIONS-1754064774920-dutncd5lt","37":{"isCertified":false,"isDispatched":false,"isNew":false,"isPendingDispatch":false,"isProcessing":true,"isRejected":false},"38":1,"39":0,"4":"67000.00","40":1,"41":null,"42":null,"43":null,"44":null,"45":0,"46":0,"47":0,"48":"AUDIT_NEW","49":"966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2","5":"GHS","6":"AUDIT","7":"","8":"2025-08-01 16:12:58","9":"AUDIT: PROCESSING","level":"info","message":"Update parameters:","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"Broadcasting voucher update for 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 to department AUDIT (original: AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"📢 REAL-TIME: Broadcasted to current department: AUDIT","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"✅ REAL-TIME: Successfully broadcasted voucher update for 966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 to all relevant departments","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"level":"info","message":"\u001b[0mPUT /api/vouchers/966e2ce9-93e0-47cc-9d86-8ca24ce7bdb2 \u001b[32m200\u001b[0m 83.408 ms - 2723\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:25"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:13:27.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:13:27"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:13:27"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 23.616 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:27"}
{"level":"info","message":"🔓 NAVIGATION RELEASE: EMMANUEL AMOAKOH (AUDIT) releasing lock for AUDIT-MISSIONS due to navigation","service":"vms-server","timestamp":"2025-08-01 16:13:30"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:13:40.909Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.408 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:13:40.912Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.348 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:40"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:13:40.914Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:13:40"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:13:40"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[32m200\u001b[0m 13.742 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:40"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.338 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:13:57.685Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:13:57"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:13:57"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 32.054 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:13:57"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:14:10.907Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.137 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:14:10"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:14:10.911Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.297 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:14:10"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:14:10.913Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:14:10"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:14:10"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[32m200\u001b[0m 16.184 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:14:10"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:14:27.685Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:14:27"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:14:27"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 15.536 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:14:27"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.232 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:14:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:14:40.895Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.648 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:14:40"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:14:40.902Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:14:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:14:40.906Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 1.977 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:14:40"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:14:40"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[32m200\u001b[0m 16.694 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:14:40"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:14:57.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:14:57"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:14:58"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 14.281 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:14:58"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:15:10.904Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.368 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:15:10"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:15:10.913Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:15:10"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:15:10.924Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 12.661 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:15:10"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:15:10"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[32m200\u001b[0m 34.132 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:15:10"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:15:35.695Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:15:35"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:15:35"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 16.974 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:15:35"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:15:40.899Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:15:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:15:40.903Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 6.220 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:15:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:15:40.907Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 9.795 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:15:40"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:15:40"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[32m200\u001b[0m 25.423 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:15:40"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 0.907 ms - 289\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:15:57"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:16:10.900Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.202 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:16:10"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:16:10.904Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.536 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:16:10"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:16:10.908Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:16:10"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:16:10"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[32m200\u001b[0m 48.583 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:16:10"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:16:35.689Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:16:35"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:16:35"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 12.013 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:16:35"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 1.461 ms - 290\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:16:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:16:40.891Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 2.757 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:16:40"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:16:40.897Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:16:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:16:40.900Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 5.011 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:16:40"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:16:40"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[32m200\u001b[0m 18.726 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:16:40"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:17:10.898Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.232 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:17:10"}
{"ip":"***********","level":"info","message":"API Request: HEAD /health","service":"vms-server","timestamp":"2025-08-01T16:17:10.904Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mHEAD /api/health \u001b[32m200\u001b[0m 3.107 ms - 187\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:17:10"}
{"ip":"***********","level":"info","message":"API Request: GET /users/online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01T16:17:10.908Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:17:10"}
{"level":"info","message":"Found 1 online users in MISSIONS department","service":"vms-server","timestamp":"2025-08-01 16:17:10"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=MISSIONS \u001b[32m200\u001b[0m 22.908 ms - 170\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:17:10"}
