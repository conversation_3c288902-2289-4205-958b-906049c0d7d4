{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🎯 Using forced port: 8080","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔧 Setting up Socket.IO middleware and handlers...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Starting workflow state migration...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"No vouchers need workflow state migration","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Users already exist (10 users found)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔄 Initializing workflow service...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Initializing workflow service...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔄 Initializing file storage...","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"File storage initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ File storage initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🌐 Environment: DEVELOPMENT","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📊 Process ID: 21252","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"New database connection established: 518","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database connection test successful","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database 'vms_production' verified","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Essential database tables verified","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database setup verification complete","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Health checks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"Database Manager initialized successfully","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Session cleanup scheduled (every 10 minutes)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🛑 Automated backup scheduler stopped","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🕒 Starting automated backup scheduler (every 24 hours)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"✅ Automated backup scheduler started (daily backups)","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🖥️  VMS System: http://************:8080","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🖥️  VMS System: http://10.25.41.234:8080","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🔌 WebSocket: http://10.25.41.234:8080/socket.io/","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   💾 Memory: 65MB RSS","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"   🔄 Uptime: 3s","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1754064653797,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-08-01 16:10:53"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-08-01 16:11:23"}
{"level":"info","message":"✅ No duplicate sessions found","service":"vms-server","timestamp":"2025-08-01 16:11:23"}
{"level":"info","message":"✅ All users have unique sessions (2 unique active users)","service":"vms-server","timestamp":"2025-08-01 16:11:23"}
{"level":"info","message":"✅ Initial session cleanup completed","service":"vms-server","timestamp":"2025-08-01 16:11:23"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:11:35.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:35"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 44.858 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"Socket P4wtzNXhxmc9iwMhAAAB is in rooms: P4wtzNXhxmc9iwMhAAAB, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-01 16:11:36"}
{"level":"info","message":"🔍 Checking for year rollover...","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"🗓️ Year rollover monitoring started - checking daily","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"📅 Current date: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"📊 Calendar year: 2025","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"📈 Calculated fiscal year: 2025","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"level":"info","message":"⚙️ Configured fiscal year: 2025","service":"vms-server","timestamp":"2025-08-01 16:11:52"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:11:53.468Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:53"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:11:53"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 24.450 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:53"}
{"level":"info","message":"User disconnected: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) has no remaining connections","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"\u001b[0mGET /audit-dashboard \u001b[36m304\u001b[0m 2.803 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"Cleared socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) - session remains active","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"Broadcasting 0 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"📢 Broadcasting user_left for active user: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"After disconnect - Total connected clients: 0, 0 in department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /health","service":"vms-server","timestamp":"2025-08-01T16:11:56.547Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"\u001b[0mGET /api/health \u001b[32m200\u001b[0m 3.371 ms - 186\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 3.441 ms - 288\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"🔍 MIDDLEWARE TRIGGERED: Socket.IO middleware called!","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"WebSocket connection from 127.0.0.1 - LAN mode, extracting user from session","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"🔐 WebSocket authenticated user from session: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"🔌 WebSocket connection established: EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT [Auth: YES]","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"🚀 Setting up authenticated connection for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) from AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: department:AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined room: admin-users","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) joined personal room: user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"Sent 0 active locks to client","service":"vms-server","timestamp":"2025-08-01 16:11:56"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Updated existing session with socket info for EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Total connected clients: 1, 1 in department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Socket 0oQi_0imDVyJ6fUTAAAE is in rooms: 0oQi_0imDVyJ6fUTAAAE, department:AUDIT, admin-users, user:d97a34d9-af6d-4026-a5e8-00db2d7a6a98","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /auth/me","service":"vms-server","timestamp":"2025-08-01T16:11:57.274Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /me","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/auth/me \u001b[36m304\u001b[0m 16.143 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mHEAD /health \u001b[32m200\u001b[0m 2.641 ms - 288\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"User EMMANUEL AMOAKOH (d97a34d9-af6d-4026-a5e8-00db2d7a6a98) explicitly joining department:AUDIT room","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"✅ REAL-TIME: Confirmed department join for EMMANUEL AMOAKOH in AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Connected clients after join: EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Broadcasting 1 connected users for department AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754064717313","service":"vms-server","timestamp":"2025-08-01T16:11:57.340Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754064717313","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users?_t=1754064717315","service":"vms-server","timestamp":"2025-08-01T16:11:57.348Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754064717315","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"GET /vouchers request at 2025-08-01T16:11:57.363Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754064717315 \u001b[32m200\u001b[0m 36.139 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Found 2 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Sample vouchers: MISAUG0002 (5010d7f3-60b3-4921-8613-3ebe1ff99457): AUDIT: PROCESSING, MISAUG0001 (5ca8b4ff-9693-4f09-93c0-1fcb68fd21c4): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-01 16:04:50","receivedBy":"EMMANUEL AMOAKOH","receivedByAudit":true,"service":"vms-server","status":"AUDIT: PROCESSING","timestamp":"2025-08-01 16:11:57","voucherId":"MISAUG0002"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754064717313 \u001b[32m200\u001b[0m 75.417 ms - 3265\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users?_t=1754064717393","service":"vms-server","timestamp":"2025-08-01T16:11:57.429Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?_t=1754064717393","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?department=AUDIT&timestamp=1754064717416","service":"vms-server","timestamp":"2025-08-01T16:11:57.433Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?department=AUDIT&timestamp=1754064717416","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"GET /vouchers request at 2025-08-01T16:11:57.444Z by user EMMANUEL AMOAKOH (AUDIT), query department: AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Fetching vouchers for specified department: AUDIT (NEW ARCHITECTURE)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/users?_t=1754064717393 \u001b[32m200\u001b[0m 25.689 ms - 2950\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Found 2 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Sample vouchers: MISAUG0002 (5010d7f3-60b3-4921-8613-3ebe1ff99457): AUDIT: PROCESSING, MISAUG0001 (5ca8b4ff-9693-4f09-93c0-1fcb68fd21c4): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-01 16:04:50","receivedBy":"EMMANUEL AMOAKOH","receivedByAudit":true,"service":"vms-server","status":"AUDIT: PROCESSING","timestamp":"2025-08-01 16:11:57","voucherId":"MISAUG0002"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?department=AUDIT&timestamp=1754064717416 \u001b[32m200\u001b[0m 39.328 ms - 3265\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:11:57.477Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[32m200\u001b[0m 33.779 ms - 8051\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/rollover/status","service":"vms-server","timestamp":"2025-08-01T16:11:57.524Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /rollover/status","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"⏰ Using system time override: 2025-07-12T19:48:10.089Z","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/years/rollover/status \u001b[36m304\u001b[0m 26.909 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /years/current","service":"vms-server","timestamp":"2025-08-01T16:11:57.564Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /current","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/years/current \u001b[36m304\u001b[0m 12.405 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /users/online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01T16:11:57.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /online?department=AUDIT","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /batches","service":"vms-server","timestamp":"2025-08-01T16:11:57.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"Found 1 online users in AUDIT department","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/users/online?department=AUDIT \u001b[32m200\u001b[0m 19.218 ms - 177\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"level":"info","message":"\u001b[0mGET /api/batches \u001b[36m304\u001b[0m 35.404 ms - -\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:11:57"}
{"ip":"127.0.0.1","level":"info","message":"API Request: GET /vouchers?timestamp=1754064720312","service":"vms-server","timestamp":"2025-08-01T16:12:00.315Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🔐 LAN Authentication for: GET /?timestamp=1754064720312","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"GET /vouchers request at 2025-08-01T16:12:00.325Z by user EMMANUEL AMOAKOH (AUDIT), query department: none","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"Fetching all vouchers for AUDIT user","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"Found 2 vouchers for request from EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"Sample vouchers: MISAUG0002 (5010d7f3-60b3-4921-8613-3ebe1ff99457): AUDIT: PROCESSING, MISAUG0001 (5ca8b4ff-9693-4f09-93c0-1fcb68fd21c4): AUDIT: PROCESSING","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"🔍 DEBUG - Sample voucher data:","receiptTime":"2025-08-01 16:04:50","receivedBy":"EMMANUEL AMOAKOH","receivedByAudit":true,"service":"vms-server","status":"AUDIT: PROCESSING","timestamp":"2025-08-01 16:12:00","voucherId":"MISAUG0002"}
{"level":"info","message":"\u001b[0mGET /api/vouchers?timestamp=1754064720312 \u001b[32m200\u001b[0m 17.600 ms - 3265\u001b[0m","service":"vms-server","timestamp":"2025-08-01 16:12:00"}
{"level":"info","message":"🔄 LOCK REQUEST: Re-authenticated EMMANUEL AMOAKOH (AUDIT) from current session","service":"vms-server","timestamp":"2025-08-01 16:12:02"}
{"level":"info","message":"✅ AUDIT EXCLUSIVE ACCESS: EMMANUEL AMOAKOH accessing MISSIONS voucher hub","service":"vms-server","timestamp":"2025-08-01 16:12:02"}
{"level":"info","message":"🔒 ISOLATED LOCK REQUEST: EMMANUEL AMOAKOH (AUDIT) requesting voucher hub lock: AUDIT-VOUCHER-HUB-MISSIONS","service":"vms-server","timestamp":"2025-08-01 16:12:02"}
{"level":"info","message":"🔒 ISOLATED ACCESS: VOUCHER HUB lock acquired - AUDIT-VOUCHER-HUB-MISSIONS by EMMANUEL AMOAKOH (AUDIT)","service":"vms-server","timestamp":"2025-08-01 16:12:02"}
