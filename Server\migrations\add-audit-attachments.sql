-- Migration: Add audit voucher attachments table
-- Date: 2025-07-26
-- Purpose: Enable audit users to attach scanned documents to vouchers

CREATE TABLE IF NOT EXISTS audit_voucher_attachments (
  id VARCHAR(36) PRIMARY KEY,
  voucher_id VARCHAR(36) NOT NULL,
  original_filename <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  stored_filename <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type VARCHAR(100) NOT NULL,
  uploaded_by VARCHAR(36) NOT NULL,
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (voucher_id) REFERENCES vouchers(id) ON DELETE CASCADE,
  FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE RESTRICT,
  INDEX idx_voucher_id (voucher_id),
  INDEX idx_uploaded_by (uploaded_by),
  INDEX idx_active (is_active),
  INDEX idx_uploaded_at (uploaded_at)
);

-- Add comment for documentation
ALTER TABLE audit_voucher_attachments COMMENT = 'Stores metadata for audit department voucher attachments';
