-- Migration: Add voucher hold functionality columns
-- Date: 2025-07-31
-- Description: Add columns to support ON-HOLD feature for vouchers in audit NEW VOUCHER tab

-- Add hold-related columns to vouchers table
ALTER TABLE vouchers 
ADD COLUMN is_on_hold BOOLEAN DEFAULT FALSE COMMENT 'Whether the voucher is currently on hold',
ADD COLUMN hold_comment TEXT NULL COMMENT 'Reason why the voucher is on hold',
ADD COLUMN hold_by VARCHAR(255) NULL COMMENT 'User who put the voucher on hold',
ADD COLUMN hold_time TIMESTAMP NULL COMMENT 'When the voucher was put on hold';

-- Add index for performance on hold status queries
CREATE INDEX idx_vouchers_hold_status ON vouchers(is_on_hold, department);

-- Update existing vouchers to have default hold status
UPDATE vouchers SET is_on_hold = FALSE WHERE is_on_hold IS NULL;

-- Add comment to document the new functionality
ALTER TABLE vouchers COMMENT = 'Vouchers table with hold functionality for audit workflow management';
