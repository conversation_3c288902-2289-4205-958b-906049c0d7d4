-- Performance Optimization Migration
-- Fixes critical database schema issues and adds performance indexes

-- Fix audit_logs table - add missing columns (ignore errors if columns exist)
ALTER TABLE audit_logs ADD COLUMN user_name VARCHAR(255);
ALTER TABLE audit_logs ADD COLUMN department VARCHAR(50);
ALTER TABLE audit_logs ADD COLUMN description TEXT;
ALTER TABLE audit_logs ADD COLUMN severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM';
ALTER TABLE audit_logs ADD COLUMN user_agent TEXT;
ALTER TABLE audit_logs ADD COLUMN timestamp DATETIME;

-- Fix users table - add missing column
ALTER TABLE users ADD COLUMN last_selected_year INT;

-- PERFORMANCE INDEXES: Add critical indexes for frequently queried columns
-- Note: MySQL will ignore duplicate index creation

-- Vouchers table indexes
CREATE INDEX idx_vouchers_department ON vouchers(department);
CREATE INDEX idx_vouchers_status ON vouchers(status);
CREATE INDEX idx_vouchers_created_at ON vouchers(created_at);
CREATE INDEX idx_vouchers_dept_status ON vouchers(department, status);
CREATE INDEX idx_vouchers_workflow_state ON vouchers(workflow_state);

-- Batch-related indexes
CREATE INDEX idx_batch_vouchers_batch_id ON batch_vouchers(batch_id);
CREATE INDEX idx_batch_vouchers_voucher_id ON batch_vouchers(voucher_id);
CREATE INDEX idx_voucher_batches_department ON voucher_batches(department);
CREATE INDEX idx_voucher_batches_received ON voucher_batches(received);

-- User and session indexes
CREATE INDEX idx_users_department ON users(department);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

-- Resource locks indexes (for concurrency)
CREATE INDEX idx_resource_locks_type_id ON resource_locks(resource_type, resource_id);
CREATE INDEX idx_resource_locks_user ON resource_locks(user_id);
CREATE INDEX idx_resource_locks_expiry ON resource_locks(expiry_time);

-- Audit and logging indexes
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);

-- Notifications indexes
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(is_read);
CREATE INDEX idx_notifications_timestamp ON notifications(timestamp);

-- Provisional cash records indexes
CREATE INDEX idx_provisional_cash_voucher_id ON provisional_cash_records(voucher_id);
CREATE INDEX idx_provisional_cash_date ON provisional_cash_records(date);

-- Voucher logs indexes
CREATE INDEX idx_voucher_logs_voucher_id ON voucher_logs(voucher_id);
CREATE INDEX idx_voucher_logs_created_at ON voucher_logs(created_at);

-- COMPOUND INDEXES for complex queries
CREATE INDEX idx_vouchers_dept_status_created ON vouchers(department, status, created_at);
CREATE INDEX idx_vouchers_workflow_dept ON vouchers(workflow_state, department);

-- Clean up any orphaned records that might be causing performance issues
DELETE FROM batch_vouchers WHERE voucher_id NOT IN (SELECT id FROM vouchers);
DELETE FROM batch_vouchers WHERE batch_id NOT IN (SELECT id FROM voucher_batches);

-- Update table statistics for query optimizer
ANALYZE TABLE vouchers;
ANALYZE TABLE voucher_batches;
ANALYZE TABLE batch_vouchers;
ANALYZE TABLE users;
ANALYZE TABLE audit_logs;
ANALYZE TABLE notifications;
ANALYZE TABLE provisional_cash_records;
ANALYZE TABLE resource_locks;
