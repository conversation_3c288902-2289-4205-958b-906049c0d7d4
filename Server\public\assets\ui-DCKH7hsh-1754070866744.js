import{r as e,R as t,a as n,b as r,c as o}from"./vendor-C6oTH5a1-1754070866744.js";var i={exports:{}},a={},s=e,c=Symbol.for("react.element"),l=Symbol.for("react.fragment"),u=Object.prototype.hasOwnProperty,d=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function p(e,t,n){var r,o={},i=null,a=null;for(r in void 0!==n&&(i=""+n),void 0!==t.key&&(i=""+t.key),void 0!==t.ref&&(a=t.ref),t)u.call(t,r)&&!f.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:c,type:e,key:i,ref:a,props:o,_owner:d.current}}a.Fragment=l,a.jsx=p,a.jsxs=p,i.exports=a;var m=i.exports;function h(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function v(...e){return t=>e.forEach(e=>function(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}(e,t))}function g(...t){return e.useCallback(v(...t),t)}function y(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const o=r.reduce((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]}),{});return e.useMemo(()=>({[`__scope${n.scopeName}`]:o}),[o])}};return r.scopeName=n.scopeName,r}var w=e.forwardRef((t,n)=>{const{children:r,...o}=t,i=e.Children.toArray(r),a=i.find(E);if(a){const t=a.props.children,r=i.map(n=>n===a?e.Children.count(t)>1?e.Children.only(null):e.isValidElement(t)?t.props.children:null:n);return m.jsx(x,{...o,ref:n,children:e.isValidElement(t)?e.cloneElement(t,void 0,r):null})}return m.jsx(x,{...o,ref:n,children:r})});w.displayName="Slot";var x=e.forwardRef((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(r);return e.cloneElement(r,{...S(o,r.props),ref:n?v(n,t):t})}return e.Children.count(r)>1?e.Children.only(null):null});x.displayName="SlotClone";var b=({children:e})=>m.jsx(m.Fragment,{children:e});function E(t){return e.isValidElement(t)&&t.type===b}function S(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function C(n){const r=n+"CollectionProvider",[o,i]=function(t,n=[]){let r=[];const o=()=>{const n=r.map(t=>e.createContext(t));return function(r){const o=r?.[t]||n;return e.useMemo(()=>({[`__scope${t}`]:{...r,[t]:o}}),[r,o])}};return o.scopeName=t,[function(n,o){const i=e.createContext(o),a=r.length;function s(n){const{scope:r,children:o,...s}=n,c=r?.[t][a]||i,l=e.useMemo(()=>s,Object.values(s));return m.jsx(c.Provider,{value:l,children:o})}return r=[...r,o],s.displayName=n+"Provider",[s,function(r,s){const c=s?.[t][a]||i,l=e.useContext(c);if(l)return l;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},y(o,...n)]}(r),[a,s]=o(r,{collectionRef:{current:null},itemMap:new Map}),c=e=>{const{scope:n,children:r}=e,o=t.useRef(null),i=t.useRef(new Map).current;return m.jsx(a,{scope:n,itemMap:i,collectionRef:o,children:r})};c.displayName=r;const l=n+"CollectionSlot",u=t.forwardRef((e,t)=>{const{scope:n,children:r}=e,o=g(t,s(l,n).collectionRef);return m.jsx(w,{ref:o,children:r})});u.displayName=l;const d=n+"CollectionItemSlot",f="data-radix-collection-item",p=t.forwardRef((e,n)=>{const{scope:r,children:o,...i}=e,a=t.useRef(null),c=g(n,a),l=s(d,r);return t.useEffect(()=>(l.itemMap.set(a,{ref:a,...i}),()=>{l.itemMap.delete(a)})),m.jsx(w,{[f]:"",ref:c,children:o})});return p.displayName=d,[{Provider:c,Slot:u,ItemSlot:p},function(e){const r=s(n+"CollectionConsumer",e);return t.useCallback(()=>{const e=r.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${f}]`));return Array.from(r.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[r.collectionRef,r.itemMap])},i]}function R(t,n=[]){let r=[];const o=()=>{const n=r.map(t=>e.createContext(t));return function(r){const o=r?.[t]||n;return e.useMemo(()=>({[`__scope${t}`]:{...r,[t]:o}}),[r,o])}};return o.scopeName=t,[function(n,o){const i=e.createContext(o),a=r.length;r=[...r,o];const s=n=>{const{scope:r,children:o,...s}=n,c=r?.[t]?.[a]||i,l=e.useMemo(()=>s,Object.values(s));return m.jsx(c.Provider,{value:l,children:o})};return s.displayName=n+"Provider",[s,function(r,s){const c=s?.[t]?.[a]||i,l=e.useContext(c);if(l)return l;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},P(o,...n)]}function P(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const o=r.reduce((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]}),{});return e.useMemo(()=>({[`__scope${n.scopeName}`]:o}),[o])}};return r.scopeName=n.scopeName,r}var N=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((t,n)=>{const r=e.forwardRef((e,t)=>{const{asChild:r,...o}=e,i=r?w:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),m.jsx(i,{...o,ref:t})});return r.displayName=`Primitive.${n}`,{...t,[n]:r}},{});function D(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}function O(t){const n=e.useRef(t);return e.useEffect(()=>{n.current=t}),e.useMemo(()=>(...e)=>n.current?.(...e),[])}var _,T="dismissableLayer.update",k="dismissableLayer.pointerDownOutside",A="dismissableLayer.focusOutside",j=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),L=e.forwardRef((t,n)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:a,onInteractOutside:s,onDismiss:c,...l}=t,u=e.useContext(j),[d,f]=e.useState(null),p=d?.ownerDocument??globalThis?.document,[,v]=e.useState({}),y=g(n,e=>f(e)),w=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),b=w.indexOf(x),E=d?w.indexOf(d):-1,S=u.layersWithOutsidePointerEventsDisabled.size>0,C=E>=b,R=function(t,n=globalThis?.document){const r=O(t),o=e.useRef(!1),i=e.useRef(()=>{});return e.useEffect(()=>{const e=e=>{if(e.target&&!o.current){let t=function(){F(k,r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{const t=e.target,n=[...u.branches].some(e=>e.contains(t));C&&!n&&(i?.(e),s?.(e),e.defaultPrevented||c?.())},p),P=function(t,n=globalThis?.document){const r=O(t),o=e.useRef(!1);return e.useEffect(()=>{const e=e=>{if(e.target&&!o.current){F(A,r,{originalEvent:e},{discrete:!1})}};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{const t=e.target;[...u.branches].some(e=>e.contains(t))||(a?.(e),s?.(e),e.defaultPrevented||c?.())},p);return function(t,n=globalThis?.document){const r=O(t);e.useEffect(()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})},[r,n])}(e=>{E===u.layers.size-1&&(o?.(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},p),e.useEffect(()=>{if(d)return r&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(_=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),I(),()=>{r&&1===u.layersWithOutsidePointerEventsDisabled.size&&(p.body.style.pointerEvents=_)}},[d,p,r,u]),e.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),I())},[d,u]),e.useEffect(()=>{const e=()=>v({});return document.addEventListener(T,e),()=>document.removeEventListener(T,e)},[]),m.jsx(N.div,{...l,ref:y,style:{pointerEvents:S?C?"auto":"none":void 0,...t.style},onFocusCapture:h(t.onFocusCapture,P.onFocusCapture),onBlurCapture:h(t.onBlurCapture,P.onBlurCapture),onPointerDownCapture:h(t.onPointerDownCapture,R.onPointerDownCapture)})});L.displayName="DismissableLayer";var M=e.forwardRef((t,n)=>{const r=e.useContext(j),o=e.useRef(null),i=g(n,o);return e.useEffect(()=>{const e=o.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),m.jsx(N.div,{...t,ref:i})});function I(){const e=new CustomEvent(T);document.dispatchEvent(e)}function F(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?D(o,i):o.dispatchEvent(i)}M.displayName="DismissableLayerBranch";var W=L,H=M,B=Boolean(globalThis?.document)?e.useLayoutEffect:()=>{},$=e.forwardRef((t,n)=>{const{container:o,...i}=t,[a,s]=e.useState(!1);B(()=>s(!0),[]);const c=o||a&&globalThis?.document?.body;return c?r.createPortal(m.jsx(N.div,{...i,ref:n}),c):null});$.displayName="Portal";var V=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef({}),i=e.useRef(t),a=e.useRef("none"),s=t?"mounted":"unmounted",[c,l]=function(t,n){return e.useReducer((e,t)=>n[e][t]??e,t)}(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect(()=>{const e=K(o.current);a.current="mounted"===c?e:"none"},[c]),B(()=>{const e=o.current,n=i.current;if(n!==t){const r=a.current,o=K(e);if(t)l("MOUNT");else if("none"===o||"none"===e?.display)l("UNMOUNT");else{l(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}i.current=t}},[t,l]),B(()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const a=K(o.current).includes(r.animationName);if(r.target===n&&a&&(l("ANIMATION_END"),!i.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},s=e=>{e.target===n&&(a.current=K(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}l("ANIMATION_END")},[n,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:e.useCallback(e=>{e&&(o.current=getComputedStyle(e)),r(e)},[])}}(n),i="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),a=g(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?e.cloneElement(i,{ref:a}):null};function K(e){return e?.animationName||"none"}function z({prop:t,defaultProp:n,onChange:r=()=>{}}){const[o,i]=function({defaultProp:t,onChange:n}){const r=e.useState(t),[o]=r,i=e.useRef(o),a=O(n);return e.useEffect(()=>{i.current!==o&&(a(o),i.current=o)},[o,i,a]),r}({defaultProp:n,onChange:r}),a=void 0!==t,s=a?t:o,c=O(r);return[s,e.useCallback(e=>{if(a){const n="function"==typeof e?e(t):e;n!==t&&c(n)}else i(e)},[a,t,i,c])]}V.displayName="Presence";var U=e.forwardRef((e,t)=>m.jsx(N.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));U.displayName="VisuallyHidden";var Y=U,q=o["useId".toString()]||(()=>{}),X=0;function Z(t){const[n,r]=e.useState(q());return B(()=>{r(e=>e??String(X++))},[t]),n?`radix-${n}`:""}const G=["top","right","bottom","left"],J=Math.min,Q=Math.max,ee=Math.round,te=Math.floor,ne=e=>({x:e,y:e}),re={left:"right",right:"left",bottom:"top",top:"bottom"},oe={start:"end",end:"start"};function ie(e,t,n){return Q(e,J(t,n))}function ae(e,t){return"function"==typeof e?e(t):e}function se(e){return e.split("-")[0]}function ce(e){return e.split("-")[1]}function le(e){return"x"===e?"y":"x"}function ue(e){return"y"===e?"height":"width"}function de(e){return["top","bottom"].includes(se(e))?"y":"x"}function fe(e){return le(de(e))}function pe(e){return e.replace(/start|end/g,e=>oe[e])}function me(e){return e.replace(/left|right|bottom|top/g,e=>re[e])}function he(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function ve(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ge(e,t,n){let{reference:r,floating:o}=e;const i=de(t),a=fe(t),s=ue(a),c=se(t),l="y"===i,u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[s]/2-o[s]/2;let p;switch(c){case"top":p={x:u,y:r.y-o.height};break;case"bottom":p={x:u,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(ce(t)){case"start":p[a]-=f*(n&&l?-1:1);break;case"end":p[a]+=f*(n&&l?-1:1)}return p}async function ye(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:a,elements:s,strategy:c}=e,{boundary:l="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=ae(t,e),m=he(p),h=s[f?"floating"===d?"reference":"floating":d],v=ve(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:l,rootBoundary:u,strategy:c})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await(null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),w=await(null==i.isElement?void 0:i.isElement(y))&&await(null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=ve(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:g,offsetParent:y,strategy:c}):g);return{top:(v.top-x.top+m.top)/w.y,bottom:(x.bottom-v.bottom+m.bottom)/w.y,left:(v.left-x.left+m.left)/w.x,right:(x.right-v.right+m.right)/w.x}}function we(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function xe(e){return G.some(t=>e[t]>=0)}function be(){return"undefined"!=typeof window}function Ee(e){return Re(e)?(e.nodeName||"").toLowerCase():"#document"}function Se(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function Ce(e){var t;return null==(t=(Re(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Re(e){return!!be()&&(e instanceof Node||e instanceof Se(e).Node)}function Pe(e){return!!be()&&(e instanceof Element||e instanceof Se(e).Element)}function Ne(e){return!!be()&&(e instanceof HTMLElement||e instanceof Se(e).HTMLElement)}function De(e){return!(!be()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof Se(e).ShadowRoot)}function Oe(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Le(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function _e(e){return["table","td","th"].includes(Ee(e))}function Te(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(n){return!1}})}function ke(e){const t=Ae(),n=Pe(e)?Le(e):e;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function Ae(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function je(e){return["html","body","#document"].includes(Ee(e))}function Le(e){return Se(e).getComputedStyle(e)}function Me(e){return Pe(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Ie(e){if("html"===Ee(e))return e;const t=e.assignedSlot||e.parentNode||De(e)&&e.host||Ce(e);return De(t)?t.host:t}function Fe(e){const t=Ie(e);return je(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ne(t)&&Oe(t)?t:Fe(t)}function We(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=Fe(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=Se(o);if(i){const e=He(a);return t.concat(a,a.visualViewport||[],Oe(o)?o:[],e&&n?We(e):[])}return t.concat(o,We(o,[],n))}function He(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Be(e){const t=Le(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Ne(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,s=ee(n)!==i||ee(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function $e(e){return Pe(e)?e:e.contextElement}function Ve(e){const t=$e(e);if(!Ne(t))return ne(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Be(t);let a=(i?ee(n.width):n.width)/r,s=(i?ee(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}const Ke=ne(0);function ze(e){const t=Se(e);return Ae()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Ke}function Ue(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=$e(e);let a=ne(1);t&&(r?Pe(r)&&(a=Ve(r)):a=Ve(e));const s=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==Se(e))&&t}(i,n,r)?ze(i):ne(0);let c=(o.left+s.x)/a.x,l=(o.top+s.y)/a.y,u=o.width/a.x,d=o.height/a.y;if(i){const e=Se(i),t=r&&Pe(r)?Se(r):r;let n=e,o=He(n);for(;o&&r&&t!==n;){const e=Ve(o),t=o.getBoundingClientRect(),r=Le(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,l*=e.y,u*=e.x,d*=e.y,c+=i,l+=a,n=Se(o),o=He(n)}}return ve({width:u,height:d,x:c,y:l})}function Ye(e,t){const n=Me(e).scrollLeft;return t?t.left+n:Ue(Ce(e)).left+n}function qe(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=Se(e),r=Ce(e),o=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,s=0,c=0;if(o){i=o.width,a=o.height;const e=Ae();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,c=o.offsetTop)}return{width:i,height:a,x:s,y:c}}(e,n);else if("document"===t)r=function(e){const t=Ce(e),n=Me(e),r=e.ownerDocument.body,o=Q(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Q(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+Ye(e);const s=-n.scrollTop;return"rtl"===Le(r).direction&&(a+=Q(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}}(Ce(e));else if(Pe(t))r=function(e,t){const n=Ue(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Ne(e)?Ve(e):ne(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{const n=ze(e);r={...t,x:t.x-n.x,y:t.y-n.y}}return ve(r)}function Xe(e,t){const n=Ie(e);return!(n===t||!Pe(n)||je(n))&&("fixed"===Le(n).position||Xe(n,t))}function Ze(e,t,n){const r=Ne(t),o=Ce(t),i="fixed"===n,a=Ue(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const c=ne(0);if(r||!r&&!i)if(("body"!==Ee(t)||Oe(o))&&(s=Me(t)),r){const e=Ue(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=Ye(o));let l=0,u=0;if(o&&!r&&!i){const e=o.getBoundingClientRect();u=e.top+s.scrollTop,l=e.left+s.scrollLeft-Ye(o,e)}return{x:a.left+s.scrollLeft-c.x-l,y:a.top+s.scrollTop-c.y-u,width:a.width,height:a.height}}function Ge(e){return"static"===Le(e).position}function Je(e,t){if(!Ne(e)||"fixed"===Le(e).position)return null;if(t)return t(e);let n=e.offsetParent;return Ce(e)===n&&(n=n.ownerDocument.body),n}function Qe(e,t){const n=Se(e);if(Te(e))return n;if(!Ne(e)){let t=Ie(e);for(;t&&!je(t);){if(Pe(t)&&!Ge(t))return t;t=Ie(t)}return n}let r=Je(e,t);for(;r&&_e(r)&&Ge(r);)r=Je(r,t);return r&&je(r)&&Ge(r)&&!ke(r)?n:r||function(e){let t=Ie(e);for(;Ne(t)&&!je(t);){if(ke(t))return t;if(Te(t))return null;t=Ie(t)}return null}(e)||n}const et={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,a=Ce(r),s=!!t&&Te(t.floating);if(r===a||s&&i)return n;let c={scrollLeft:0,scrollTop:0},l=ne(1);const u=ne(0),d=Ne(r);if((d||!d&&!i)&&(("body"!==Ee(r)||Oe(a))&&(c=Me(r)),Ne(r))){const e=Ue(r);l=Ve(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-c.scrollLeft*l.x+u.x,y:n.y*l.y-c.scrollTop*l.y+u.y}},getDocumentElement:Ce,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[..."clippingAncestors"===n?Te(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=We(e,[],!1).filter(e=>Pe(e)&&"body"!==Ee(e)),o=null;const i="fixed"===Le(e).position;let a=i?Ie(e):e;for(;Pe(a)&&!je(a);){const t=Le(a),n=ke(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||Oe(a)&&!n&&Xe(e,a))?r=r.filter(e=>e!==a):o=t,a=Ie(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],s=i.reduce((e,n)=>{const r=qe(t,n,o);return e.top=Q(r.top,e.top),e.right=J(r.right,e.right),e.bottom=J(r.bottom,e.bottom),e.left=Q(r.left,e.left),e},qe(t,a,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:Qe,getElementRects:async function(e){const t=this.getOffsetParent||Qe,n=this.getDimensions,r=await n(e.floating);return{reference:Ze(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=Be(e);return{width:t,height:n}},getScale:Ve,isElement:Pe,isRTL:function(e){return"rtl"===Le(e).direction}};function tt(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,l=$e(e),u=o||i?[...l?We(l):[],...We(t)]:[];u.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});const d=l&&s?function(e,t){let n,r=null;const o=Ce(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function a(s,c){void 0===s&&(s=!1),void 0===c&&(c=1),i();const{left:l,top:u,width:d,height:f}=e.getBoundingClientRect();if(s||t(),!d||!f)return;const p={rootMargin:-te(u)+"px "+-te(o.clientWidth-(l+d))+"px "+-te(o.clientHeight-(u+f))+"px "+-te(l)+"px",threshold:Q(0,J(1,c))||1};let m=!0;function h(e){const t=e[0].intersectionRatio;if(t!==c){if(!m)return a();t?a(!1,t):n=setTimeout(()=>{a(!1,1e-7)},1e3)}m=!1}try{r=new IntersectionObserver(h,{...p,root:o.ownerDocument})}catch(v){r=new IntersectionObserver(h,p)}r.observe(e)}(!0),i}(l,n):null;let f,p=-1,m=null;a&&(m=new ResizeObserver(e=>{let[r]=e;r&&r.target===l&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),n()}),l&&!c&&m.observe(l),m.observe(t));let h=c?Ue(e):null;return c&&function t(){const r=Ue(e);!h||r.x===h.x&&r.y===h.y&&r.width===h.width&&r.height===h.height||n();h=r,f=requestAnimationFrame(t)}(),n(),()=>{var e;u.forEach(e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=m)||e.disconnect(),m=null,c&&cancelAnimationFrame(f)}}const nt=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:a,middlewareData:s}=t,c=await async function(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),a=se(n),s=ce(n),c="y"===de(n),l=["left","top"].includes(a)?-1:1,u=i&&c?-1:1,d=ae(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof m&&(p="end"===s?-1*m:m),c?{x:p*u,y:f*l}:{x:f*l,y:p*u}}(t,e);return a===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:a}}}}},rt=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=ae(e,t),l={x:n,y:r},u=await ye(t,c),d=de(se(o)),f=le(d);let p=l[f],m=l[d];if(i){const e="y"===f?"bottom":"right";p=ie(p+u["y"===f?"top":"left"],p,p-u[e])}if(a){const e="y"===d?"bottom":"right";m=ie(m+u["y"===d?"top":"left"],m,m-u[e])}const h=s.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:i,[d]:a}}}}}},ot=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:a,initialPlacement:s,platform:c,elements:l}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:h=!0,...v}=ae(e,t);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const g=se(o),y=de(s),w=se(s)===s,x=await(null==c.isRTL?void 0:c.isRTL(l.floating)),b=f||(w||!h?[me(s)]:function(e){const t=me(e);return[pe(e),t,pe(t)]}(s)),E="none"!==m;!f&&E&&b.push(...function(e,t,n,r){const o=ce(e);let i=function(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:a;default:return[]}}(se(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(pe)))),i}(s,h,m,x));const S=[s,...b],C=await ye(t,v),R=[];let P=(null==(r=i.flip)?void 0:r.overflows)||[];if(u&&R.push(C[g]),d){const e=function(e,t,n){void 0===n&&(n=!1);const r=ce(e),o=fe(e),i=ue(o);let a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=me(a)),[a,me(a)]}(o,a,x);R.push(C[e[0]],C[e[1]])}if(P=[...P,{placement:o,overflows:R}],!R.every(e=>e<=0)){var N,D;const e=((null==(N=i.flip)?void 0:N.index)||0)+1,t=S[e];if(t)return{data:{index:e,overflows:P},reset:{placement:t}};let n=null==(D=P.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:D.placement;if(!n)switch(p){case"bestFit":{var O;const e=null==(O=P.filter(e=>{if(E){const t=de(e.placement);return t===y||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:O[0];e&&(n=e);break}case"initialPlacement":n=s}if(o!==n)return{reset:{placement:n}}}return{}}}},it=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:a,elements:s}=t,{apply:c=()=>{},...l}=ae(e,t),u=await ye(t,l),d=se(o),f=ce(o),p="y"===de(o),{width:m,height:h}=i.floating;let v,g;"top"===d||"bottom"===d?(v=d,g=f===(await(null==a.isRTL?void 0:a.isRTL(s.floating))?"start":"end")?"left":"right"):(g=d,v="end"===f?"top":"bottom");const y=h-u.top-u.bottom,w=m-u.left-u.right,x=J(h-u[v],y),b=J(m-u[g],w),E=!t.middlewareData.shift;let S=x,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!f){const e=Q(u.left,0),t=Q(u.right,0),n=Q(u.top,0),r=Q(u.bottom,0);p?C=m-2*(0!==e||0!==t?e+t:Q(u.left,u.right)):S=h-2*(0!==n||0!==r?n+r:Q(u.top,u.bottom))}await c({...t,availableWidth:C,availableHeight:S});const R=await a.getDimensions(s.floating);return m!==R.width||h!==R.height?{reset:{rects:!0}}:{}}}},at=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ae(e,t);switch(r){case"referenceHidden":{const e=we(await ye(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:xe(e)}}}case"escaped":{const e=we(await ye(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:xe(e)}}}default:return{}}}}},st=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:a,elements:s,middlewareData:c}=t,{element:l,padding:u=0}=ae(e,t)||{};if(null==l)return{};const d=he(u),f={x:n,y:r},p=fe(o),m=ue(p),h=await a.getDimensions(l),v="y"===p,g=v?"top":"left",y=v?"bottom":"right",w=v?"clientHeight":"clientWidth",x=i.reference[m]+i.reference[p]-f[p]-i.floating[m],b=f[p]-i.reference[p],E=await(null==a.getOffsetParent?void 0:a.getOffsetParent(l));let S=E?E[w]:0;S&&await(null==a.isElement?void 0:a.isElement(E))||(S=s.floating[w]||i.floating[m]);const C=x/2-b/2,R=S/2-h[m]/2-1,P=J(d[g],R),N=J(d[y],R),D=P,O=S-h[m]-N,_=S/2-h[m]/2+C,T=ie(D,_,O),k=!c.arrow&&null!=ce(o)&&_!==T&&i.reference[m]/2-(_<D?P:N)-h[m]/2<0,A=k?_<D?_-D:_-O:0;return{[p]:f[p]+A,data:{[p]:T,centerOffset:_-T-A,...k&&{alignmentOffset:A}},reset:k}}}),ct=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:s=0,mainAxis:c=!0,crossAxis:l=!0}=ae(e,t),u={x:n,y:r},d=de(o),f=le(d);let p=u[f],m=u[d];const h=ae(s,t),v="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(c){const e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(l){var g,y;const e="y"===f?"width":"height",t=["top","left"].includes(se(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[f]:p,[d]:m}}}},lt=(e,t,n)=>{const r=new Map,o={platform:et,...n},i={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,s=i.filter(Boolean),c=await(null==a.isRTL?void 0:a.isRTL(t));let l=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=ge(l,r,c),f=r,p={},m=0;for(let h=0;h<s.length;h++){const{name:n,fn:i}=s[h],{x:v,y:g,data:y,reset:w}=await i({x:u,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:l,platform:a,elements:{reference:e,floating:t}});u=null!=v?v:u,d=null!=g?g:d,p={...p,[n]:{...p[n],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(l=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),({x:u,y:d}=ge(l,f,c))),h=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}})(e,t,{...o,platform:i})};var ut="undefined"!=typeof document?e.useLayoutEffect:e.useEffect;function dt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!dt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!dt(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ft(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function pt(e,t){const n=ft(e);return Math.round(t*n)/n}function mt(t){const n=e.useRef(t);return ut(()=>{n.current=t}),n}const ht=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?st({element:n.current,padding:r}).fn(t):{}:n?st({element:n,padding:r}).fn(t):{};var o}}),vt=(e,t)=>({...nt(e),options:[e,t]}),gt=(e,t)=>({...rt(e),options:[e,t]}),yt=(e,t)=>({...ct(e),options:[e,t]}),wt=(e,t)=>({...ot(e),options:[e,t]}),xt=(e,t)=>({...it(e),options:[e,t]}),bt=(e,t)=>({...at(e),options:[e,t]}),Et=(e,t)=>({...ht(e),options:[e,t]});var St=e.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return m.jsx(N.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:m.jsx("polygon",{points:"0,0 30,0 15,10"})})});St.displayName="Arrow";var Ct=St;function Rt(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){const o=r.reduce((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]}),{});return e.useMemo(()=>({[`__scope${n.scopeName}`]:o}),[o])}};return r.scopeName=n.scopeName,r}function Pt(t){const[n,r]=e.useState(void 0);return B(()=>{if(t){r({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver(e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let o,i;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,i=t.blockSize}else o=t.offsetWidth,i=t.offsetHeight;r({width:o,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}r(void 0)},[t]),n}var Nt="Popper",[Dt,Ot]=function(t,n=[]){let r=[];const o=()=>{const n=r.map(t=>e.createContext(t));return function(r){const o=r?.[t]||n;return e.useMemo(()=>({[`__scope${t}`]:{...r,[t]:o}}),[r,o])}};return o.scopeName=t,[function(n,o){const i=e.createContext(o),a=r.length;function s(n){const{scope:r,children:o,...s}=n,c=r?.[t][a]||i,l=e.useMemo(()=>s,Object.values(s));return m.jsx(c.Provider,{value:l,children:o})}return r=[...r,o],s.displayName=n+"Provider",[s,function(r,s){const c=s?.[t][a]||i,l=e.useContext(c);if(l)return l;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},Rt(o,...n)]}(Nt),[_t,Tt]=Dt(Nt),kt=t=>{const{__scopePopper:n,children:r}=t,[o,i]=e.useState(null);return m.jsx(_t,{scope:n,anchor:o,onAnchorChange:i,children:r})};kt.displayName=Nt;var At="PopperAnchor",jt=e.forwardRef((t,n)=>{const{__scopePopper:r,virtualRef:o,...i}=t,a=Tt(At,r),s=e.useRef(null),c=g(n,s);return e.useEffect(()=>{a.onAnchorChange(o?.current||s.current)}),o?null:m.jsx(N.div,{...i,ref:c})});jt.displayName=At;var Lt="PopperContent",[Mt,It]=Dt(Lt),Ft=e.forwardRef((t,r)=>{const{__scopePopper:o,side:i="bottom",sideOffset:a=0,align:s="center",alignOffset:c=0,arrowPadding:l=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:h=!1,updatePositionStrategy:v="optimized",onPlaced:y,...w}=t,x=Tt(Lt,o),[b,E]=e.useState(null),S=g(r,e=>E(e)),[C,R]=e.useState(null),P=Pt(C),D=P?.width??0,_=P?.height??0,T=i+("center"!==s?"-"+s:""),k="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},A=Array.isArray(d)?d:[d],j=A.length>0,L={padding:k,boundary:A.filter($t),altBoundary:j},{refs:M,floatingStyles:I,placement:F,isPositioned:W,middlewareData:H}=function(t){void 0===t&&(t={});const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a,elements:{reference:s,floating:c}={},transform:l=!0,whileElementsMounted:u,open:d}=t,[f,p]=e.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),[m,h]=e.useState(i);dt(m,i)||h(i);const[v,g]=e.useState(null),[y,w]=e.useState(null),x=e.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),b=e.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=s||v,S=c||y,C=e.useRef(null),R=e.useRef(null),P=e.useRef(f),N=null!=u,D=mt(u),O=mt(a),_=mt(d),T=e.useCallback(()=>{if(!C.current||!R.current)return;const e={placement:r,strategy:o,middleware:m};O.current&&(e.platform=O.current),lt(C.current,R.current,e).then(e=>{const t={...e,isPositioned:!1!==_.current};k.current&&!dt(P.current,t)&&(P.current=t,n.flushSync(()=>{p(t)}))})},[m,r,o,O,_]);ut(()=>{!1===d&&P.current.isPositioned&&(P.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);const k=e.useRef(!1);ut(()=>(k.current=!0,()=>{k.current=!1}),[]),ut(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(D.current)return D.current(E,S,T);T()}},[E,S,T,D,N]);const A=e.useMemo(()=>({reference:C,floating:R,setReference:x,setFloating:b}),[x,b]),j=e.useMemo(()=>({reference:E,floating:S}),[E,S]),L=e.useMemo(()=>{const e={position:o,left:0,top:0};if(!j.floating)return e;const t=pt(j.floating,f.x),n=pt(j.floating,f.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...ft(j.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:t,top:n}},[o,l,j.floating,f.x,f.y]);return e.useMemo(()=>({...f,update:T,refs:A,elements:j,floatingStyles:L}),[f,T,A,j,L])}({strategy:"fixed",placement:T,whileElementsMounted:(...e)=>tt(...e,{animationFrame:"always"===v}),elements:{reference:x.anchor},middleware:[vt({mainAxis:a+_,alignmentAxis:c}),u&&gt({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?yt():void 0,...L}),u&&wt({...L}),xt({...L,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),C&&Et({element:C,padding:l}),Vt({arrowWidth:D,arrowHeight:_}),h&&bt({strategy:"referenceHidden",...L})]}),[$,V]=Kt(F),K=O(y);B(()=>{W&&K?.()},[W,K]);const z=H.arrow?.x,U=H.arrow?.y,Y=0!==H.arrow?.centerOffset,[q,X]=e.useState();return B(()=>{b&&X(window.getComputedStyle(b).zIndex)},[b]),m.jsx("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:W?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[H.transformOrigin?.x,H.transformOrigin?.y].join(" "),...H.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:m.jsx(Mt,{scope:o,placedSide:$,onArrowChange:R,arrowX:z,arrowY:U,shouldHideArrow:Y,children:m.jsx(N.div,{"data-side":$,"data-align":V,...w,ref:S,style:{...w.style,animation:W?void 0:"none"}})})})});Ft.displayName=Lt;var Wt="PopperArrow",Ht={top:"bottom",right:"left",bottom:"top",left:"right"},Bt=e.forwardRef(function(e,t){const{__scopePopper:n,...r}=e,o=It(Wt,n),i=Ht[o.placedSide];return m.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:m.jsx(Ct,{...r,ref:t,style:{...r.style,display:"block"}})})});function $t(e){return null!==e}Bt.displayName=Wt;var Vt=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,i=0!==o.arrow?.centerOffset,a=i?0:e.arrowWidth,s=i?0:e.arrowHeight,[c,l]=Kt(n),u={start:"0%",center:"50%",end:"100%"}[l],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+s/2;let p="",m="";return"bottom"===c?(p=i?u:`${d}px`,m=-s+"px"):"top"===c?(p=i?u:`${d}px`,m=`${r.floating.height+s}px`):"right"===c?(p=-s+"px",m=i?u:`${f}px`):"left"===c&&(p=`${r.floating.width+s}px`,m=i?u:`${f}px`),{data:{x:p,y:m}}}});function Kt(e){const[t,n="center"]=e.split("-");return[t,n]}var zt=kt,Ut=jt,Yt=Ft,qt=Bt;function Xt(e,[t,n]){return Math.min(n,Math.max(t,e))}var Zt=e.createContext(void 0);function Gt(t){const n=e.useContext(Zt);return t||n||"ltr"}var Jt=0;function Qt(){e.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??en()),document.body.insertAdjacentElement("beforeend",e[1]??en()),Jt++,()=>{1===Jt&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),Jt--}},[])}function en(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var tn="focusScope.autoFocusOnMount",nn="focusScope.autoFocusOnUnmount",rn={bubbles:!1,cancelable:!0},on=e.forwardRef((t,n)=>{const{loop:r=!1,trapped:o=!1,onMountAutoFocus:i,onUnmountAutoFocus:a,...s}=t,[c,l]=e.useState(null),u=O(i),d=O(a),f=e.useRef(null),p=g(n,e=>l(e)),h=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect(()=>{if(o){let e=function(e){if(h.paused||!c)return;const t=e.target;c.contains(t)?f.current=t:ln(f.current,{select:!0})},t=function(e){if(h.paused||!c)return;const t=e.relatedTarget;null!==t&&(c.contains(t)||ln(f.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&ln(c)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return c&&r.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[o,c,h.paused]),e.useEffect(()=>{if(c){un.add(h);const t=document.activeElement;if(!c.contains(t)){const n=new CustomEvent(tn,rn);c.addEventListener(tn,u),c.dispatchEvent(n),n.defaultPrevented||(!function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ln(r,{select:t}),document.activeElement!==n)return}((e=an(c),e.filter(e=>"A"!==e.tagName)),{select:!0}),document.activeElement===t&&ln(c))}return()=>{c.removeEventListener(tn,u),setTimeout(()=>{const e=new CustomEvent(nn,rn);c.addEventListener(nn,d),c.dispatchEvent(e),e.defaultPrevented||ln(t??document.body,{select:!0}),c.removeEventListener(nn,d),un.remove(h)},0)}}var e},[c,u,d,h]);const v=e.useCallback(e=>{if(!r&&!o)return;if(h.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){const t=e.currentTarget,[o,i]=function(e){const t=an(e),n=sn(t,e),r=sn(t.reverse(),e);return[n,r]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&ln(i,{select:!0})):(e.preventDefault(),r&&ln(o,{select:!0})):n===t&&e.preventDefault()}},[r,o,h.paused]);return m.jsx(N.div,{tabIndex:-1,...s,ref:p,onKeyDown:v})});function an(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function sn(e,t){for(const n of e)if(!cn(n,{upTo:t}))return n}function cn(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function ln(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}on.displayName="FocusScope";var un=function(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=dn(e,t),e.unshift(t)},remove(t){e=dn(e,t),e[0]?.resume()}}}();function dn(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}function fn(t){const n=e.useRef({value:t,previous:t});return e.useMemo(()=>(n.current.value!==t&&(n.current.previous=n.current.value,n.current.value=t),n.current.previous),[t])}var pn=new WeakMap,mn=new WeakMap,hn={},vn=0,gn=function(e){return e&&(e.host||gn(e.parentNode))},yn=function(e,t,n,r){var o=function(e,t){return t.map(function(t){if(e.contains(t))return t;var n=gn(t);return n&&e.contains(n)?n:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)}).filter(function(e){return Boolean(e)})}(t,Array.isArray(e)?e:[e]);hn[n]||(hn[n]=new WeakMap);var i=hn[n],a=[],s=new Set,c=new Set(o),l=function(e){e&&!s.has(e)&&(s.add(e),l(e.parentNode))};o.forEach(l);var u=function(e){e&&!c.has(e)&&Array.prototype.forEach.call(e.children,function(e){if(s.has(e))u(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,c=(pn.get(e)||0)+1,l=(i.get(e)||0)+1;pn.set(e,c),i.set(e,l),a.push(e),1===c&&o&&mn.set(e,!0),1===l&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(d){console.error("aria-hidden: cannot operate on ",e,d)}})};return u(t),s.clear(),vn++,function(){a.forEach(function(e){var t=pn.get(e)-1,o=i.get(e)-1;pn.set(e,t),i.set(e,o),t||(mn.has(e)||e.removeAttribute(r),mn.delete(e)),o||e.removeAttribute(n)}),--vn||(pn=new WeakMap,pn=new WeakMap,mn=new WeakMap,hn={})}},wn=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),yn(r,o,n,"aria-hidden")):function(){return null}},xn=function(){return xn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},xn.apply(this,arguments)};function bn(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}"function"==typeof SuppressedError&&SuppressedError;var En="right-scroll-bar-position",Sn="width-before-scroll-bar";function Cn(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var Rn="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,Pn=new WeakMap;function Nn(t,n){var r,o,i,a=(r=null,o=function(e){return t.forEach(function(t){return Cn(t,e)})},(i=e.useState(function(){return{value:r,callback:o,facade:{get current(){return i.value},set current(e){var t=i.value;t!==e&&(i.value=e,i.callback(e,t))}}}})[0]).callback=o,i.facade);return Rn(function(){var e=Pn.get(a);if(e){var n=new Set(e),r=new Set(t),o=a.current;n.forEach(function(e){r.has(e)||Cn(e,null)}),r.forEach(function(e){n.has(e)||Cn(e,o)})}Pn.set(a,t)},[t]),a}function Dn(e){return e}var On=function(t){var n=t.sideCar,r=bn(t,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=n.read();if(!o)throw new Error("Sidecar medium not found");return e.createElement(o,xn({},r))};On.isSideCarExport=!0;var _n=function(e){void 0===e&&(e={});var t=function(e,t){void 0===t&&(t=Dn);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}}}(null);return t.options=xn({async:!0,ssr:!1},e),t}(),Tn=function(){},kn=e.forwardRef(function(t,n){var r=e.useRef(null),o=e.useState({onScrollCapture:Tn,onWheelCapture:Tn,onTouchMoveCapture:Tn}),i=o[0],a=o[1],s=t.forwardProps,c=t.children,l=t.className,u=t.removeScrollBar,d=t.enabled,f=t.shards,p=t.sideCar,m=t.noIsolation,h=t.inert,v=t.allowPinchZoom,g=t.as,y=void 0===g?"div":g,w=t.gapMode,x=bn(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),b=p,E=Nn([r,n]),S=xn(xn({},x),i);return e.createElement(e.Fragment,null,d&&e.createElement(b,{sideCar:_n,removeScrollBar:u,shards:f,noIsolation:m,inert:h,setCallbacks:a,allowPinchZoom:!!v,lockRef:r,gapMode:w}),s?e.cloneElement(e.Children.only(c),xn(xn({},S),{ref:E})):e.createElement(y,xn({},S,{className:l,ref:E}),c))});kn.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},kn.classNames={fullWidth:Sn,zeroRight:En};function An(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return t&&e.setAttribute("nonce",t),e}var jn=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=An())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Ln=function(){var t,n=(t=jn(),function(n,r){e.useEffect(function(){return t.add(n),function(){t.remove()}},[n&&r])});return function(e){var t=e.styles,r=e.dynamic;return n(t,r),null}},Mn={left:0,top:0,right:0,gap:0},In=function(e){return parseInt(e||"",10)||0},Fn=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Mn;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[In(n),In(r),In(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Wn=Ln(),Hn="data-scroll-locked",Bn=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(Hn,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(En," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(Sn," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(En," .").concat(En," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(Sn," .").concat(Sn," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(Hn,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},$n=function(){var e=parseInt(document.body.getAttribute(Hn)||"0",10);return isFinite(e)?e:0},Vn=function(t){var n=t.noRelative,r=t.noImportant,o=t.gapMode,i=void 0===o?"margin":o;e.useEffect(function(){return document.body.setAttribute(Hn,($n()+1).toString()),function(){var e=$n()-1;e<=0?document.body.removeAttribute(Hn):document.body.setAttribute(Hn,e.toString())}},[]);var a=e.useMemo(function(){return Fn(i)},[i]);return e.createElement(Wn,{styles:Bn(a,!n,i,r?"":"!important")})},Kn=!1;if("undefined"!=typeof window)try{var zn=Object.defineProperty({},"passive",{get:function(){return Kn=!0,!0}});window.addEventListener("test",zn,zn),window.removeEventListener("test",zn,zn)}catch(Si){Kn=!1}var Un=!!Kn&&{passive:!1},Yn=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},qn=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),Xn(e,r)){var o=Zn(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Xn=function(e,t){return"v"===e?function(e){return Yn(e,"overflowY")}(t):function(e){return Yn(e,"overflowX")}(t)},Zn=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},Gn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Jn=function(e){return[e.deltaX,e.deltaY]},Qn=function(e){return e&&"current"in e?e.current:e},er=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},tr=0,nr=[];function rr(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const or=(ir=function(t){var n=e.useRef([]),r=e.useRef([0,0]),o=e.useRef(),i=e.useState(tr++)[0],a=e.useState(Ln)[0],s=e.useRef(t);e.useEffect(function(){s.current=t},[t]),e.useEffect(function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(i));var e=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([t.lockRef.current],(t.shards||[]).map(Qn),!0).filter(Boolean);return e.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),e.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[t.inert,t.lockRef.current,t.shards]);var c=e.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var n,i=Gn(e),a=r.current,c="deltaX"in e?e.deltaX:a[0]-i[0],l="deltaY"in e?e.deltaY:a[1]-i[1],u=e.target,d=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=qn(d,u);if(!f)return!0;if(f?n=d:(n="v"===d?"h":"v",f=qn(d,u)),!f)return!1;if(!o.current&&"changedTouches"in e&&(c||l)&&(o.current=n),!n)return!0;var p=o.current||n;return function(e,t,n,r,o){var i=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),a=i*r,s=n.target,c=t.contains(s),l=!1,u=a>0,d=0,f=0;do{var p=Zn(e,s),m=p[0],h=p[1]-p[2]-i*m;(m||h)&&Xn(e,s)&&(d+=h,f+=m),s=s instanceof ShadowRoot?s.host:s.parentNode}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return u&&(Math.abs(d)<1||!o)?l=!0:u||!(Math.abs(f)<1)&&o||(l=!0),l}(p,t,e,"h"===p?c:l,!0)},[]),l=e.useCallback(function(e){var t=e;if(nr.length&&nr[nr.length-1]===a){var r="deltaY"in t?Jn(t):Gn(t),o=n.current.filter(function(e){return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(n=e.delta,o=r,n[0]===o[0]&&n[1]===o[1]);var n,o})[0];if(o&&o.should)t.cancelable&&t.preventDefault();else if(!o){var i=(s.current.shards||[]).map(Qn).filter(Boolean).filter(function(e){return e.contains(t.target)});(i.length>0?c(t,i[0]):!s.current.noIsolation)&&t.cancelable&&t.preventDefault()}}},[]),u=e.useCallback(function(e,t,r,o){var i={name:e,delta:t,target:r,should:o,shadowParent:rr(r)};n.current.push(i),setTimeout(function(){n.current=n.current.filter(function(e){return e!==i})},1)},[]),d=e.useCallback(function(e){r.current=Gn(e),o.current=void 0},[]),f=e.useCallback(function(e){u(e.type,Jn(e),e.target,c(e,t.lockRef.current))},[]),p=e.useCallback(function(e){u(e.type,Gn(e),e.target,c(e,t.lockRef.current))},[]);e.useEffect(function(){return nr.push(a),t.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,Un),document.addEventListener("touchmove",l,Un),document.addEventListener("touchstart",d,Un),function(){nr=nr.filter(function(e){return e!==a}),document.removeEventListener("wheel",l,Un),document.removeEventListener("touchmove",l,Un),document.removeEventListener("touchstart",d,Un)}},[]);var m=t.removeScrollBar,h=t.inert;return e.createElement(e.Fragment,null,h?e.createElement(a,{styles:er(i)}):null,m?e.createElement(Vn,{gapMode:t.gapMode}):null)},_n.useMedium(ir),On);var ir,ar=e.forwardRef(function(t,n){return e.createElement(kn,xn({},t,{ref:n,sideCar:or}))});ar.classNames=kn.classNames;var sr=[" ","Enter","ArrowUp","ArrowDown"],cr=[" ","Enter"],lr="Select",[ur,dr,fr]=C(lr),[pr,mr]=R(lr,[fr,Ot]),hr=Ot(),[vr,gr]=pr(lr),[yr,wr]=pr(lr),xr=t=>{const{__scopeSelect:n,children:r,open:o,defaultOpen:i,onOpenChange:a,value:s,defaultValue:c,onValueChange:l,dir:u,name:d,autoComplete:f,disabled:p,required:h,form:v}=t,g=hr(n),[y,w]=e.useState(null),[x,b]=e.useState(null),[E,S]=e.useState(!1),C=Gt(u),[R=!1,P]=z({prop:o,defaultProp:i,onChange:a}),[N,D]=z({prop:s,defaultProp:c,onChange:l}),O=e.useRef(null),_=!y||(v||!!y.closest("form")),[T,k]=e.useState(new Set),A=Array.from(T).map(e=>e.props.value).join(";");return m.jsx(zt,{...g,children:m.jsxs(vr,{required:h,scope:n,trigger:y,onTriggerChange:w,valueNode:x,onValueNodeChange:b,valueNodeHasChildren:E,onValueNodeHasChildrenChange:S,contentId:Z(),value:N,onValueChange:D,open:R,onOpenChange:P,dir:C,triggerPointerDownPosRef:O,disabled:p,children:[m.jsx(ur.Provider,{scope:n,children:m.jsx(yr,{scope:t.__scopeSelect,onNativeOptionAdd:e.useCallback(e=>{k(t=>new Set(t).add(e))},[]),onNativeOptionRemove:e.useCallback(e=>{k(t=>{const n=new Set(t);return n.delete(e),n})},[]),children:r})}),_?m.jsxs(ao,{"aria-hidden":!0,required:h,tabIndex:-1,name:d,autoComplete:f,value:N,onChange:e=>D(e.target.value),disabled:p,form:v,children:[void 0===N?m.jsx("option",{value:""}):null,Array.from(T)]},A):null]})})};xr.displayName=lr;var br="SelectTrigger",Er=e.forwardRef((t,n)=>{const{__scopeSelect:r,disabled:o=!1,...i}=t,a=hr(r),s=gr(br,r),c=s.disabled||o,l=g(n,s.onTriggerChange),u=dr(r),d=e.useRef("touch"),[f,p,v]=so(e=>{const t=u().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=co(t,e,n);void 0!==r&&s.onValueChange(r.value)}),y=e=>{c||(s.onOpenChange(!0),v()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return m.jsx(Ut,{asChild:!0,...a,children:m.jsx(N.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":io(s.value)?"":void 0,...i,ref:l,onClick:h(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==d.current&&y(e)}),onPointerDown:h(i.onPointerDown,e=>{d.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:h(i.onKeyDown,e=>{const t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||p(e.key),t&&" "===e.key||sr.includes(e.key)&&(y(),e.preventDefault())})})})});Er.displayName=br;var Sr="SelectValue",Cr=e.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:i,placeholder:a="",...s}=e,c=gr(Sr,n),{onValueNodeHasChildrenChange:l}=c,u=void 0!==i,d=g(t,c.onValueNodeChange);return B(()=>{l(u)},[l,u]),m.jsx(N.span,{...s,ref:d,style:{pointerEvents:"none"},children:io(c.value)?m.jsx(m.Fragment,{children:a}):i})});Cr.displayName=Sr;var Rr=e.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return m.jsx(N.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});Rr.displayName="SelectIcon";var Pr=e=>m.jsx($,{asChild:!0,...e});Pr.displayName="SelectPortal";var Nr="SelectContent",Dr=e.forwardRef((t,r)=>{const o=gr(Nr,t.__scopeSelect),[i,a]=e.useState();if(B(()=>{a(new DocumentFragment)},[]),!o.open){const e=i;return e?n.createPortal(m.jsx(_r,{scope:t.__scopeSelect,children:m.jsx(ur.Slot,{scope:t.__scopeSelect,children:m.jsx("div",{children:t.children})})}),e):null}return m.jsx(kr,{...t,ref:r})});Dr.displayName=Nr;var Or=10,[_r,Tr]=pr(Nr),kr=e.forwardRef((t,n)=>{const{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:s,side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:p,collisionPadding:v,sticky:y,hideWhenDetached:x,avoidCollisions:b,...E}=t,S=gr(Nr,r),[C,R]=e.useState(null),[P,N]=e.useState(null),D=g(n,e=>R(e)),[O,_]=e.useState(null),[T,k]=e.useState(null),A=dr(r),[j,M]=e.useState(!1),I=e.useRef(!1);e.useEffect(()=>{if(C)return wn(C)},[C]),Qt();const F=e.useCallback(e=>{const[t,...n]=A().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(const i of e){if(i===o)return;if(i?.scrollIntoView({block:"nearest"}),i===t&&P&&(P.scrollTop=0),i===r&&P&&(P.scrollTop=P.scrollHeight),i?.focus(),document.activeElement!==o)return}},[A,P]),W=e.useCallback(()=>F([O,C]),[F,O,C]);e.useEffect(()=>{j&&W()},[j,W]);const{onOpenChange:H,triggerPointerDownPosRef:B}=S;e.useEffect(()=>{if(C){let e={x:0,y:0};const t=t=>{e={x:Math.abs(Math.round(t.pageX)-(B.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(B.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():C.contains(n.target)||H(!1),document.removeEventListener("pointermove",t),B.current=null};return null!==B.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[C,H,B]),e.useEffect(()=>{const e=()=>H(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[H]);const[$,V]=so(e=>{const t=A().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=co(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=e.useCallback((e,t,n)=>{const r=!I.current&&!n;(void 0!==S.value&&S.value===t||r)&&(_(e),r&&(I.current=!0))},[S.value]),z=e.useCallback(()=>C?.focus(),[C]),U=e.useCallback((e,t,n)=>{const r=!I.current&&!n;(void 0!==S.value&&S.value===t||r)&&k(e)},[S.value]),Y="popper"===o?jr:Ar,q=Y===jr?{side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:p,collisionPadding:v,sticky:y,hideWhenDetached:x,avoidCollisions:b}:{};return m.jsx(_r,{scope:r,content:C,viewport:P,onViewportChange:N,itemRefCallback:K,selectedItem:O,onItemLeave:z,itemTextRefCallback:U,focusSelectedItem:W,selectedItemText:T,position:o,isPositioned:j,searchRef:$,children:m.jsx(ar,{as:w,allowPinchZoom:!0,children:m.jsx(on,{asChild:!0,trapped:S.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:h(i,e=>{S.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:m.jsx(L,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>S.onOpenChange(!1),children:m.jsx(Y,{role:"listbox",id:S.contentId,"data-state":S.open?"open":"closed",dir:S.dir,onContextMenu:e=>e.preventDefault(),...E,...q,onPlaced:()=>M(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:h(E.onKeyDown,e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=A().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});kr.displayName="SelectContentImpl";var Ar=e.forwardRef((t,n)=>{const{__scopeSelect:r,onPlaced:o,...i}=t,a=gr(Nr,r),s=Tr(Nr,r),[c,l]=e.useState(null),[u,d]=e.useState(null),f=g(n,e=>d(e)),p=dr(r),h=e.useRef(!1),v=e.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:x,focusSelectedItem:b}=s,E=e.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&u&&y&&w&&x){const e=a.trigger.getBoundingClientRect(),t=u.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=x.getBoundingClientRect();if("rtl"!==a.dir){const o=r.left-t.left,i=n.left-o,a=e.left-i,s=e.width+a,l=Math.max(s,t.width),u=window.innerWidth-Or,d=Xt(i,[Or,Math.max(Or,u-l)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{const o=t.right-r.right,i=window.innerWidth-n.right-o,a=window.innerWidth-e.right-i,s=e.width+a,l=Math.max(s,t.width),u=window.innerWidth-Or,d=Xt(i,[Or,Math.max(Or,u-l)]);c.style.minWidth=s+"px",c.style.right=d+"px"}const i=p(),s=window.innerHeight-2*Or,l=y.scrollHeight,d=window.getComputedStyle(u),f=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),v=parseInt(d.borderBottomWidth,10),g=f+m+l+parseInt(d.paddingBottom,10)+v,b=Math.min(5*w.offsetHeight,g),E=window.getComputedStyle(y),S=parseInt(E.paddingTop,10),C=parseInt(E.paddingBottom,10),R=e.top+e.height/2-Or,P=s-R,N=w.offsetHeight/2,D=f+m+(w.offsetTop+N),O=g-D;if(D<=R){const e=i.length>0&&w===i[i.length-1].ref.current;c.style.bottom="0px";const t=u.clientHeight-y.offsetTop-y.offsetHeight,n=D+Math.max(P,N+(e?C:0)+t+v);c.style.height=n+"px"}else{const e=i.length>0&&w===i[0].ref.current;c.style.top="0px";const t=Math.max(R,f+y.offsetTop+(e?S:0)+N)+O;c.style.height=t+"px",y.scrollTop=D-R+y.offsetTop}c.style.margin=`${Or}px 0`,c.style.minHeight=b+"px",c.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>h.current=!0)}},[p,a.trigger,a.valueNode,c,u,y,w,x,a.dir,o]);B(()=>E(),[E]);const[S,C]=e.useState();B(()=>{u&&C(window.getComputedStyle(u).zIndex)},[u]);const R=e.useCallback(e=>{e&&!0===v.current&&(E(),b?.(),v.current=!1)},[E,b]);return m.jsx(Lr,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:R,children:m.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:m.jsx(N.div,{...i,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});Ar.displayName="SelectItemAlignedPosition";var jr=e.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=Or,...i}=e,a=hr(n);return m.jsx(Yt,{...a,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});jr.displayName="SelectPopperPosition";var[Lr,Mr]=pr(Nr,{}),Ir="SelectViewport",Fr=e.forwardRef((t,n)=>{const{__scopeSelect:r,nonce:o,...i}=t,a=Tr(Ir,r),s=Mr(Ir,r),c=g(n,a.onViewportChange),l=e.useRef(0);return m.jsxs(m.Fragment,{children:[m.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),m.jsx(ur.Slot,{scope:r,children:m.jsx(N.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:h(i.onScroll,e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if(r?.current&&n){const e=Math.abs(l.current-t.scrollTop);if(e>0){const r=window.innerHeight-2*Or,o=parseFloat(n.style.minHeight),i=parseFloat(n.style.height),a=Math.max(o,i);if(a<r){const o=a+e,i=Math.min(r,o),s=o-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=s>0?s:0,n.style.justifyContent="flex-end")}}}l.current=t.scrollTop})})})]})});Fr.displayName=Ir;var Wr="SelectGroup",[Hr,Br]=pr(Wr);e.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Z();return m.jsx(Hr,{scope:n,id:o,children:m.jsx(N.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=Wr;var $r="SelectLabel",Vr=e.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Br($r,n);return m.jsx(N.div,{id:o.id,...r,ref:t})});Vr.displayName=$r;var Kr="SelectItem",[zr,Ur]=pr(Kr),Yr=e.forwardRef((t,n)=>{const{__scopeSelect:r,value:o,disabled:i=!1,textValue:a,...s}=t,c=gr(Kr,r),l=Tr(Kr,r),u=c.value===o,[d,f]=e.useState(a??""),[p,v]=e.useState(!1),y=g(n,e=>l.itemRefCallback?.(e,o,i)),w=Z(),x=e.useRef("touch"),b=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return m.jsx(zr,{scope:r,value:o,disabled:i,textId:w,isSelected:u,onItemTextChange:e.useCallback(e=>{f(t=>t||(e?.textContent??"").trim())},[]),children:m.jsx(ur.ItemSlot,{scope:r,value:o,disabled:i,textValue:d,children:m.jsx(N.div,{role:"option","aria-labelledby":w,"data-highlighted":p?"":void 0,"aria-selected":u&&p,"data-state":u?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...s,ref:y,onFocus:h(s.onFocus,()=>v(!0)),onBlur:h(s.onBlur,()=>v(!1)),onClick:h(s.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:h(s.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:h(s.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:h(s.onPointerMove,e=>{x.current=e.pointerType,i?l.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:h(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&l.onItemLeave?.()}),onKeyDown:h(s.onKeyDown,e=>{""!==l.searchRef?.current&&" "===e.key||(cr.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});Yr.displayName=Kr;var qr="SelectItemText",Xr=e.forwardRef((t,r)=>{const{__scopeSelect:o,className:i,style:a,...s}=t,c=gr(qr,o),l=Tr(qr,o),u=Ur(qr,o),d=wr(qr,o),[f,p]=e.useState(null),h=g(r,e=>p(e),u.onItemTextChange,e=>l.itemTextRefCallback?.(e,u.value,u.disabled)),v=f?.textContent,y=e.useMemo(()=>m.jsx("option",{value:u.value,disabled:u.disabled,children:v},u.value),[u.disabled,u.value,v]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=d;return B(()=>(w(y),()=>x(y)),[w,x,y]),m.jsxs(m.Fragment,{children:[m.jsx(N.span,{id:u.textId,...s,ref:h}),u.isSelected&&c.valueNode&&!c.valueNodeHasChildren?n.createPortal(s.children,c.valueNode):null]})});Xr.displayName=qr;var Zr="SelectItemIndicator",Gr=e.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return Ur(Zr,n).isSelected?m.jsx(N.span,{"aria-hidden":!0,...r,ref:t}):null});Gr.displayName=Zr;var Jr="SelectScrollUpButton",Qr=e.forwardRef((t,n)=>{const r=Tr(Jr,t.__scopeSelect),o=Mr(Jr,t.__scopeSelect),[i,a]=e.useState(!1),s=g(n,o.onScrollButtonChange);return B(()=>{if(r.viewport&&r.isPositioned){let e=function(){const e=t.scrollTop>0;a(e)};const t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?m.jsx(no,{...t,ref:s,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});Qr.displayName=Jr;var eo="SelectScrollDownButton",to=e.forwardRef((t,n)=>{const r=Tr(eo,t.__scopeSelect),o=Mr(eo,t.__scopeSelect),[i,a]=e.useState(!1),s=g(n,o.onScrollButtonChange);return B(()=>{if(r.viewport&&r.isPositioned){let e=function(){const e=t.scrollHeight-t.clientHeight,n=Math.ceil(t.scrollTop)<e;a(n)};const t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?m.jsx(no,{...t,ref:s,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});to.displayName=eo;var no=e.forwardRef((t,n)=>{const{__scopeSelect:r,onAutoScroll:o,...i}=t,a=Tr("SelectScrollButton",r),s=e.useRef(null),c=dr(r),l=e.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return e.useEffect(()=>()=>l(),[l]),B(()=>{const e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),m.jsx(N.div,{"aria-hidden":!0,...i,ref:n,style:{flexShrink:0,...i.style},onPointerDown:h(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:h(i.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:h(i.onPointerLeave,()=>{l()})})}),ro=e.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return m.jsx(N.div,{"aria-hidden":!0,...r,ref:t})});ro.displayName="SelectSeparator";var oo="SelectArrow";function io(e){return""===e||void 0===e}e.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=hr(n),i=gr(oo,n),a=Tr(oo,n);return i.open&&"popper"===a.position?m.jsx(qt,{...o,...r,ref:t}):null}).displayName=oo;var ao=e.forwardRef((t,n)=>{const{value:r,...o}=t,i=e.useRef(null),a=g(n,i),s=fn(r);return e.useEffect(()=>{const e=i.current,t=window.HTMLSelectElement.prototype,n=Object.getOwnPropertyDescriptor(t,"value").set;if(s!==r&&n){const t=new Event("change",{bubbles:!0});n.call(e,r),e.dispatchEvent(t)}},[s,r]),m.jsx(U,{asChild:!0,children:m.jsx("select",{...o,ref:a,defaultValue:r})})});function so(t){const n=O(t),r=e.useRef(""),o=e.useRef(0),i=e.useCallback(e=>{const t=r.current+e;n(t),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(t)},[n]),a=e.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return e.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,i,a]}function co(e,t,n){const r=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=n?e.indexOf(n):-1;let i=(a=e,s=Math.max(o,0),a.map((e,t)=>a[(s+t)%a.length]));var a,s;1===r.length&&(i=i.filter(e=>e!==n));const c=i.find(e=>e.textValue.toLowerCase().startsWith(r.toLowerCase()));return c!==n?c:void 0}ao.displayName="BubbleSelect";var lo=xr,uo=Er,fo=Cr,po=Rr,mo=Pr,ho=Dr,vo=Fr,go=Vr,yo=Yr,wo=Xr,xo=Gr,bo=Qr,Eo=to,So=ro,Co="Dialog",[Ro,Po]=R(Co),[No,Do]=Ro(Co),Oo=t=>{const{__scopeDialog:n,children:r,open:o,defaultOpen:i,onOpenChange:a,modal:s=!0}=t,c=e.useRef(null),l=e.useRef(null),[u=!1,d]=z({prop:o,defaultProp:i,onChange:a});return m.jsx(No,{scope:n,triggerRef:c,contentRef:l,contentId:Z(),titleId:Z(),descriptionId:Z(),open:u,onOpenChange:d,onOpenToggle:e.useCallback(()=>d(e=>!e),[d]),modal:s,children:r})};Oo.displayName=Co;var _o="DialogTrigger",To=e.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Do(_o,n),i=g(t,o.triggerRef);return m.jsx(N.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Zo(o.open),...r,ref:i,onClick:h(e.onClick,o.onOpenToggle)})});To.displayName=_o;var ko="DialogPortal",[Ao,jo]=Ro(ko,{forceMount:void 0}),Lo=t=>{const{__scopeDialog:n,forceMount:r,children:o,container:i}=t,a=Do(ko,n);return m.jsx(Ao,{scope:n,forceMount:r,children:e.Children.map(o,e=>m.jsx(V,{present:r||a.open,children:m.jsx($,{asChild:!0,container:i,children:e})}))})};Lo.displayName=ko;var Mo="DialogOverlay",Io=e.forwardRef((e,t)=>{const n=jo(Mo,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Do(Mo,e.__scopeDialog);return i.modal?m.jsx(V,{present:r||i.open,children:m.jsx(Fo,{...o,ref:t})}):null});Io.displayName=Mo;var Fo=e.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Do(Mo,n);return m.jsx(ar,{as:w,allowPinchZoom:!0,shards:[o.contentRef],children:m.jsx(N.div,{"data-state":Zo(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Wo="DialogContent",Ho=e.forwardRef((e,t)=>{const n=jo(Wo,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Do(Wo,e.__scopeDialog);return m.jsx(V,{present:r||i.open,children:i.modal?m.jsx(Bo,{...o,ref:t}):m.jsx($o,{...o,ref:t})})});Ho.displayName=Wo;var Bo=e.forwardRef((t,n)=>{const r=Do(Wo,t.__scopeDialog),o=e.useRef(null),i=g(n,r.contentRef,o);return e.useEffect(()=>{const e=o.current;if(e)return wn(e)},[]),m.jsx(Vo,{...t,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:h(t.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:h(t.onPointerDownOutside,e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:h(t.onFocusOutside,e=>e.preventDefault())})}),$o=e.forwardRef((t,n)=>{const r=Do(Wo,t.__scopeDialog),o=e.useRef(!1),i=e.useRef(!1);return m.jsx(Vo,{...t,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{t.onCloseAutoFocus?.(e),e.defaultPrevented||(o.current||r.triggerRef.current?.focus(),e.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:e=>{t.onInteractOutside?.(e),e.defaultPrevented||(o.current=!0,"pointerdown"===e.detail.originalEvent.type&&(i.current=!0));const n=e.target,a=r.triggerRef.current?.contains(n);a&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&i.current&&e.preventDefault()}})}),Vo=e.forwardRef((t,n)=>{const{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...s}=t,c=Do(Wo,r),l=e.useRef(null),u=g(n,l);return Qt(),m.jsxs(m.Fragment,{children:[m.jsx(on,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:m.jsx(L,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Zo(c.open),...s,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),m.jsxs(m.Fragment,{children:[m.jsx(ei,{titleId:c.titleId}),m.jsx(ti,{contentRef:l,descriptionId:c.descriptionId})]})]})}),Ko="DialogTitle",zo=e.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Do(Ko,n);return m.jsx(N.h2,{id:o.titleId,...r,ref:t})});zo.displayName=Ko;var Uo="DialogDescription",Yo=e.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Do(Uo,n);return m.jsx(N.p,{id:o.descriptionId,...r,ref:t})});Yo.displayName=Uo;var qo="DialogClose",Xo=e.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Do(qo,n);return m.jsx(N.button,{type:"button",...r,ref:t,onClick:h(e.onClick,()=>o.onOpenChange(!1))})});function Zo(e){return e?"open":"closed"}Xo.displayName=qo;var Go="DialogTitleWarning",[Jo,Qo]=function(t,n){const r=e.createContext(n),o=t=>{const{children:n,...o}=t,i=e.useMemo(()=>o,Object.values(o));return m.jsx(r.Provider,{value:i,children:n})};return o.displayName=t+"Provider",[o,function(o){const i=e.useContext(r);if(i)return i;if(void 0!==n)return n;throw new Error(`\`${o}\` must be used within \`${t}\``)}]}(Go,{contentName:Wo,titleName:Ko,docsSlug:"dialog"}),ei=({titleId:t})=>{const n=Qo(Go),r=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return e.useEffect(()=>{if(t){document.getElementById(t)||console.error(r)}},[r,t]),null},ti=({contentRef:t,descriptionId:n})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Qo("DialogDescriptionWarning").contentName}}.`;return e.useEffect(()=>{const e=t.current?.getAttribute("aria-describedby");if(n&&e){document.getElementById(n)||console.warn(r)}},[r,t,n]),null},ni=Oo,ri=To,oi=Lo,ii=Io,ai=Ho,si=zo,ci=Yo,li=Xo,ui="Checkbox",[di,fi]=R(ui),[pi,mi]=di(ui),hi=e.forwardRef((t,n)=>{const{__scopeCheckbox:r,name:o,checked:i,defaultChecked:a,required:s,disabled:c,value:l="on",onCheckedChange:u,form:d,...f}=t,[p,v]=e.useState(null),y=g(n,e=>v(e)),w=e.useRef(!1),x=!p||(d||!!p.closest("form")),[b=!1,E]=z({prop:i,defaultProp:a,onChange:u}),S=e.useRef(b);return e.useEffect(()=>{const e=p?.form;if(e){const t=()=>E(S.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[p,E]),m.jsxs(pi,{scope:r,state:b,disabled:c,children:[m.jsx(N.button,{type:"button",role:"checkbox","aria-checked":wi(b)?"mixed":b,"aria-required":s,"data-state":xi(b),"data-disabled":c?"":void 0,disabled:c,value:l,...f,ref:y,onKeyDown:h(t.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:h(t.onClick,e=>{E(e=>!!wi(e)||!e),x&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),x&&m.jsx(yi,{control:p,bubbles:!w.current,name:o,value:l,checked:b,required:s,disabled:c,form:d,style:{transform:"translateX(-100%)"},defaultChecked:!wi(a)&&a})]})});hi.displayName=ui;var vi="CheckboxIndicator",gi=e.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:r,...o}=e,i=mi(vi,n);return m.jsx(V,{present:r||wi(i.state)||!0===i.state,children:m.jsx(N.span,{"data-state":xi(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});gi.displayName=vi;var yi=t=>{const{control:n,checked:r,bubbles:o=!0,defaultChecked:i,...a}=t,s=e.useRef(null),c=fn(r),l=Pt(n);e.useEffect(()=>{const e=s.current,t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set;if(c!==r&&n){const t=new Event("click",{bubbles:o});e.indeterminate=wi(r),n.call(e,!wi(r)&&r),e.dispatchEvent(t)}},[c,r,o]);const u=e.useRef(!wi(r)&&r);return m.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i??u.current,...a,tabIndex:-1,ref:s,style:{...t.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function wi(e){return"indeterminate"===e}function xi(e){return wi(e)?"indeterminate":e?"checked":"unchecked"}var bi=hi,Ei=gi;export{bi as $,Ut as A,H as B,Yt as C,L as D,lo as E,fo as F,ai as G,li as H,po as I,si as J,ci as K,go as L,oi as M,ni as N,ii as O,N as P,ri as Q,W as R,b as S,uo as T,Gt as U,U as V,wn as W,Qt as X,on as Y,v as Z,ar as _,R as a,Ei as a0,Xt as a1,Po as a2,Jo as a3,fn as a4,Pt as a5,z as b,C as c,V as d,O as e,h as f,$ as g,B as h,D as i,m as j,Ot as k,Y as l,qt as m,Z as n,zt as o,w as p,bo as q,Eo as r,mo as s,ho as t,g as u,vo as v,yo as w,xo as x,wo as y,So as z};
