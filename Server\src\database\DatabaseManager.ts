import mysql from 'mysql2/promise';
import { logger } from '../utils/logger.js';

/**
 * PRODUCTION-READY Database Manager
 * Handles connection pooling, health monitoring, automatic recovery, and transaction management
 */
export class DatabaseManager {
  private pool: mysql.Pool | null = null;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 5000; // 5 seconds
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private connectionConfig: mysql.PoolOptions;

  constructor() {
    this.connectionConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production',

      // PRODUCTION: Optimized connection pool for high concurrency
      waitForConnections: true,
      connectionLimit: 50, // Increased for production load (was 25)
      queueLimit: 100, // Higher queue for peak traffic (was 50)
      // Note: acquireTimeout and timeout are not valid mysql2 PoolOptions
      multipleStatements: false,
      charset: 'utf8mb4',
      timezone: '+00:00',
      supportBigNumbers: true,
      bigNumberStrings: true,

      // ROBUSTNESS: Connection health settings
      enableKeepAlive: true,
      keepAliveInitialDelay: 0,

      // Note: onConnection and onEnqueue are not valid mysql2 PoolOptions
      // Pool monitoring will be handled separately
    };

    this.setupGracefulShutdown();
  }

  /**
   * Initialize database connection with automatic retry
   */
  async initialize(): Promise<void> {
    try {
      await this.createPool();
      await this.testConnection();
      await this.setupDatabase();
      this.startHealthCheck();
      this.isConnected = true;
      this.reconnectAttempts = 0;
      logger.info('Database Manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database:', error);
      await this.handleConnectionFailure();
    }
  }

  /**
   * Create connection pool with error handling
   */
  private async createPool(): Promise<void> {
    this.pool = mysql.createPool(this.connectionConfig);

    // Handle pool events
    this.pool.on('connection', (connection) => {
      logger.info(`New database connection established: ${connection.threadId}`);
    });

    // Note: MySQL2 pool doesn't have error event, errors are handled per connection
  }

  /**
   * Test database connection
   */
  private async testConnection(): Promise<void> {
    if (!this.pool) throw new Error('Pool not initialized');

    const connection = await this.pool.getConnection();
    try {
      await connection.ping();
      logger.info('Database connection test successful');
    } finally {
      connection.release();
    }
  }

  /**
   * Setup database and tables if needed
   */
  private async setupDatabase(): Promise<void> {
    try {
      // Create database if it doesn't exist
      await this.createDatabaseIfNotExists();

      // Verify tables exist (basic check)
      await this.verifyTables();

      logger.info('Database setup verification complete');
    } catch (error) {
      logger.error('Database setup failed:', error);
      throw error;
    }
  }

  /**
   * Create database if it doesn't exist
   */
  private async createDatabaseIfNotExists(): Promise<void> {
    const tempConnection = await mysql.createConnection({
      host: this.connectionConfig.host,
      port: this.connectionConfig.port,
      user: this.connectionConfig.user,
      password: this.connectionConfig.password,
    });

    try {
      await tempConnection.query(`CREATE DATABASE IF NOT EXISTS ${this.connectionConfig.database}`);
      logger.info(`Database '${this.connectionConfig.database}' verified`);
    } finally {
      await tempConnection.end();
    }
  }

  /**
   * Verify essential tables exist
   */
  private async verifyTables(): Promise<void> {
    const essentialTables = ['users', 'vouchers', 'voucher_batches'];

    for (const table of essentialTables) {
      const [rows] = await this.pool!.query(
        'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?',
        [this.connectionConfig.database, table]
      );

      if ((rows as any)[0].count === 0) {
        throw new Error(`Essential table '${table}' does not exist`);
      }
    }

    logger.info('Essential database tables verified');
  }

  /**
   * ENHANCED: Execute query with automatic retry and comprehensive error handling
   */
  async query<T = any>(sql: string, params?: any[], retryCount: number = 0): Promise<T[]> {
    const maxRetries = 3;

    if (!this.isConnected || !this.pool) {
      // ROBUSTNESS: Attempt to reconnect if not connected
      if (retryCount === 0) {
        logger.warn('⚠️ Database not connected - attempting to reconnect...');
        try {
          await this.initialize();
        } catch (error) {
          throw new Error('Database unavailable - connection failed');
        }
      } else {
        throw new Error('Database not connected');
      }
    }

    try {
      const startTime = Date.now();
      const [results] = await this.pool!.execute(sql, params);
      const duration = Date.now() - startTime;

      // MONITORING: Log slow queries for performance optimization
      if (duration > 5000) {
        logger.warn(`🐌 Slow query detected (${duration}ms):`, {
          sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
          duration
        });
      }

      return results as T[];
    } catch (error: any) {
      logger.error('❌ Database query error:', {
        sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
        error: error.message,
        code: error.code,
        retryCount
      });

      // ROBUSTNESS: Handle connection errors with automatic retry
      if (this.isConnectionError(error) && retryCount < maxRetries) {
        logger.info(`🔄 Retrying query due to connection error (attempt ${retryCount + 1}/${maxRetries})`);

        // Trigger reconnection
        await this.handleConnectionFailure();

        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, retryCount), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));

        // Retry the query
        return this.query(sql, params, retryCount + 1);
      }

      // ROBUSTNESS: Provide user-friendly error messages
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Duplicate entry - this record already exists');
      } else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
        throw new Error('Invalid reference - related record not found');
      } else if (error.code === 'ER_ROW_IS_REFERENCED_2') {
        throw new Error('Cannot delete - record is referenced by other data');
      } else if (this.isConnectionError(error)) {
        throw new Error('Database connection lost - please try again');
      }

      throw error;
    }
  }

  /**
   * ENHANCED: Execute transaction with automatic rollback and comprehensive error handling
   */
  async withTransaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>, retryCount: number = 0): Promise<T> {
    const maxRetries = 2;

    if (!this.pool) {
      throw new Error('Database not connected');
    }

    let connection: mysql.PoolConnection | null = null;

    try {
      // ROBUSTNESS: Timeout for getting connection
      connection = await Promise.race([
        this.pool.getConnection(),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Connection timeout')), 30000)
        )
      ]);

      await connection.beginTransaction();

      const startTime = Date.now();
      const result = await callback(connection);
      const duration = Date.now() - startTime;

      await connection.commit();

      // MONITORING: Log long transactions
      if (duration > 10000) {
        logger.warn(`🐌 Long transaction detected: ${duration}ms`);
      }

      return result;
    } catch (error: any) {
      // ROBUSTNESS: Ensure rollback even if connection is problematic
      if (connection) {
        try {
          await connection.rollback();
          logger.info('✅ Transaction rolled back successfully');
        } catch (rollbackError) {
          logger.error('❌ Failed to rollback transaction:', rollbackError);
        }
      }

      logger.error('❌ Transaction failed:', {
        error: error.message,
        code: error.code,
        retryCount
      });

      // ROBUSTNESS: Retry on connection errors
      if (this.isConnectionError(error) && retryCount < maxRetries) {
        logger.info(`🔄 Retrying transaction due to connection error (attempt ${retryCount + 1}/${maxRetries})`);

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));

        return this.withTransaction(callback, retryCount + 1);
      }

      throw error;
    } finally {
      if (connection) {
        try {
          connection.release();
        } catch (releaseError) {
          logger.error('❌ Failed to release connection:', releaseError);
        }
      }
    }
  }

  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{ connected: boolean; poolStatus: any; uptime: number }> {
    try {
      if (!this.pool) return { connected: false, poolStatus: null, uptime: 0 };

      const connection = await this.pool.getConnection();
      try {
        await connection.ping();
        return {
          connected: true,
          poolStatus: {
            // MySQL2 doesn't expose internal pool stats directly
            available: true,
            healthy: true
          },
          uptime: process.uptime()
        };
      } finally {
        connection.release();
      }
    } catch (error) {
      return { connected: false, poolStatus: null, uptime: 0 };
    }
  }

  /**
   * CRITICAL FIX: Disabled periodic health checks to prevent infinite loops
   */
  private startHealthCheck(): void {
    // REMOVED: setInterval health checks that were causing database query loops
    logger.info('Health checks DISABLED to prevent infinite loops');
  }

  /**
   * ENHANCED: Handle connection failures with exponential backoff and circuit breaker
   */
  private async handleConnectionFailure(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('❌ Max reconnection attempts reached - entering degraded mode');
      this.isConnected = false;

      // ROBUSTNESS: Schedule a final retry after extended delay
      setTimeout(() => {
        logger.info('🔄 Attempting final recovery after extended delay...');
        this.reconnectAttempts = 0; // Reset counter for final attempt
        this.handleConnectionFailure();
      }, 300000); // 5 minutes

      return;
    }

    this.isConnected = false;
    this.reconnectAttempts++;

    // ENHANCED: Exponential backoff with jitter to prevent thundering herd
    const baseDelay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    const jitter = Math.random() * 1000; // Add up to 1 second jitter
    const delay = Math.min(baseDelay + jitter, 60000); // Cap at 60 seconds

    logger.warn(`⚠️ Database connection lost - attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${Math.round(delay/1000)}s`);

    setTimeout(async () => {
      try {
        // ROBUSTNESS: Close existing pool before recreating
        if (this.pool) {
          await this.pool.end();
          this.pool = null;
        }

        await this.initialize();
        logger.info('✅ Database connection restored successfully');
      } catch (error: any) {
        logger.error(`❌ Reconnection attempt ${this.reconnectAttempts} failed:`, {
          error: error.message,
          code: error.code,
          errno: error.errno
        });

        // Continue retry cycle
        await this.handleConnectionFailure();
      }
    }, delay);
  }

  /**
   * Check if error is connection-related
   */
  private isConnectionError(error: any): boolean {
    const connectionErrors = [
      'PROTOCOL_CONNECTION_LOST',
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
      'ETIMEDOUT'
    ];

    return connectionErrors.includes(error.code);
  }

  /**
   * Setup graceful shutdown
   */
  private setupGracefulShutdown(): void {
    const shutdown = async () => {
      logger.info('Shutting down database manager...');

      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
      }

      if (this.pool) {
        await this.pool.end();
        logger.info('Database connections closed');
      }
    };

    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);
  }

  /**
   * MONITORING: Get connection pool statistics for production monitoring
   */
  getPoolStats() {
    if (!this.pool) {
      return { error: 'Pool not initialized' };
    }

    const pool = this.pool as any;
    return {
      totalConnections: pool._allConnections?.length || 0,
      freeConnections: pool._freeConnections?.length || 0,
      acquiringConnections: pool._acquiringConnections?.length || 0,
      queuedRequests: pool._connectionQueue?.length || 0,
      connectionLimit: this.connectionConfig.connectionLimit || 50,
      queueLimit: this.connectionConfig.queueLimit || 100
    };
  }

  /**
   * MONITORING: Log pool health for production monitoring
   */
  logPoolHealth() {
    const stats = this.getPoolStats();

    if ('error' in stats) {
      logger.warn('📊 Pool stats unavailable:', stats.error);
      return;
    }

    const utilizationPercent = Math.round((stats.totalConnections / stats.connectionLimit) * 100);
    const queueUtilizationPercent = Math.round((stats.queuedRequests / stats.queueLimit) * 100);

    logger.info('📊 DB Pool Health:', {
      connections: `${stats.totalConnections}/${stats.connectionLimit} (${utilizationPercent}%)`,
      free: stats.freeConnections,
      acquiring: stats.acquiringConnections,
      queued: `${stats.queuedRequests}/${stats.queueLimit} (${queueUtilizationPercent}%)`
    });

    // ALERTS: Warn about high utilization
    if (utilizationPercent > 80) {
      logger.warn(`⚠️ High DB connection utilization: ${utilizationPercent}%`);
    }

    if (queueUtilizationPercent > 50) {
      logger.warn(`⚠️ High DB queue utilization: ${queueUtilizationPercent}%`);
    }
  }

  /**
   * Get connection status
   */
  isHealthy(): boolean {
    return this.isConnected && this.pool !== null;
  }
}

// Singleton instance
export const databaseManager = new DatabaseManager();
