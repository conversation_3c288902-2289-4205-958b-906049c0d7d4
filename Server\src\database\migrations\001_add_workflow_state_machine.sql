-- Migration: Add Workflow State Machine Support
-- This migration adds the new workflow state machine columns and tables

-- 1. Add new columns to vouchers table
ALTER TABLE vouchers 
ADD COLUMN workflow_state VARCHAR(50) NOT NULL DEFAULT 'FINANCE_PENDING' AFTER status,
ADD COLUMN badge_type VARCHAR(20) DEFAULT 'NONE' AFTER workflow_state,
ADD COLUMN is_copy BOOLEAN DEFAULT FALSE AFTER badge_type,
ADD COLUMN parent_voucher_id VARCHAR(36) NULL AFTER is_copy,
ADD COLUMN version INT NOT NULL DEFAULT 1 AFTER parent_voucher_id;

-- 2. Create workflow audit log table
CREATE TABLE workflow_audit_log (
  id VARCHAR(36) PRIMARY KEY,
  voucher_id VARCHAR(36) NOT NULL,
  from_state VARCHAR(50) NOT NULL,
  to_state VARCHAR(50) NOT NULL,
  event_type VARCHAR(50) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  copy_id VARCHAR(36) NULL,
  metadata JSON NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_voucher_id (voucher_id),
  INDEX idx_timestamp (timestamp),
  INDEX idx_user_id (user_id),
  
  FOREIGN KEY (voucher_id) REFERENCES vouchers(id) ON DELETE CASCADE,
  FOREIGN KEY (copy_id) REFERENCES vouchers(id) ON DELETE SET NULL
);

-- 3. Create workflow state transitions table (for validation)
CREATE TABLE workflow_transitions (
  id VARCHAR(36) PRIMARY KEY,
  from_state VARCHAR(50) NOT NULL,
  event_type VARCHAR(50) NOT NULL,
  to_state VARCHAR(50) NOT NULL,
  requires_copy BOOLEAN DEFAULT FALSE,
  copy_state VARCHAR(50) NULL,
  badge_type VARCHAR(20) DEFAULT 'NONE',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_transition (from_state, event_type),
  INDEX idx_from_state (from_state),
  INDEX idx_event_type (event_type)
);

-- 4. Insert valid workflow transitions
INSERT INTO workflow_transitions (id, from_state, event_type, to_state, requires_copy, copy_state, badge_type) VALUES
-- Normal Flow
(UUID(), 'FINANCE_PENDING', 'SUBMIT_TO_AUDIT', 'FINANCE_PROCESSING', FALSE, NULL, 'NONE'),
(UUID(), 'FINANCE_PROCESSING', 'RECEIVE_FROM_FINANCE', 'AUDIT_NEW', FALSE, NULL, 'NONE'),
(UUID(), 'AUDIT_NEW', 'START_WORK', 'AUDIT_PENDING_DISPATCH', FALSE, NULL, 'NONE'),
(UUID(), 'AUDIT_PENDING_DISPATCH', 'DISPATCH_TO_FINANCE', 'AUDIT_DISPATCHED', FALSE, NULL, 'NONE'),
(UUID(), 'AUDIT_DISPATCHED', 'RECEIVE_FROM_FINANCE', 'FINANCE_CERTIFIED', FALSE, NULL, 'NONE'),

-- Rejection Flow
(UUID(), 'AUDIT_NEW', 'REJECT_VOUCHER', 'AUDIT_PENDING_DISPATCH_REJECTED', TRUE, 'AUDIT_REJECTED_COPY', 'REJECTED'),
(UUID(), 'AUDIT_PENDING_DISPATCH_REJECTED', 'DISPATCH_TO_FINANCE', 'FINANCE_REJECTED', FALSE, NULL, 'REJECTED'),

-- Resubmission Flow
(UUID(), 'FINANCE_REJECTED', 'RESUBMIT_FROM_REJECTED', 'FINANCE_PROCESSING', FALSE, NULL, 'RE_SUBMITTED'),
(UUID(), 'FINANCE_PROCESSING', 'RECEIVE_FROM_FINANCE', 'AUDIT_NEW_RESUBMITTED', FALSE, NULL, 'RE_SUBMITTED'),
(UUID(), 'AUDIT_NEW_RESUBMITTED', 'START_WORK', 'AUDIT_PENDING_DISPATCH', FALSE, NULL, 'NONE'),

-- Return Flow
(UUID(), 'AUDIT_NEW', 'RETURN_VOUCHER', 'AUDIT_PENDING_DISPATCH_RETURNED', TRUE, 'AUDIT_RETURNED_COPY', 'RETURNED'),
(UUID(), 'AUDIT_PENDING_DISPATCH_RETURNED', 'DISPATCH_TO_FINANCE', 'FINANCE_RETURNED', FALSE, NULL, 'RETURNED'),
(UUID(), 'FINANCE_RETURNED', 'RESUBMIT_FROM_RETURNED', 'FINANCE_PROCESSING', FALSE, NULL, 'RETURNED');

-- 5. Create indexes for performance
CREATE INDEX idx_vouchers_workflow_state ON vouchers(workflow_state);
CREATE INDEX idx_vouchers_badge_type ON vouchers(badge_type);
CREATE INDEX idx_vouchers_is_copy ON vouchers(is_copy);
CREATE INDEX idx_vouchers_parent_id ON vouchers(parent_voucher_id);
CREATE INDEX idx_vouchers_version ON vouchers(version);

-- 6. Create composite indexes for tab filtering
CREATE INDEX idx_vouchers_workflow_department ON vouchers(workflow_state, department);
CREATE INDEX idx_vouchers_workflow_original_dept ON vouchers(workflow_state, original_department);

-- 7. Migrate existing vouchers to new workflow states
-- Map existing status values to new workflow states
UPDATE vouchers SET 
  workflow_state = CASE 
    WHEN status = 'PENDING SUBMISSION' THEN 'FINANCE_PENDING'
    WHEN status = 'VOUCHER PROCESSING' AND department = 'AUDIT' THEN 'AUDIT_NEW'
    WHEN status = 'VOUCHER PROCESSING' AND department != 'AUDIT' THEN 'FINANCE_PROCESSING'
    WHEN status = 'VOUCHER CERTIFIED' AND department = 'AUDIT' THEN 'AUDIT_DISPATCHED'
    WHEN status = 'VOUCHER CERTIFIED' AND department != 'AUDIT' THEN 'FINANCE_CERTIFIED'
    WHEN status = 'VOUCHER REJECTED' AND department = 'AUDIT' THEN 'AUDIT_REJECTED_COPY'
    WHEN status = 'VOUCHER REJECTED' AND department != 'AUDIT' THEN 'FINANCE_REJECTED'
    WHEN status = 'VOUCHER RETURNED' AND department = 'AUDIT' THEN 'AUDIT_RETURNED_COPY'
    WHEN status = 'VOUCHER RETURNED' AND department != 'AUDIT' THEN 'FINANCE_RETURNED'
    ELSE 'FINANCE_PENDING'
  END,
  badge_type = CASE
    WHEN voucher_id LIKE '%RE-SUBMITTED%' THEN 'RE_SUBMITTED'
    WHEN voucher_id LIKE '%RETURNED%' THEN 'RETURNED'
    WHEN voucher_id LIKE '%REJECTED%' THEN 'REJECTED'
    ELSE 'NONE'
  END,
  is_copy = CASE
    WHEN voucher_id LIKE '%_COPY' THEN TRUE
    ELSE FALSE
  END
WHERE workflow_state = 'FINANCE_PENDING'; -- Only update records that haven't been migrated

-- 8. Add constraints after migration
ALTER TABLE vouchers 
ADD CONSTRAINT chk_workflow_state CHECK (
  workflow_state IN (
    'FINANCE_PENDING', 'FINANCE_PROCESSING', 'FINANCE_CERTIFIED', 'FINANCE_REJECTED', 'FINANCE_RETURNED',
    'AUDIT_NEW', 'AUDIT_NEW_RESUBMITTED', 'AUDIT_PENDING_DISPATCH', 
    'AUDIT_PENDING_DISPATCH_REJECTED', 'AUDIT_PENDING_DISPATCH_RETURNED',
    'AUDIT_DISPATCHED', 'AUDIT_REJECTED_COPY', 'AUDIT_RETURNED_COPY'
  )
),
ADD CONSTRAINT chk_badge_type CHECK (
  badge_type IN ('NONE', 'RE_SUBMITTED', 'RETURNED', 'REJECTED')
);

-- 9. Create trigger to auto-increment version on updates
DELIMITER $$
CREATE TRIGGER voucher_version_increment 
BEFORE UPDATE ON vouchers
FOR EACH ROW
BEGIN
  IF NEW.workflow_state != OLD.workflow_state THEN
    SET NEW.version = OLD.version + 1;
    SET NEW.last_modified = NOW();
  END IF;
END$$
DELIMITER ;

-- 10. Create view for easy tab filtering
CREATE VIEW voucher_tab_view AS
SELECT 
  v.*,
  CASE 
    WHEN v.workflow_state IN ('FINANCE_PENDING') THEN 'pending'
    WHEN v.workflow_state IN ('FINANCE_PROCESSING') THEN 'processing'
    WHEN v.workflow_state IN ('FINANCE_CERTIFIED') THEN 'certified'
    WHEN v.workflow_state IN ('FINANCE_REJECTED') THEN 'rejected'
    WHEN v.workflow_state IN ('FINANCE_RETURNED') THEN 'returned'
    WHEN v.workflow_state IN ('AUDIT_NEW', 'AUDIT_NEW_RESUBMITTED') THEN 'new-vouchers'
    WHEN v.workflow_state IN ('AUDIT_PENDING_DISPATCH', 'AUDIT_PENDING_DISPATCH_REJECTED', 'AUDIT_PENDING_DISPATCH_RETURNED') THEN 'pending-dispatch'
    WHEN v.workflow_state IN ('AUDIT_DISPATCHED') THEN 'dispatched'
    WHEN v.workflow_state IN ('AUDIT_REJECTED_COPY') THEN 'rejected'
    WHEN v.workflow_state IN ('AUDIT_RETURNED_COPY') THEN 'returned'
    ELSE 'unknown'
  END as tab_name,
  CASE
    WHEN v.department = 'AUDIT' AND v.workflow_state LIKE 'AUDIT_%' THEN 'AUDIT'
    WHEN v.department != 'AUDIT' AND v.workflow_state LIKE 'FINANCE_%' THEN v.original_department
    ELSE NULL
  END as visible_to_department
FROM vouchers v
WHERE v.deleted = FALSE;
