-- Migration: Add rejected voucher tracking fields
-- This migration adds new fields to support the enhanced rejected voucher flow

-- Add new tracking fields for rejected vouchers
ALTER TABLE vouchers 
ADD COLUMN rejected_dispatched_by VARCHAR(255) NULL,
ADD COLUMN rejected_dispatch_time DATETIME NULL,
ADD COLUMN rejected_received_by <PERSON><PERSON><PERSON><PERSON>(255) NULL,
ADD COLUMN rejected_receipt_time DATETIME NULL,
ADD COLUMN is_rejected_voucher BOOLEAN DEFAULT FALSE;

-- Add indexes for better performance on the new fields
CREATE INDEX idx_vouchers_rejected_dispatched_by ON vouchers(rejected_dispatched_by);
CREATE INDEX idx_vouchers_rejected_dispatch_time ON vouchers(rejected_dispatch_time);
CREATE INDEX idx_vouchers_is_rejected_voucher ON vouchers(is_rejected_voucher);

-- Update existing rejected vouchers to have the is_rejected_voucher flag
UPDATE vouchers 
SET is_rejected_voucher = TRUE 
WHERE status = 'VOUCHER REJECTED';
