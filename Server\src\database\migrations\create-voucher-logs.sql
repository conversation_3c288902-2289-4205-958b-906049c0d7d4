CREATE TABLE IF NOT EXISTS voucher_logs (
  id VARCHAR(36) PRIMARY KEY,
  voucherId VARCHAR(36) NOT NULL,
  previousStatus VARCHAR(50) NOT NULL,
  newStatus VARCHAR(50) NOT NULL,
  userId VARCHAR(36) NOT NULL,
  reason TEXT,
  metadata JSON,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (voucherId) REFERENCES vouchers(id),
  FOREIGN KEY (userId) REFERENCES users(id),
  INDEX idx_voucher_logs_voucher (voucherId),
  INDEX idx_voucher_logs_user (userId),
  INDEX idx_voucher_logs_created (createdAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 