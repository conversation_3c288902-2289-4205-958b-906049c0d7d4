import mysql from 'mysql2/promise';
import { logger } from '../utils/logger.js';

interface YearDatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  waitForConnections: boolean;
  connectionLimit: number;
  queueLimit: number;
}

class YearDatabaseManager {
  private pools: Map<string, mysql.Pool> = new Map();
  private baseConfig: YearDatabaseConfig;

  constructor() {
    this.baseConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production',
      waitForConnections: true,
      connectionLimit: 10, // Smaller pool per year
      queueLimit: 0
    };
  }

  /**
   * Get database name for a specific year
   */
  private getYearDatabase(year: number): string {
    const currentYear = new Date().getFullYear();
    if (year === currentYear) {
      return this.baseConfig.database; // Use main database for current year
    }
    return `vms_${year}`;
  }

  /**
   * Get or create connection pool for a specific year
   */
  private async getYearPool(year: number): Promise<mysql.Pool> {
    const dbName = this.getYearDatabase(year);
    
    if (this.pools.has(dbName)) {
      return this.pools.get(dbName)!;
    }

    // Create new pool for this year
    const config = {
      ...this.baseConfig,
      database: dbName
    };

    const pool = mysql.createPool(config);
    this.pools.set(dbName, pool);

    logger.info(`Created connection pool for year ${year} (database: ${dbName})`);
    return pool;
  }

  /**
   * Execute query on specific year database
   */
  async queryYear(year: number, sql: string, params: any[] = []): Promise<any> {
    try {
      const pool = await this.getYearPool(year);
      const [results] = await pool.execute(sql, params);
      return results;
    } catch (error) {
      logger.error(`Error executing query on year ${year}:`, error);
      throw error;
    }
  }

  /**
   * Execute query on database specified in session
   */
  async queryWithSession(session: any, sql: string, params: any[] = []): Promise<any> {
    const year = session?.selectedYear || new Date().getFullYear();
    return this.queryYear(year, sql, params);
  }

  /**
   * Check if year database exists
   */
  async yearDatabaseExists(year: number): Promise<boolean> {
    try {
      const dbName = this.getYearDatabase(year);
      const pool = await this.getYearPool(new Date().getFullYear()); // Use current year pool to check
      
      const [results] = await pool.execute(
        'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
        [dbName]
      );
      
      return Array.isArray(results) && results.length > 0;
    } catch (error) {
      logger.error(`Error checking if year ${year} database exists:`, error);
      return false;
    }
  }

  /**
   * Get all available year databases
   */
  async getAvailableYears(): Promise<number[]> {
    try {
      const pool = await this.getYearPool(new Date().getFullYear());
      const [results] = await pool.execute('SHOW DATABASES') as any[];
      
      const years: number[] = [];
      
      for (const row of results) {
        const dbName = row.Database;
        if (dbName.startsWith('vms_') && dbName.match(/vms_\d{4}$/)) {
          const year = parseInt(dbName.replace('vms_', ''));
          if (!isNaN(year)) {
            years.push(year);
          }
        }
      }

      // Always include current year
      const currentYear = new Date().getFullYear();
      if (!years.includes(currentYear)) {
        years.push(currentYear);
      }

      return years.sort((a, b) => b - a); // Sort descending
    } catch (error) {
      logger.error('Error getting available years:', error);
      return [new Date().getFullYear()]; // Fallback to current year
    }
  }

  /**
   * Create new year database
   */
  async createYearDatabase(year: number): Promise<boolean> {
    try {
      const dbName = this.getYearDatabase(year);
      const pool = await this.getYearPool(new Date().getFullYear());

      // Create database
      await pool.execute(`CREATE DATABASE IF NOT EXISTS ${dbName}`);

      // Get new pool for the created database
      const newPool = await this.getYearPool(year);

      // Copy table structure from current year
      const currentDb = this.baseConfig.database;
      const tables = [
        'users', 'vouchers', 'voucher_batches', 'batch_vouchers',
        'provisional_cash_records', 'notifications', 'audit_logs',
        'voucher_logs', 'active_sessions'
      ];

      for (const table of tables) {
        try {
          const [createResults] = await pool.execute(`SHOW CREATE TABLE ${currentDb}.${table}`) as any[];
          if (createResults.length > 0) {
            const createSQL = createResults[0]['Create Table'];
            await newPool.execute(createSQL);
          }
        } catch (tableError) {
          logger.warn(`Could not copy table ${table} to year ${year}:`, tableError);
        }
      }

      // Copy users data (users should exist across all years)
      await newPool.execute(`INSERT IGNORE INTO users SELECT * FROM ${currentDb}.users`);

      logger.info(`Successfully created year database: ${dbName}`);
      return true;
    } catch (error) {
      logger.error(`Error creating year ${year} database:`, error);
      return false;
    }
  }

  /**
   * Close all connection pools
   */
  async closeAll(): Promise<void> {
    const closePromises = Array.from(this.pools.values()).map(pool => pool.end());
    await Promise.all(closePromises);
    this.pools.clear();
    logger.info('Closed all year database connection pools');
  }

  /**
   * Get statistics for a specific year
   */
  async getYearStatistics(year: number): Promise<any> {
    try {
      const exists = await this.yearDatabaseExists(year);
      if (!exists) {
        return {
          year,
          voucherCount: 0,
          totalAmount: 0,
          departments: [],
          isActive: false,
          lastActivity: new Date().toISOString()
        };
      }

      // Get voucher statistics
      const voucherStats = await this.queryYear(year, `
        SELECT 
          COUNT(*) as voucher_count,
          COALESCE(SUM(amount), 0) as total_amount
        FROM vouchers
      `);

      // Get departments
      const departments = await this.queryYear(year, `
        SELECT DISTINCT department 
        FROM vouchers 
        WHERE department IS NOT NULL
        ORDER BY department
      `);

      // Get last activity
      const lastActivity = await this.queryYear(year, `
        SELECT MAX(created_at) as last_activity 
        FROM vouchers
      `);

      // Check recent activity
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentActivity = await this.queryYear(year, `
        SELECT COUNT(*) as recent_count 
        FROM vouchers 
        WHERE created_at >= ?
      `, [thirtyDaysAgo.toISOString()]);

      return {
        year,
        voucherCount: voucherStats[0]?.voucher_count || 0,
        totalAmount: parseFloat(voucherStats[0]?.total_amount || '0'),
        departments: departments.map((d: any) => d.department),
        isActive: (recentActivity[0]?.recent_count || 0) > 0,
        lastActivity: lastActivity[0]?.last_activity || new Date().toISOString()
      };
    } catch (error) {
      logger.error(`Error getting statistics for year ${year}:`, error);
      return {
        year,
        voucherCount: 0,
        totalAmount: 0,
        departments: [],
        isActive: false,
        lastActivity: new Date().toISOString()
      };
    }
  }
}

// Export singleton instance
export const yearDatabaseManager = new YearDatabaseManager();

// Export helper function for middleware
export function getYearFromSession(session: any): number {
  return session?.selectedYear || new Date().getFullYear();
}

// Export helper function to get database name from session
export function getDatabaseFromSession(session: any): string {
  const year = getYearFromSession(session);
  const currentYear = new Date().getFullYear();
  
  if (year === currentYear) {
    return process.env.DB_NAME || 'vms_production';
  }
  
  return `vms_${year}`;
}
