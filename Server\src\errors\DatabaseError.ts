export class DatabaseError extends Error {
  public readonly code: string;
  public readonly details?: Record<string, any>;

  constructor(
    message: string, 
    code: string = 'DATABASE_ERROR',
    details?: Record<string, any>
  ) {
    super(message);
    this.name = 'DatabaseError';
    this.code = code;
    this.details = details;
    
    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, DatabaseError.prototype);
  }

  public toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      details: this.details
    };
  }
} 