import { EventEmitter } from 'events';

/**
 * Centralized Event Bus for VMS System
 * Eliminates circular dependencies by providing a single event hub
 */
class VMSEventBus extends EventEmitter {
  private static instance: VMSEventBus;

  private constructor() {
    super();
    this.setMaxListeners(50); // Increase limit for production use
    console.log('🎯 VMS Event Bus initialized');
  }

  public static getInstance(): VMSEventBus {
    if (!VMSEventBus.instance) {
      VMSEventBus.instance = new VMSEventBus();
    }
    return VMSEventBus.instance;
  }

  // Batch Events
  public emitBatchCreated(batchData: any) {
    console.log(`📡 Event: Batch created - ${batchData.id}`);
    this.emit('batch:created', batchData);
  }

  public emitBatchUpdated(batchData: any) {
    console.log(`📡 Event: Batch updated - ${batchData.id}`);
    this.emit('batch:updated', batchData);
  }

  public emitBatchReceived(batchData: any) {
    console.log(`📡 Event: Batch received - ${batchData.id}`);
    this.emit('batch:received', batchData);
  }

  // Notification Events
  public emitNotificationCreated(notificationData: any) {
    console.log(`📡 Event: Notification created for ${notificationData.user_id}`);
    this.emit('notification:created', notificationData);
  }

  // Voucher Events
  public emitVoucherUpdated(voucherData: any) {
    console.log(`📡 Event: Voucher updated - ${voucherData.id}`);
    this.emit('voucher:updated', voucherData);
  }

  // Generic data events
  public emitDataUpdate(entityType: string, actionType: string, data: any) {
    console.log(`📡 Event: ${entityType} ${actionType} - ${data.id || 'unknown'}`);
    this.emit('data:update', { entityType, actionType, data });
  }
}

// Export singleton instance
export const eventBus = VMSEventBus.getInstance();

// Event type definitions for TypeScript
export interface BatchEvent {
  id: string;
  department: string;
  sent_by: string;
  vouchers: any[];
  voucherIds: string[];
}

export interface NotificationEvent {
  id: string;
  user_id: string;
  message: string;
  is_read: boolean;
  timestamp: string;
  batch_id?: string;
  voucher_id?: string;
  type: string;
  from_audit?: boolean;
}

export interface VoucherEvent {
  id: string;
  voucher_id: string;
  status: string;
  department: string;
}
