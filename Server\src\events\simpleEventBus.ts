/**
 * ZERO-DEPENDENCY EVENT BUS
 * Completely isolated from all other modules to prevent circular dependencies
 */

type EventCallback = (...args: any[]) => void;

class SimpleEventBus {
  private static instance: SimpleEventBus;
  private listeners: Map<string, EventCallback[]> = new Map();

  private constructor() {
    console.log('🎯 Simple Event Bus initialized - Zero dependencies');
  }

  public static getInstance(): SimpleEventBus {
    if (!SimpleEventBus.instance) {
      SimpleEventBus.instance = new SimpleEventBus();
    }
    return SimpleEventBus.instance;
  }

  public on(event: string, callback: EventCallback): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  public emit(event: string, ...args: any[]): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  public off(event: string, callback: EventCallback): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Batch Events
  public emitBatchCreated(batchData: any): void {
    console.log(`📡 Event: Batch created - ${batchData.id}`);
    this.emit('batch:created', batchData);
  }

  public emitBatchUpdated(batchData: any): void {
    console.log(`📡 Event: Batch updated - ${batchData.id}`);
    this.emit('batch:updated', batchData);
  }

  public emitBatchReceived(batchData: any): void {
    console.log(`📡 Event: Batch received - ${batchData.id}`);
    this.emit('batch:received', batchData);
  }

  // Notification Events
  public emitNotificationCreated(notificationData: any): void {
    console.log(`📡 Event: Notification created for ${notificationData.user_id}`);
    this.emit('notification:created', notificationData);
  }

  // Voucher Events
  public emitVoucherUpdated(voucherData: any): void {
    console.log(`📡 Event: Voucher updated - ${voucherData.id}`);
    this.emit('voucher:updated', voucherData);
  }
}

// Export singleton instance
export const simpleEventBus = SimpleEventBus.getInstance();
