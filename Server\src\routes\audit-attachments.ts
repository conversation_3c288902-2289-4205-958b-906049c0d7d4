import express from 'express';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import archiver from 'archiver';
import { authenticate } from '../middleware/auth.js';
import { query } from '../database/db.js';
import { AuditAttachmentsService } from '../services/audit-attachments-service.js';
import { logger } from '../utils/logger.js';
import { STORAGE_CONFIG } from '../utils/file-storage.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Audit department access check
function requireAuditAccess(req: express.Request, res: express.Response, next: express.NextFunction) {
  if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
    next();
  } else {
    res.status(403).json({ error: 'Audit department access required' });
  }
}

// Apply audit access check to all routes
router.use(requireAuditAccess);

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: STORAGE_CONFIG.maxFileSize,
    files: 5 // Max 5 files per upload
  },
  fileFilter: (req, file, cb) => {
    if (STORAGE_CONFIG.allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Only PDF and JPG files are allowed'));
    }
  }
});

/**
 * Upload attachment(s) to voucher
 * POST /api/audit/vouchers/:voucherId/attachments
 */
router.post('/vouchers/:voucherId/attachments', upload.array('files', 5), async (req, res) => {
  try {
    const { voucherId } = req.params;
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      return res.status(400).json({ error: 'No files provided' });
    }

    // Get voucher data for filename generation
    const vouchers = await query('SELECT claimant, description FROM vouchers WHERE id = ?', [voucherId]) as any[];
    if (vouchers.length === 0) {
      return res.status(404).json({ error: 'Voucher not found' });
    }

    const voucherData = vouchers[0];
    const uploadedAttachments = [];

    // Process each file
    for (const file of files) {
      try {
        const attachment = await AuditAttachmentsService.uploadAttachment(
          voucherId,
          file,
          req.user.id,
          voucherData
        );
        uploadedAttachments.push(attachment);
      } catch (fileError) {
        logger.error(`Error uploading file ${file.originalname}:`, fileError);
        // Continue with other files, but log the error
      }
    }

    if (uploadedAttachments.length === 0) {
      return res.status(500).json({ error: 'Failed to upload any files' });
    }

    res.json({
      success: true,
      message: `Successfully uploaded ${uploadedAttachments.length} file(s)`,
      attachments: uploadedAttachments
    });

  } catch (error) {
    logger.error('Error uploading audit attachments:', error);
    res.status(500).json({ error: 'Failed to upload attachments' });
  }
});

/**
 * Get all attachments for a voucher
 * GET /api/audit/vouchers/:voucherId/attachments
 */
router.get('/vouchers/:voucherId/attachments', async (req, res) => {
  try {
    const { voucherId } = req.params;
    const attachments = await AuditAttachmentsService.getVoucherAttachments(voucherId);
    
    res.json(attachments);
  } catch (error) {
    logger.error('Error fetching voucher attachments:', error);
    res.status(500).json({ error: 'Failed to fetch attachments' });
  }
});

/**
 * Download single attachment
 * GET /api/audit/attachments/:attachmentId/download
 */
router.get('/attachments/:attachmentId/download', async (req, res) => {
  try {
    const { attachmentId } = req.params;
    const attachment = await AuditAttachmentsService.getAttachmentById(attachmentId);
    
    if (!attachment) {
      return res.status(404).json({ error: 'Attachment not found' });
    }

    if (!fs.existsSync(attachment.file_path)) {
      return res.status(404).json({ error: 'File not found on disk' });
    }

    // Set headers for download
    res.setHeader('Content-Disposition', `attachment; filename="${attachment.stored_filename}"`);
    res.setHeader('Content-Type', attachment.mime_type);
    res.setHeader('Content-Length', attachment.file_size);

    // Stream file to response
    const fileStream = fs.createReadStream(attachment.file_path);
    fileStream.pipe(res);

  } catch (error) {
    logger.error('Error downloading attachment:', error);
    res.status(500).json({ error: 'Failed to download attachment' });
  }
});

/**
 * View attachment in browser
 * GET /api/audit/attachments/:attachmentId/view
 */
router.get('/attachments/:attachmentId/view', async (req, res) => {
  try {
    const { attachmentId } = req.params;
    const attachment = await AuditAttachmentsService.getAttachmentById(attachmentId);
    
    if (!attachment) {
      return res.status(404).json({ error: 'Attachment not found' });
    }

    if (!fs.existsSync(attachment.file_path)) {
      return res.status(404).json({ error: 'File not found on disk' });
    }

    // Set headers for inline viewing
    res.setHeader('Content-Type', attachment.mime_type);
    res.setHeader('Content-Length', attachment.file_size);
    res.setHeader('Content-Disposition', `inline; filename="${attachment.stored_filename}"`);

    // Stream file to response
    const fileStream = fs.createReadStream(attachment.file_path);
    fileStream.pipe(res);

  } catch (error) {
    logger.error('Error viewing attachment:', error);
    res.status(500).json({ error: 'Failed to view attachment' });
  }
});

/**
 * Delete attachment
 * DELETE /api/audit/attachments/:attachmentId
 */
router.delete('/attachments/:attachmentId', async (req, res) => {
  try {
    const { attachmentId } = req.params;
    const success = await AuditAttachmentsService.deleteAttachment(attachmentId, req.user.name);
    
    if (success) {
      res.json({ success: true, message: 'Attachment deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete attachment' });
    }
  } catch (error) {
    logger.error('Error deleting attachment:', error);
    res.status(500).json({ error: 'Failed to delete attachment' });
  }
});

export default router;
