import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { query } from '../database/db.js';
import { authenticate } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';

export const auditRouter = express.Router();

// Apply authentication middleware to all routes
auditRouter.use(authenticate);

// Check if user has audit access
function hasAuditAccess(req: express.Request, res: express.Response, next: express.NextFunction) {
  if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
    next();
  } else {
    res.status(403).json({ error: 'Access denied' });
  }
}

// Apply audit access check to all routes
auditRouter.use(hasAuditAccess);

// Get audit dashboard data
auditRouter.get('/dashboard', async (req, res) => {
  try {
    // Get counts for different voucher statuses
    const pendingCount = await query(
      "SELECT COUNT(*) as count FROM vouchers WHERE status = 'PENDING RECEIPT' OR status = 'AUDIT: PROCESSING'"
    ) as any[];
    
    const certifiedCount = await query(
      "SELECT COUNT(*) as count FROM vouchers WHERE status = 'VOUCHER CERTIFIED'"
    ) as any[];
    
    const rejectedCount = await query(
      "SELECT COUNT(*) as count FROM vouchers WHERE status = 'VOUCHER REJECTED'"
    ) as any[];
    
    const returnedCount = await query(
      "SELECT COUNT(*) as count FROM vouchers WHERE status = 'VOUCHER RETURNED'"
    ) as any[];
    
    // Get counts by department
    const departmentCounts = await query(
      "SELECT department, COUNT(*) as count FROM vouchers GROUP BY department"
    ) as any[];
    
    // Get recent vouchers
    const recentVouchers = await query(
      "SELECT * FROM vouchers ORDER BY date DESC LIMIT 10"
    ) as any[];
    
    // Get provisional cash records
    const provisionalCashRecords = await query(
      "SELECT * FROM provisional_cash_records ORDER BY date DESC LIMIT 10"
    ) as any[];
    
    res.json({
      counts: {
        pending: pendingCount[0].count,
        certified: certifiedCount[0].count,
        rejected: rejectedCount[0].count,
        returned: returnedCount[0].count
      },
      departmentCounts,
      recentVouchers,
      provisionalCashRecords
    });
  } catch (error) {
    logger.error('Get audit dashboard data error:', error);
    res.status(500).json({ error: 'Failed to get audit dashboard data' });
  }
});

// Get audit analytics data
auditRouter.get('/analytics', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    let dateFilter = '';
    const params = [];
    
    if (startDate && endDate) {
      dateFilter = 'WHERE date BETWEEN ? AND ?';
      params.push(startDate, endDate);
    } else if (startDate) {
      dateFilter = 'WHERE date >= ?';
      params.push(startDate);
    } else if (endDate) {
      dateFilter = 'WHERE date <= ?';
      params.push(endDate);
    }
    
    // Get voucher counts by status
    const statusCounts = await query(
      `SELECT status, COUNT(*) as count FROM vouchers ${dateFilter} GROUP BY status`,
      params
    ) as any[];
    
    // Get voucher counts by department
    const departmentCounts = await query(
      `SELECT department, COUNT(*) as count FROM vouchers ${dateFilter} GROUP BY department`,
      params
    ) as any[];
    
    // Get total amounts by department
    const departmentAmounts = await query(
      `SELECT department, SUM(amount) as total FROM vouchers ${dateFilter} GROUP BY department`,
      params
    ) as any[];
    
    // Get voucher counts by month
    const monthCounts = await query(
      `SELECT SUBSTRING(date, 1, 3) as month, COUNT(*) as count 
       FROM vouchers ${dateFilter} 
       GROUP BY SUBSTRING(date, 1, 3)
       ORDER BY FIELD(SUBSTRING(date, 1, 3), 'JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC')`,
      params
    ) as any[];
    
    res.json({
      statusCounts,
      departmentCounts,
      departmentAmounts,
      monthCounts
    });
  } catch (error) {
    logger.error('Get audit analytics data error:', error);
    res.status(500).json({ error: 'Failed to get audit analytics data' });
  }
});

// Log audit action
auditRouter.post('/log', async (req, res) => {
  try {
    const { action, resourceType, resourceId, details } = req.body;
    
    // Validate required fields
    if (!action || !resourceType) {
      return res.status(400).json({ error: 'Action and resource type are required' });
    }
    
    // Create audit log
    const id = uuidv4();
    await query(
      `INSERT INTO audit_logs (
        id, user_id, action, resource_type, resource_id, details, timestamp, ip_address
      ) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)`,
      [
        id,
        req.user.id,
        action,
        resourceType,
        resourceId || null,
        details || null,
        req.ip
      ]
    );
    
    // Get created log
    const logs = await query('SELECT * FROM audit_logs WHERE id = ?', [id]) as any[];
    
    res.status(201).json(logs[0]);
  } catch (error) {
    logger.error('Create audit log error:', error);
    res.status(500).json({ error: 'Failed to create audit log' });
  }
});

// Get audit logs for a specific resource
auditRouter.get('/logs/:resourceType/:resourceId', async (req, res) => {
  try {
    const { resourceType, resourceId } = req.params;
    
    const logs = await query(
      'SELECT * FROM audit_logs WHERE resource_type = ? AND resource_id = ? ORDER BY timestamp DESC',
      [resourceType, resourceId]
    ) as any[];
    
    res.json(logs);
  } catch (error) {
    logger.error('Get resource audit logs error:', error);
    res.status(500).json({ error: 'Failed to get resource audit logs' });
  }
});
