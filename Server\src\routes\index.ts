import express from 'express';
import { authRouter } from './auth.js';
import { userRouter } from './users.js';
import { voucherRouter } from './vouchers.js';
import { batchRouter } from './batches.js';
import { provisionalCashRouter } from './provisionalCash.js';
import { notificationRouter } from './notifications.js';
import { adminRouter } from './admin.js';
import { auditRouter } from './audit.js';
import { yearRouter } from './years.js';
import { pendriveBackupRouter } from './pendrive-backup.js';
// import { auditTrailRouter } from './audit-trail.js'; // REMOVED: Audit trail functionality removed
import workflowRouter from './workflow.js';
import passwordChangeRequestsRouter from './password-change-requests.js';
import auditAttachmentsRouter from './audit-attachments.js';
import { loginUpdatesRouter } from './login-updates.js';
import { authenticate } from '../middleware/auth.js';

const apiRouter = express.Router();

// Mount all routes
apiRouter.use('/auth', authRouter);
apiRouter.use('/users', userRouter);
apiRouter.use('/vouchers', voucherRouter);
apiRouter.use('/batches', batchRouter);
apiRouter.use('/provisional-cash', provisionalCashRouter);
apiRouter.use('/notifications', notificationRouter);
apiRouter.use('/admin', adminRouter);
apiRouter.use('/audit', auditRouter);
apiRouter.use('/years', yearRouter);
apiRouter.use('/pendrive-backup', pendriveBackupRouter);
// apiRouter.use('/audit-trail', auditTrailRouter); // REMOVED: Audit trail functionality removed from admin dashboard
apiRouter.use('/workflow', workflowRouter);
apiRouter.use('/password-change-requests', passwordChangeRequestsRouter);
apiRouter.use('/audit', auditAttachmentsRouter);
apiRouter.use('/login-updates', loginUpdatesRouter);

// API version and status endpoint
apiRouter.get('/', (req, res) => {
  res.json({
    name: 'Voucher Management System API',
    version: '1.0.0',
    status: 'active'
  });
});

// Health endpoint for service discovery
apiRouter.get('/health', (req, res) => {
  const healthData = {
    status: 'healthy',
    service: 'vms-server',
    serviceName: 'VMS-Server',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '3.0.0',
    environment: process.env.NODE_ENV || 'production',
    pid: process.pid
  };

  res.json(healthData);
});

// Basic system info endpoint (accessible to all authenticated users)
apiRouter.get('/system-info', authenticate, async (req, res) => {
  try {
    const { query } = await import('../database/db.js');

    // Get basic system information that's safe for all users
    const activeUsers = await query(`
      SELECT COUNT(DISTINCT user_id) as count
      FROM active_sessions
      WHERE is_active = TRUE
      AND last_activity >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    `) as any[];

    const totalSessions = await query(`
      SELECT COUNT(*) as count
      FROM active_sessions
      WHERE is_active = TRUE
    `) as any[];

    const systemInfo = {
      activeUsers: parseInt(activeUsers[0]?.count || '0'),
      totalSessions: parseInt(totalSessions[0]?.count || '0'),
      serverTime: new Date().toISOString(),
      uptime: process.uptime(),
      status: 'healthy'
    };

    res.json(systemInfo);
  } catch (error) {
    const { logger } = await import('../utils/logger.js');
    logger.error('System info error:', error);
    res.status(500).json({ error: 'Failed to get system info' });
  }
});

// Basic analytics endpoint (accessible to all authenticated users)
apiRouter.get('/basic-analytics', authenticate, async (req, res) => {
  try {
    const { query } = await import('../database/db.js');
    const timeframe = req.query.timeframe as string || 'week';

    let dateCondition = '';
    switch (timeframe) {
      case 'today':
        dateCondition = 'DATE(created_at) = CURDATE()';
        break;
      case 'week':
        dateCondition = 'created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case 'month':
        dateCondition = 'created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      default:
        dateCondition = 'created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
    }

    // Get voucher metrics
    const voucherMetrics = await query(`
      SELECT
        COUNT(*) as total_vouchers,
        SUM(CASE WHEN status = 'PENDING_DISPATCH' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'DISPATCHED' THEN 1 ELSE 0 END) as dispatched,
        SUM(CASE WHEN status = 'CERTIFIED' THEN 1 ELSE 0 END) as certified,
        SUM(amount) as total_amount,
        AVG(amount) as average_amount
      FROM vouchers
      WHERE ${dateCondition}
    `) as any[];

    // Get department activity
    const departmentActivity = await query(`
      SELECT
        department,
        COUNT(*) as voucher_count,
        SUM(amount) as total_amount
      FROM vouchers
      WHERE ${dateCondition}
      GROUP BY department
      ORDER BY voucher_count DESC
    `) as any[];

    // Get daily activity (for charts)
    const dailyActivity = await query(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as voucher_count,
        SUM(amount) as total_amount
      FROM vouchers
      WHERE ${dateCondition}
      GROUP BY DATE(created_at)
      ORDER BY date
    `) as any[];

    const analytics = {
      voucherMetrics: voucherMetrics[0] || {
        total_vouchers: 0,
        pending: 0,
        dispatched: 0,
        certified: 0,
        total_amount: 0,
        average_amount: 0
      },
      departmentActivity,
      dailyActivity,
      timeframe,
      generatedAt: new Date().toISOString()
    };

    res.json(analytics);
  } catch (error) {
    const { logger } = await import('../utils/logger.js');
    logger.error('Basic analytics error:', error);
    res.status(500).json({ error: 'Failed to get analytics data' });
  }
});

export { apiRouter };
