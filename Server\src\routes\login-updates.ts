/**
 * 🎯 LOGIN PAGE REAL-TIME UPDATES
 * Server-Sent Events endpoint for login page user updates
 * No authentication required - public endpoint
 */

import express from 'express';
import { logger } from '../utils/logger.js';

const loginUpdatesRouter = express.Router();

// Store active SSE connections
const sseConnections = new Set<express.Response>();

// Server-Sent Events endpoint for login page updates
loginUpdatesRouter.get('/user-updates', (req, res) => {
  // PRODUCTION FIX: Enhanced SSE error handling
  let connectionClosed = false;

  // Set SSE headers with proper timeout handling
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
    'X-Accel-Buffering': 'no' // Disable nginx buffering
  });

  // PRODUCTION FIX: Safe connection management
  const cleanupConnection = () => {
    if (!connectionClosed) {
      connectionClosed = true;
      sseConnections.delete(res);
      try {
        if (!res.headersSent) {
          res.end();
        }
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  };

  // Add connection to active connections
  sseConnections.add(res);
  logger.info(`SSE connection established for login updates. Total connections: ${sseConnections.size}`);

  // Send initial connection confirmation with error handling
  try {
    res.write(`data: ${JSON.stringify({
      type: 'connected',
      message: 'Login updates stream connected',
      timestamp: Date.now()
    })}\n\n`);
  } catch (error: any) {
    logger.warn('Failed to send initial SSE message:', error.message);
    cleanupConnection();
    return;
  }

  // PRODUCTION FIX: Enhanced error handling for all connection events
  req.on('close', () => {
    if (!connectionClosed) {
      logger.info(`SSE connection closed gracefully. Remaining connections: ${sseConnections.size - 1}`);
      cleanupConnection();
    }
  });

  req.on('error', (error: any) => {
    // PRODUCTION FIX: Filter out normal connection reset errors
    if (error.code === 'ECONNRESET' || error.code === 'EPIPE' || error.message === 'aborted') {
      logger.debug(`SSE connection reset (normal): ${error.code || error.message}`);
    } else {
      logger.error('SSE connection error (unexpected):', {
        code: error.code,
        message: error.message,
        stack: error.stack
      });
    }
    cleanupConnection();
  });

  // PRODUCTION FIX: Handle response errors
  res.on('error', (error: any) => {
    if (error.code === 'ECONNRESET' || error.code === 'EPIPE') {
      logger.debug(`SSE response error (normal): ${error.code}`);
    } else {
      logger.error('SSE response error (unexpected):', {
        code: error.code,
        message: error.message
      });
    }
    cleanupConnection();
  });

  // PRODUCTION FIX: Connection timeout handling
  const connectionTimeout = setTimeout(() => {
    logger.debug('SSE connection timeout, closing gracefully');
    cleanupConnection();
  }, 300000); // 5 minutes timeout

  req.on('close', () => {
    clearTimeout(connectionTimeout);
  });
});

// PRODUCTION FIX: Enhanced broadcast function with robust error handling
export function broadcastLoginUpdate(type: string, data: any) {
  if (sseConnections.size === 0) {
    return; // No active connections
  }

  const message = {
    type,
    data,
    timestamp: Date.now()
  };

  const messageData = `data: ${JSON.stringify(message)}\n\n`;

  // PRODUCTION FIX: Robust connection handling
  const deadConnections: express.Response[] = [];
  let successfulBroadcasts = 0;

  sseConnections.forEach((connection) => {
    try {
      // Check if connection is still writable
      if (connection.writable && !connection.destroyed) {
        connection.write(messageData);
        successfulBroadcasts++;
      } else {
        // Connection is no longer valid
        deadConnections.push(connection);
      }
    } catch (error: any) {
      // PRODUCTION FIX: Handle specific error types
      if (error.code === 'ECONNRESET' || error.code === 'EPIPE' || error.message === 'aborted') {
        logger.debug(`SSE broadcast failed (connection reset): ${error.code || error.message}`);
      } else {
        logger.warn('SSE broadcast failed (unexpected error):', {
          code: error.code,
          message: error.message
        });
      }
      deadConnections.push(connection);
    }
  });

  // PRODUCTION FIX: Clean up dead connections silently
  deadConnections.forEach(connection => {
    sseConnections.delete(connection);
  });

  // Only log if there were successful broadcasts or unexpected issues
  if (successfulBroadcasts > 0) {
    logger.info(`Broadcasted login update to ${successfulBroadcasts} connections: ${type}`);
  } else if (deadConnections.length > 0) {
    logger.debug(`Cleaned up ${deadConnections.length} dead SSE connections`);
  }
}

export { loginUpdatesRouter };
