import express from 'express';
import bcrypt from 'bcrypt';
import { query } from '../database/db.js';
import { logger } from '../utils/logger.js';
import { authenticate } from '../middleware/auth.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Admin access check middleware
function requireAdminAccess(req: express.Request, res: express.Response, next: express.NextFunction) {
  if (req.user.department === 'SYSTEM ADMIN' || req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({ error: 'Admin access required' });
  }
}


// Get pending password change requests (admin only)
router.get('/', async (req, res) => {
  try {
    const requests = await query(`
      SELECT
        pcr.*,
        u.name as current_user_name,
        u.department as current_user_department
      FROM password_change_requests pcr
      LEFT JOIN users u ON pcr.user_id = u.id
      WHERE pcr.status = 'PENDING'
      ORDER BY pcr.requested_at DESC
    `) as any[];

    res.json(requests);

  } catch (error) {
    logger.error('Error fetching password change requests:', error);
    res.status(500).json({ error: 'Failed to fetch password change requests' });
  }
});

// Approve password change request (admin only)
router.put('/:requestId/approve', requireAdminAccess, async (req, res) => {
  try {
    const { requestId } = req.params;
    // Use authenticated user's information
    const adminId = req.user.id;
    const adminName = req.user.name;

    // Get the request
    const requests = await query(
      'SELECT * FROM password_change_requests WHERE id = ? AND status = "PENDING"',
      [requestId]
    ) as any[];

    if (requests.length === 0) {
      return res.status(404).json({ error: 'Password change request not found or already processed' });
    }

    const request = requests[0];

    // Update user's password
    await query(
      'UPDATE users SET password = ? WHERE id = ?',
      [request.new_password_hash, request.user_id]
    );

    // Mark request as approved
    await query(
      `UPDATE password_change_requests 
       SET status = 'APPROVED', processed_at = NOW(), processed_by = ?, admin_notes = ?
       WHERE id = ?`,
      [adminName, `Approved by ${adminName}`, requestId]
    );

    logger.info(`Password change request approved: ${requestId} by admin ${adminName}`);

    res.json({
      success: true,
      message: 'Password change request approved successfully'
    });

  } catch (error) {
    logger.error('Error approving password change request:', error);
    res.status(500).json({ error: 'Failed to approve password change request' });
  }
});

// Reject password change request (admin only)
router.put('/:requestId/reject', requireAdminAccess, async (req, res) => {
  try {
    const { requestId } = req.params;
    const { reason } = req.body;
    // Use authenticated user's information
    const adminId = req.user.id;
    const adminName = req.user.name;

    // Get the request
    const requests = await query(
      'SELECT * FROM password_change_requests WHERE id = ? AND status = "PENDING"',
      [requestId]
    ) as any[];

    if (requests.length === 0) {
      return res.status(404).json({ error: 'Password change request not found or already processed' });
    }

    // Mark request as rejected
    await query(
      `UPDATE password_change_requests 
       SET status = 'REJECTED', processed_at = NOW(), processed_by = ?, admin_notes = ?
       WHERE id = ?`,
      [adminName, reason || `Rejected by ${adminName}`, requestId]
    );

    logger.info(`Password change request rejected: ${requestId} by admin ${adminName}`);

    res.json({
      success: true,
      message: 'Password change request rejected successfully'
    });

  } catch (error) {
    logger.error('Error rejecting password change request:', error);
    res.status(500).json({ error: 'Failed to reject password change request' });
  }
});

export default router;
