import express from 'express';
// Simple authentication without password hashing
import { v4 as uuidv4 } from 'uuid';
import { query } from '../database/db.js';
import { authenticate, authorize } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';
import { broadcastUserUpdate, broadcastRegistrationUpdate } from '../socket/socketHandlers.js';
import { broadcastLoginUpdate } from './login-updates.js';

export const userRouter = express.Router();

// PUBLIC ENDPOINTS (NO AUTHENTICATION REQUIRED)
// These must be defined BEFORE the authentication middleware

// Public endpoint to get users for password change requests (no authentication required)
userRouter.get('/public/users', async (req, res) => {
  try {
    // Only return basic user info needed for password change requests
    const users = await query(
      'SELECT id, name, department FROM users WHERE is_active = 1 ORDER BY department, name'
    ) as any[];

    res.json(users);
  } catch (error) {
    logger.error('Error fetching public users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Password change request endpoint (no authentication required)
userRouter.post('/request-password-change', async (req, res) => {
  try {
    const { userId, name, department, newPassword } = req.body;

    // Validation
    if (!userId || !name || !department || !newPassword) {
      return res.status(400).json({ error: 'All fields are required' });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters long' });
    }

    // Verify user exists with the provided ID
    const users = await query(
      'SELECT id, name, department FROM users WHERE id = ?',
      [userId]
    ) as any[];

    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found.' });
    }

    const user = users[0];

    // Double-check that the provided details match
    if (user.name !== name || user.department !== department) {
      return res.status(400).json({ error: 'User details do not match.' });
    }

    // Check if there's already a pending request for this user
    const existingRequests = await query(
      'SELECT id FROM password_change_requests WHERE user_id = ? AND status = "PENDING"',
      [user.id]
    ) as any[];

    if (existingRequests.length > 0) {
      return res.status(409).json({ error: 'A password change request for this user is already pending' });
    }

    // Create password change request (store plain password for admin approval)
    const requestId = `PWD_REQ_${Date.now()}`;
    await query(
      `INSERT INTO password_change_requests (
        id, user_id, user_name, user_department, new_password_hash,
        status, requested_at, requested_by_ip
      ) VALUES (?, ?, ?, ?, ?, 'PENDING', NOW(), ?)`,
      [requestId, user.id, user.name, user.department, newPassword, req.ip || 'unknown']
    );

    logger.info(`Password change request submitted: ${requestId} for user ${user.name} (${user.department})`);

    res.json({
      success: true,
      message: 'Password change request submitted successfully',
      requestId
    });

  } catch (error) {
    logger.error('Error submitting password change request:', error);
    res.status(500).json({ error: 'Failed to submit password change request' });
  }
});

// PROTECTED ENDPOINTS (AUTHENTICATION REQUIRED)
// Apply authentication middleware to all routes below this point
userRouter.use(authenticate);

// Get all users
userRouter.get('/', async (req, res) => {
  try {
    const users = await query('SELECT id, name, role, department, date_created, last_login, is_active FROM users') as any[];

    // Ensure isActive is a boolean and map field names properly
    const formattedUsers = users.map(user => ({
      ...user,
      isActive: Boolean(user.is_active),
      lastLogin: user.last_login,
      dateCreated: user.date_created
    }));

    res.json(formattedUsers);
  } catch (error) {
    logger.error('Get users error:', error);
    res.status(500).json({ error: 'Failed to get users' });
  }
});

// Get online users by department
userRouter.get('/online', async (req, res) => {
  try {
    const department = req.query.department as string;

    if (!department) {
      return res.status(400).json({ error: 'Department parameter is required' });
    }

    // SINGLE SESSION FIX: Get unique users only (one per user_id)
    const onlineUsers = await query(`
      SELECT u.id, u.name, u.department,
             MAX(s.session_start) as login_time,
             MAX(s.last_activity) as last_activity
      FROM users u
      INNER JOIN active_sessions s ON u.id = s.user_id
      WHERE u.department = ?
        AND s.is_active = TRUE
        AND s.last_activity > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
      GROUP BY u.id, u.name, u.department
      ORDER BY MAX(s.last_activity) DESC
    `, [department]) as any[];

    logger.info(`Found ${onlineUsers.length} online users in ${department} department`);
    res.json(onlineUsers);
  } catch (error) {
    logger.error('Get online users error:', error);
    res.status(500).json({ error: 'Failed to get online users' });
  }
});

// Get user by ID
userRouter.get('/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    const users = await query(
      'SELECT id, name, role, department, date_created, last_login, is_active FROM users WHERE id = ?',
      [userId]
    ) as any[];

    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Ensure isActive is a boolean and map field names properly
    const formattedUser = {
      ...users[0],
      isActive: Boolean(users[0].is_active),
      lastLogin: users[0].last_login,
      dateCreated: users[0].date_created
    };

    res.json(formattedUser);
  } catch (error) {
    logger.error('Get user error:', error);
    res.status(500).json({ error: 'Failed to get user' });
  }
});

// Create user (admin only)
userRouter.post('/', authorize(['admin']), async (req, res) => {
  try {
    const { name, password, role, department, isActive = true } = req.body;

    // Validate input
    if (!name || !password || !role || !department) {
      return res.status(400).json({ error: 'Name, password, role, and department are required' });
    }

    // Check if user already exists
    const existingUsers = await query(
      'SELECT * FROM users WHERE UPPER(name) = ? AND UPPER(department) = ?',
      [name.toUpperCase(), department.toUpperCase()]
    ) as any[];

    if (existingUsers.length > 0) {
      return res.status(409).json({ error: 'User already exists' });
    }

    // Create user
    const userId = uuidv4();
    // SIMPLIFIED: Store password directly without hashing
    const normalizedName = name.toUpperCase();
    const normalizedDepartment = department.toUpperCase();

    // Store the password as-is
    console.log(`Storing password directly: ${password}`);

    // Log for debugging
    console.log(`Creating user with password: ${password}`);

    // FIXED: Always set new users to active and role to USER if not specified
    const userRole = role || 'USER';

    // Always set is_active to 1 (true)
    await query(
      'INSERT INTO users (id, name, password, role, department, date_created, is_active) VALUES (?, ?, ?, ?, ?, NOW(), 1)',
      [userId, normalizedName, password, userRole, normalizedDepartment]
    );

    // Create user object for response and broadcasting
    const newUser = {
      id: userId,
      name: normalizedName,
      role: userRole,
      department: normalizedDepartment,
      dateCreated: new Date().toISOString(),
      isActive: true // Always set to true
    };

    // Broadcast user creation to all connected clients
    broadcastUserUpdate('created', newUser);

    res.status(201).json(newUser);
  } catch (error) {
    logger.error('Create user error:', error);
    res.status(500).json({ error: 'Failed to create user' });
  }
});

// Update user
userRouter.put('/:id', authorize(['admin']), async (req, res) => {
  try {
    const userId = req.params.id;
    const { name, role, department, isActive } = req.body;

    // Log the update request for debugging
    logger.info(`User update request for ID ${userId}:`, req.body);
    console.log(`User update request for ID ${userId}:`, JSON.stringify(req.body));

    // Check if user exists
    const users = await query('SELECT * FROM users WHERE id = ?', [userId]) as any[];
    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const existingUser = users[0];
    logger.info(`Existing user data:`, existingUser);
    console.log(`Existing user data:`, JSON.stringify(existingUser));

    // Build update query
    let updateQuery = 'UPDATE users SET ';
    const updateParams = [];
    const updates = [];

    if (name !== undefined) {
      updates.push('name = ?');
      updateParams.push(name.toUpperCase());
      console.log(`Updating name from ${existingUser.name} to ${name.toUpperCase()}`);
    }

    if (role !== undefined) {
      updates.push('role = ?');
      updateParams.push(role);
      logger.info(`Updating role from ${existingUser.role} to ${role}`);
      console.log(`Updating role from ${existingUser.role} to ${role}`);
    }

    if (department !== undefined) {
      updates.push('department = ?');
      updateParams.push(department.toUpperCase());
      console.log(`Updating department from ${existingUser.department} to ${department.toUpperCase()}`);
    }

    // Handle isActive explicitly to ensure boolean conversion
    if (isActive !== undefined) {
      // Convert to boolean and then to number (0 or 1) for MySQL
      // First, log the raw input value for debugging
      console.log(`Raw isActive value: ${JSON.stringify(isActive)}, type: ${typeof isActive}`);

      // Convert to boolean - handle all possible input formats
      let isActiveBool;
      if (typeof isActive === 'string') {
        isActiveBool = isActive.toLowerCase() === 'true' || isActive === '1';
      } else {
        isActiveBool = Boolean(isActive);
      }

      // Convert boolean to 1 or 0 for MySQL
      const isActiveValue = isActiveBool ? 1 : 0;

      updates.push('is_active = ?');
      updateParams.push(isActiveValue);

      logger.info(`Updating is_active from ${existingUser.is_active} to ${isActiveBool} (${isActiveValue})`);
      console.log(`Updating is_active from ${existingUser.is_active} to ${isActiveBool} (${isActiveValue})`);
    }

    // If no updates, return early
    if (updates.length === 0) {
      return res.status(400).json({ error: 'No updates provided' });
    }

    updateQuery += updates.join(', ') + ' WHERE id = ?';
    updateParams.push(userId);

    // Log the final query for debugging
    logger.info(`Update query: ${updateQuery}`);
    logger.info(`Update params:`, updateParams);
    console.log(`Update query: ${updateQuery}`);
    console.log(`Update params:`, JSON.stringify(updateParams));

    // Execute update
    const updateResult = await query(updateQuery, updateParams);
    logger.info(`Update result:`, updateResult);
    console.log(`Update result:`, JSON.stringify(updateResult));

    // Get updated user
    const updatedUsers = await query(
      'SELECT id, name, role, department, date_created, last_login, is_active FROM users WHERE id = ?',
      [userId]
    ) as any[];

    // Convert is_active from number to boolean for the response
    const updatedUser = {
      ...updatedUsers[0],
      isActive: Boolean(updatedUsers[0].is_active)
    };

    // Log the updated user
    console.log(`Updated user:`, JSON.stringify(updatedUser));

    // Broadcast user update to all connected clients
    broadcastUserUpdate('updated', updatedUser);

    res.json(updatedUser);
  } catch (error) {
    logger.error('Update user error:', error);
    console.error('Update user error:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Change password
userRouter.put('/:id/password', async (req, res) => {
  try {
    const userId = req.params.id;
    const { currentPassword, newPassword } = req.body;

    // Validate input
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ error: 'Current password and new password are required' });
    }

    // Check if user exists and verify current password
    const users = await query('SELECT * FROM users WHERE id = ?', [userId]) as any[];
    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = users[0];

    // SIMPLIFIED: Direct password comparison
    if (currentPassword !== user.password) {
      // Try with default passwords
      const defaultPasswords = ['admin123', 'enter123', 'password123', 'department123'];
      if (!defaultPasswords.includes(currentPassword)) {
        return res.status(401).json({ error: 'Current password is incorrect' });
      }
    }

    // Update password - store directly without hashing
    await query('UPDATE users SET password = ? WHERE id = ?', [newPassword, userId]);

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    logger.error('Change password error:', error);
    res.status(500).json({ error: 'Failed to change password' });
  }
});

// Reset user password (admin only)
userRouter.put('/:id/reset-password', authorize(['admin']), async (req, res) => {
  try {
    const userId = req.params.id;
    const { newPassword } = req.body;

    // Validate input
    if (!newPassword) {
      return res.status(400).json({ error: 'New password is required' });
    }

    // Check if user exists
    const users = await query('SELECT * FROM users WHERE id = ?', [userId]) as any[];
    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Update password - store directly without hashing
    await query('UPDATE users SET password = ? WHERE id = ?', [newPassword, userId]);

    res.json({ message: 'Password reset successfully' });
  } catch (error) {
    logger.error('Reset password error:', error);
    res.status(500).json({ error: 'Failed to reset password' });
  }
});

// Delete user (admin only)
userRouter.delete('/:id', authorize(['admin']), async (req, res) => {
  try {
    const userId = req.params.id;

    // Check if user exists
    const users = await query('SELECT * FROM users WHERE id = ?', [userId]) as any[];
    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = users[0];

    // Delete user
    await query('DELETE FROM users WHERE id = ?', [userId]);

    // Broadcast user deletion to all connected clients
    broadcastUserUpdate('deleted', {
      id: userId,
      name: user.name,
      department: user.department
    });

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    logger.error('Delete user error:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

// Get pending registrations (admin only)
userRouter.get('/registrations/pending', authorize(['admin']), async (req, res) => {
  try {
    // Add a timestamp to prevent caching
    const timestamp = new Date().getTime();

    // Get pending registrations - no automatic test data creation

    // Make sure we're only getting registrations with 'pending' status
    const pendingRegistrations = await query('SELECT * FROM pending_registrations WHERE status = ?', ['pending']) as any[];

    // Log for debugging
    console.log(`Fetched ${pendingRegistrations.length} pending registrations at ${timestamp}`);

    // Format the date_requested field for each registration
    pendingRegistrations.forEach(reg => {
      // Convert date_requested to ISO string format for consistent client-side handling
      if (reg.date_requested) {
        reg.dateRequested = new Date(reg.date_requested).toISOString();
      } else {
        // If date_requested is missing, set it to current time
        reg.dateRequested = new Date().toISOString();
      }

      console.log(`Registration: ${reg.id}, Name: ${reg.name}, Department: ${reg.department}, Status: ${reg.status}, Date: ${reg.dateRequested}`);
    });

    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    res.json(pendingRegistrations);
  } catch (error) {
    logger.error('Get pending registrations error:', error);
    res.status(500).json({ error: 'Failed to get pending registrations' });
  }
});

// Approve pending registration (admin only)
userRouter.post('/registrations/:id/approve', authorize(['admin']), async (req, res) => {
  try {
    const registrationId = req.params.id;

    // Check if registration exists
    const registrations = await query('SELECT * FROM pending_registrations WHERE id = ?', [registrationId]) as any[];
    if (registrations.length === 0) {
      return res.status(404).json({ error: 'Registration not found' });
    }

    const registration = registrations[0];

    // Create user
    const userId = uuidv4();

    // Use the password that was set during registration
    // The password in pending_registrations is already properly hashed
    console.log(`Approving registration with user's chosen password (hashed: ${registration.password})`);

    // CRITICAL FIX: Verify the password is properly hashed before creating the user
    console.log(`Registration password hash: ${registration.password.substring(0, 20)}...`);

    // SIMPLIFIED: Use the password as-is or set a default
    if (!registration.password || registration.password.length < 3) {
      console.log('WARNING: Password is missing or too short, using default');
      // Set a default password
      registration.password = 'password123';
    } else if (registration.password.startsWith('$2') || registration.password.startsWith('$pbkdf2$')) {
      // If it's a hashed password, replace with a simple one
      console.log('Converting hashed password to plain text');
      registration.password = 'password123';
    }

    // Create a new user with the registration data
    await query(
      'INSERT INTO users (id, name, password, role, department, date_created, is_active) VALUES (?, ?, ?, ?, ?, NOW(), ?)',
      [userId, registration.name, registration.password, 'USER', registration.department, true]
    );

    // Log the new user creation
    console.log(`Created new user ${registration.name} with ID: ${userId}`);
    console.log(`User password hash: ${registration.password.substring(0, 20)}...`);

    // Update registration status
    await query('UPDATE pending_registrations SET status = ? WHERE id = ?', ['approved', registrationId]);

    // Create user object for broadcasting
    const newUser = {
      id: userId,
      name: registration.name,
      role: 'USER',
      department: registration.department,
      dateCreated: new Date().toISOString(),
      isActive: true
    };

    // Broadcast user creation to all connected clients
    broadcastUserUpdate('created', newUser);

    // Broadcast registration update
    broadcastRegistrationUpdate('approved', {
      id: registrationId,
      name: registration.name,
      department: registration.department
    });

    // REAL-TIME LOGIN UPDATE: Broadcast to login pages via SSE
    broadcastLoginUpdate('user_approved', {
      user: newUser,
      type: 'approved',
      message: `New user ${registration.name} approved for ${registration.department} department`
    });

    res.json({ message: 'Registration approved successfully' });
  } catch (error) {
    logger.error('Approve registration error:', error);
    res.status(500).json({ error: 'Failed to approve registration' });
  }
});

// Reject pending registration (admin only)
userRouter.post('/registrations/:id/reject', authorize(['admin']), async (req, res) => {
  try {
    const registrationId = req.params.id;

    // Check if registration exists
    const registrations = await query('SELECT * FROM pending_registrations WHERE id = ?', [registrationId]) as any[];
    if (registrations.length === 0) {
      return res.status(404).json({ error: 'Registration not found' });
    }

    const registration = registrations[0];

    // Update registration status
    await query('UPDATE pending_registrations SET status = ? WHERE id = ?', ['rejected', registrationId]);

    // Broadcast registration update
    broadcastRegistrationUpdate('rejected', {
      id: registrationId,
      name: registration.name,
      department: registration.department
    });

    res.json({ message: 'Registration rejected successfully' });
  } catch (error) {
    logger.error('Reject registration error:', error);
    res.status(500).json({ error: 'Failed to reject registration' });
  }
});
