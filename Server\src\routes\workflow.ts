/**
 * Workflow API Routes
 * Handles voucher workflow state transitions and management
 */

import express from 'express';
import { VoucherWorkflowStateMachine, WorkflowEvent, WorkflowState } from '../workflow/VoucherWorkflowStateMachine';
import { authenticate, attachDatabase } from '../middleware/auth';
import { validateWorkflowTransition } from '../middleware/workflow-validation';
import { getWorkflowManager } from '../services/workflow-service';

const router = express.Router();

// Apply database middleware to all routes
router.use(attachDatabase);

/**
 * GET /api/workflow/vouchers/:department
 * Get vouchers filtered by workflow state for specific department
 */
router.get('/vouchers/:department', authenticate, async (req, res) => {
  try {
    const { department } = req.params;
    const { tab, search, dateRange } = req.query;

    // Get all vouchers for department
    const query = `
      SELECT v.*, 
             CASE 
               WHEN v.workflow_state LIKE 'AUDIT_%' AND ? = 'AUDIT' THEN 1
               WHEN v.workflow_state LIKE 'FINANCE_%' AND v.original_department = ? THEN 1
               ELSE 0
             END as is_visible
      FROM vouchers v
      WHERE v.deleted = FALSE
      HAVING is_visible = 1
      ORDER BY 
        CASE v.badge_type
          WHEN 'RE_SUBMITTED' THEN 4

          WHEN 'REJECTED' THEN 2
          ELSE 1
        END DESC,
        v.created_at DESC
    `;

    const [vouchersResult] = await req.db.execute(query, [department, department]);

    // Ensure we have an array of vouchers
    const vouchers = Array.isArray(vouchersResult) ? vouchersResult : [];

    // Filter by tab if specified
    let filteredVouchers = vouchers;
    if (tab) {
      filteredVouchers = vouchers.filter((voucher: any) => {
        const voucherTab = VoucherWorkflowStateMachine.getTabForVoucher(
          voucher, // Pass full voucher object for resubmission logic
          department
        );
        return voucherTab === tab;
      });
    }

    // Apply search filter if specified
    if (search) {
      const searchTerm = search.toString().toLowerCase();
      filteredVouchers = filteredVouchers.filter((voucher: any) =>
        voucher.voucher_id.toLowerCase().includes(searchTerm) ||
        voucher.description?.toLowerCase().includes(searchTerm) ||
        voucher.payee_name?.toLowerCase().includes(searchTerm)
      );
    }

    // Calculate tab counts
    const tabCounts: Record<string, number> = {};
    const availableTabs = VoucherWorkflowStateMachine.getAvailableTabs(department);

    availableTabs.forEach(tabName => {
      tabCounts[tabName] = vouchers.filter((voucher: any) => {
        const voucherTab = VoucherWorkflowStateMachine.getTabForVoucher(
          voucher, // Pass full voucher object for resubmission logic
          department
        );
        return voucherTab === tabName;
      }).length;
    });

    res.json({
      success: true,
      data: {
        vouchers: filteredVouchers,
        tabCounts,
        totalCount: vouchers.length,
        filteredCount: filteredVouchers.length
      }
    });

  } catch (error) {
    console.error('Error fetching workflow vouchers:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch vouchers'
    });
  }
});

/**
 * POST /api/workflow/transition
 * Execute workflow state transition
 */
router.post('/transition', authenticate, validateWorkflowTransition, async (req, res) => {
  try {
    const { voucherId, event, metadata } = req.body;
    const userId = req.user.id;
    const userDepartment = req.user.department;

    const workflowManager = getWorkflowManager();
    const result = await workflowManager.transitionVoucher({
      type: event as WorkflowEvent,
      voucherId,
      context: {
        voucherId,
        userId,
        userDepartment,
        originalDepartment: req.body.originalDepartment,
        timestamp: new Date(),
        metadata
      }
    });

    // WORKFLOW REAL-TIME FIX: Broadcast voucher update after workflow transition
    if (result.success && result.voucher) {
      const { broadcastVoucherUpdate } = await import('../socket/socketHandlers.js');

      broadcastVoucherUpdate('workflow_transition', {
        id: result.voucher.id,
        voucher_id: result.voucher.voucher_id,
        status: result.voucher.status,
        department: result.voucher.department,
        original_department: result.voucher.original_department,
        workflow_state: result.voucher.workflow_state,
        badge_type: result.voucher.badge_type,
        previousState: result.previousState,
        newState: result.newState
      });

      console.log(`📢 REAL-TIME: Broadcasted workflow transition for ${result.voucher.voucher_id}: ${result.previousState} → ${result.newState}`);
    }

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error executing workflow transition:', error);
    res.status(400).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to execute workflow transition'
    });
  }
});

/**
 * GET /api/workflow/voucher/:id/actions
 * Get available actions for a voucher
 */
router.get('/voucher/:id/actions', authenticate, async (req, res) => {
  try {
    const { id } = req.params;
    const userDepartment = req.user.department;

    // Get voucher
    const [vouchers] = await req.db.execute(
      'SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE',
      [id]
    );

    if (vouchers.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Voucher not found'
      });
    }

    const voucher = vouchers[0];
    const currentState = voucher.workflow_state as WorkflowState;

    // Get valid events for current state
    const validEvents = VoucherWorkflowStateMachine.getValidEvents(currentState);

    // Filter events based on user department
    const availableEvents = validEvents.filter(event => {
      if (userDepartment === 'AUDIT') {
        return [
          WorkflowEvent.RECEIVE_FROM_FINANCE,
          WorkflowEvent.START_WORK,
          WorkflowEvent.CERTIFY_VOUCHER,
          WorkflowEvent.REJECT_VOUCHER,
          WorkflowEvent.RETURN_VOUCHER,
          WorkflowEvent.DISPATCH_TO_FINANCE
        ].includes(event);
      } else {
        return [
          WorkflowEvent.SUBMIT_TO_AUDIT,
          WorkflowEvent.RESUBMIT_FROM_REJECTED,
          WorkflowEvent.RESUBMIT_FROM_RETURNED,

        ].includes(event);
      }
    });

    // Map events to action names
    const actions = availableEvents.map(event => ({
      event,
      name: getActionName(event),
      requiresConfirmation: requiresConfirmation(event),
      icon: getActionIcon(event)
    }));

    res.json({
      success: true,
      data: {
        voucher: {
          id: voucher.id,
          voucher_id: voucher.voucher_id,
          workflow_state: voucher.workflow_state,
          badge_type: voucher.badge_type
        },
        actions,
        currentState,
        isEditable: VoucherWorkflowStateMachine.isVoucherEditable(voucher, userDepartment)
      }
    });

  } catch (error) {
    console.error('Error getting voucher actions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get voucher actions'
    });
  }
});

/**
 * GET /api/workflow/audit-log/:voucherId
 * Get workflow audit log for a voucher
 */
router.get('/audit-log/:voucherId', authenticate, async (req, res) => {
  try {
    const { voucherId } = req.params;

    const [logs] = await req.db.execute(`
      SELECT 
        wal.*,
        u.name as user_name,
        u.department as user_department
      FROM workflow_audit_log wal
      LEFT JOIN users u ON wal.user_id = u.id
      WHERE wal.voucher_id = ?
      ORDER BY wal.timestamp DESC
    `, [voucherId]);

    res.json({
      success: true,
      data: logs
    });

  } catch (error) {
    console.error('Error fetching audit log:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch audit log'
    });
  }
});

/**
 * GET /api/workflow/stats/:department
 * Get workflow statistics for department
 */
router.get('/stats/:department', authenticate, async (req, res) => {
  try {
    const { department } = req.params;
    const { period = '30' } = req.query;

    const query = `
      SELECT 
        v.workflow_state,
        v.badge_type,
        COUNT(*) as count,
        AVG(TIMESTAMPDIFF(HOUR, v.created_at, COALESCE(v.last_modified, NOW()))) as avg_processing_hours
      FROM vouchers v
      WHERE v.deleted = FALSE
        AND v.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND (
          (? = 'AUDIT' AND v.workflow_state LIKE 'AUDIT_%') OR
          (? != 'AUDIT' AND v.workflow_state LIKE 'FINANCE_%' AND v.original_department = ?)
        )
      GROUP BY v.workflow_state, v.badge_type
      ORDER BY count DESC
    `;

    const [stats] = await req.db.execute(query, [period, department, department, department]);

    res.json({
      success: true,
      data: {
        period: `${period} days`,
        statistics: stats
      }
    });

  } catch (error) {
    console.error('Error fetching workflow stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch workflow statistics'
    });
  }
});

// Helper functions
function getActionName(event: WorkflowEvent): string {
  const actionNames = {
    [WorkflowEvent.CREATE_VOUCHER]: 'Create Voucher',
    [WorkflowEvent.SUBMIT_TO_AUDIT]: 'Submit to Audit',
    [WorkflowEvent.RECEIVE_FROM_FINANCE]: 'Receive from Finance',
    [WorkflowEvent.START_WORK]: 'Start Work',
    [WorkflowEvent.CERTIFY_VOUCHER]: 'Certify Voucher',
    [WorkflowEvent.REJECT_VOUCHER]: 'Reject Voucher',
    [WorkflowEvent.RETURN_VOUCHER]: 'Return Voucher',
    [WorkflowEvent.DISPATCH_TO_FINANCE]: 'Dispatch to Finance',
    [WorkflowEvent.RESUBMIT_FROM_REJECTED]: 'Resubmit from Rejected',
    [WorkflowEvent.RESUBMIT_FROM_RETURNED]: 'Resubmit from Returned',

  };
  return actionNames[event] || event;
}

function requiresConfirmation(event: WorkflowEvent): boolean {
  return [
    WorkflowEvent.REJECT_VOUCHER,
    WorkflowEvent.RETURN_VOUCHER,
    WorkflowEvent.DISPATCH_TO_FINANCE
  ].includes(event);
}

function getActionIcon(event: WorkflowEvent): string {
  const icons = {
    [WorkflowEvent.CREATE_VOUCHER]: 'plus',
    [WorkflowEvent.SUBMIT_TO_AUDIT]: 'send',
    [WorkflowEvent.RECEIVE_FROM_FINANCE]: 'inbox',
    [WorkflowEvent.START_WORK]: 'play',
    [WorkflowEvent.CERTIFY_VOUCHER]: 'check-circle',
    [WorkflowEvent.REJECT_VOUCHER]: 'x-circle',
    [WorkflowEvent.RETURN_VOUCHER]: 'arrow-left',
    [WorkflowEvent.DISPATCH_TO_FINANCE]: 'arrow-right',
    [WorkflowEvent.RESUBMIT_FROM_REJECTED]: 'refresh-cw',
    [WorkflowEvent.RESUBMIT_FROM_RETURNED]: 'refresh-cw',

  };
  return icons[event] || 'circle';
}

export default router;
