// PRODUCTION-GRADE: Comprehensive System Cleanup Script
// Clears all test data while preserving user accounts and system configuration
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'vms@2025@1989',
  database: process.env.DB_NAME || 'vms_production',
};

// Create connection
const connection = await mysql.createConnection(dbConfig);

// Enhanced logger with timestamps
const logger = {
  info: (message) => console.log(`[${new Date().toISOString()}] [INFO] ${message}`),
  warn: (message) => console.log(`[${new Date().toISOString()}] [WARN] ${message}`),
  error: (message, error) => console.error(`[${new Date().toISOString()}] [ERROR] ${message}`, error),
  success: (message) => console.log(`[${new Date().toISOString()}] [SUCCESS] ✅ ${message}`)
};

// Execute a query with error handling
async function query(sql, params = []) {
  try {
    const [results] = await connection.query(sql, params);
    return results;
  } catch (error) {
    logger.error('Database query error:', error);
    throw error;
  }
}

// Get count of records in a table
async function getCount(tableName) {
  try {
    const result = await query(`SELECT COUNT(*) as count FROM ${tableName}`);
    return result[0].count;
  } catch (error) {
    logger.warn(`Could not get count for table ${tableName}: ${error.message}`);
    return 0;
  }
}

async function clearSystemData() {
  try {
    logger.info('🧹 Starting COMPREHENSIVE system cleanup for fresh production start...');
    logger.info('📋 This will preserve: Users, System Settings, and Configuration');
    logger.info('🗑️  This will remove: All vouchers, batches, sessions, locks, and test data');

    // Disable foreign key checks for cleanup
    await query('SET FOREIGN_KEY_CHECKS = 0');
    logger.info('Disabled foreign key checks for cleanup');

    // 1. VOUCHER DATA CLEANUP
    logger.info('\n📄 CLEANING VOUCHER DATA...');

    const voucherCount = await getCount('vouchers');
    logger.info(`Found ${voucherCount} vouchers to delete`);

    if (voucherCount > 0) {
      await query('DELETE FROM vouchers');
      logger.success(`Deleted ${voucherCount} vouchers`);
    }

    // 2. BATCH DATA CLEANUP
    logger.info('\n📦 CLEANING BATCH DATA...');

    const batchCount = await getCount('voucher_batches');
    logger.info(`Found ${batchCount} batches to delete`);

    if (batchCount > 0) {
      // Clear junction table first
      await query('DELETE FROM batch_vouchers');
      logger.success('Cleared batch-voucher relationships');

      await query('DELETE FROM voucher_batches');
      logger.success(`Deleted ${batchCount} voucher batches`);
    }

    // 3. SESSION CLEANUP
    logger.info('\n🔐 CLEANING SESSION DATA...');

    const sessionCount = await getCount('active_sessions');
    logger.info(`Found ${sessionCount} active sessions to clear`);

    if (sessionCount > 0) {
      await query('DELETE FROM active_sessions');
      logger.success(`Cleared ${sessionCount} active sessions`);
    }

    // 4. RESOURCE LOCKS CLEANUP
    logger.info('\n🔒 CLEANING RESOURCE LOCKS...');

    const lockCount = await getCount('resource_locks');
    logger.info(`Found ${lockCount} resource locks to clear`);

    if (lockCount > 0) {
      await query('DELETE FROM resource_locks');
      logger.success(`Cleared ${lockCount} resource locks`);
    }

    // 5. WORKFLOW SESSIONS CLEANUP
    logger.info('\n⚙️  CLEANING WORKFLOW SESSIONS...');

    try {
      const workflowCount = await getCount('voucher_work_sessions');
      logger.info(`Found ${workflowCount} workflow sessions to clear`);

      if (workflowCount > 0) {
        await query('DELETE FROM voucher_work_sessions');
        logger.success(`Cleared ${workflowCount} workflow sessions`);
      }
    } catch (error) {
      logger.warn('Workflow sessions table may not exist - skipping');
    }

    // 6. NOTIFICATIONS CLEANUP
    logger.info('\n🔔 CLEANING NOTIFICATIONS...');

    try {
      const notificationCount = await getCount('notifications');
      logger.info(`Found ${notificationCount} notifications to clear`);

      if (notificationCount > 0) {
        await query('DELETE FROM notifications');
        logger.success(`Cleared ${notificationCount} notifications`);
      }
    } catch (error) {
      logger.warn('Notifications table may not exist - skipping');
    }

    // 7. AUDIT LOGS CLEANUP (Optional - keep for security)
    logger.info('\n📋 AUDIT LOGS...');
    logger.info('Keeping audit logs for security compliance (recommended)');
    // Uncomment the next lines if you want to clear audit logs too:
    // const auditCount = await getCount('audit_logs');
    // await query('DELETE FROM audit_logs');
    // logger.success(`Cleared ${auditCount} audit log entries`);

    // 8. ATTACHMENT CLEANUP
    logger.info('\n📎 CLEANING ATTACHMENTS...');

    try {
      const attachmentCount = await getCount('voucher_attachments');
      logger.info(`Found ${attachmentCount} attachments to clear`);

      if (attachmentCount > 0) {
        await query('DELETE FROM voucher_attachments');
        logger.success(`Cleared ${attachmentCount} voucher attachments`);
      }
    } catch (error) {
      logger.warn('Attachments table may not exist - skipping');
    }

    // Re-enable foreign key checks
    await query('SET FOREIGN_KEY_CHECKS = 1');
    logger.info('Re-enabled foreign key checks');

    // 9. VERIFY CLEANUP
    logger.info('\n✅ VERIFYING CLEANUP...');

    const finalVoucherCount = await getCount('vouchers');
    const finalBatchCount = await getCount('voucher_batches');
    const finalSessionCount = await getCount('active_sessions');
    const finalLockCount = await getCount('resource_locks');

    logger.success(`Final counts after cleanup:`);
    logger.success(`  - Vouchers: ${finalVoucherCount}`);
    logger.success(`  - Batches: ${finalBatchCount}`);
    logger.success(`  - Sessions: ${finalSessionCount}`);
    logger.success(`  - Locks: ${finalLockCount}`);

    // 10. PRESERVE IMPORTANT DATA
    logger.info('\n🛡️  PRESERVED DATA:');
    const userCount = await getCount('users');
    logger.success(`  - Users: ${userCount} (preserved)`);

    try {
      const settingsCount = await getCount('system_settings');
      logger.success(`  - System Settings: ${settingsCount} (preserved)`);
    } catch (error) {
      logger.warn('System settings table may not exist');
    }

    logger.info('\n🎉 SYSTEM CLEANUP COMPLETED SUCCESSFULLY!');
    logger.info('🚀 Your VMS system is now ready for fresh production use');
    logger.info('👥 All user accounts and system configuration have been preserved');
    logger.info('📊 All test data, vouchers, and sessions have been cleared');

    // Close the connection
    await connection.end();
    process.exit(0);

  } catch (error) {
    logger.error('❌ Error during system cleanup:', error);

    // Re-enable foreign key checks even on error
    try {
      await query('SET FOREIGN_KEY_CHECKS = 1');
    } catch (err) {
      // Ignore error
    }

    // Close the connection even on error
    try {
      await connection.end();
    } catch (err) {
      // Ignore error on closing
    }

    process.exit(1);
  }
}

// Run the cleanup function
clearSystemData();
