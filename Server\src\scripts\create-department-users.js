const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const dotenv = require('dotenv');

dotenv.config();

// Database configuration (matching server config)
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'vms@2025@1989',
  database: process.env.DB_NAME || 'vms_production'
};

async function createDepartmentUsers() {
  let connection;
  
  try {
    console.log('🔗 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    
    // Hash password for all test users
    const password = 'password123';
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Define test users for each department
    const testUsers = [
      {
        id: uuidv4(),
        name: 'Test User - Finance',
        email: '<EMAIL>',
        password: hashedPassword,
        department: 'FINANCE',
        role: 'USER'
      },
      {
        id: uuidv4(),
        name: 'Test User - Ministries',
        email: '<EMAIL>',
        password: hashedPassword,
        department: 'MINISTRIES',
        role: 'USER'
      },
      {
        id: uuidv4(),
        name: 'Test User - Pensions',
        email: '<EMAIL>',
        password: hashedPassword,
        department: 'PENSIONS',
        role: 'USER'
      },
      {
        id: uuidv4(),
        name: 'Test User - Pentmedia',
        email: '<EMAIL>',
        password: hashedPassword,
        department: 'PENTMEDIA',
        role: 'USER'
      },
      {
        id: uuidv4(),
        name: 'Test User - Missions',
        email: '<EMAIL>',
        password: hashedPassword,
        department: 'MISSIONS',
        role: 'USER'
      },
      {
        id: uuidv4(),
        name: 'Test User - Pentsos',
        email: '<EMAIL>',
        password: hashedPassword,
        department: 'PENTSOS',
        role: 'USER'
      }
    ];
    
    console.log('👥 Creating test users for all departments...');
    
    for (const user of testUsers) {
      try {
        // Check if user already exists
        const [existingUsers] = await connection.execute(
          'SELECT id FROM users WHERE email = ? OR department = ?',
          [user.email, user.department]
        );
        
        if (existingUsers.length > 0) {
          console.log(`⚠️  User for ${user.department} department already exists, skipping...`);
          continue;
        }
        
        // Insert new user
        await connection.execute(
          `INSERT INTO users (id, name, email, password, department, role, date_created, is_active)
           VALUES (?, ?, ?, ?, ?, ?, NOW(), 1)`,
          [user.id, user.name, user.email, user.password, user.department, user.role]
        );
        
        console.log(`✅ Created user for ${user.department}: ${user.email}`);
        
      } catch (error) {
        console.error(`❌ Failed to create user for ${user.department}:`, error.message);
      }
    }
    
    // Verify user creation
    console.log('\n📊 Verifying user creation...');
    const [allUsers] = await connection.execute(
      'SELECT department, COUNT(*) as user_count FROM users GROUP BY department ORDER BY department'
    );
    
    console.log('\n📈 USERS BY DEPARTMENT:');
    console.log('========================');
    allUsers.forEach(row => {
      console.log(`${row.department.padEnd(15)} : ${row.user_count} users`);
    });
    
    // Show login credentials
    console.log('\n🔑 TEST USER LOGIN CREDENTIALS:');
    console.log('================================');
    testUsers.forEach(user => {
      console.log(`${user.department.padEnd(15)} : ${user.email} / password123`);
    });
    
    console.log('\n✅ Department user creation completed successfully!');
    
  } catch (error) {
    console.error('❌ Error creating department users:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
if (require.main === module) {
  createDepartmentUsers();
}

module.exports = { createDepartmentUsers };
