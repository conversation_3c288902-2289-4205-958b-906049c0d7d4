/**
 * Feature Parity Audit Script
 * Analyzes current system state for department feature parity
 */

import { query } from '../../dist/database/db.js';
import { logger } from '../../dist/utils/logger.js';

async function runFeatureParityAudit() {
  try {
    logger.info('🔍 Starting Feature Parity Audit...');
    
    // 1. Check Users by Department
    console.log('\n=== USERS BY DEPARTMENT ===');
    const users = await query('SELECT name, department, role FROM users ORDER BY department, name');
    const departmentCounts = {};
    
    users.forEach(user => {
      if (!departmentCounts[user.department]) {
        departmentCounts[user.department] = 0;
      }
      departmentCounts[user.department]++;
      console.log(`${user.name} - ${user.department} (${user.role})`);
    });
    
    console.log('\n=== DEPARTMENT USER COUNTS ===');
    Object.entries(departmentCounts).forEach(([dept, count]) => {
      console.log(`${dept}: ${count} users`);
    });
    
    // 2. Check Vouchers by Department
    console.log('\n=== VOUCHERS BY DEPARTMENT ===');
    const vouchers = await query('SELECT department, original_department, status, COUNT(*) as count FROM vouchers GROUP BY department, original_department, status ORDER BY department');
    vouchers.forEach(v => {
      console.log(`${v.department} (orig: ${v.original_department || 'N/A'}) - ${v.status}: ${v.count} vouchers`);
    });
    
    // 3. Check Batches by Department
    console.log('\n=== BATCHES BY DEPARTMENT ===');
    const batches = await query('SELECT department, from_audit, received, COUNT(*) as count FROM voucher_batches GROUP BY department, from_audit, received ORDER BY department');
    batches.forEach(b => {
      console.log(`${b.department} - From Audit: ${b.from_audit}, Received: ${b.received}: ${b.count} batches`);
    });
    
    // 4. Check Available Departments
    console.log('\n=== DEFINED DEPARTMENTS ===');
    const definedDepartments = ["FINANCE", "MINISTRIES", "PENSIONS", "PENTMEDIA", "MISSIONS", "PENTSOS", "AUDIT", "SYSTEM ADMIN"];
    definedDepartments.forEach(dept => {
      const hasUsers = departmentCounts[dept] > 0;
      console.log(`${dept}: ${hasUsers ? '✅ Has Users' : '❌ No Users'}`);
    });
    
    // 5. Check Database Schema for Department-Specific Fields
    console.log('\n=== DATABASE SCHEMA ANALYSIS ===');
    const voucherColumns = await query('DESCRIBE vouchers');
    console.log('Voucher table columns:');
    voucherColumns.forEach(col => {
      if (col.Field.toLowerCase().includes('department') || col.Field.toLowerCase().includes('original')) {
        console.log(`  ⭐ ${col.Field}: ${col.Type} (${col.Null === 'YES' ? 'nullable' : 'required'})`);
      } else {
        console.log(`  ${col.Field}: ${col.Type}`);
      }
    });
    
    logger.info('✅ Feature Parity Audit completed');
    
  } catch (error) {
    logger.error('❌ Feature Parity Audit failed:', error);
  } finally {
    process.exit(0);
  }
}

runFeatureParityAudit();
