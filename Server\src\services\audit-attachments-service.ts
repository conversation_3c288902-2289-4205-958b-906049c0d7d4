import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import { query } from '../database/db.js';
import { logger } from '../utils/logger.js';
import { 
  getVoucherStoragePath, 
  generateStoredFilename, 
  ensureDirectoryExists,
  validateFile 
} from '../utils/file-storage.js';

export interface AuditAttachment {
  id: string;
  voucher_id: string;
  original_filename: string;
  stored_filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  uploaded_by: string;
  uploaded_at: string;
  is_active: boolean;
  uploader_name?: string;
}

export class AuditAttachmentsService {
  
  /**
   * Upload and attach file to voucher
   */
  static async uploadAttachment(
    voucherId: string,
    file: any,
    uploadedBy: string,
    voucherData: { claimant: string; description: string }
  ): Promise<AuditAttachment> {
    try {
      // Validate file
      const validation = validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Generate unique ID and filenames
      const attachmentId = uuidv4();
      const originalExtension = path.extname(file.originalname);
      const storedFilename = generateStoredFilename(
        voucherData.claimant,
        voucherData.description,
        originalExtension
      );

      // Ensure unique filename (add counter if exists)
      const voucherStoragePath = getVoucherStoragePath(voucherId);
      ensureDirectoryExists(voucherStoragePath);
      
      let finalStoredFilename = storedFilename;
      let counter = 1;
      while (fs.existsSync(path.join(voucherStoragePath, finalStoredFilename))) {
        const nameWithoutExt = path.parse(storedFilename).name;
        const ext = path.parse(storedFilename).ext;
        finalStoredFilename = `${nameWithoutExt}_${counter}${ext}`;
        counter++;
      }

      // Save file to storage
      const filePath = path.join(voucherStoragePath, finalStoredFilename);
      await fs.promises.writeFile(filePath, file.buffer);

      // Save metadata to database
      const attachmentData = {
        id: attachmentId,
        voucher_id: voucherId,
        original_filename: file.originalname,
        stored_filename: finalStoredFilename,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.mimetype,
        uploaded_by: uploadedBy
      };

      await query(`
        INSERT INTO audit_voucher_attachments 
        (id, voucher_id, original_filename, stored_filename, file_path, file_size, mime_type, uploaded_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        attachmentData.id,
        attachmentData.voucher_id,
        attachmentData.original_filename,
        attachmentData.stored_filename,
        attachmentData.file_path,
        attachmentData.file_size,
        attachmentData.mime_type,
        attachmentData.uploaded_by
      ]);

      logger.info(`Audit attachment uploaded: ${finalStoredFilename} for voucher ${voucherId}`);

      return {
        ...attachmentData,
        uploaded_at: new Date().toISOString(),
        is_active: true
      };

    } catch (error) {
      logger.error('Error uploading audit attachment:', error);
      throw error;
    }
  }

  /**
   * Get all attachments for a voucher
   */
  static async getVoucherAttachments(voucherId: string): Promise<AuditAttachment[]> {
    try {
      const attachments = await query(`
        SELECT 
          ava.*,
          u.name as uploader_name
        FROM audit_voucher_attachments ava
        LEFT JOIN users u ON ava.uploaded_by = u.id
        WHERE ava.voucher_id = ? AND ava.is_active = TRUE
        ORDER BY ava.uploaded_at DESC
      `, [voucherId]) as AuditAttachment[];

      return attachments;
    } catch (error) {
      logger.error('Error fetching voucher attachments:', error);
      throw error;
    }
  }

  /**
   * Get single attachment by ID
   */
  static async getAttachmentById(attachmentId: string): Promise<AuditAttachment | null> {
    try {
      const attachments = await query(`
        SELECT 
          ava.*,
          u.name as uploader_name
        FROM audit_voucher_attachments ava
        LEFT JOIN users u ON ava.uploaded_by = u.id
        WHERE ava.id = ? AND ava.is_active = TRUE
      `, [attachmentId]) as AuditAttachment[];

      return attachments.length > 0 ? attachments[0] : null;
    } catch (error) {
      logger.error('Error fetching attachment by ID:', error);
      throw error;
    }
  }

  /**
   * Delete attachment (soft delete)
   */
  static async deleteAttachment(attachmentId: string, deletedBy: string): Promise<boolean> {
    try {
      await query(`
        UPDATE audit_voucher_attachments 
        SET is_active = FALSE 
        WHERE id = ?
      `, [attachmentId]);

      logger.info(`Audit attachment deleted: ${attachmentId} by ${deletedBy}`);
      return true;
    } catch (error) {
      logger.error('Error deleting audit attachment:', error);
      throw error;
    }
  }
}
