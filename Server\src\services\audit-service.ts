import { query } from '../database/db.js';
import { logger } from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

export interface AuditLogEntry {
  id: string;
  timestamp: string;
  user: {
    id: string;
    name: string;
    department: string;
  };
  action: string;
  description: string;
  resourceType: string;
  resourceId?: string;
  details: object;
  ipAddress: string;
  userAgent: string;
  severity: 'INFO' | 'WARNING' | 'ERROR';
}

export class AuditService {
  
  // Ensure audit_logs table exists
  static async ensureAuditTable() {
    try {
      await query(`
        CREATE TABLE IF NOT EXISTS audit_logs (
          id VARCHAR(36) PRIMARY KEY,
          timestamp DATETIME NOT NULL,
          user_id VARCHAR(36) NOT NULL,
          user_name VARCHAR(255) NOT NULL,
          department VARCHAR(50) NOT NULL,
          action VARCHAR(100) NOT NULL,
          description TEXT NOT NULL,
          resource_type VARCHAR(50) NOT NULL,
          resource_id VARCHAR(255),
          details JSON,
          ip_address VARCHAR(50),
          user_agent TEXT,
          severity ENUM('INFO', 'WARNING', 'ERROR') DEFAULT 'INFO',
          INDEX (timestamp),
          INDEX (user_id),
          INDEX (department),
          INDEX (action),
          INDEX (resource_type),
          INDEX (severity)
        )
      `);
    } catch (error) {
      logger.error('Failed to ensure audit_logs table:', error);
    }
  }

  // Log user activity in plain English
  static async logActivity(params: {
    userId: string;
    userName: string;
    department: string;
    action: string;
    description: string;
    resourceType: string;
    resourceId?: string;
    details?: object;
    ipAddress: string;
    userAgent: string;
    severity?: 'INFO' | 'WARNING' | 'ERROR';
  }) {
    try {
      // Ensure table exists first
      await this.ensureAuditTable();

      // Create proper MySQL datetime format
      const mysqlTimestamp = new Date().toISOString().slice(0, 19).replace('T', ' ');

      const auditEntry: AuditLogEntry = {
        id: uuidv4(),
        timestamp: new Date().toISOString(), // Keep ISO for response
        user: {
          id: params.userId,
          name: params.userName,
          department: params.department
        },
        action: params.action,
        description: params.description,
        resourceType: params.resourceType,
        resourceId: params.resourceId,
        details: params.details || {},
        ipAddress: params.ipAddress,
        userAgent: params.userAgent,
        severity: params.severity || 'INFO'
      };

      // Insert into audit_logs table with MySQL-compatible timestamp
      await query(
        `INSERT INTO audit_logs (
          id, timestamp, user_id, user_name, department, action, description,
          resource_type, resource_id, details, ip_address, user_agent, severity
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          auditEntry.id,
          mysqlTimestamp, // Use MySQL-compatible format
          auditEntry.user.id,
          auditEntry.user.name,
          auditEntry.user.department,
          auditEntry.action,
          auditEntry.description,
          auditEntry.resourceType,
          auditEntry.resourceId,
          JSON.stringify(auditEntry.details),
          auditEntry.ipAddress,
          auditEntry.userAgent,
          auditEntry.severity
        ]
      );

      logger.info(`Audit logged: ${auditEntry.description}`);
      return auditEntry;

    } catch (error) {
      logger.error('Failed to log audit entry:', error);
      throw error;
    }
  }

  // Get audit logs with filtering
  static async getAuditLogs(filters: {
    startDate?: string;
    endDate?: string;
    userId?: string;
    department?: string;
    action?: string;
    severity?: string;
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (filters.startDate) {
        whereClause += ' AND timestamp >= ?';
        params.push(filters.startDate);
      }

      if (filters.endDate) {
        whereClause += ' AND timestamp <= ?';
        params.push(filters.endDate);
      }

      if (filters.userId) {
        whereClause += ' AND user_id = ?';
        params.push(filters.userId);
      }

      if (filters.department) {
        whereClause += ' AND department = ?';
        params.push(filters.department);
      }

      if (filters.action) {
        whereClause += ' AND action = ?';
        params.push(filters.action);
      }

      if (filters.severity) {
        whereClause += ' AND severity = ?';
        params.push(filters.severity);
      }

      const limit = filters.limit || 100;
      const offset = filters.offset || 0;

      const logs = await query(
        `SELECT * FROM audit_logs 
         ${whereClause} 
         ORDER BY timestamp DESC 
         LIMIT ? OFFSET ?`,
        [...params, limit, offset]
      ) as any[];

      // Parse details JSON
      const parsedLogs = logs.map(log => ({
        ...log,
        details: JSON.parse(log.details || '{}')
      }));

      return parsedLogs;

    } catch (error) {
      logger.error('Failed to get audit logs:', error);
      throw error;
    }
  }

  // Get audit statistics
  static async getAuditStats(timeframe: 'today' | 'week' | 'month' = 'today') {
    try {
      let dateCondition = '';
      
      switch (timeframe) {
        case 'today':
          dateCondition = 'DATE(timestamp) = CURDATE()';
          break;
        case 'week':
          dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
          break;
        case 'month':
          dateCondition = 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
          break;
      }

      const stats = await query(
        `SELECT 
          COUNT(*) as total_activities,
          COUNT(DISTINCT user_id) as active_users,
          COUNT(CASE WHEN action = 'LOGIN' THEN 1 END) as logins,
          COUNT(CASE WHEN action = 'CREATE_VOUCHER' THEN 1 END) as vouchers_created,
          COUNT(CASE WHEN action = 'CERTIFY_VOUCHER' THEN 1 END) as vouchers_certified,
          COUNT(CASE WHEN severity = 'ERROR' THEN 1 END) as errors,
          COUNT(CASE WHEN severity = 'WARNING' THEN 1 END) as warnings
         FROM audit_logs 
         WHERE ${dateCondition}`,
        []
      ) as any[];

      return stats[0] || {
        total_activities: 0,
        active_users: 0,
        logins: 0,
        vouchers_created: 0,
        vouchers_certified: 0,
        errors: 0,
        warnings: 0
      };

    } catch (error) {
      logger.error('Failed to get audit stats:', error);
      throw error;
    }
  }

  // Get recent activities for live feed
  static async getRecentActivities(limit: number = 20) {
    try {
      const activities = await query(
        `SELECT * FROM audit_logs 
         ORDER BY timestamp DESC 
         LIMIT ?`,
        [limit]
      ) as any[];

      return activities.map(activity => ({
        ...activity,
        details: JSON.parse(activity.details || '{}')
      }));

    } catch (error) {
      logger.error('Failed to get recent activities:', error);
      throw error;
    }
  }

  // Helper methods for common audit actions
  static async logLogin(userId: string, userName: string, department: string, ipAddress: string, userAgent: string) {
    return this.logActivity({
      userId,
      userName,
      department,
      action: 'LOGIN',
      description: `${userName} logged in to ${department} department`,
      resourceType: 'USER',
      resourceId: userId,
      ipAddress,
      userAgent,
      severity: 'INFO'
    });
  }

  static async logLogout(userId: string, userName: string, department: string, ipAddress: string, userAgent: string) {
    return this.logActivity({
      userId,
      userName,
      department,
      action: 'LOGOUT',
      description: `${userName} logged out from ${department} department`,
      resourceType: 'USER',
      resourceId: userId,
      ipAddress,
      userAgent,
      severity: 'INFO'
    });
  }

  static async logVoucherCreate(userId: string, userName: string, department: string, voucherId: string, amount: number, ipAddress: string, userAgent: string) {
    return this.logActivity({
      userId,
      userName,
      department,
      action: 'CREATE_VOUCHER',
      description: `${userName} created voucher #${voucherId} for GH₵ ${amount.toLocaleString()}`,
      resourceType: 'VOUCHER',
      resourceId: voucherId,
      details: { amount },
      ipAddress,
      userAgent,
      severity: 'INFO'
    });
  }

  static async logVoucherDispatch(userId: string, userName: string, department: string, voucherCount: number, targetDept: string, ipAddress: string, userAgent: string) {
    return this.logActivity({
      userId,
      userName,
      department,
      action: 'DISPATCH_VOUCHERS',
      description: `${userName} dispatched ${voucherCount} vouchers from ${department} to ${targetDept}`,
      resourceType: 'VOUCHER_BATCH',
      details: { voucherCount, targetDepartment: targetDept },
      ipAddress,
      userAgent,
      severity: 'INFO'
    });
  }

  static async logVoucherCertify(userId: string, userName: string, department: string, voucherId: string, ipAddress: string, userAgent: string) {
    return this.logActivity({
      userId,
      userName,
      department,
      action: 'CERTIFY_VOUCHER',
      description: `${userName} certified voucher #${voucherId} and returned to originating department`,
      resourceType: 'VOUCHER',
      resourceId: voucherId,
      ipAddress,
      userAgent,
      severity: 'INFO'
    });
  }

  static async logBackupCreate(userId: string, userName: string, department: string, backupType: string, ipAddress: string, userAgent: string) {
    return this.logActivity({
      userId,
      userName,
      department,
      action: 'CREATE_BACKUP',
      description: `${userName} created ${backupType} backup`,
      resourceType: 'SYSTEM',
      details: { backupType },
      ipAddress,
      userAgent,
      severity: 'INFO'
    });
  }

  static async logSystemError(userId: string, userName: string, department: string, error: string, ipAddress: string, userAgent: string) {
    return this.logActivity({
      userId,
      userName,
      department,
      action: 'SYSTEM_ERROR',
      description: `System error occurred for ${userName}: ${error}`,
      resourceType: 'SYSTEM',
      details: { error },
      ipAddress,
      userAgent,
      severity: 'ERROR'
    });
  }
}

export default AuditService;
