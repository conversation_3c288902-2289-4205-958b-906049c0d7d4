/**
 * Enterprise Monitoring Service
 * Advanced monitoring and alerting for production-level admin dashboard
 */

import { query } from '../database/db.js';
import { logger } from '../utils/logger.js';
import { AuditPopulator } from './audit-populator.js';

export class EnterpriseMonitoring {
  
  /**
   * Get comprehensive system metrics for enterprise dashboard
   */
  static async getSystemMetrics(): Promise<any> {
    try {
      // Ensure audit logs are populated
      await AuditPopulator.populateAuditLogs();

      const metrics = await Promise.all([
        this.getDatabaseMetrics(),
        this.getPerformanceMetrics(),
        this.getBusinessMetrics(),
        this.getSecurityMetrics(),
        this.getUserActivityMetrics()
      ]);

      return {
        database: metrics[0],
        performance: metrics[1],
        business: metrics[2],
        security: metrics[3],
        userActivity: metrics[4],
        timestamp: new Date().toISOString(),
        status: this.calculateOverallStatus(metrics)
      };
    } catch (error) {
      logger.error('Error getting system metrics:', error);
      throw error;
    }
  }

  /**
   * Get database performance metrics
   */
  private static async getDatabaseMetrics(): Promise<any> {
    try {
      const [connections] = await query('SHOW STATUS LIKE "Threads_connected"') as any[];
      const [maxConnections] = await query('SHOW VARIABLES LIKE "max_connections"') as any[];
      const [dbSize] = await query(`
        SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
      `) as any[];

      const [slowQueries] = await query('SHOW STATUS LIKE "Slow_queries"') as any[];
      const [uptime] = await query('SHOW STATUS LIKE "Uptime"') as any[];

      const currentConnections = parseInt(connections[0]?.Value || '0');
      const maxConn = parseInt(maxConnections[0]?.Value || '0');

      return {
        connections: currentConnections,
        maxConnections: maxConn,
        connectionUtilization: Math.round((currentConnections / maxConn) * 100),
        sizeMB: parseFloat(dbSize[0]?.size_mb || '0'),
        slowQueries: parseInt(slowQueries[0]?.Value || '0'),
        uptime: parseInt(uptime[0]?.Value || '0'),
        status: currentConnections / maxConn > 0.8 ? 'warning' : 'healthy'
      };
    } catch (error) {
      logger.error('Error getting database metrics:', error);
      return { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get system performance metrics
   */
  private static async getPerformanceMetrics(): Promise<any> {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      return {
        memory: {
          rss: memoryUsage.rss,
          heapTotal: memoryUsage.heapTotal,
          heapUsed: memoryUsage.heapUsed,
          external: memoryUsage.external,
          utilization: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        uptime: process.uptime(),
        nodeVersion: process.version,
        platform: process.platform,
        pid: process.pid,
        status: memoryUsage.heapUsed / memoryUsage.heapTotal > 0.8 ? 'warning' : 'healthy'
      };
    } catch (error) {
      logger.error('Error getting performance metrics:', error);
      return { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get business intelligence metrics
   */
  private static async getBusinessMetrics(): Promise<any> {
    try {
      // Today's voucher statistics
      const [voucherStats] = await query(`
        SELECT 
          COUNT(*) as total_vouchers,
          COUNT(CASE WHEN status = 'VOUCHER CERTIFIED' THEN 1 END) as certified_today,
          COUNT(CASE WHEN status = 'VOUCHER REJECTED' THEN 1 END) as rejected_today,
          COUNT(CASE WHEN created_at >= CURDATE() THEN 1 END) as created_today,
          AVG(CASE WHEN status = 'VOUCHER CERTIFIED' THEN 
            TIMESTAMPDIFF(HOUR, created_at, NOW()) END) as avg_processing_hours
        FROM vouchers 
        WHERE created_at >= CURDATE()
      `) as any[];

      // Batch statistics (using correct column names)
      const [batchStats] = await query(`
        SELECT
          COUNT(*) as total_batches,
          COUNT(CASE WHEN received = 1 THEN 1 END) as received_today
        FROM voucher_batches
        WHERE sent_time >= CURDATE()
      `) as any[];

      // Department performance
      const [deptStats] = await query(`
        SELECT 
          department,
          COUNT(*) as vouchers_created,
          COUNT(CASE WHEN status = 'VOUCHER CERTIFIED' THEN 1 END) as vouchers_certified
        FROM vouchers 
        WHERE created_at >= CURDATE()
        GROUP BY department
        ORDER BY vouchers_created DESC
      `) as any[];

      const stats = voucherStats[0] || {};
      const batches = batchStats[0] || {};

      return {
        vouchers: {
          created: parseInt(stats.created_today || '0'),
          certified: parseInt(stats.certified_today || '0'),
          rejected: parseInt(stats.rejected_today || '0'),
          processingRate: stats.total_vouchers > 0 ? 
            Math.round((stats.certified_today / stats.total_vouchers) * 100) : 0,
          avgProcessingHours: parseFloat(stats.avg_processing_hours || '0')
        },
        batches: {
          created: parseInt(batches.total_batches || '0'),
          received: parseInt(batches.received_today || '0')
        },
        departments: deptStats,
        status: 'healthy'
      };
    } catch (error) {
      logger.error('Error getting business metrics:', error);
      return { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get security and audit metrics
   */
  private static async getSecurityMetrics(): Promise<any> {
    try {
      // Recent security events
      const [securityEvents] = await query(`
        SELECT 
          COUNT(*) as total_events,
          COUNT(CASE WHEN severity = 'ERROR' THEN 1 END) as error_events,
          COUNT(CASE WHEN severity = 'WARNING' THEN 1 END) as warning_events,
          COUNT(CASE WHEN action = 'LOGIN' THEN 1 END) as login_attempts,
          COUNT(CASE WHEN action = 'LOGIN' AND severity = 'ERROR' THEN 1 END) as failed_logins
        FROM audit_logs 
        WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      `) as any[];

      // Active sessions analysis
      const [sessionStats] = await query(`
        SELECT 
          COUNT(*) as total_sessions,
          COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_sessions,
          COUNT(DISTINCT user_id) as unique_users,
          AVG(TIMESTAMPDIFF(MINUTE, session_start, COALESCE(session_end, NOW()))) as avg_session_minutes
        FROM active_sessions 
        WHERE session_start >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      `) as any[];

      const events = securityEvents[0] || {};
      const sessions = sessionStats[0] || {};

      return {
        events: {
          total: parseInt(events.total_events || '0'),
          errors: parseInt(events.error_events || '0'),
          warnings: parseInt(events.warning_events || '0'),
          loginAttempts: parseInt(events.login_attempts || '0'),
          failedLogins: parseInt(events.failed_logins || '0')
        },
        sessions: {
          total: parseInt(sessions.total_sessions || '0'),
          active: parseInt(sessions.active_sessions || '0'),
          uniqueUsers: parseInt(sessions.unique_users || '0'),
          avgSessionMinutes: parseFloat(sessions.avg_session_minutes || '0')
        },
        status: events.error_events > 10 ? 'warning' : 'healthy'
      };
    } catch (error) {
      logger.error('Error getting security metrics:', error);
      return { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get user activity metrics
   */
  private static async getUserActivityMetrics(): Promise<any> {
    try {
      // Current active users
      const [activeUsers] = await query(`
        SELECT 
          user_name, department, last_activity,
          TIMESTAMPDIFF(MINUTE, last_activity, NOW()) as idle_minutes
        FROM active_sessions 
        WHERE is_active = TRUE 
        ORDER BY last_activity DESC
        LIMIT 10
      `) as any[];

      // Department activity
      const [deptActivity] = await query(`
        SELECT 
          department,
          COUNT(DISTINCT user_id) as active_users,
          COUNT(*) as total_activities
        FROM audit_logs 
        WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        GROUP BY department
        ORDER BY total_activities DESC
      `) as any[];

      return {
        activeUsers: activeUsers,
        departmentActivity: deptActivity,
        totalActiveUsers: activeUsers.length,
        status: 'healthy'
      };
    } catch (error) {
      logger.error('Error getting user activity metrics:', error);
      return { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Calculate overall system status
   */
  private static calculateOverallStatus(metrics: any[]): string {
    const statuses = metrics.map(m => m.status).filter(s => s);
    
    if (statuses.includes('error')) return 'error';
    if (statuses.includes('warning')) return 'warning';
    return 'healthy';
  }

  /**
   * Get system alerts
   */
  static async getSystemAlerts(): Promise<any[]> {
    try {
      const alerts = [];
      const metrics = await this.getSystemMetrics();

      // Database connection alert
      if (metrics.database.connectionUtilization > 80) {
        alerts.push({
          type: 'warning',
          title: 'High Database Connection Usage',
          message: `Database connections at ${metrics.database.connectionUtilization}%`,
          timestamp: new Date().toISOString()
        });
      }

      // Memory usage alert
      if (metrics.performance.memory.utilization > 80) {
        alerts.push({
          type: 'warning',
          title: 'High Memory Usage',
          message: `Memory utilization at ${metrics.performance.memory.utilization}%`,
          timestamp: new Date().toISOString()
        });
      }

      // Security alert
      if (metrics.security.events.errors > 5) {
        alerts.push({
          type: 'error',
          title: 'Security Events Detected',
          message: `${metrics.security.events.errors} error events in the last 24 hours`,
          timestamp: new Date().toISOString()
        });
      }

      return alerts;
    } catch (error) {
      logger.error('Error getting system alerts:', error);
      return [];
    }
  }
}
