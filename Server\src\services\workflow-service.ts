/**
 * Workflow Service Initialization
 * Sets up the workflow manager and related services
 */

import { VoucherWorkflowManager } from '../workflow/VoucherWorkflowManager';
import { logger } from '../utils/logger';

let workflowManager: VoucherWorkflowManager | null = null;

/**
 * Initialize workflow service
 */
export async function initializeWorkflowService(db: any, eventPublisher: any) {
  try {
    logger.info('Initializing workflow service...');

    // Create audit logger
    const auditLogger = {
      log: async (data: any, tx?: any) => {
        const query = `
          INSERT INTO workflow_audit_log 
          (id, voucher_id, from_state, to_state, event_type, user_id, timestamp, copy_id, metadata)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const values = [
          generateId(),
          data.voucherId,
          data.fromState,
          data.toState,
          data.event,
          data.userId,
          data.timestamp,
          data.copyId || null,
          JSON.stringify(data.metadata || {})
        ];

        if (tx) {
          await tx.execute(query, values);
        } else {
          await db.execute(query, values);
        }
      }
    };

    // Create event publisher
    const workflowEventPublisher = {
      publish: async (eventType: string, eventData: any) => {
        try {
          // Publish to WebSocket if available
          if (eventPublisher && eventPublisher.emit) {
            eventPublisher.emit(eventType, eventData);
          }

          // Log event for debugging
          logger.info(`Workflow event published: ${eventType}`, {
            voucherId: eventData.voucherId,
            newState: eventData.newState,
            affectedDepartments: eventData.affectedDepartments
          });

        } catch (error) {
          logger.error('Error publishing workflow event:', error);
        }
      }
    };

    // Initialize workflow manager
    workflowManager = new VoucherWorkflowManager(db, workflowEventPublisher, auditLogger);

    logger.info('✅ Workflow service initialized successfully');
    return workflowManager;

  } catch (error) {
    logger.error('❌ Failed to initialize workflow service:', error);
    throw error;
  }
}

/**
 * Get workflow manager instance
 */
export function getWorkflowManager(): VoucherWorkflowManager {
  if (!workflowManager) {
    throw new Error('Workflow service not initialized. Call initializeWorkflowService first.');
  }
  return workflowManager;
}

/**
 * Generate unique ID
 */
function generateId(): string {
  return require('uuid').v4();
}

/**
 * Workflow service health check
 */
export async function checkWorkflowServiceHealth(): Promise<{
  status: 'healthy' | 'unhealthy';
  details: any;
}> {
  try {
    if (!workflowManager) {
      return {
        status: 'unhealthy',
        details: { error: 'Workflow manager not initialized' }
      };
    }

    // Test basic functionality
    const testResult = {
      workflowManagerInitialized: !!workflowManager,
      timestamp: new Date().toISOString()
    };

    return {
      status: 'healthy',
      details: testResult
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      details: { error: error instanceof Error ? error.message : 'Unknown error' }
    };
  }
}
