import { query } from '../database/db.js';
import { logger } from '../utils/logger.js';
import { yearDatabaseManager } from '../database/year-manager.js';

interface SystemSettings {
  fiscal_year_start: string;
  fiscal_year_end: string;
  current_fiscal_year: number;
  system_time: string;
  auto_backup_enabled: boolean;
  session_timeout: number;
}

export class YearRolloverService {
  private rolloverCheckInterval: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL = 24 * 60 * 60 * 1000; // Check daily
  private rolloverInProgress = false;
  private rolloverStatus: any = null;

  constructor() {
    // SAFETY: Don't start automatic monitoring immediately on startup
    // This prevents unwanted rollovers during development/testing
    setTimeout(() => {
      this.startRolloverMonitoring();
    }, 60000); // Wait 1 minute after startup
  }

  /**
   * Start monitoring for automatic year rollover
   */
  private startRolloverMonitoring(): void {
    // Check immediately on startup
    this.checkForYearRollover();

    // Then check daily
    this.rolloverCheckInterval = setInterval(() => {
      this.checkForYearRollover();
    }, this.CHECK_INTERVAL);

    logger.info('🗓️ Year rollover monitoring started - checking daily');
  }

  /**
   * Stop monitoring (for graceful shutdown)
   */
  public stopRolloverMonitoring(): void {
    if (this.rolloverCheckInterval) {
      clearInterval(this.rolloverCheckInterval);
      this.rolloverCheckInterval = null;
      logger.info('🗓️ Year rollover monitoring stopped');
    }
  }

  /**
   * Check if year rollover is needed based on fiscal year configuration
   */
  private async checkForYearRollover(): Promise<void> {
    try {
      logger.info('🔍 Checking for year rollover...');

      // Get system settings
      const settings = await this.getSystemSettings();
      if (!settings) {
        logger.warn('⚠️ No system settings found - skipping year rollover check');
        return;
      }

      // Determine current date (use system time override if set)
      const currentDate = this.getCurrentDate(settings);
      const currentCalendarYear = currentDate.getFullYear();

      // Calculate fiscal year based on settings
      const fiscalYear = this.calculateFiscalYear(currentDate, settings);

      logger.info(`📅 Current date: ${currentDate.toISOString()}`);
      logger.info(`📊 Calendar year: ${currentCalendarYear}`);
      logger.info(`📈 Calculated fiscal year: ${fiscalYear}`);
      logger.info(`⚙️ Configured fiscal year: ${settings.current_fiscal_year}`);

      // SAFETY CHECK: Only rollover if we're clearly in the next year AND it's a reasonable rollover
      const rolloverNeeded = fiscalYear > settings.current_fiscal_year;
      const isReasonableRollover = (fiscalYear - settings.current_fiscal_year) === 1; // Only 1 year ahead

      if (rolloverNeeded && isReasonableRollover) {
        logger.info(`🚀 YEAR ROLLOVER NEEDED: ${settings.current_fiscal_year} → ${fiscalYear}`);

        // ADDITIONAL SAFETY: Don't rollover if system time is overridden for testing
        if (settings.system_time && settings.system_time !== new Date().toISOString().slice(0, 19)) {
          logger.warn(`⚠️ ROLLOVER SKIPPED: System time is overridden for testing (${settings.system_time})`);
          logger.warn(`⚠️ To enable rollover, reset system time in Admin Panel or set current_fiscal_year to ${fiscalYear}`);
          return;
        }

        await this.performYearRollover(settings.current_fiscal_year, fiscalYear, settings);
      } else if (rolloverNeeded && !isReasonableRollover) {
        logger.warn(`⚠️ SUSPICIOUS ROLLOVER DETECTED: ${settings.current_fiscal_year} → ${fiscalYear} (${fiscalYear - settings.current_fiscal_year} years ahead)`);
        logger.warn(`⚠️ This may indicate a configuration issue. Please check fiscal year settings.`);
      } else {
        logger.debug(`✅ No rollover needed - fiscal year ${settings.current_fiscal_year} is current`);
      }

    } catch (error) {
      logger.error('❌ Error during year rollover check:', error);
    }
  }

  /**
   * Get system settings from database
   */
  private async getSystemSettings(): Promise<SystemSettings | null> {
    try {
      const results = await query('SELECT * FROM system_settings LIMIT 1') as SystemSettings[];
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      logger.error('Error fetching system settings:', error);
      return null;
    }
  }

  /**
   * Get current date (with system time override support)
   */
  private getCurrentDate(settings: SystemSettings): Date {
    if (settings.system_time && settings.system_time !== new Date().toISOString().slice(0, 19)) {
      // Use system time override if configured
      const overrideDate = new Date(settings.system_time);
      if (!isNaN(overrideDate.getTime())) {
        logger.info(`⏰ Using system time override: ${overrideDate.toISOString()}`);
        return overrideDate;
      }
    }
    
    // Use actual system time
    return new Date();
  }

  /**
   * Calculate fiscal year based on current date and fiscal year configuration
   * CONSERVATIVE APPROACH: Only rollover when we're clearly in the next fiscal year
   */
  private calculateFiscalYear(currentDate: Date, settings: SystemSettings): number {
    const currentMonth = currentDate.getMonth(); // 0-11
    const currentYear = currentDate.getFullYear();

    // Convert month names to numbers
    const monthMap: Record<string, number> = {
      'JAN': 0, 'FEB': 1, 'MAR': 2, 'APR': 3, 'MAY': 4, 'JUN': 5,
      'JUL': 6, 'AUG': 7, 'SEP': 8, 'OCT': 9, 'NOV': 10, 'DEC': 11
    };

    const fiscalStartMonth = monthMap[settings.fiscal_year_start] ?? 0; // Default to January

    // CONSERVATIVE LOGIC: For calendar year (JAN-DEC), only rollover after January 1st
    if (settings.fiscal_year_start === 'JAN') {
      // For calendar year, fiscal year = calendar year
      return currentYear;
    }

    // For non-calendar fiscal years, use traditional logic
    if (currentMonth < fiscalStartMonth) {
      return currentYear;
    } else {
      return currentYear + 1;
    }
  }

  /**
   * Perform automatic year rollover with detailed status tracking
   */
  private async performYearRollover(oldYear: number, newYear: number, settings: SystemSettings): Promise<void> {
    try {
      this.rolloverInProgress = true;
      logger.info(`🔄 STARTING YEAR ROLLOVER: ${oldYear} → ${newYear}`);

      // Initialize rollover status
      this.rolloverStatus = {
        isRolloverInProgress: true,
        currentStep: 'Initializing year rollover...',
        progress: 0,
        oldYear,
        newYear,
        estimatedTimeRemaining: 120, // 2 minutes estimate
        steps: [
          { id: 'init', name: 'Initialize Rollover', description: 'Preparing for year transition', status: 'in-progress', startTime: new Date().toISOString() },
          { id: 'backup', name: 'Create Backup', description: 'Backing up current year data', status: 'pending' },
          { id: 'database', name: 'Create New Database', description: `Creating database for ${newYear}`, status: 'pending' },
          { id: 'settings', name: 'Update Settings', description: 'Updating system configuration', status: 'pending' },
          { id: 'audit', name: 'Log Event', description: 'Recording rollover in audit log', status: 'pending' },
          { id: 'notify', name: 'Notify Administrators', description: 'Sending completion notifications', status: 'pending' },
          { id: 'complete', name: 'Finalize', description: 'Completing rollover process', status: 'pending' }
        ]
      };

      // Step 1: Initialize (already done)
      await this.updateRolloverStep('init', 'completed', 'Rollover initialization complete');
      this.rolloverStatus.progress = 15;

      // Step 2: Create backup if auto-backup is enabled
      await this.updateRolloverStep('backup', 'in-progress', 'Creating backup of current data...');
      if (settings.auto_backup_enabled) {
        logger.info('💾 Creating backup before year rollover...');
        await this.createYearRolloverBackup(oldYear);
      }
      await this.updateRolloverStep('backup', 'completed', 'Backup created successfully');
      this.rolloverStatus.progress = 30;

      // Step 3: Create new year database
      await this.updateRolloverStep('database', 'in-progress', `Creating database for ${newYear}...`);
      logger.info(`🗄️ Creating database for year ${newYear}...`);
      const dbCreated = await yearDatabaseManager.createYearDatabase(newYear);

      if (!dbCreated) {
        throw new Error(`Failed to create database for year ${newYear}`);
      }
      await this.updateRolloverStep('database', 'completed', `Database for ${newYear} created successfully`);
      this.rolloverStatus.progress = 60;

      // Step 4: Update system settings
      await this.updateRolloverStep('settings', 'in-progress', 'Updating system configuration...');
      logger.info('⚙️ Updating system settings...');
      await query(
        'UPDATE system_settings SET current_fiscal_year = ? ORDER BY id LIMIT 1',
        [newYear]
      );
      await this.updateRolloverStep('settings', 'completed', 'System settings updated');
      this.rolloverStatus.progress = 75;

      // Step 5: Log the rollover event
      await this.updateRolloverStep('audit', 'in-progress', 'Recording rollover event...');
      await this.logYearRolloverEvent(oldYear, newYear);
      await this.updateRolloverStep('audit', 'completed', 'Rollover event logged');
      this.rolloverStatus.progress = 90;

      // Step 6: Notify administrators
      await this.updateRolloverStep('notify', 'in-progress', 'Notifying administrators...');
      await this.notifyAdministrators(oldYear, newYear);
      await this.updateRolloverStep('notify', 'completed', 'Administrators notified');
      this.rolloverStatus.progress = 95;

      // Step 7: Finalize
      await this.updateRolloverStep('complete', 'in-progress', 'Finalizing rollover...');
      await this.updateRolloverStep('complete', 'completed', 'Year rollover completed successfully');
      this.rolloverStatus.progress = 100;
      this.rolloverStatus.currentStep = `Year rollover to ${newYear} completed successfully`;
      this.rolloverStatus.estimatedTimeRemaining = 0;

      logger.info(`✅ YEAR ROLLOVER COMPLETED: ${oldYear} → ${newYear}`);

      // Keep status for a few minutes for users to see completion
      setTimeout(() => {
        this.rolloverInProgress = false;
        this.rolloverStatus = null;
      }, 5 * 60 * 1000); // 5 minutes

    } catch (error) {
      logger.error(`❌ YEAR ROLLOVER FAILED: ${oldYear} → ${newYear}`, error);

      // Update status to show error
      if (this.rolloverStatus) {
        this.rolloverStatus.error = error instanceof Error ? error.message : 'Unknown error';
        this.rolloverStatus.currentStep = 'Rollover failed - please contact administrator';
      }

      // Notify administrators of failure
      await this.notifyRolloverFailure(oldYear, newYear, error);
      this.rolloverInProgress = false;
      throw error;
    }
  }

  /**
   * Update rollover step status
   */
  private async updateRolloverStep(stepId: string, status: string, description?: string): Promise<void> {
    if (!this.rolloverStatus) return;

    const step = this.rolloverStatus.steps.find((s: any) => s.id === stepId);
    if (step) {
      step.status = status;
      if (description) step.description = description;
      if (status === 'in-progress') step.startTime = new Date().toISOString();
      if (status === 'completed') step.endTime = new Date().toISOString();

      this.rolloverStatus.currentStep = description || step.description;
    }

    // Small delay to make progress visible
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  /**
   * Create backup before year rollover
   */
  private async createYearRolloverBackup(year: number): Promise<void> {
    try {
      // This would integrate with your existing backup system
      logger.info(`💾 Backup created for year ${year} before rollover`);
      
      // Update last backup date (fix MySQL subquery issue)
      await query(
        'UPDATE system_settings SET last_backup_date = ? ORDER BY id LIMIT 1',
        [new Date().toISOString()]
      );
    } catch (error) {
      logger.error('Backup creation failed:', error);
      // Don't fail rollover if backup fails, but log it
    }
  }

  /**
   * Log year rollover event
   */
  private async logYearRolloverEvent(oldYear: number, newYear: number): Promise<void> {
    try {
      await query(
        `INSERT INTO audit_logs (id, user_id, action, resource_type, resource_id, details, timestamp, ip_address)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          `rollover-${Date.now()}`,
          'system',
          'YEAR_ROLLOVER',
          'SYSTEM',
          'fiscal_year',
          JSON.stringify({ oldYear, newYear, automatic: true }),
          new Date().toISOString(),
          'system'
        ]
      );
    } catch (error) {
      logger.error('Failed to log year rollover event:', error);
    }
  }

  /**
   * Notify administrators of successful rollover
   */
  private async notifyAdministrators(oldYear: number, newYear: number): Promise<void> {
    try {
      // Get all admin users
      const admins = await query(
        'SELECT id FROM users WHERE role = ? AND is_active = TRUE',
        ['ADMIN']
      ) as any[];

      // Create notifications for each admin (fix ID length issue)
      for (const admin of admins) {
        const notificationId = `rollover-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        await query(
          `INSERT INTO notifications (id, user_id, message, type, timestamp, is_read)
           VALUES (?, ?, ?, ?, ?, ?)`,
          [
            notificationId.substring(0, 36), // Limit to 36 characters
            admin.id,
            `Automatic year rollover completed: ${oldYear} → ${newYear}. New fiscal year database created.`,
            'SYSTEM_ALERT',
            new Date().toISOString(),
            false
          ]
        );
      }

      logger.info(`📧 Notified ${admins.length} administrators of year rollover`);
    } catch (error) {
      logger.error('Failed to notify administrators:', error);
    }
  }

  /**
   * Notify administrators of rollover failure
   */
  private async notifyRolloverFailure(oldYear: number, newYear: number, error: any): Promise<void> {
    try {
      const admins = await query(
        'SELECT id FROM users WHERE role = ? AND is_active = TRUE',
        ['ADMIN']
      ) as any[];

      for (const admin of admins) {
        const notificationId = `fail-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        await query(
          `INSERT INTO notifications (id, user_id, message, type, timestamp, is_read)
           VALUES (?, ?, ?, ?, ?, ?)`,
          [
            notificationId.substring(0, 36), // Limit to 36 characters
            admin.id,
            `URGENT: Automatic year rollover failed (${oldYear} → ${newYear}). Manual intervention required. Error: ${error.message}`,
            'SYSTEM_ERROR',
            new Date().toISOString(),
            false
          ]
        );
      }
    } catch (notifyError) {
      logger.error('Failed to notify administrators of rollover failure:', notifyError);
    }
  }

  /**
   * Manual year rollover trigger (for admin use)
   */
  public async triggerManualRollover(targetYear: number): Promise<boolean> {
    try {
      const settings = await this.getSystemSettings();
      if (!settings) {
        throw new Error('System settings not found');
      }

      logger.info(`🔧 Manual year rollover triggered: ${settings.current_fiscal_year} → ${targetYear}`);
      await this.performYearRollover(settings.current_fiscal_year, targetYear, settings);
      return true;
    } catch (error) {
      logger.error('Manual year rollover failed:', error);
      return false;
    }
  }

  /**
   * Get rollover status and next rollover date
   */
  public async getRolloverStatus(): Promise<any> {
    try {
      // If rollover is in progress, return detailed status
      if (this.rolloverInProgress && this.rolloverStatus) {
        return this.rolloverStatus;
      }

      const settings = await this.getSystemSettings();
      if (!settings) return null;

      const currentDate = this.getCurrentDate(settings);
      const nextFiscalYear = this.calculateFiscalYear(currentDate, settings);

      // Calculate next rollover date
      const nextRolloverDate = this.calculateNextRolloverDate(settings);

      return {
        isRolloverInProgress: false,
        currentFiscalYear: settings.current_fiscal_year,
        nextFiscalYear,
        nextRolloverDate: nextRolloverDate.toISOString(),
        daysUntilRollover: Math.ceil((nextRolloverDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24)),
        rolloverNeeded: nextFiscalYear > settings.current_fiscal_year,
        progress: 0,
        steps: []
      };
    } catch (error) {
      logger.error('Error getting rollover status:', error);
      return null;
    }
  }

  /**
   * Calculate next rollover date
   */
  private calculateNextRolloverDate(settings: SystemSettings): Date {
    const currentYear = new Date().getFullYear();
    const monthMap: Record<string, number> = {
      'JAN': 0, 'FEB': 1, 'MAR': 2, 'APR': 3, 'MAY': 4, 'JUN': 5,
      'JUL': 6, 'AUG': 7, 'SEP': 8, 'OCT': 9, 'NOV': 10, 'DEC': 11
    };

    const fiscalStartMonth = monthMap[settings.fiscal_year_start] ?? 0;
    const nextRolloverDate = new Date(currentYear + 1, fiscalStartMonth, 1);
    
    return nextRolloverDate;
  }
}

// Export singleton instance
export const yearRolloverService = new YearRolloverService();
