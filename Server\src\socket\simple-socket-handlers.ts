/**
 * BULLETPROOF WebSocket Handlers
 * 
 * Simplified, reliable WebSocket handling for VMS
 * - No complex dependencies
 * - Robust error handling
 * - Simple connection management
 */

import { Server as SocketIOServer, Socket } from 'socket.io';
import { logger } from '../utils/logger.js';

// Simple connection tracking
const connections = new Map<string, {
  id: string;
  userId?: string;
  userName?: string;
  department?: string;
  connectedAt: number;
  lastActivity: number;
}>();

let io: SocketIOServer | null = null;

export function setIoInstance(ioInstance: SocketIOServer) {
  io = ioInstance;
  logger.info('✅ Socket.IO instance set');
}

export function getIoInstance(): SocketIOServer | null {
  return io;
}

export function setupSocketHandlers(ioInstance: SocketIOServer) {
  io = ioInstance;

  io.on('connection', (socket: Socket) => {
    handleConnection(socket);
  });

  // Global error handling
  io.on('error', (error) => {
    logger.error('Socket.IO server error:', error);
  });

  logger.info('✅ Socket handlers initialized');
}

function handleConnection(socket: Socket) {
  const connectionId = socket.id;
  const now = Date.now();

  // Track connection
  connections.set(connectionId, {
    id: connectionId,
    connectedAt: now,
    lastActivity: now
  });

  logger.info(`🔗 WebSocket connected: ${connectionId}`);

  // Authentication
  socket.on('authenticate', (data) => {
    try {
      const { userId, userName, department, token } = data || {};
      
      if (!userId || !userName) {
        socket.emit('auth_error', { message: 'Missing required fields' });
        return;
      }

      // Update connection info
      const connection = connections.get(connectionId);
      if (connection) {
        connection.userId = userId;
        connection.userName = userName;
        connection.department = department;
        connection.lastActivity = Date.now();
      }

      // Join department room if provided
      if (department) {
        socket.join(department);
        logger.info(`📂 Socket joined department: ${connectionId} → ${department}`);
      }

      socket.emit('authenticated', {
        success: true,
        connectionId,
        timestamp: Date.now()
      });

      logger.info(`✅ Socket authenticated: ${connectionId} (${userName} - ${department})`);

    } catch (error) {
      logger.error('Authentication error:', error);
      socket.emit('auth_error', { message: 'Authentication failed' });
    }
  });

  // Join department room
  socket.on('join_department', (data) => {
    try {
      const { department } = data || {};
      
      if (!department) {
        socket.emit('join_error', { message: 'Department is required' });
        return;
      }

      socket.join(department);
      
      const connection = connections.get(connectionId);
      if (connection) {
        connection.department = department;
        connection.lastActivity = Date.now();
      }

      socket.emit('joined_department', {
        department,
        success: true,
        timestamp: Date.now()
      });

      logger.info(`📂 Socket joined department: ${connectionId} → ${department}`);

    } catch (error) {
      logger.error('Join department error:', error);
      socket.emit('join_error', { message: 'Failed to join department' });
    }
  });

  // Heartbeat
  socket.on('ping', () => {
    try {
      socket.emit('pong', { timestamp: Date.now() });
      updateActivity(connectionId);
    } catch (error) {
      logger.error('Ping error:', error);
    }
  });

  // Generic message broadcasting
  socket.on('broadcast_to_department', (data) => {
    try {
      const { department, event, payload } = data || {};
      
      if (!department || !event) {
        socket.emit('broadcast_error', { message: 'Department and event are required' });
        return;
      }

      io?.to(department).emit(event, payload);
      updateActivity(connectionId);

      logger.debug(`📡 Broadcast to ${department}: ${event}`);

    } catch (error) {
      logger.error('Broadcast error:', error);
      socket.emit('broadcast_error', { message: 'Broadcast failed' });
    }
  });

  // Disconnection
  socket.on('disconnect', (reason) => {
    handleDisconnection(connectionId, reason);
  });

  // Error handling
  socket.on('error', (error) => {
    logger.error(`Socket error for ${connectionId}:`, error);
  });

  // Activity tracking for all events
  socket.onAny(() => {
    updateActivity(connectionId);
  });
}

function updateActivity(connectionId: string) {
  const connection = connections.get(connectionId);
  if (connection) {
    connection.lastActivity = Date.now();
  }
}

function handleDisconnection(connectionId: string, reason: string) {
  const connection = connections.get(connectionId);
  
  if (connection) {
    connections.delete(connectionId);
    logger.info(`🔌 WebSocket disconnected: ${connectionId} (${reason})`);
  }
}

// Utility functions for sending messages
export function sendToUser(userId: string, event: string, data: any): boolean {
  if (!io) return false;

  try {
    // Find all connections for this user
    let sent = false;
    for (const [socketId, connection] of connections) {
      if (connection.userId === userId) {
        const socket = io.sockets.sockets.get(socketId);
        if (socket) {
          socket.emit(event, data);
          sent = true;
        }
      }
    }

    if (sent) {
      logger.debug(`📤 Message sent to user ${userId}: ${event}`);
    }

    return sent;
  } catch (error) {
    logger.error('Send to user error:', error);
    return false;
  }
}

export function sendToDepartment(department: string, event: string, data: any): boolean {
  if (!io) return false;

  try {
    io.to(department).emit(event, data);
    logger.debug(`📤 Message sent to department ${department}: ${event}`);
    return true;
  } catch (error) {
    logger.error('Send to department error:', error);
    return false;
  }
}

export function broadcast(event: string, data: any): boolean {
  if (!io) return false;

  try {
    io.emit(event, data);
    logger.debug(`📡 Broadcast sent: ${event}`);
    return true;
  } catch (error) {
    logger.error('Broadcast error:', error);
    return false;
  }
}

export function getConnectionStats() {
  const stats = {
    total: connections.size,
    byDepartment: {} as Record<string, number>,
    authenticated: 0
  };

  for (const connection of connections.values()) {
    if (connection.userId) {
      stats.authenticated++;
    }
    if (connection.department) {
      stats.byDepartment[connection.department] = (stats.byDepartment[connection.department] || 0) + 1;
    }
  }

  return stats;
}

// Clean up inactive connections
setInterval(() => {
  const now = Date.now();
  const timeout = 5 * 60 * 1000; // 5 minutes

  for (const [socketId, connection] of connections) {
    if (now - connection.lastActivity > timeout) {
      const socket = io?.sockets.sockets.get(socketId);
      if (socket) {
        socket.disconnect(true);
      }
      connections.delete(socketId);
      logger.info(`🧹 Cleaned up inactive connection: ${socketId}`);
    }
  }
}, 60000); // Check every minute
