/**
 * Production-Grade Event Deduplication System
 * Prevents infinite loops and duplicate event processing
 */

import { logger } from './logger.js';

interface EventKey {
  type: string;
  entityId: string;
  userId?: string;
  timestamp?: number;
}

interface EventRecord {
  key: string;
  timestamp: number;
  count: number;
  lastSeen: number;
}

export class EventDeduplicator {
  private static instance: EventDeduplicator;
  private eventCache = new Map<string, EventRecord>();
  private readonly maxAge = 30000; // 30 seconds
  private readonly maxDuplicates = 3; // Max 3 identical events
  private readonly cleanupInterval = 60000; // Cleanup every minute
  private cleanupTimer?: NodeJS.Timeout;

  private constructor() {
    this.startCleanup();
  }

  static getInstance(): EventDeduplicator {
    if (!EventDeduplicator.instance) {
      EventDeduplicator.instance = new EventDeduplicator();
    }
    return EventDeduplicator.instance;
  }

  /**
   * Check if event should be processed (not a duplicate)
   */
  shouldProcess(eventKey: EventKey): boolean {
    const key = this.generateKey(eventKey);
    const now = Date.now();
    const existing = this.eventCache.get(key);

    if (!existing) {
      // First time seeing this event
      this.eventCache.set(key, {
        key,
        timestamp: now,
        count: 1,
        lastSeen: now
      });
      return true;
    }

    // Check if event is too old (reset counter)
    if (now - existing.timestamp > this.maxAge) {
      this.eventCache.set(key, {
        key,
        timestamp: now,
        count: 1,
        lastSeen: now
      });
      return true;
    }

    // Check if we've seen too many duplicates
    if (existing.count >= this.maxDuplicates) {
      logger.warn(`🚫 Event deduplication: Blocking duplicate event`, {
        eventType: eventKey.type,
        entityId: eventKey.entityId,
        count: existing.count,
        age: now - existing.timestamp
      });
      return false;
    }

    // Update existing record
    existing.count++;
    existing.lastSeen = now;
    this.eventCache.set(key, existing);

    return true;
  }

  /**
   * Mark event as processed (for tracking)
   */
  markProcessed(eventKey: EventKey): void {
    const key = this.generateKey(eventKey);
    const existing = this.eventCache.get(key);
    
    if (existing) {
      existing.lastSeen = Date.now();
      this.eventCache.set(key, existing);
    }
  }

  /**
   * Generate unique key for event
   */
  private generateKey(eventKey: EventKey): string {
    const parts = [
      eventKey.type,
      eventKey.entityId,
      eventKey.userId || 'system'
    ];
    return parts.join(':');
  }

  /**
   * Start cleanup timer
   */
  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  /**
   * Clean up old events
   */
  private cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, record] of this.eventCache.entries()) {
      if (now - record.lastSeen > this.maxAge * 2) {
        this.eventCache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      logger.debug(`🧹 Event deduplicator cleaned ${cleaned} old events`);
    }
  }

  /**
   * Get current cache stats
   */
  getStats(): { totalEvents: number; oldestEvent: number; newestEvent: number } {
    const now = Date.now();
    let oldest = now;
    let newest = 0;

    for (const record of this.eventCache.values()) {
      if (record.timestamp < oldest) oldest = record.timestamp;
      if (record.lastSeen > newest) newest = record.lastSeen;
    }

    return {
      totalEvents: this.eventCache.size,
      oldestEvent: oldest === now ? 0 : now - oldest,
      newestEvent: newest === 0 ? 0 : now - newest
    };
  }

  /**
   * Clear all events (for testing)
   */
  clear(): void {
    this.eventCache.clear();
    logger.info('🧹 Event deduplicator cache cleared');
  }

  /**
   * Shutdown cleanup
   */
  shutdown(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    this.eventCache.clear();
  }
}

// Export singleton instance
export const eventDeduplicator = EventDeduplicator.getInstance();
