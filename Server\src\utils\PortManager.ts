import net from 'net';
import { logger } from './logger.js';

/**
 * Dynamic Port Manager
 * Automatically finds available ports and handles conflicts
 */
export class PortManager {
  private static instance: PortManager;
  private usedPorts: Set<number> = new Set();
  private preferredPorts: number[] = [3000, 3001, 3002, 8080, 8081, 8082];
  private portRange = { min: 3000, max: 8999 };

  private constructor() {}

  static getInstance(): PortManager {
    if (!PortManager.instance) {
      PortManager.instance = new PortManager();
    }
    return PortManager.instance;
  }

  /**
   * Find an available port starting from preferred ports
   */
  async findAvailablePort(preferredPort?: number): Promise<number> {
    // Try preferred port first if provided
    if (preferredPort && await this.isPortAvailable(preferredPort)) {
      this.usedPorts.add(preferredPort);
      logger.info(`🎯 Using preferred port: ${preferredPort}`);
      return preferredPort;
    }

    // Try preferred ports list
    for (const port of this.preferredPorts) {
      if (await this.isPortAvailable(port)) {
        this.usedPorts.add(port);
        logger.info(`✅ Found available preferred port: ${port}`);
        return port;
      }
    }

    // Search in range
    for (let port = this.portRange.min; port <= this.portRange.max; port++) {
      if (!this.usedPorts.has(port) && await this.isPortAvailable(port)) {
        this.usedPorts.add(port);
        logger.info(`🔍 Found available port in range: ${port}`);
        return port;
      }
    }

    throw new Error(`No available ports found in range ${this.portRange.min}-${this.portRange.max}`);
  }

  /**
   * Check if a port is available
   */
  private async isPortAvailable(port: number): Promise<boolean> {
    return new Promise((resolve) => {
      const server = net.createServer();
      
      server.listen(port, () => {
        server.once('close', () => {
          resolve(true);
        });
        server.close();
      });

      server.on('error', () => {
        resolve(false);
      });
    });
  }

  /**
   * Release a port when no longer needed
   */
  releasePort(port: number): void {
    this.usedPorts.delete(port);
    logger.info(`🔓 Released port: ${port}`);
  }

  /**
   * Get all currently used ports
   */
  getUsedPorts(): number[] {
    return Array.from(this.usedPorts);
  }

  /**
   * Check if port is in use by this manager
   */
  isPortInUse(port: number): boolean {
    return this.usedPorts.has(port);
  }

  /**
   * Find multiple available ports
   */
  async findMultiplePorts(count: number): Promise<number[]> {
    const ports: number[] = [];
    
    for (let i = 0; i < count; i++) {
      try {
        const port = await this.findAvailablePort();
        ports.push(port);
      } catch (error) {
        // Release any ports we've allocated if we can't get all requested
        ports.forEach(p => this.releasePort(p));
        throw new Error(`Could not allocate ${count} ports. Only found ${ports.length}`);
      }
    }

    return ports;
  }

  /**
   * Set custom port range
   */
  setPortRange(min: number, max: number): void {
    if (min >= max || min < 1024 || max > 65535) {
      throw new Error('Invalid port range. Must be between 1024-65535 and min < max');
    }
    this.portRange = { min, max };
    logger.info(`📊 Port range updated: ${min}-${max}`);
  }

  /**
   * Add preferred ports
   */
  addPreferredPorts(ports: number[]): void {
    this.preferredPorts = [...new Set([...this.preferredPorts, ...ports])];
    logger.info(`⭐ Preferred ports updated: ${this.preferredPorts.join(', ')}`);
  }

  /**
   * Get port allocation summary
   */
  getPortSummary(): {
    usedPorts: number[];
    preferredPorts: number[];
    portRange: { min: number; max: number };
    availableInRange: number;
  } {
    const availableInRange = this.portRange.max - this.portRange.min + 1 - this.usedPorts.size;
    
    return {
      usedPorts: Array.from(this.usedPorts),
      preferredPorts: this.preferredPorts,
      portRange: this.portRange,
      availableInRange
    };
  }
}

/**
 * Singleton instance
 */
export const portManager = PortManager.getInstance();
