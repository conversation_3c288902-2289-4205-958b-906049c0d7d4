/**
 * PRODUCTION-READY Retry Manager with Exponential Backoff
 * Handles transient failures with intelligent retry strategies
 */

export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number;        // Base delay in milliseconds
  maxDelay: number;         // Maximum delay in milliseconds
  backoffFactor: number;    // Exponential backoff multiplier
  jitter: boolean;          // Add randomness to prevent thundering herd
  retryableErrors?: string[]; // Error types that should trigger retry
  onRetry?: (attempt: number, error: any) => void; // Callback on retry
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: any;
  attempts: number;
  totalTime: number;
}

export class RetryManager {
  protected readonly defaultOptions: RetryOptions = {
    maxAttempts: 3,
    baseDelay: 1000,      // 1 second
    maxDelay: 30000,      // 30 seconds
    backoffFactor: 2,
    jitter: true,
    retryableErrors: [
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'PROTOCOL_CONNECTION_LOST',
      'ER_LOCK_WAIT_TIMEOUT',
      'ER_LOCK_DEADLOCK'
    ]
  };

  /**
   * Execute function with retry logic
   */
  async execute<T>(
    fn: () => Promise<T>,
    options: Partial<RetryOptions> = {}
  ): Promise<T> {
    const opts = { ...this.defaultOptions, ...options };
    const startTime = Date.now();
    let lastError: any;

    for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
      try {
        const result = await fn();
        return result;
      } catch (error) {
        lastError = error;

        // Check if error is retryable
        if (!this.isRetryableError(error, opts.retryableErrors)) {
          throw error;
        }

        // Don't retry on last attempt
        if (attempt === opts.maxAttempts) {
          break;
        }

        // Calculate delay with exponential backoff and jitter
        const delay = this.calculateDelay(attempt, opts);

        // Call retry callback if provided
        if (opts.onRetry) {
          opts.onRetry(attempt, error);
        }

        // Wait before retrying
        await this.sleep(delay);
      }
    }

    // All attempts failed
    throw lastError;
  }

  /**
   * Execute with detailed result information
   */
  async executeWithResult<T>(
    fn: () => Promise<T>,
    options: Partial<RetryOptions> = {}
  ): Promise<RetryResult<T>> {
    const opts = { ...this.defaultOptions, ...options };
    const startTime = Date.now();
    let lastError: any;

    for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
      try {
        const result = await fn();
        return {
          success: true,
          result,
          attempts: attempt,
          totalTime: Date.now() - startTime
        };
      } catch (error) {
        lastError = error;

        if (!this.isRetryableError(error, opts.retryableErrors) || attempt === opts.maxAttempts) {
          break;
        }

        const delay = this.calculateDelay(attempt, opts);

        if (opts.onRetry) {
          opts.onRetry(attempt, error);
        }

        await this.sleep(delay);
      }
    }

    return {
      success: false,
      error: lastError,
      attempts: opts.maxAttempts,
      totalTime: Date.now() - startTime
    };
  }

  /**
   * Check if error should trigger a retry
   */
  private isRetryableError(error: any, retryableErrors?: string[]): boolean {
    if (!retryableErrors || retryableErrors.length === 0) {
      return true; // Retry all errors if no specific errors defined
    }

    return retryableErrors.some(retryableError =>
      error.message?.includes(retryableError) ||
      error.code === retryableError ||
      error.name === retryableError ||
      error.errno === retryableError
    );
  }

  /**
   * Calculate delay with exponential backoff and jitter
   */
  private calculateDelay(attempt: number, options: RetryOptions): number {
    // Exponential backoff: baseDelay * (backoffFactor ^ (attempt - 1))
    let delay = options.baseDelay * Math.pow(options.backoffFactor, attempt - 1);

    // Cap at maximum delay
    delay = Math.min(delay, options.maxDelay);

    // Add jitter to prevent thundering herd problem
    if (options.jitter) {
      // Add random jitter of ±25%
      const jitterRange = delay * 0.25;
      const jitter = (Math.random() - 0.5) * 2 * jitterRange;
      delay += jitter;
    }

    return Math.max(0, Math.floor(delay));
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create a retryable version of a function
   */
  wrap<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    options: Partial<RetryOptions> = {}
  ): T {
    const retryManager = this;

    return (async (...args: any[]) => {
      return retryManager.execute(() => fn(...args), options);
    }) as T;
  }
}

/**
 * Specialized retry managers for different scenarios
 */
export class DatabaseRetryManager extends RetryManager {
  constructor() {
    super();
    // Override default options for database operations
    Object.assign(this.defaultOptions, {
      maxAttempts: 5,
      baseDelay: 500,
      maxDelay: 10000,
      retryableErrors: [
        'PROTOCOL_CONNECTION_LOST',
        'ECONNRESET',
        'ENOTFOUND',
        'ECONNREFUSED',
        'ETIMEDOUT',
        'ER_LOCK_WAIT_TIMEOUT',
        'ER_LOCK_DEADLOCK',
        'ER_QUERY_INTERRUPTED'
      ]
    });
  }
}

export class APIRetryManager extends RetryManager {
  constructor() {
    super();
    // Override default options for API calls
    Object.assign(this.defaultOptions, {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 15000,
      retryableErrors: [
        'ECONNRESET',
        'ENOTFOUND',
        'ECONNREFUSED',
        'ETIMEDOUT',
        'NETWORK_ERROR',
        'TIMEOUT'
      ]
    });
  }
}

export class WebSocketRetryManager extends RetryManager {
  constructor() {
    super();
    // Override default options for WebSocket connections
    Object.assign(this.defaultOptions, {
      maxAttempts: 10,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffFactor: 1.5, // Slower backoff for WebSocket
      retryableErrors: [
        'ECONNRESET',
        'ENOTFOUND',
        'ECONNREFUSED',
        'ETIMEDOUT',
        'WS_CONNECTION_FAILED',
        'WS_DISCONNECTED'
      ]
    });
  }
}

// Singleton instances
export const retryManager = new RetryManager();
export const databaseRetryManager = new DatabaseRetryManager();
export const apiRetryManager = new APIRetryManager();
export const webSocketRetryManager = new WebSocketRetryManager();
