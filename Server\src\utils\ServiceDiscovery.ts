import dgram from 'dgram';
import os from 'os';
import { logger } from './logger.js';

export interface ServiceInfo {
  serviceName: string;
  host: string;
  port: number;
  version: string;
  timestamp: number;
  capabilities: string[];
}

/**
 * Service Discovery System
 * Handles automatic server announcement and client discovery
 */
export class ServiceDiscovery {
  private static instance: ServiceDiscovery;
  private broadcastSocket: dgram.Socket | null = null;
  private discoverySocket: dgram.Socket | null = null;
  private serviceInfo: ServiceInfo | null = null;
  private broadcastInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  // Configuration
  private readonly BROADCAST_PORT = 45454;
  private readonly DISCOVERY_PORT = 45455;
  private readonly BROADCAST_INTERVAL = 5000; // 5 seconds
  private readonly SERVICE_NAME = 'VMS-Server';

  private constructor() {}

  static getInstance(): ServiceDiscovery {
    if (!ServiceDiscovery.instance) {
      ServiceDiscovery.instance = new ServiceDiscovery();
    }
    return ServiceDiscovery.instance;
  }

  /**
   * Start service announcement
   */
  async startAnnouncement(port: number): Promise<void> {
    if (this.isRunning) {
      logger.warn('🔊 Service discovery already running');
      return;
    }

    try {
      // Get local IP addresses
      const localIPs = this.getLocalIPAddresses();
      
      this.serviceInfo = {
        serviceName: this.SERVICE_NAME,
        host: localIPs[0] || 'localhost',
        port: port,
        version: '3.0.0',
        timestamp: Date.now(),
        capabilities: ['voucher-management', 'real-time-updates', 'multi-user']
      };

      // Create broadcast socket
      this.broadcastSocket = dgram.createSocket('udp4');
      this.broadcastSocket.bind(() => {
        this.broadcastSocket!.setBroadcast(true);
        logger.info(`📡 Service discovery broadcast started on port ${this.BROADCAST_PORT}`);
      });

      // Create discovery response socket
      this.discoverySocket = dgram.createSocket('udp4');
      this.discoverySocket.bind(this.DISCOVERY_PORT, () => {
        logger.info(`🔍 Service discovery listener started on port ${this.DISCOVERY_PORT}`);
      });

      // Handle discovery requests
      this.discoverySocket.on('message', (msg, rinfo) => {
        try {
          const request = JSON.parse(msg.toString());
          if (request.type === 'VMS_DISCOVERY_REQUEST') {
            this.sendDiscoveryResponse(rinfo.address, rinfo.port);
          }
        } catch (error) {
          // Ignore invalid messages
        }
      });

      // Start periodic announcements
      this.startPeriodicAnnouncements();
      
      this.isRunning = true;
      logger.info(`🎯 VMS Service Discovery started - Broadcasting on all network interfaces`);
      logger.info(`📊 Service Info: ${JSON.stringify(this.serviceInfo, null, 2)}`);

    } catch (error) {
      logger.error('❌ Failed to start service discovery:', error);
      throw error;
    }
  }

  /**
   * Stop service announcement
   */
  async stopAnnouncement(): Promise<void> {
    if (!this.isRunning) return;

    try {
      // Stop periodic announcements
      if (this.broadcastInterval) {
        clearInterval(this.broadcastInterval);
        this.broadcastInterval = null;
      }

      // Close sockets
      if (this.broadcastSocket) {
        this.broadcastSocket.close();
        this.broadcastSocket = null;
      }

      if (this.discoverySocket) {
        this.discoverySocket.close();
        this.discoverySocket = null;
      }

      this.isRunning = false;
      logger.info('🔇 Service discovery stopped');

    } catch (error) {
      logger.error('❌ Error stopping service discovery:', error);
    }
  }

  /**
   * Send discovery response to specific client
   */
  private sendDiscoveryResponse(clientIP: string, clientPort: number): void {
    if (!this.serviceInfo || !this.broadcastSocket) return;

    const response = {
      type: 'VMS_DISCOVERY_RESPONSE',
      service: this.serviceInfo,
      localIPs: this.getLocalIPAddresses()
    };

    const message = Buffer.from(JSON.stringify(response));
    this.broadcastSocket.send(message, clientPort, clientIP, (error) => {
      if (error) {
        logger.error(`❌ Failed to send discovery response to ${clientIP}:${clientPort}:`, error);
      } else {
        logger.info(`📤 Sent discovery response to ${clientIP}:${clientPort}`);
      }
    });
  }

  /**
   * Start periodic service announcements
   */
  private startPeriodicAnnouncements(): void {
    this.broadcastInterval = setInterval(() => {
      this.broadcastServiceInfo();
    }, this.BROADCAST_INTERVAL);

    // Send initial announcement
    this.broadcastServiceInfo();
  }

  /**
   * Broadcast service information
   */
  private broadcastServiceInfo(): void {
    if (!this.serviceInfo || !this.broadcastSocket) return;

    const announcement = {
      type: 'VMS_SERVICE_ANNOUNCEMENT',
      service: {
        ...this.serviceInfo,
        timestamp: Date.now() // Update timestamp
      },
      localIPs: this.getLocalIPAddresses()
    };

    const message = Buffer.from(JSON.stringify(announcement));
    
    // Broadcast to all network interfaces
    const broadcastAddresses = this.getBroadcastAddresses();
    
    broadcastAddresses.forEach(address => {
      this.broadcastSocket!.send(message, this.BROADCAST_PORT, address, (error) => {
        if (error) {
          logger.debug(`❌ Broadcast failed to ${address}:`, error.message);
        } else {
          logger.debug(`📡 Broadcasted to ${address}:${this.BROADCAST_PORT}`);
        }
      });
    });
  }

  /**
   * Get local IP addresses
   */
  private getLocalIPAddresses(): string[] {
    const interfaces = os.networkInterfaces();
    const addresses: string[] = [];

    for (const name of Object.keys(interfaces)) {
      const nets = interfaces[name];
      if (!nets) continue;

      for (const net of nets) {
        // Skip internal and non-IPv4 addresses
        if (net.family === 'IPv4' && !net.internal) {
          addresses.push(net.address);
        }
      }
    }

    return addresses;
  }

  /**
   * Get broadcast addresses for all network interfaces
   */
  private getBroadcastAddresses(): string[] {
    const interfaces = os.networkInterfaces();
    const broadcasts: string[] = ['***************']; // Global broadcast

    for (const name of Object.keys(interfaces)) {
      const nets = interfaces[name];
      if (!nets) continue;

      for (const net of nets) {
        if (net.family === 'IPv4' && !net.internal && net.netmask) {
          // Calculate broadcast address
          const broadcast = this.calculateBroadcastAddress(net.address, net.netmask);
          if (broadcast && !broadcasts.includes(broadcast)) {
            broadcasts.push(broadcast);
          }
        }
      }
    }

    return broadcasts;
  }

  /**
   * Calculate broadcast address from IP and netmask
   */
  private calculateBroadcastAddress(ip: string, netmask: string): string | null {
    try {
      const ipParts = ip.split('.').map(Number);
      const maskParts = netmask.split('.').map(Number);
      
      const broadcast = ipParts.map((part, index) => {
        return part | (255 - maskParts[index]);
      });

      return broadcast.join('.');
    } catch (error) {
      return null;
    }
  }

  /**
   * Get current service information
   */
  getServiceInfo(): ServiceInfo | null {
    return this.serviceInfo;
  }

  /**
   * Check if service discovery is running
   */
  isServiceRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Update service port (when port changes dynamically)
   */
  updateServicePort(newPort: number): void {
    if (this.serviceInfo) {
      this.serviceInfo.port = newPort;
      this.serviceInfo.timestamp = Date.now();
      logger.info(`🔄 Service port updated to: ${newPort}`);
    }
  }
}

/**
 * Singleton instance
 */
export const serviceDiscovery = ServiceDiscovery.getInstance();
