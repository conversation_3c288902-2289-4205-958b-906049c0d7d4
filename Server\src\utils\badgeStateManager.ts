/**
 * Badge State Manager
 * Centralized backend logic for managing voucher badge states
 * Ensures consistent badge persistence across all workflow transitions
 */

import { logger } from '../utils/logger';

export interface VoucherBadgeState {
  is_resubmitted: boolean;
  resubmission_certified_visible_to_finance: boolean;
  resubmission_tracking_visible_to_audit: boolean;
  last_resubmission_date: Date | null;
  resubmission_count: number;

  badge_persistence_flags: any;
}

export class BadgeStateManager {
  /**
   * Determines if a voucher should be marked as a resubmission
   */
  static isResubmissionVoucher(voucher: any): boolean {
    // Primary indicators
    const hasResubmissionFlag = voucher.is_resubmitted === 1 || voucher.is_resubmitted === true;
    const hasResubmissionStatus = voucher.status === 'RE-SUBMISSION';
    const hasResubmissionWorkflow = voucher.workflow_state?.includes('RESUBMISSION') ||
                                   voucher.workflow_state === 'AUDIT_NEW_RESUBMITTED';
    const hasRejectionHistory = voucher.rejected_by && voucher.rejected_by.trim() !== '';

    return hasResubmissionFlag || hasResubmissionStatus || hasResubmissionWorkflow || hasRejectionHistory;
  }



  /**
   * Sets the resubmission flag and related fields for a voucher
   * REQUIREMENT 3: Enhanced to preserve original rejection reasons
   */
  static async setResubmissionState(
    connection: any,
    voucherId: string,
    isResubmission: boolean,
    options: {
      incrementCount?: boolean;
      setCertifiedVisibility?: boolean;
      setAuditVisibility?: boolean;
      preserveRejectionReason?: boolean;
    } = {}
  ): Promise<void> {
    const {
      incrementCount = false,
      setCertifiedVisibility = false,
      setAuditVisibility = false,
      preserveRejectionReason = true
    } = options;

    try {
      // REQUIREMENT 3: First preserve original rejection information if this is a new resubmission
      if (isResubmission && preserveRejectionReason) {
        await this.preserveOriginalRejectionReason(connection, voucherId);
      }

      let updateQuery = `
        UPDATE vouchers SET
        is_resubmitted = ?,
        last_resubmission_date = ${isResubmission ? 'NOW()' : 'NULL'}
      `;
      let updateParams = [isResubmission ? 1 : 0];

      // Increment resubmission count if requested
      if (incrementCount && isResubmission) {
        updateQuery += ', resubmission_count = COALESCE(resubmission_count, 0) + 1';
      }

      // Set visibility flags if requested
      if (setCertifiedVisibility) {
        updateQuery += ', resubmission_certified_visible_to_finance = ?';
        updateParams.push(isResubmission ? 1 : 0);
      }

      if (setAuditVisibility) {
        updateQuery += ', resubmission_tracking_visible_to_audit = ?';
        updateParams.push(isResubmission ? 1 : 0);
      }

      updateQuery += ' WHERE id = ?';
      updateParams.push(voucherId as any);

      await connection.query(updateQuery, updateParams);

      logger.info(`🏷️  Badge State: Set resubmission=${isResubmission} for voucher ${voucherId}`);
    } catch (error) {
      logger.error(`❌ Error setting resubmission state for voucher ${voucherId}:`, error);
      throw error;
    }
  }

  /**
   * REQUIREMENT 3: Preserves original rejection reason for resubmissions
   * ENHANCED: Now finds rejection reason from copy vouchers if main voucher comment is null
   */
  static async preserveOriginalRejectionReason(connection: any, voucherId: string): Promise<void> {
    try {
      // Check if original rejection info is already preserved
      const [existing] = await connection.query(`
        SELECT voucher_id, original_rejection_reason, original_rejected_by, original_rejection_date,
               rejected_by, rejection_time, comment
        FROM vouchers
        WHERE id = ?
      `, [voucherId]);

      if (existing.length === 0) {
        throw new Error(`Voucher ${voucherId} not found`);
      }

      const voucher = existing[0];

      // Only preserve if not already preserved and there's rejection info to preserve
      if (!voucher.original_rejection_reason && voucher.rejected_by) {
        let rejectionReason = voucher.comment;

        // CRITICAL FIX: If main voucher has no comment, find it from the rejection copy
        if (!rejectionReason || rejectionReason === 'null' || rejectionReason.trim() === '') {
          logger.info(`🔍 Badge State: Main voucher ${voucherId} has no rejection reason, searching copy vouchers...`);

          const [copyVouchers] = await connection.query(`
            SELECT comment, rejected_by, rejection_time
            FROM vouchers
            WHERE voucher_id LIKE ? AND is_rejection_copy = 1 AND comment IS NOT NULL AND comment != 'null' AND comment != ''
            ORDER BY created_at DESC
            LIMIT 1
          `, [`${voucher.voucher_id}%`]);

          if (copyVouchers.length > 0) {
            rejectionReason = copyVouchers[0].comment;
            logger.info(`✅ Badge State: Found rejection reason in copy voucher: "${rejectionReason}"`);
          }
        }

        // Update the main voucher with preserved rejection info
        // CRITICAL FIX: Only update comment if it's currently empty/null, preserve existing comments
        await connection.query(`
          UPDATE vouchers SET
          original_rejection_reason = ?,
          original_rejected_by = rejected_by,
          original_rejection_date = COALESCE(rejection_time, NOW()),
          comment = CASE
            WHEN comment IS NULL OR comment = '' OR comment = 'null' OR comment = 'NO COMMENT PROVIDED'
            THEN COALESCE(?, 'Rejected without specific reason')
            ELSE comment
          END
          WHERE id = ?
        `, [
          rejectionReason || 'Rejected without specific reason',
          rejectionReason,
          voucherId
        ]);

        logger.info(`🏷️  Badge State: Preserved original rejection reason for voucher ${voucherId}: "${rejectionReason || 'Rejected without specific reason'}"`);
      }
    } catch (error) {
      logger.error(`❌ Error preserving original rejection reason for voucher ${voucherId}:`, error);
      throw error;
    }
  }





  /**
   * Ensures badge persistence for certified returns
   * These should appear in both Finance CERTIFIED and Audit DISPATCHED tabs
   */
  static async ensureCertifiedResubmissionVisibility(
    connection: any,
    voucherId: string,
    isCertified: boolean
  ): Promise<void> {
    try {
      // Check if this is a resubmission
      const [vouchers] = await connection.query(
        'SELECT is_resubmitted, status FROM vouchers WHERE id = ?',
        [voucherId]
      );

      if (vouchers.length === 0) {
        throw new Error(`Voucher ${voucherId} not found`);
      }

      const voucher = vouchers[0];
      const isResubmission = voucher.is_resubmitted === 1;

      if (isResubmission && isCertified) {
        // Set visibility flags for certified resubmissions
        await connection.query(`
          UPDATE vouchers SET 
          resubmission_certified_visible_to_finance = 1,
          resubmission_tracking_visible_to_audit = 1,
          badge_persistence_flags = JSON_SET(
            COALESCE(badge_persistence_flags, '{}'),
            '$.certified_resubmission',
            true,
            '$.persistent_in_dispatched',
            true,
            '$.persistent_in_certified',
            true
          )
          WHERE id = ?
        `, [voucherId]);

        logger.info(`🎯 Badge Persistence: Set certified resubmission visibility for voucher ${voucherId}`);
      }
    } catch (error) {
      logger.error(`❌ Error ensuring certified resubmission visibility for voucher ${voucherId}:`, error);
      throw error;
    }
  }



  /**
   * Gets the current badge state for a voucher
   */
  static async getBadgeState(connection: any, voucherId: string): Promise<VoucherBadgeState | null> {
    try {
      const [vouchers] = await connection.query(`
        SELECT
          is_resubmitted,
          resubmission_certified_visible_to_finance,
          resubmission_tracking_visible_to_audit,
          last_resubmission_date,
          resubmission_count,

          badge_persistence_flags
        FROM vouchers
        WHERE id = ?
      `, [voucherId]);

      if (vouchers.length === 0) {
        return null;
      }

      const voucher = vouchers[0];
      return {
        is_resubmitted: voucher.is_resubmitted === 1,
        resubmission_certified_visible_to_finance: voucher.resubmission_certified_visible_to_finance === 1,
        resubmission_tracking_visible_to_audit: voucher.resubmission_tracking_visible_to_audit === 1,
        last_resubmission_date: voucher.last_resubmission_date,
        resubmission_count: voucher.resubmission_count || 0,

        badge_persistence_flags: voucher.badge_persistence_flags
      };
    } catch (error) {
      logger.error(`❌ Error getting badge state for voucher ${voucherId}:`, error);
      throw error;
    }
  }

  /**
   * Clears all resubmission flags (used when voucher is reset)
   */
  static async clearResubmissionState(connection: any, voucherId: string): Promise<void> {
    try {
      await connection.query(`
        UPDATE vouchers SET 
        is_resubmitted = 0,
        resubmission_certified_visible_to_finance = 0,
        resubmission_tracking_visible_to_audit = 0,
        last_resubmission_date = NULL,
        badge_persistence_flags = NULL
        WHERE id = ?
      `, [voucherId]);

      logger.info(`🧹 Badge State: Cleared resubmission state for voucher ${voucherId}`);
    } catch (error) {
      logger.error(`❌ Error clearing resubmission state for voucher ${voucherId}:`, error);
      throw error;
    }
  }


}
