import fs from 'fs';
import path from 'path';
import { logger } from './logger.js';

// File storage configuration
export const STORAGE_CONFIG = {
  baseDir: path.join(process.cwd(), '..', 'uploads'),
  auditAttachmentsDir: 'audit-attachments',
  allowedMimeTypes: ['application/pdf', 'image/jpeg'],
  allowedExtensions: ['.pdf', '.jpg', '.jpeg'],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFilenameLength: 200
};

/**
 * Initialize storage directories
 */
export function initializeStorage(): void {
  try {
    const currentYear = new Date().getFullYear().toString();
    const auditDir = path.join(STORAGE_CONFIG.baseDir, STORAGE_CONFIG.auditAttachmentsDir, currentYear);
    
    // Create directories if they don't exist
    if (!fs.existsSync(STORAGE_CONFIG.baseDir)) {
      fs.mkdirSync(STORAGE_CONFIG.baseDir, { recursive: true });
      logger.info('Created base uploads directory');
    }
    
    if (!fs.existsSync(auditDir)) {
      fs.mkdirSync(auditDir, { recursive: true });
      logger.info(`Created audit attachments directory for ${currentYear}`);
    }
    
    logger.info('File storage initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize file storage:', error);
    throw error;
  }
}

/**
 * Get storage path for a voucher
 */
export function getVoucherStoragePath(voucherId: string): string {
  const currentYear = new Date().getFullYear().toString();
  return path.join(
    STORAGE_CONFIG.baseDir,
    STORAGE_CONFIG.auditAttachmentsDir,
    currentYear,
    `voucher-${voucherId}`
  );
}

/**
 * Sanitize filename for safe storage
 */
export function sanitizeFilename(filename: string): string {
  // Remove or replace unsafe characters
  return filename
    .replace(/[<>:"/\\|?*]/g, '_') // Replace unsafe chars with underscore
    .replace(/\s+/g, '_') // Replace spaces with underscore
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .substring(0, STORAGE_CONFIG.maxFilenameLength); // Limit length
}

/**
 * Generate stored filename from voucher data
 */
export function generateStoredFilename(claimant: string, description: string, originalExtension: string): string {
  const sanitizedClaimant = sanitizeFilename(claimant || 'Unknown_Claimant');
  const sanitizedDescription = sanitizeFilename(description || 'Document');
  
  return `${sanitizedClaimant}_${sanitizedDescription}${originalExtension}`;
}

/**
 * Validate file type and size
 */
export function validateFile(file: any): { isValid: boolean; error?: string } {
  // Check file size
  if (file.size > STORAGE_CONFIG.maxFileSize) {
    return {
      isValid: false,
      error: `File size exceeds maximum limit of ${STORAGE_CONFIG.maxFileSize / (1024 * 1024)}MB`
    };
  }
  
  // Check MIME type
  if (!STORAGE_CONFIG.allowedMimeTypes.includes(file.mimetype)) {
    return {
      isValid: false,
      error: 'Only PDF and JPG files are allowed'
    };
  }
  
  // Check file extension
  const extension = path.extname(file.originalname).toLowerCase();
  if (!STORAGE_CONFIG.allowedExtensions.includes(extension)) {
    return {
      isValid: false,
      error: 'Only .pdf, .jpg, and .jpeg files are allowed'
    };
  }
  
  return { isValid: true };
}

/**
 * Ensure directory exists
 */
export function ensureDirectoryExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}
