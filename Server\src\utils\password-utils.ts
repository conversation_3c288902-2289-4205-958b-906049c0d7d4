/**
 * Password utilities using Node.js crypto module instead of bcrypt
 * This is a drop-in replacement for bcrypt to avoid native module issues in Docker
 */
import crypto from 'crypto';

// Constants
const ITERATIONS = 10000; // Higher is more secure but slower
const KEY_LENGTH = 64;
const DIGEST = 'sha512';
const ENCODING = 'base64';
const SALT_LENGTH = 16;

/**
 * Generate a salt for password hashing
 * @returns A random salt string
 */
function generateSalt(): string {
  return crypto.randomBytes(SALT_LENGTH).toString(ENCODING);
}

/**
 * Hash a password with a salt
 * @param password The plain text password to hash
 * @param salt The salt to use (optional, will generate if not provided)
 * @returns The hashed password with salt and parameters
 */
function hashWithSalt(password: string, salt?: string): string {
  const actualSalt = salt || generateSalt();
  
  const hash = crypto.pbkdf2Sync(
    password,
    actualSalt,
    ITERATIONS,
    KEY_LENGTH,
    DIGEST
  ).toString(ENCODING);
  
  // Format: $pbkdf2$iterations$salt$hash
  return `$pbkdf2$${ITERATIONS}$${actualSalt}$${hash}`;
}

/**
 * Hash a password (bcrypt-compatible API)
 * @param password The plain text password to hash
 * @param saltRounds Ignored, kept for API compatibility with bcrypt
 * @returns A Promise that resolves to the hashed password
 */
export async function hash(password: string, saltRounds: number): Promise<string> {
  return hashWithSalt(password);
}

/**
 * Compare a password with a hash (bcrypt-compatible API)
 * @param password The plain text password to check
 * @param hashedPassword The hashed password to compare against
 * @returns A Promise that resolves to true if the password matches, false otherwise
 */
export async function compare(password: string, hashedPassword: string): Promise<boolean> {
  // Handle legacy bcrypt hashes
  if (hashedPassword.startsWith('$2')) {
    // If it's a bcrypt hash, we can't verify it without bcrypt
    // In this case, we'll return false and let the application handle it
    console.warn('Legacy bcrypt hash detected, cannot verify without bcrypt module');
    return false;
  }
  
  try {
    // Parse the stored hash
    const parts = hashedPassword.split('$');
    
    if (parts.length !== 5 || parts[1] !== 'pbkdf2') {
      // Not a valid hash format
      return false;
    }
    
    const iterations = parseInt(parts[2], 10);
    const salt = parts[3];
    const storedHash = parts[4];
    
    // Hash the input password with the same salt and iterations
    const inputHash = crypto.pbkdf2Sync(
      password,
      salt,
      iterations,
      KEY_LENGTH,
      DIGEST
    ).toString(ENCODING);
    
    // Compare the hashes
    return crypto.timingSafeEqual(
      Buffer.from(storedHash),
      Buffer.from(inputHash)
    );
  } catch (error) {
    console.error('Error comparing passwords:', error);
    return false;
  }
}

// Export a default object with the same API as bcrypt
export default {
  hash,
  compare,
  // Add sync versions for compatibility
  hashSync: (password: string, saltRounds: number): string => hashWithSalt(password),
  compareSync: (password: string, hashedPassword: string): boolean => {
    try {
      // Handle legacy bcrypt hashes
      if (hashedPassword.startsWith('$2')) {
        console.warn('Legacy bcrypt hash detected, cannot verify without bcrypt module');
        return false;
      }
      
      // Parse the stored hash
      const parts = hashedPassword.split('$');
      
      if (parts.length !== 5 || parts[1] !== 'pbkdf2') {
        // Not a valid hash format
        return false;
      }
      
      const iterations = parseInt(parts[2], 10);
      const salt = parts[3];
      const storedHash = parts[4];
      
      // Hash the input password with the same salt and iterations
      const inputHash = crypto.pbkdf2Sync(
        password,
        salt,
        iterations,
        KEY_LENGTH,
        DIGEST
      ).toString(ENCODING);
      
      // Compare the hashes
      return crypto.timingSafeEqual(
        Buffer.from(storedHash),
        Buffer.from(inputHash)
      );
    } catch (error) {
      console.error('Error comparing passwords:', error);
      return false;
    }
  }
};
