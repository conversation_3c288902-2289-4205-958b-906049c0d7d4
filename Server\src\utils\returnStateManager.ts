/**
 * Return State Manager
 * Centralized backend logic for managing return voucher states
 * Mirrors the BadgeStateManager pattern for consistency
 */

import { logger } from '../utils/logger';

export class ReturnStateManager {
  /**
   * Determines if a voucher should be marked as a return voucher
   */
  static isReturnVoucher(voucher: any): boolean {
    // Primary indicators
    const hasReturnFlag = voucher.is_returned_voucher === 1 || voucher.is_returned_voucher === true;
    const hasReturnStatus = voucher.status === 'VOUCHER RETURNED';
    const hasReturnWorkflow = voucher.workflow_state?.includes('RETURNED') ||
                             voucher.workflow_state === 'FINANCE_RETURNED' ||
                             voucher.workflow_state === 'AUDIT_RETURNED_COPY' ||
                             voucher.workflow_state === 'AUDIT_PENDING_DISPATCH_RETURNED';
    const hasReturnHistory = voucher.returned_by && voucher.returned_by.trim() !== '';

    return hasReturnFlag || hasReturnStatus || hasReturnWorkflow || hasReturnHistory;
  }

  /**
   * Sets the return flag and related fields for a voucher
   * Mirrors the resubmission state management pattern
   */
  static async setReturnState(
    connection: any, 
    voucherId: string, 
    isReturn: boolean, 
    options: {
      incrementCount?: boolean;
      setCertifiedVisibility?: boolean;
      setAuditVisibility?: boolean;
      preserveReturnReason?: boolean;
    } = {}
  ): Promise<void> {
    const { 
      incrementCount = false, 
      setCertifiedVisibility = false, 
      setAuditVisibility = false,
      preserveReturnReason = true 
    } = options;

    logger.info(`🔄 Return State: Setting return state for voucher ${voucherId} to ${isReturn}`);

    try {
      // First preserve original return information if this is a new return
      if (isReturn && preserveReturnReason) {
        await this.preserveOriginalReturnReason(connection, voucherId);
      }

      let updateQuery = `
        UPDATE vouchers SET
        is_returned_voucher = ?,
        last_return_date = ${isReturn ? 'NOW()' : 'NULL'}
      `;
      let updateParams = [isReturn ? 1 : 0];

      // Increment return count if requested
      if (incrementCount && isReturn) {
        updateQuery += ', return_count = COALESCE(return_count, 0) + 1';
      }

      // Set visibility flags if requested
      if (setCertifiedVisibility) {
        updateQuery += ', return_certified_visible_to_finance = ?';
        updateParams.push(isReturn ? 1 : 0);
      }

      if (setAuditVisibility) {
        updateQuery += ', return_audit_visible = ?';
        updateParams.push(isReturn ? 1 : 0);
      }

      updateQuery += ' WHERE id = ?';
      updateParams.push(voucherId as any);

      await connection.query(updateQuery, updateParams);

      logger.info(`✅ Return State: Successfully set return state for voucher ${voucherId} - setCertifiedVisibility: ${setCertifiedVisibility}`);
    } catch (error) {
      logger.error(`❌ Error setting return state for voucher ${voucherId}:`, error);
      throw error;
    }
  }

  /**
   * Preserves original return reason for return vouchers
   * ENHANCED: Handles resubmission scenarios and finds reasons from copy vouchers
   */
  static async preserveOriginalReturnReason(connection: any, voucherId: string): Promise<void> {
    try {
      // Get current voucher data including return history
      const [vouchers] = await connection.query(`
        SELECT
          voucher_id, comment, returned_by, return_time,
          original_return_reason, original_returned_by,
          is_returned_voucher, is_resubmitted
        FROM vouchers WHERE id = ?
      `, [voucherId as any]);

      if (vouchers.length === 0) {
        logger.warn(`⚠️ Return State: Voucher ${voucherId} not found for return reason preservation`);
        return;
      }

      const voucher = vouchers[0];

      // Skip if we already have preserved the original return reason
      if (voucher.original_return_reason) {
        logger.info(`📝 Return State: Original return reason already preserved for voucher ${voucherId}`);
        return;
      }

      let returnReason = voucher.comment;

      // ENHANCED: If main voucher comment is null/empty, find reason from copy vouchers
      if (!returnReason || returnReason.trim() === '') {
        logger.info(`🔍 Return State: Main voucher ${voucherId} has no comment, searching copy vouchers...`);

        const [copyVouchers] = await connection.query(`
          SELECT comment, returned_by, return_time
          FROM vouchers
          WHERE voucher_id = ?
          AND id != ?
          AND (is_returned_copy = 1 OR workflow_state LIKE '%RETURNED%')
          AND comment IS NOT NULL
          AND comment != ''
          ORDER BY return_time DESC
          LIMIT 1
        `, [voucher.voucher_id, voucherId]);

        if (copyVouchers.length > 0) {
          returnReason = copyVouchers[0].comment;
          logger.info(`✅ Return State: Found return reason from copy voucher: "${returnReason}"`);
        }
      }

      // Preserve the return reason if we found one
      if (returnReason && returnReason.trim() !== '') {
        await connection.query(`
          UPDATE vouchers SET
          original_return_reason = ?,
          original_returned_by = COALESCE(returned_by, ?),
          comment = COALESCE(?, comment, 'Returned without specific reason')
          WHERE id = ?
        `, [
          returnReason.trim(),
          voucher.returned_by,
          returnReason.trim(),
          voucherId as any
        ]);

        logger.info(`📝 Return State: Preserved original return reason for voucher ${voucherId}: "${returnReason.trim()}"`);
      } else {
        // Set a default reason if none found
        await connection.query(`
          UPDATE vouchers SET
          original_return_reason = 'Returned without specific reason',
          original_returned_by = returned_by,
          comment = COALESCE(comment, 'Returned without specific reason')
          WHERE id = ?
        `, [voucherId as any]);

        logger.info(`📝 Return State: Set default return reason for voucher ${voucherId}`);
      }
    } catch (error) {
      logger.error(`❌ Error preserving original return reason for voucher ${voucherId}:`, error);
      throw error;
    }
  }

  /**
   * Ensures return voucher visibility in Finance RETURNED tab
   * Mirrors the certified resubmission visibility pattern
   * ENHANCED: Now supports dual-tab visibility for certified returned vouchers
   */
  static async ensureCertifiedReturnVisibility(
    connection: any,
    voucherId: string,
    visible: boolean
  ): Promise<void> {
    try {
      // PRODUCTION FIX: Mirror exact resubmission pattern - always set the flag when visible=true
      if (visible) {
        // Check if this is a returned voucher
        const [vouchers] = await connection.query(
          'SELECT is_returned_voucher, status FROM vouchers WHERE id = ?',
          [voucherId]
        );

        if (vouchers.length === 0) {
          throw new Error(`Voucher ${voucherId} not found`);
        }

        const voucher = vouchers[0];
        const isReturnedVoucher = voucher.is_returned_voucher === 1;

        if (isReturnedVoucher) {
          // CRITICAL: Always set visibility flag for returned vouchers (mirrors resubmission logic)
          await connection.query(`
            UPDATE vouchers SET
            return_certified_visible_to_finance = 1,
            badge_persistence_flags = JSON_SET(
              COALESCE(badge_persistence_flags, '{}'),
              '$.certified_returned',
              true,
              '$.persistent_in_dispatched',
              true,
              '$.persistent_in_certified',
              true,
              '$.dual_tab_visibility',
              true
            )
            WHERE id = ?
          `, [voucherId]);

          logger.info(`🎯 DUAL VISIBILITY: Set certified return visibility flag for voucher ${voucherId}`);
        }
      } else {
        // Clear visibility when visible=false
        await connection.query(
          'UPDATE vouchers SET return_certified_visible_to_finance = ? WHERE id = ?',
          [0, voucherId as any]
        );

        logger.info(`🎯 Return State: Cleared certified return visibility for voucher ${voucherId}`);
      }
    } catch (error) {
      logger.error(`❌ Error setting certified return visibility for voucher ${voucherId}:`, error);
      throw error;
    }
  }

  /**
   * Clears all return flags (used when voucher is reset)
   */
  static async clearReturnState(connection: any, voucherId: string): Promise<void> {
    try {
      await connection.query(`
        UPDATE vouchers SET 
        is_returned_voucher = 0,
        return_certified_visible_to_finance = 0,
        return_audit_visible = 0,
        last_return_date = NULL
        WHERE id = ?
      `, [voucherId as any]);

      logger.info(`🧹 Return State: Cleared return state for voucher ${voucherId}`);
    } catch (error) {
      logger.error(`❌ Error clearing return state for voucher ${voucherId}:`, error);
      throw error;
    }
  }

  /**
   * Ensures return voucher resubmission visibility in Finance CERTIFIED tab
   * Mirrors the certified resubmission visibility pattern for returned vouchers
   */
  static async ensureCertifiedReturnResubmissionVisibility(
    connection: any,
    voucherId: string,
    visible: boolean
  ): Promise<void> {
    try {
      await connection.query(
        'UPDATE vouchers SET return_certified_visible_to_finance = ? WHERE id = ?',
        [visible ? 1 : 0, voucherId as any]
      );

      logger.info(`🎯 Return Resubmission: Set certified return resubmission visibility for voucher ${voucherId} to ${visible}`);
    } catch (error) {
      logger.error(`❌ Error setting certified return resubmission visibility for voucher ${voucherId}:`, error);
      throw error;
    }
  }

  /**
   * Gets return voucher metadata for display
   */
  static async getReturnMetadata(connection: any, voucherId: string): Promise<any> {
    try {
      const [vouchers] = await connection.query(`
        SELECT
          is_returned_voucher,
          return_count,
          last_return_date,
          original_return_reason,
          original_returned_by,
          return_certified_visible_to_finance,
          return_audit_visible
        FROM vouchers
        WHERE id = ?
      `, [voucherId as any]);

      return vouchers.length > 0 ? vouchers[0] : null;
    } catch (error) {
      logger.error(`❌ Error getting return metadata for voucher ${voucherId}:`, error);
      throw error;
    }
  }

  /**
   * PHASE 2 FIX: Complete returned voucher resubmission workflow
   * Handles the full cycle: returned → resubmitted → certified
   * Ensures proper badge persistence and dual-tab visibility
   */
  static async completeCertifiedReturnWorkflow(
    connection: any,
    voucherId: string
  ): Promise<void> {
    try {
      logger.info(`🔄 PHASE 2: Completing certified return workflow for voucher ${voucherId}`);

      // Get current voucher state
      const [vouchers] = await connection.query('SELECT * FROM vouchers WHERE id = ?', [voucherId]);

      if (vouchers.length === 0) {
        throw new Error(`Voucher ${voucherId} not found`);
      }

      const voucher = vouchers[0];

      // Verify this is a certified returned voucher
      const isCertifiedReturn = (
        voucher.is_returned_voucher === 1 &&
        voucher.status === 'VOUCHER CERTIFIED'
      );

      if (!isCertifiedReturn) {
        logger.warn(`⚠️ PHASE 2: Voucher ${voucherId} is not a certified returned voucher, skipping workflow completion`);
        return;
      }

      // Set complete Phase 2 state - CRITICAL FIX for final resting places
      await connection.query(`
        UPDATE vouchers SET
        return_certified_visible_to_finance = 1,
        resubmission_certified_visible_to_finance = 1,
        is_resubmitted = 1,
        workflow_state = 'AUDIT_DISPATCHED',
        status = 'VOUCHER CERTIFIED',
        department = 'FINANCE',
        badge_persistence_flags = JSON_SET(
          COALESCE(badge_persistence_flags, '{}'),
          '$.certified_returned',
          true,
          '$.persistent_in_dispatched',
          true,
          '$.persistent_in_certified',
          true,
          '$.dual_tab_visibility',
          true,
          '$.preserve_return_reason',
          true,
          '$.phase_2_complete',
          true,
          '$.remove_from_returned_tab',
          true
        )
        WHERE id = ?
      `, [voucherId]);

      logger.info(`✅ PHASE 2: Completed certified return workflow for voucher ${voucherId} - dual-tab visibility enabled`);
    } catch (error) {
      logger.error(`❌ PHASE 2: Error completing certified return workflow for voucher ${voucherId}:`, error);
      throw error;
    }
  }
}
