import { query } from '../database/db.js';
import { logger } from './logger.js';

/**
 * Clean up duplicate sessions for all users
 * Keeps only the most recent active session per user
 */
export async function deduplicateUserSessions(): Promise<void> {
  try {
    logger.info('🔄 Starting session deduplication process...');

    // Find users with multiple active sessions
    const duplicateUsers = await query(`
      SELECT user_id, user_name, COUNT(*) as session_count
      FROM active_sessions 
      WHERE is_active = TRUE 
      GROUP BY user_id, user_name 
      HAVING COUNT(*) > 1
      ORDER BY session_count DESC
    `) as any[];

    if (duplicateUsers.length === 0) {
      logger.info('✅ No duplicate sessions found');
      return;
    }

    logger.info(`🔍 Found ${duplicateUsers.length} users with duplicate sessions`);

    let totalCleaned = 0;

    for (const user of duplicateUsers) {
      // Get all sessions for this user, ordered by most recent first
      const userSessions = await query(`
        SELECT id, session_start, last_activity
        FROM active_sessions 
        WHERE user_id = ? AND is_active = TRUE 
        ORDER BY last_activity DESC, session_start DESC
      `, [user.user_id]) as any[];

      if (userSessions.length > 1) {
        // Keep the most recent session, deactivate the rest
        const sessionsToDeactivate = userSessions.slice(1);
        const sessionIds = sessionsToDeactivate.map(s => s.id);

        await query(`
          UPDATE active_sessions 
          SET is_active = FALSE, session_end = NOW() 
          WHERE id IN (${sessionIds.map(() => '?').join(',')})
        `, sessionIds);

        totalCleaned += sessionsToDeactivate.length;
        
        logger.info(`🧹 Cleaned ${sessionsToDeactivate.length} duplicate sessions for user ${user.user_name}`);
      }
    }

    logger.info(`✅ Session deduplication complete: cleaned ${totalCleaned} duplicate sessions`);

  } catch (error) {
    logger.error('❌ Session deduplication failed:', error);
    throw error;
  }
}

/**
 * Get session statistics for monitoring
 */
export async function getSessionStats(): Promise<{
  totalSessions: number;
  activeSessions: number;
  uniqueActiveUsers: number;
  duplicateUsers: number;
}> {
  try {
    // Get total sessions
    const [totalResult] = await query('SELECT COUNT(*) as count FROM active_sessions') as any[];
    
    // Get active sessions
    const [activeResult] = await query('SELECT COUNT(*) as count FROM active_sessions WHERE is_active = TRUE') as any[];
    
    // Get unique active users
    const [uniqueResult] = await query(`
      SELECT COUNT(DISTINCT user_id) as count 
      FROM active_sessions 
      WHERE is_active = TRUE
    `) as any[];
    
    // Get users with duplicate sessions
    const duplicateUsers = await query(`
      SELECT COUNT(*) as count
      FROM (
        SELECT user_id
        FROM active_sessions 
        WHERE is_active = TRUE 
        GROUP BY user_id 
        HAVING COUNT(*) > 1
      ) as duplicates
    `) as any[];

    return {
      totalSessions: totalResult.count,
      activeSessions: activeResult.count,
      uniqueActiveUsers: uniqueResult.count,
      duplicateUsers: duplicateUsers[0]?.count || 0
    };

  } catch (error) {
    logger.error('Failed to get session stats:', error);
    throw error;
  }
}

/**
 * Clean up sessions for a specific user (useful for logout)
 */
export async function cleanupUserSessions(userId: string, keepMostRecent: boolean = true): Promise<number> {
  try {
    if (keepMostRecent) {
      // Get all sessions for this user
      const userSessions = await query(`
        SELECT id, session_start, last_activity
        FROM active_sessions 
        WHERE user_id = ? AND is_active = TRUE 
        ORDER BY last_activity DESC, session_start DESC
      `, [userId]) as any[];

      if (userSessions.length > 1) {
        // Keep the most recent, deactivate the rest
        const sessionsToDeactivate = userSessions.slice(1);
        const sessionIds = sessionsToDeactivate.map(s => s.id);

        await query(`
          UPDATE active_sessions 
          SET is_active = FALSE, session_end = NOW() 
          WHERE id IN (${sessionIds.map(() => '?').join(',')})
        `, sessionIds);

        logger.info(`Cleaned ${sessionsToDeactivate.length} duplicate sessions for user ${userId}`);
        return sessionsToDeactivate.length;
      }
    } else {
      // Deactivate all sessions for this user
      const result = await query(`
        UPDATE active_sessions 
        SET is_active = FALSE, session_end = NOW() 
        WHERE user_id = ? AND is_active = TRUE
      `, [userId]) as any;

      logger.info(`Deactivated all sessions for user ${userId}`);
      return result.affectedRows || 0;
    }

    return 0;
  } catch (error) {
    logger.error(`Failed to cleanup sessions for user ${userId}:`, error);
    throw error;
  }
}
