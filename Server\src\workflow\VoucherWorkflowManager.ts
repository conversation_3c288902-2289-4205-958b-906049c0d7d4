/**
 * VMS Voucher Workflow Manager
 * Handles atomic state transitions and voucher copy management
 */

import {
  WorkflowState,
  WorkflowEvent,
  BadgeType,
  VoucherWorkflowContext,
  VoucherWorkflowStateMachine
} from './VoucherWorkflowStateMachine';
import { circuitBreakerManager } from '../utils/CircuitBreaker.js';
import { eventDeduplicator } from '../utils/EventDeduplicator.js';
import { logger } from '../utils/logger.js';

export interface VoucherWorkflowResult {
  success: boolean;
  voucher: any;
  copy?: any;
  previousState: WorkflowState;
  newState: WorkflowState;
  affectedTabs: string[];
  error?: string;
}

export interface WorkflowEventData {
  type: WorkflowEvent;
  voucherId: string;
  context: VoucherWorkflowContext;
  data?: Record<string, any>;
}

export class VoucherWorkflowManager {
  private db: any;
  private eventPublisher: any;
  private auditLogger: any;

  constructor(db: any, eventPublisher: any, auditLogger: any) {
    this.db = db;
    this.eventPublisher = eventPublisher;
    this.auditLogger = auditLogger;
  }

  /**
   * Execute workflow transition atomically with production-grade error recovery
   */
  async transitionVoucher(eventData: WorkflowEventData): Promise<VoucherWorkflowResult> {
    const { type: event, voucherId, context } = eventData;

    // PRODUCTION: Event deduplication to prevent duplicate transitions
    const eventKey = {
      type: `workflow_transition_${event}`,
      entityId: voucherId,
      userId: context.userId
    };

    if (!eventDeduplicator.shouldProcess(eventKey)) {
      logger.warn(`🚫 WORKFLOW PROTECTION: Blocked duplicate transition for voucher ${voucherId}`);
      throw new Error('Duplicate workflow transition blocked');
    }

    // PRODUCTION: Circuit breaker protection for workflow operations
    return await circuitBreakerManager.execute('workflow_transition', async () => {
      return await this.withTransaction(async (tx) => {
        let currentState: WorkflowState = WorkflowState.FINANCE_PENDING; // Default value

        try {
          // 1. Load current voucher with retry logic
          const currentVoucher = await this.getVoucherByIdWithRetry(voucherId, tx);
          if (!currentVoucher) {
            throw new Error(`Voucher ${voucherId} not found`);
          }

          currentState = currentVoucher.workflow_state as WorkflowState;

          // 2. Validate transition with enhanced error context
          const transition = VoucherWorkflowStateMachine.getTransition(currentState, event);
          if (!transition) {
            const validEvents = VoucherWorkflowStateMachine.getValidEvents(currentState);
            throw new Error(
              `Invalid transition from ${currentState} with event ${event}. Valid events: ${validEvents.join(', ')}`
            );
          }

      // 3. Calculate new state and properties
      const newState = transition.toState;
      const badge = transition.badge || BadgeType.NONE;
      const affectedTabs = this.getAffectedTabs(currentState, newState, context);

      // 4. Create copy if required (for rejection/return flows)
      let copy = null;
      if (transition.requiresCopy && transition.copyState) {
        copy = await this.createVoucherCopy(currentVoucher, transition.copyState, tx);
      }

      // 5. Update original voucher
      const updatedVoucher = await this.updateVoucherState(
        voucherId,
        {
          workflow_state: newState,
          badge_type: badge,
          department: this.getDepartmentForState(newState, currentVoucher),
          version: currentVoucher.version + 1,
          last_modified: new Date()
        },
        tx
      );

      // 6. Log audit trail
      await this.logWorkflowTransition({
        voucherId,
        fromState: currentState,
        toState: newState,
        event,
        userId: context.userId,
        timestamp: context.timestamp,
        copyId: copy?.id
      }, tx);

          // 7. Publish real-time event
          await this.publishWorkflowEvent({
            type: 'VOUCHER_STATE_CHANGED',
            voucherId,
            previousState: currentState,
            newState,
            affectedDepartments: [currentVoucher.original_department, 'AUDIT'],
            affectedTabs,
            copy
          });

          // Mark event as processed
          eventDeduplicator.markProcessed(eventKey);

          return {
            success: true,
            voucher: updatedVoucher,
            copy,
            previousState: currentState,
            newState,
            affectedTabs
          };
        } catch (error) {
          logger.error(`Workflow transition failed for voucher ${voucherId}:`, error);

          // PRODUCTION: Enhanced error recovery
          if (error instanceof Error) {
            await this.handleWorkflowError(voucherId, currentState, event, error, tx);
          }
          throw error;
        }
      });
    }, {
      failureThreshold: 3,
      recoveryTimeout: 30000, // 30 seconds
      monitoringPeriod: 60000  // 1 minute
    });
  }

  /**
   * Create voucher copy for rejection/return flows
   */
  private async createVoucherCopy(
    originalVoucher: any, 
    copyState: WorkflowState, 
    tx: any
  ): Promise<any> {
    const copyData = {
      ...originalVoucher,
      id: this.generateId(),
      voucher_id: `${originalVoucher.voucher_id}_COPY`,
      workflow_state: copyState,
      is_copy: true,
      parent_voucher_id: originalVoucher.id,
      created_at: new Date(),
      version: 1
    };

    delete copyData.version; // Will be set to 1 above

    const columns = Object.keys(copyData).join(', ');
    const placeholders = Object.keys(copyData).map(() => '?').join(', ');
    const values = Object.values(copyData);

    const query = `
      INSERT INTO vouchers (${columns}) VALUES (${placeholders})
    `;

    await tx.execute(query, values);
    return copyData;
  }

  /**
   * Update voucher state atomically
   */
  private async updateVoucherState(
    voucherId: string, 
    updates: Record<string, any>, 
    tx: any
  ): Promise<any> {
    const setClause = Object.keys(updates)
      .map(key => `${key} = ?`)
      .join(', ');

    const query = `
      UPDATE vouchers 
      SET ${setClause}
      WHERE id = ?
    `;

    const values = [...Object.values(updates), voucherId];
    await tx.execute(query, values);

    // Return updated voucher
    return await this.getVoucherById(voucherId, tx);
  }

  /**
   * Get voucher by ID
   */
  private async getVoucherById(voucherId: string, tx?: any): Promise<any> {
    const query = `
      SELECT * FROM vouchers WHERE id = ?
    `;

    if (tx) {
      const [rows] = await tx.execute(query, [voucherId]);
      return rows[0] || null;
    } else {
      const [rows] = await this.db.execute(query, [voucherId]);
      return rows[0] || null;
    }
  }

  /**
   * Determine department based on workflow state
   */
  private getDepartmentForState(state: WorkflowState, voucher: any): string {
    if (state.startsWith('AUDIT_')) {
      return 'AUDIT';
    }
    if (state.startsWith('FINANCE_')) {
      return voucher.original_department;
    }
    return voucher.department;
  }

  /**
   * Get affected tabs for real-time updates
   */
  private getAffectedTabs(
    fromState: WorkflowState, 
    toState: WorkflowState, 
    context: VoucherWorkflowContext
  ): string[] {
    const tabs: string[] = [];

    // Add tabs for both states
    const fromTab = VoucherWorkflowStateMachine.getTabForVoucher(fromState, context.userDepartment);
    const toTab = VoucherWorkflowStateMachine.getTabForVoucher(toState, context.userDepartment);

    if (fromTab) tabs.push(fromTab);
    if (toTab && toTab !== fromTab) tabs.push(toTab);

    // Add tabs for other affected departments
    const auditFromTab = VoucherWorkflowStateMachine.getTabForVoucher(fromState, 'AUDIT');
    const auditToTab = VoucherWorkflowStateMachine.getTabForVoucher(toState, 'AUDIT');

    if (auditFromTab && !tabs.includes(auditFromTab)) tabs.push(auditFromTab);
    if (auditToTab && !tabs.includes(auditToTab)) tabs.push(auditToTab);

    return tabs;
  }

  /**
   * Log workflow transition for audit trail
   */
  private async logWorkflowTransition(data: any, tx: any): Promise<void> {
    const query = `
      INSERT INTO workflow_audit_log 
      (voucher_id, from_state, to_state, event_type, user_id, timestamp, copy_id)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    await tx.execute(query, [
      data.voucherId,
      data.fromState,
      data.toState,
      data.event,
      data.userId,
      data.timestamp,
      data.copyId || null
    ]);
  }

  /**
   * Publish real-time workflow event
   */
  private async publishWorkflowEvent(eventData: any): Promise<void> {
    if (this.eventPublisher) {
      await this.eventPublisher.publish('workflow.state.changed', eventData);
    }
  }

  /**
   * Execute function within database transaction
   */
  private async withTransaction<T>(fn: (tx: any) => Promise<T>): Promise<T> {
    const connection = await this.db.getConnection();
    await connection.beginTransaction();

    try {
      const result = await fn(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return require('uuid').v4();
  }

  /**
   * Enhanced voucher retrieval with retry logic
   */
  private async getVoucherByIdWithRetry(voucherId: string, tx: any, maxRetries: number = 3): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.getVoucherById(voucherId, tx);
      } catch (error) {
        lastError = error as Error;
        logger.warn(`Voucher retrieval attempt ${attempt}/${maxRetries} failed for ${voucherId}:`, error);

        if (attempt < maxRetries) {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw lastError || new Error(`Failed to retrieve voucher ${voucherId} after ${maxRetries} attempts`);
  }

  /**
   * Handle workflow errors with recovery mechanisms
   */
  private async handleWorkflowError(
    voucherId: string,
    currentState: WorkflowState,
    event: WorkflowEvent,
    error: Error,
    tx: any
  ): Promise<void> {
    try {
      // Log detailed error information
      logger.error(`🚨 WORKFLOW ERROR RECOVERY: Voucher ${voucherId}`, {
        currentState,
        event,
        error: error.message,
        timestamp: new Date().toISOString()
      });

      // PRODUCTION: Attempt automatic recovery for known error types
      if (error.message.includes('Invalid transition')) {
        await this.attemptStateRecovery(voucherId, currentState, tx);
      } else if (error.message.includes('not found')) {
        await this.attemptVoucherRecovery(voucherId, tx);
      }

    } catch (recoveryError) {
      logger.error(`🚨 WORKFLOW ERROR RECOVERY FAILED for voucher ${voucherId}:`, recoveryError);
    }
  }

  /**
   * Attempt to recover from invalid state transitions
   */
  private async attemptStateRecovery(voucherId: string, currentState: WorkflowState, tx: any): Promise<void> {
    logger.info(`🔧 ATTEMPTING STATE RECOVERY for voucher ${voucherId} in state ${currentState}`);

    // For now, just log the recovery attempt
    // In production, this could implement specific recovery logic
    // based on the current state and business rules
  }

  /**
   * Attempt to recover missing vouchers
   */
  private async attemptVoucherRecovery(voucherId: string, tx: any): Promise<void> {
    logger.info(`🔧 ATTEMPTING VOUCHER RECOVERY for ${voucherId}`);

    // For now, just log the recovery attempt
    // In production, this could check backup tables or implement
    // voucher restoration logic
  }
}
