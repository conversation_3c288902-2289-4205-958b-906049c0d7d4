/**
 * VMS Voucher Workflow State Machine
 * Single Source of Truth for all voucher workflow states and transitions
 * Based on END TO END VOUCHER WORKFLOW.txt specification
 */

export enum WorkflowState {
  // Finance States
  FINANCE_PENDING = 'FINANCE_PENDING',
  FINANCE_PROCESSING = 'FINANCE_PROCESSING',
  FINANCE_CERTIFIED = 'FINANCE_CERTIFIED',
  FINANCE_REJECTED = 'FINANCE_REJECTED',
  FINANCE_RETURNED = 'FINANCE_RETURNED',

  FINANCE_RESUBMISSION_RECEIVED = 'FINANCE_RESUBMISSION_RECEIVED',

  // Audit States
  AUDIT_NEW = 'AUDIT_NEW',
  AUDIT_NEW_RESUBMITTED = 'AUDIT_NEW_RESUBMITTED',
  AUDIT_PENDING_DISPATCH = 'AUDIT_PENDING_DISPATCH',
  AUDIT_PENDING_DISPATCH_REJECTED = 'AUDIT_PENDING_DISPATCH_REJECTED',
  AUDIT_PENDING_DISPATCH_RETURNED = 'AUDIT_PENDING_DISPATCH_RETURNED',

  AUDIT_DISPATCHED = 'AUDIT_DISPATCHED',
  AUDIT_REJECTED_COPY = 'AUDIT_REJECTED_COPY',
  AUDIT_RETURNED_COPY = 'AUDIT_RETURNED_COPY',

}

export enum WorkflowEvent {
  // Finance Events
  CREATE_VOUCHER = 'CREATE_VOUCHER',
  SUBMIT_TO_AUDIT = 'SUBMIT_TO_AUDIT',
  RESUBMIT_FROM_REJECTED = 'RESUBMIT_FROM_REJECTED',
  RESUBMIT_FROM_RETURNED = 'RESUBMIT_FROM_RETURNED',


  // Audit Events
  RECEIVE_FROM_FINANCE = 'RECEIVE_FROM_FINANCE',
  START_WORK = 'START_WORK',
  CERTIFY_VOUCHER = 'CERTIFY_VOUCHER',
  REJECT_VOUCHER = 'REJECT_VOUCHER',
  RETURN_VOUCHER = 'RETURN_VOUCHER',
  DISPATCH_TO_FINANCE = 'DISPATCH_TO_FINANCE'
}

export enum BadgeType {
  NONE = 'NONE',
  RE_SUBMITTED = 'RE_SUBMITTED',
  RESUBMISSION = 'RESUBMISSION',
  RETURNED = 'RETURNED',
  REJECTED = 'REJECTED'
}

export interface WorkflowTransition {
  fromState: WorkflowState;
  event: WorkflowEvent;
  toState: WorkflowState;
  isValid: boolean;
  requiresCopy?: boolean;
  copyState?: WorkflowState;
  badge?: BadgeType;
}

export interface VoucherWorkflowContext {
  voucherId: string;
  userId: string;
  userDepartment: string;
  originalDepartment: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export class VoucherWorkflowStateMachine {
  private static readonly TRANSITIONS: WorkflowTransition[] = [
    // Normal Flow
    {
      fromState: WorkflowState.FINANCE_PENDING,
      event: WorkflowEvent.SUBMIT_TO_AUDIT,
      toState: WorkflowState.FINANCE_PROCESSING,
      isValid: true
    },
    {
      fromState: WorkflowState.FINANCE_PROCESSING,
      event: WorkflowEvent.RECEIVE_FROM_FINANCE,
      toState: WorkflowState.AUDIT_NEW,
      isValid: true
    },
    {
      fromState: WorkflowState.AUDIT_NEW,
      event: WorkflowEvent.START_WORK,
      toState: WorkflowState.AUDIT_PENDING_DISPATCH,
      isValid: true
    },
    {
      fromState: WorkflowState.AUDIT_PENDING_DISPATCH,
      event: WorkflowEvent.DISPATCH_TO_FINANCE,
      toState: WorkflowState.AUDIT_DISPATCHED,
      isValid: true
    },
    {
      fromState: WorkflowState.AUDIT_DISPATCHED,
      event: WorkflowEvent.RECEIVE_FROM_FINANCE,
      toState: WorkflowState.FINANCE_CERTIFIED,
      isValid: true
    },

    // Rejection Flow
    {
      fromState: WorkflowState.AUDIT_NEW,
      event: WorkflowEvent.REJECT_VOUCHER,
      toState: WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED,
      isValid: true,
      requiresCopy: true,
      copyState: WorkflowState.AUDIT_REJECTED_COPY,
      badge: BadgeType.REJECTED
    },
    {
      fromState: WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED,
      event: WorkflowEvent.DISPATCH_TO_FINANCE,
      toState: WorkflowState.FINANCE_REJECTED,
      isValid: true
    },

    // Return Flow (mirrors rejection flow)
    {
      fromState: WorkflowState.AUDIT_NEW,
      event: WorkflowEvent.RETURN_VOUCHER,
      toState: WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED,
      isValid: true,
      requiresCopy: true,
      copyState: WorkflowState.AUDIT_RETURNED_COPY,
      badge: BadgeType.RETURNED
    },
    {
      fromState: WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED,
      event: WorkflowEvent.DISPATCH_TO_FINANCE,
      toState: WorkflowState.FINANCE_RETURNED,
      isValid: true
    },

    // Resubmission Flow
    {
      fromState: WorkflowState.FINANCE_REJECTED,
      event: WorkflowEvent.RESUBMIT_FROM_REJECTED,
      toState: WorkflowState.FINANCE_PROCESSING,
      isValid: true,
      badge: BadgeType.RE_SUBMITTED
    },
    // Return Voucher Resubmission Flow (mirrors rejection resubmission)
    {
      fromState: WorkflowState.FINANCE_RETURNED,
      event: WorkflowEvent.RESUBMIT_FROM_RETURNED,
      toState: WorkflowState.FINANCE_PROCESSING,
      isValid: true,
      badge: BadgeType.RE_SUBMITTED
    },
    {
      fromState: WorkflowState.FINANCE_PROCESSING,
      event: WorkflowEvent.RECEIVE_FROM_FINANCE,
      toState: WorkflowState.AUDIT_NEW_RESUBMITTED,
      isValid: true,
      badge: BadgeType.RE_SUBMITTED
    },
    {
      fromState: WorkflowState.AUDIT_NEW_RESUBMITTED,
      event: WorkflowEvent.START_WORK,
      toState: WorkflowState.AUDIT_PENDING_DISPATCH,
      isValid: true
    },


  ];

  private static readonly STATE_TO_TAB_MAPPING = {
    // Finance Dashboard Tabs
    [WorkflowState.FINANCE_PENDING]: 'pending',
    [WorkflowState.FINANCE_PROCESSING]: 'processing',
    [WorkflowState.FINANCE_CERTIFIED]: 'certified',
    [WorkflowState.FINANCE_REJECTED]: 'rejected',
    [WorkflowState.FINANCE_RETURNED]: 'returned',

    [WorkflowState.FINANCE_RESUBMISSION_RECEIVED]: 'processing',

    // Audit Dashboard Tabs
    [WorkflowState.AUDIT_NEW]: 'new-vouchers',
    [WorkflowState.AUDIT_NEW_RESUBMITTED]: 'new-vouchers',
    [WorkflowState.AUDIT_PENDING_DISPATCH]: 'pending-dispatch',
    [WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED]: 'pending-dispatch',
    [WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED]: 'pending-dispatch',

    [WorkflowState.AUDIT_DISPATCHED]: 'dispatched',
    [WorkflowState.AUDIT_REJECTED_COPY]: 'rejected',
    [WorkflowState.AUDIT_RETURNED_COPY]: 'returned-vouchers',

  };

  /**
   * Get valid transition for given state and event
   */
  static getTransition(fromState: WorkflowState, event: WorkflowEvent): WorkflowTransition | null {
    return this.TRANSITIONS.find(t => 
      t.fromState === fromState && t.event === event && t.isValid
    ) || null;
  }

  /**
   * Get tab name for voucher based on workflow state and user department
   * ENHANCED: Now supports resubmission override logic for certified resubmissions and returned voucher resubmissions
   */
  static getTabForVoucher(voucher: any, userDepartment: string): string | null {
    const { workflow_state, department, original_department, status, workStarted, dispatched, finance_received,
            is_resubmitted, resubmission_certified_visible_to_finance, is_returned_voucher, return_certified_visible_to_finance } = voucher;

    // RESUBMISSION OVERRIDE SYSTEM: Check resubmission visibility flags first
    if (is_resubmitted === 1 &&
        status === 'VOUCHER CERTIFIED' &&
        resubmission_certified_visible_to_finance === 1 &&
        userDepartment === original_department) {
      return 'certified';
    }

    // RETURN VOUCHER OVERRIDE SYSTEM: Check return voucher visibility flags
    // CRITICAL FIX: Only show in RETURNED tab if NOT certified (Phase 1 only)
    if (is_returned_voucher === 1 &&
        status === 'VOUCHER RETURNED' &&
        return_certified_visible_to_finance === 1 &&
        userDepartment === original_department &&
        !voucher.is_returned_copy) { // Only original vouchers, not copies
      return 'returned';
    }

    // PRODUCTION FIX: Mirror exact resubmission pattern for returned vouchers
    // RETURNED VOUCHER OVERRIDE SYSTEM: Check return visibility flags (mirrors resubmission logic)
    if (is_returned_voucher === 1 &&
        status === 'VOUCHER CERTIFIED' &&
        return_certified_visible_to_finance === 1 &&
        userDepartment === original_department) {
      return 'certified'; // Shows in Finance Department CERTIFIED tab
    }

    // ADDITIONAL: For Audit users viewing Finance vouchers in DISPATCHED tab
    if (is_returned_voucher === 1 &&
        status === 'VOUCHER CERTIFIED' &&
        return_certified_visible_to_finance === 1 &&
        userDepartment === 'AUDIT' &&
        original_department === 'FINANCE') {
      return 'dispatched'; // Shows in Finance Voucher Hub DISPATCHED tab
    }



    // FALLBACK: If workflow_state is not set, use legacy status-based logic
    if (!workflow_state) {
      return this.getLegacyTabForVoucher(voucher, userDepartment);
    }

    // Only show vouchers relevant to user's department
    if (userDepartment === 'AUDIT') {
      if (workflow_state.startsWith('AUDIT_')) {
        return this.STATE_TO_TAB_MAPPING[workflow_state as WorkflowState] || null;
      }
    } else {
      // Finance or original department
      if (workflow_state.startsWith('FINANCE_')) {
        return this.STATE_TO_TAB_MAPPING[workflow_state as WorkflowState] || null;
      }


    }
    return null;
  }

  /**
   * Legacy tab determination for vouchers without workflow_state
   */
  private static getLegacyTabForVoucher(voucher: any, userDepartment: string): string | null {
    const { status, department, original_department, workStarted, dispatched, finance_received } = voucher;

    // Legacy logic for backwards compatibility
    if (userDepartment === 'AUDIT') {
      if (status === 'PENDING RECEIPT' || status === 'AUDIT: PROCESSING') return 'new';
      if (status === 'VOUCHER CERTIFIED' || status === 'VOUCHER REJECTED') return 'dispatched';
    } else {
      // Finance department logic
      if (status === 'PENDING SUBMISSION') return 'pending-submission';
      if (status === 'VOUCHER PROCESSING') return 'processing';
      if (status === 'VOUCHER CERTIFIED') return 'certified';
      if (status === 'VOUCHER REJECTED') return 'rejected';

    }

    return null;
  }

  /**
   * Validate if transition is allowed
   */
  static canTransition(fromState: WorkflowState, event: WorkflowEvent): boolean {
    const transition = this.getTransition(fromState, event);
    return transition !== null && transition.isValid;
  }

  /**
   * Get all possible events for a given state
   */
  static getValidEvents(fromState: WorkflowState): WorkflowEvent[] {
    return this.TRANSITIONS
      .filter(t => t.fromState === fromState && t.isValid)
      .map(t => t.event);
  }

  /**
   * Get badge type for workflow state
   */
  static getBadgeType(workflowState: WorkflowState, context?: VoucherWorkflowContext): BadgeType {
    if (workflowState === WorkflowState.AUDIT_NEW_RESUBMITTED) {
      return BadgeType.RE_SUBMITTED;
    }
    if (workflowState === WorkflowState.AUDIT_REJECTED_COPY) {
      return BadgeType.REJECTED;
    }
    if (workflowState === WorkflowState.AUDIT_RETURNED_COPY) {
      return BadgeType.RETURNED;
    }

    return BadgeType.NONE;
  }

  /**
   * Get all available tabs for department
   */
  static getAvailableTabs(userDepartment: string): string[] {
    if (userDepartment === 'AUDIT') {
      return ['new-vouchers', 'pending-dispatch', 'dispatched', 'rejected', 'returned-vouchers'];
    } else {
      return ['pending', 'processing', 'certified', 'rejected', 'returned'];
    }
  }

  /**
   * Check if voucher is editable based on workflow state
   */
  static isVoucherEditable(voucher: any, userDepartment: string): boolean {
    const workflowState = voucher.workflow_state as WorkflowState;

    if (userDepartment === 'AUDIT') {
      return workflowState === WorkflowState.AUDIT_NEW ||
             workflowState === WorkflowState.AUDIT_NEW_RESUBMITTED ||
             workflowState === WorkflowState.AUDIT_PENDING_DISPATCH;
    }

    // Finance users can edit pending vouchers
    return workflowState === WorkflowState.FINANCE_PENDING;
  }
}
