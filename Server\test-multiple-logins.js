const axios = require("axios"); async function testMultipleLogins() { const users = [ {name: "MR. FELIX AYISI", dept: "FINANCE", pwd: "123123"}, {name: "<PERSON>ERRY JOHN", dept: "PENSIONS", pwd: "123123"}, {name: "SAMMY MAWUKO", dept: "MINISTRIES", pwd: "123123"} ]; for (const user of users) { try { const response = await axios.post("http://localhost:8080/api/auth/login", { username: user.name, department: user.dept, password: user.pwd }); console.log(`✅ ${user.name}: SUCCESS`); } catch (error) { console.log(`❌ ${user.name}: FAILED - ${error.response?.data?.error}`); } } } testMultipleLogins();
