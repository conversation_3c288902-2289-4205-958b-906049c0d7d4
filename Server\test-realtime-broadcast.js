// Test script to verify real-time broadcasting system
const { broadcastToAuditDepartment } = require('./dist/socket/socketHandlers');

console.log('🧪 Testing real-time broadcast system...');

// Test broadcast to Audit department
const testNotification = {
  type: 'NEW_VOUCHER_BATCH',
  message: 'Test voucher batch received',
  batch_id: 'TEST_BATCH_001',
  voucher_count: 1,
  timestamp: new Date().toISOString()
};

console.log('📡 Broadcasting test notification to Audit department...');
broadcastToAuditDepartment(testNotification);
console.log('✅ Broadcast function called successfully');
