{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "declaration": true, "sourceMap": true, "downlevelIteration": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts", "dist"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node", "transpileOnly": true, "files": true}}