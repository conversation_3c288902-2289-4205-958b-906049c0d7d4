{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": false, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts", "dist"], "ts-node": {"transpileOnly": true, "files": true}}