{"root": ["./src/index.ts", "./src/types.ts", "./src/config/configmanager.ts", "./src/config/database.config.ts", "./src/database/databasemanager.ts", "./src/database/db.ts", "./src/database/year-manager.ts", "./src/errors/databaseerror.ts", "./src/events/eventbus.ts", "./src/events/simpleeventbus.ts", "./src/middleware/auth.ts", "./src/middleware/workflow-validation.ts", "./src/routes/admin.ts", "./src/routes/audit-trail.ts", "./src/routes/audit.ts", "./src/routes/auth.ts", "./src/routes/batches.ts", "./src/routes/index.ts", "./src/routes/notifications.ts", "./src/routes/pendrive-backup.ts", "./src/routes/provisionalcash.ts", "./src/routes/users.ts", "./src/routes/vouchers.ts", "./src/routes/workflow.ts", "./src/routes/years.ts", "./src/scripts/seed-test-vouchers.ts", "./src/services/audit-populator.ts", "./src/services/audit-service.ts", "./src/services/enterprise-monitoring.ts", "./src/services/workflow-service.ts", "./src/services/year-rollover-service.ts", "./src/socket/simple-socket-handlers.ts", "./src/socket/sockethandlers.ts", "./src/tasks/session-cleanup.ts", "./src/utils/circuitbreaker.ts", "./src/utils/healthmonitor.ts", "./src/utils/portmanager.ts", "./src/utils/retrymanager.ts", "./src/utils/servicediscovery.ts", "./src/utils/badgestatemanager.ts", "./src/utils/errors.ts", "./src/utils/legacy-voucher-status-compat.ts", "./src/utils/logger.ts", "./src/utils/password-utils.ts", "./src/utils/port-manager.ts", "./src/utils/returnstatemanager.ts", "./src/utils/session-deduplication.ts", "./src/websocket/websocketmanager.ts", "./src/workflow/voucherworkflowmanager.ts", "./src/workflow/voucherworkflowstatemachine.ts"], "version": "5.8.3"}