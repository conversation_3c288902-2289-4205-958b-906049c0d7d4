// COMPREHENSIVE TEST: Bidirectional Real-time Notifications
// Tests both Department → Audit AND Audit → Department notifications

const { io } = require('socket.io-client');
const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');

async function testBidirectionalNotifications() {
  console.log('🧪 TESTING BIDIRECTIONAL REAL-TIME NOTIFICATIONS');
  console.log('=' .repeat(60));

  // Database connection
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'Asiedu123',
    database: 'vms_production'
  });

  try {
    // Get test users
    const [financeUsers] = await connection.execute(
      'SELECT * FROM users WHERE department = "FINANCE" AND is_active = 1 LIMIT 1'
    );
    const [auditUsers] = await connection.execute(
      'SELECT * FROM users WHERE department = "AUDIT" AND is_active = 1 LIMIT 1'
    );

    if (financeUsers.length === 0 || auditUsers.length === 0) {
      console.log('❌ Missing test users');
      return;
    }

    const financeUser = financeUsers[0];
    const auditUser = auditUsers[0];

    console.log(`👤 Finance User: ${financeUser.name} (${financeUser.id})`);
    console.log(`👤 Audit User: ${auditUser.name} (${auditUser.id})`);
    console.log('');

    // TEST 1: Department → Audit Notification
    console.log('🔄 TEST 1: FINANCE → AUDIT NOTIFICATION');
    console.log('-'.repeat(40));

    // Create Audit WebSocket connection
    const auditSocket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true
    });

    let auditNotificationReceived = false;
    let auditNotificationData = null;

    auditSocket.on('connect', () => {
      console.log('✅ Audit WebSocket connected');
      auditSocket.emit('join_department', {
        department: 'AUDIT',
        userId: auditUser.id,
        userName: auditUser.name
      });
    });

    auditSocket.on('joined_department', (data) => {
      console.log('✅ Audit joined department room:', data.department);
    });

    auditSocket.on('new_batch_notification', (data) => {
      console.log('🔔 AUDIT RECEIVED NOTIFICATION:', data);
      auditNotificationReceived = true;
      auditNotificationData = data;
    });

    // Wait for connection setup
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Create a test voucher and send to Audit
    const testVoucherId = uuidv4();
    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, department, amount, description, 
        created_by, status, workflow_state, workStarted
      ) VALUES (?, ?, 'FINANCE', 5000, 'Test voucher for notification', ?, 'PENDING SUBMISSION', 'PENDING_SUBMISSION', 1)
    `, [testVoucherId, `FINTEST${Date.now()}`, financeUser.id]);

    // Send voucher to Audit (this should trigger notification)
    const response = await fetch('http://localhost:8080/api/vouchers/send-to-audit', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        voucherIds: [testVoucherId],
        dispatchedBy: financeUser.name,
        department: 'FINANCE'
      })
    });

    if (response.ok) {
      console.log('✅ Voucher sent to Audit successfully');
    } else {
      console.log('❌ Failed to send voucher to Audit');
    }

    // Wait for notification
    await new Promise(resolve => setTimeout(resolve, 3000));

    if (auditNotificationReceived) {
      console.log('✅ TEST 1 PASSED: Audit received real-time notification');
      console.log(`   📄 Message: ${auditNotificationData.message}`);
      console.log(`   🏢 From: ${auditNotificationData.department}`);
    } else {
      console.log('❌ TEST 1 FAILED: Audit did not receive notification');
    }

    console.log('');

    // TEST 2: Audit → Department Notification
    console.log('🔄 TEST 2: AUDIT → FINANCE NOTIFICATION');
    console.log('-'.repeat(40));

    // Create Finance WebSocket connection
    const financeSocket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true
    });

    let financeNotificationReceived = false;
    let financeNotificationData = null;

    financeSocket.on('connect', () => {
      console.log('✅ Finance WebSocket connected');
      financeSocket.emit('join_department', {
        department: 'FINANCE',
        userId: financeUser.id,
        userName: financeUser.name
      });
    });

    financeSocket.on('joined_department', (data) => {
      console.log('✅ Finance joined department room:', data.department);
    });

    financeSocket.on('new_batch_notification', (data) => {
      console.log('🔔 FINANCE RECEIVED NOTIFICATION:', data);
      financeNotificationReceived = true;
      financeNotificationData = data;
    });

    // Wait for connection setup
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Get the voucher that was sent to Audit and dispatch it back
    const [sentVouchers] = await connection.execute(
      'SELECT * FROM vouchers WHERE id = ? AND status = "PENDING RECEIPT"',
      [testVoucherId]
    );

    if (sentVouchers.length > 0) {
      console.log('✅ Found voucher in Audit, dispatching back to Finance...');

      // Dispatch voucher back to Finance (this should trigger notification)
      const dispatchResponse = await fetch('http://************:8080/api/batches', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          department: 'FINANCE',
          voucherIds: [testVoucherId],
          fromAudit: true,
          dispatchedBy: auditUser.name
        })
      });

      if (dispatchResponse.ok) {
        console.log('✅ Voucher dispatched back to Finance successfully');
      } else {
        console.log('❌ Failed to dispatch voucher back to Finance');
      }

      // Wait for notification
      await new Promise(resolve => setTimeout(resolve, 3000));

      if (financeNotificationReceived) {
        console.log('✅ TEST 2 PASSED: Finance received real-time notification');
        console.log(`   📄 Message: ${financeNotificationData.message}`);
        console.log(`   🏢 From: ${financeNotificationData.department}`);
      } else {
        console.log('❌ TEST 2 FAILED: Finance did not receive notification');
      }
    } else {
      console.log('❌ TEST 2 SKIPPED: Voucher not found in Audit');
    }

    console.log('');

    // SUMMARY
    console.log('📊 TEST SUMMARY');
    console.log('=' .repeat(60));
    console.log(`✅ Department → Audit: ${auditNotificationReceived ? 'WORKING' : 'FAILED'}`);
    console.log(`✅ Audit → Department: ${financeNotificationReceived ? 'WORKING' : 'FAILED'}`);

    if (auditNotificationReceived && financeNotificationReceived) {
      console.log('🎉 BIDIRECTIONAL NOTIFICATIONS: FULLY WORKING!');
    } else {
      console.log('⚠️  BIDIRECTIONAL NOTIFICATIONS: NEEDS FIXING');
    }

    // Cleanup
    auditSocket.disconnect();
    financeSocket.disconnect();

    // Clean up test voucher
    await connection.execute('DELETE FROM vouchers WHERE id = ?', [testVoucherId]);
    await connection.execute('DELETE FROM voucher_batches WHERE voucher_ids LIKE ?', [`%${testVoucherId}%`]);
    await connection.execute('DELETE FROM notifications WHERE message LIKE ?', ['%Test voucher%']);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await connection.end();
  }
}

// Run the test
testBidirectionalNotifications().catch(console.error);
