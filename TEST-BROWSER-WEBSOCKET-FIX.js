const io = require('socket.io-client');

async function testBrowserWebSocketFix() {
  console.log('🔍 TESTING BROWSER WEBSOCKET FIX...\n');
  
  try {
    // Simulate exactly what the browser does
    console.log('1️⃣  Simulating browser WebSocket connection...');
    
    // Connect to same-origin like the browser does (using relative path)
    const socket = io('http://10.25.41.232:8080', {
      // Use same config as browser
      reconnection: true,
      reconnectionAttempts: Infinity,
      reconnectionDelay: 500,
      reconnectionDelayMax: 2000,
      randomizationFactor: 0,
      timeout: 5000,
      forceNew: false,
      upgrade: true,
      rememberUpgrade: true,
      transports: ['polling', 'websocket'],
      tryAllTransports: true
    });
    
    let connected = false;
    let roomJoined = false;
    let joinResponse = null;
    let notificationReceived = false;
    
    // Set up event listeners (exactly like browser)
    socket.on('connect', () => {
      console.log('   ✅ WebSocket connected - Socket ID:', socket.id);
      connected = true;
      
      // Simulate the fixed browser behavior - join department room immediately
      console.log('   📡 Joining AUDIT department room (simulating browser fix)...');
      socket.emit('join_department', {
        department: 'AUDIT',
        userId: 'd97a34d9-af6d-4026-a5e8-00db2d7a6a98',
        userName: 'EMMANUEL AMOAKOH'
      });
    });
    
    // Listen for join confirmation (new fix)
    socket.on('joined_department', (data) => {
      console.log('   ✅ Department room join confirmed:', data);
      roomJoined = true;
      joinResponse = data;
    });
    
    // Listen for notifications
    socket.on('new_batch_notification', (data) => {
      console.log('   🔔 REAL-TIME NOTIFICATION RECEIVED!');
      console.log('   📄 Data:', JSON.stringify(data, null, 2));
      notificationReceived = true;
    });
    
    socket.on('connect_error', (error) => {
      console.log('   ❌ Connection error:', error.message);
    });
    
    socket.on('disconnect', (reason) => {
      console.log('   🔌 Disconnected:', reason);
    });
    
    // Wait for connection and room joining
    console.log('   ⏳ Waiting for connection and room joining...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('\n📊 BROWSER SIMULATION RESULTS:');
    console.log(`   Connected: ${connected ? '✅' : '❌'}`);
    console.log(`   Room Joined: ${roomJoined ? '✅' : '❌'}`);
    
    if (joinResponse) {
      console.log(`   Join Response:`, joinResponse);
    }
    
    if (!connected) {
      console.log('\n❌ WEBSOCKET CONNECTION FAILED');
      console.log('💡 Browser will not be able to receive real-time notifications');
      socket.disconnect();
      return;
    }
    
    if (!roomJoined) {
      console.log('\n❌ DEPARTMENT ROOM JOIN FAILED');
      console.log('💡 Browser connected but cannot receive department-specific notifications');
      console.log('🔧 Check server-side join_department handler');
      socket.disconnect();
      return;
    }
    
    console.log('\n✅ BROWSER WEBSOCKET SIMULATION SUCCESSFUL!');
    console.log('🎯 The audit user\'s browser should now be able to receive real-time notifications');
    console.log('📡 WebSocket connected and joined AUDIT department room');
    
    // Test notification reception by waiting a bit longer
    console.log('\n2️⃣  Testing notification reception...');
    console.log('   ⏳ Waiting for any incoming notifications...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    if (notificationReceived) {
      console.log('   ✅ Notification system is working!');
    } else {
      console.log('   ℹ️  No notifications received (this is normal if no vouchers were sent)');
    }
    
    // Cleanup
    socket.disconnect();
    
    console.log('\n🎯 SUMMARY:');
    console.log('   The WebSocket fix should resolve the real-time notification issue.');
    console.log('   The audit user\'s browser will now properly join the AUDIT department room.');
    console.log('   When vouchers are sent to audit, the notification should appear immediately.');
    
  } catch (error) {
    console.error('❌ Error during browser WebSocket simulation:', error);
  }
}

// Run the test
testBrowserWebSocketFix();
