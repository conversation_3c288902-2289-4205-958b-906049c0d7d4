/**
 * 🎯 TEST: LOGIN DROPDOWN REAL-TIME UPDATE
 * Tests that the login dropdown updates immediately after admin approval
 */

const axios = require('axios');

const BASE_URL = 'http://10.25.41.232:8080';

async function testLoginRealtimeUpdate() {
  console.log('🔍 TESTING LOGIN DROPDOWN REAL-TIME UPDATE');
  console.log('=' .repeat(60));
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };

  // Test 1: Check initial user count
  let initialUserCount = 0;
  try {
    console.log('1️⃣  Getting initial user count...');
    const response = await axios.get(`${BASE_URL}/api/auth/users-by-department`);
    
    if (response.status === 200) {
      initialUserCount = response.data.length;
      console.log(`   ✅ Initial users: ${initialUserCount}`);
      results.passed++;
      results.tests.push({ name: 'Initial User Count', status: 'PASS', count: initialUserCount });
    } else {
      throw new Error('Failed to get users');
    }
  } catch (error) {
    console.log('   ❌ Failed to get initial user count');
    results.failed++;
    results.tests.push({ name: 'Initial User Count', status: 'FAIL', error: error.message });
    return results;
  }

  // Test 2: Create a test registration
  let registrationId = null;
  try {
    console.log('2️⃣  Creating test registration...');
    const registrationData = {
      name: 'TEST USER REALTIME',
      password: 'testpass123',
      department: 'FINANCE'
    };

    const response = await axios.post(`${BASE_URL}/api/auth/register`, registrationData);
    
    if (response.status === 201) {
      console.log('   ✅ Test registration created');
      results.passed++;
      results.tests.push({ name: 'Create Test Registration', status: 'PASS' });
    } else {
      throw new Error('Failed to create registration');
    }
  } catch (error) {
    console.log('   ❌ Failed to create test registration');
    results.failed++;
    results.tests.push({ name: 'Create Test Registration', status: 'FAIL', error: error.message });
    return results;
  }

  // Test 3: Get the registration ID
  try {
    console.log('3️⃣  Getting registration ID...');
    
    // Login as admin first
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      name: 'SYSTEM ADMINISTRATOR',
      password: 'admin123'
    });

    if (loginResponse.status !== 200) {
      throw new Error('Admin login failed');
    }

    const cookies = loginResponse.headers['set-cookie'];
    const sessionCookie = cookies ? cookies.find(c => c.includes('vms_session_id')) : null;
    
    if (!sessionCookie) {
      throw new Error('No session cookie received');
    }

    // Get pending registrations
    const pendingResponse = await axios.get(`${BASE_URL}/api/users/registrations/pending`, {
      headers: {
        'Cookie': sessionCookie
      }
    });

    if (pendingResponse.status === 200 && pendingResponse.data.length > 0) {
      registrationId = pendingResponse.data[0].id;
      console.log(`   ✅ Found registration ID: ${registrationId}`);
      results.passed++;
      results.tests.push({ name: 'Get Registration ID', status: 'PASS', id: registrationId });
    } else {
      throw new Error('No pending registrations found');
    }
  } catch (error) {
    console.log('   ❌ Failed to get registration ID');
    results.failed++;
    results.tests.push({ name: 'Get Registration ID', status: 'FAIL', error: error.message });
    return results;
  }

  // Test 4: Approve the registration
  try {
    console.log('4️⃣  Approving registration...');
    
    // Get admin session again
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      name: 'SYSTEM ADMINISTRATOR',
      password: 'admin123'
    });

    const cookies = loginResponse.headers['set-cookie'];
    const sessionCookie = cookies ? cookies.find(c => c.includes('vms_session_id')) : null;

    const approveResponse = await axios.post(
      `${BASE_URL}/api/users/registrations/${registrationId}/approve`,
      {},
      {
        headers: {
          'Cookie': sessionCookie
        }
      }
    );

    if (approveResponse.status === 200) {
      console.log('   ✅ Registration approved successfully');
      results.passed++;
      results.tests.push({ name: 'Approve Registration', status: 'PASS' });
    } else {
      throw new Error('Failed to approve registration');
    }
  } catch (error) {
    console.log('   ❌ Failed to approve registration');
    results.failed++;
    results.tests.push({ name: 'Approve Registration', status: 'FAIL', error: error.message });
    return results;
  }

  // Test 5: Wait and check if user count increased
  try {
    console.log('5️⃣  Checking if user count increased...');
    
    // Wait a moment for the update to propagate
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const response = await axios.get(`${BASE_URL}/api/auth/users-by-department`);
    
    if (response.status === 200) {
      const newUserCount = response.data.length;
      const hasNewUser = response.data.some(user => user.name === 'TEST USER REALTIME');
      
      if (newUserCount > initialUserCount && hasNewUser) {
        console.log(`   ✅ User count increased: ${initialUserCount} → ${newUserCount}`);
        console.log('   ✅ New user found in dropdown data');
        results.passed++;
        results.tests.push({ 
          name: 'User Count Increased', 
          status: 'PASS', 
          before: initialUserCount, 
          after: newUserCount 
        });
      } else {
        throw new Error(`User count not increased or user not found. Before: ${initialUserCount}, After: ${newUserCount}, Has user: ${hasNewUser}`);
      }
    } else {
      throw new Error('Failed to get updated users');
    }
  } catch (error) {
    console.log('   ❌ User count did not increase properly');
    results.failed++;
    results.tests.push({ name: 'User Count Increased', status: 'FAIL', error: error.message });
  }

  // Test 6: Verify cache headers are set correctly
  try {
    console.log('6️⃣  Checking cache headers...');
    const response = await axios.get(`${BASE_URL}/api/auth/users-by-department`);
    
    const cacheControl = response.headers['cache-control'];
    const pragma = response.headers['pragma'];
    const expires = response.headers['expires'];
    
    const hasNoCacheHeaders = 
      cacheControl && cacheControl.includes('no-cache') &&
      pragma === 'no-cache' &&
      expires === '0';
    
    if (hasNoCacheHeaders) {
      console.log('   ✅ Proper no-cache headers set');
      results.passed++;
      results.tests.push({ name: 'Cache Headers', status: 'PASS' });
    } else {
      throw new Error('Missing or incorrect cache headers');
    }
  } catch (error) {
    console.log('   ❌ Cache headers not properly set');
    results.failed++;
    results.tests.push({ name: 'Cache Headers', status: 'FAIL', error: error.message });
  }

  // Final Results
  console.log('\n' + '=' .repeat(60));
  console.log('🎯 LOGIN REAL-TIME UPDATE TEST RESULTS');
  console.log('=' .repeat(60));
  console.log(`✅ PASSED: ${results.passed}`);
  console.log(`❌ FAILED: ${results.failed}`);
  console.log(`📊 SUCCESS RATE: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 LOGIN DROPDOWN REAL-TIME UPDATE WORKING PERFECTLY!');
    console.log('✅ Users will see new accounts immediately after admin approval');
  } else {
    console.log('\n⚠️  ISSUES DETECTED - REVIEW FAILED TESTS');
    console.log('\nFailed Tests:');
    results.tests.filter(t => t.status === 'FAIL').forEach(test => {
      console.log(`   - ${test.name}: ${test.error}`);
    });
  }
  
  return results;
}

// Run test
testLoginRealtimeUpdate().catch(console.error);
