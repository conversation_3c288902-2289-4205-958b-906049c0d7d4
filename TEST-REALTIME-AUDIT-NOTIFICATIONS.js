/**
 * 🔍 TEST REAL-TIME AUDIT NOTIFICATIONS
 * Test the complete real-time notification flow when vouchers are sent to Audit
 */

const mysql = require('mysql2/promise');
const io = require('socket.io-client');

async function testRealtimeAuditNotifications() {
  console.log('🔍 TESTING REAL-TIME AUDIT NOTIFICATIONS');
  console.log('=' .repeat(60));
  
  let connection;
  let auditSocket;
  
  try {
    // 1. Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 2. Check current system state
    console.log('\n📊 Checking current system state...');
    
    const [vouchers] = await connection.execute(`
      SELECT voucher_id, department, status, sent_to_audit, received_by_audit 
      FROM vouchers 
      ORDER BY id DESC 
      LIMIT 3
    `);
    
    const [batches] = await connection.execute(`
      SELECT id, department, received, from_audit 
      FROM voucher_batches 
      ORDER BY id DESC 
      LIMIT 3
    `);
    
    const [notifications] = await connection.execute(`
      SELECT type, message, user_id, is_read 
      FROM notifications 
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    console.log(`🎫 Recent vouchers: ${vouchers.length}`);
    vouchers.forEach(v => {
      console.log(`   ${v.voucher_id} (${v.department}) - Sent to Audit: ${v.sent_to_audit ? 'YES' : 'NO'}`);
    });
    
    console.log(`📦 Recent batches: ${batches.length}`);
    batches.forEach(b => {
      console.log(`   Batch ${b.id} (${b.department}) - Received: ${b.received ? 'YES' : 'NO'}`);
    });
    
    console.log(`🔔 Recent notifications: ${notifications.length}`);
    notifications.forEach(n => {
      console.log(`   ${n.type}: ${n.message} - Read: ${n.is_read ? 'YES' : 'NO'}`);
    });
    
    // 3. Test WebSocket connection as Audit user
    console.log('\n🔌 Testing WebSocket connection as Audit user...');
    
    // Get an Audit user
    const [auditUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'AUDIT' LIMIT 1
    `);
    
    if (auditUsers.length === 0) {
      console.log('❌ No Audit users found in system');
      return;
    }
    
    const auditUser = auditUsers[0];
    console.log(`👤 Using Audit user: ${auditUser.name} (ID: ${auditUser.id})`);
    
    // Connect WebSocket as Audit user
    auditSocket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 5000
    });
    
    // Set up event listeners
    let notificationReceived = false;
    let batchUpdateReceived = false;
    let voucherUpdateReceived = false;
    
    auditSocket.on('connect', () => {
      console.log('✅ WebSocket connected as Audit user');
      
      // Join Audit department room
      auditSocket.emit('join_department', { 
        department: 'AUDIT',
        userId: auditUser.id,
        userName: auditUser.name
      });
      
      console.log('📡 Joined AUDIT department room');
    });
    
    auditSocket.on('new_batch_notification', (data) => {
      console.log('🔔 REAL-TIME: Received new_batch_notification:', data);
      notificationReceived = true;
    });
    
    auditSocket.on('batch_update', (data) => {
      console.log('📦 REAL-TIME: Received batch_update:', data);
      batchUpdateReceived = true;
    });
    
    auditSocket.on('voucher_update', (data) => {
      console.log('🎫 REAL-TIME: Received voucher_update:', data);
      voucherUpdateReceived = true;
    });
    
    auditSocket.on('connect_error', (error) => {
      console.log('❌ WebSocket connection error:', error.message);
    });
    
    auditSocket.on('disconnect', (reason) => {
      console.log('🔌 WebSocket disconnected:', reason);
    });
    
    // Wait for connection
    await new Promise(resolve => {
      auditSocket.on('connect', resolve);
      setTimeout(resolve, 3000); // Timeout after 3 seconds
    });
    
    // 4. Check server logs for WebSocket activity
    console.log('\n📋 Checking server WebSocket status...');
    
    // Test if server is broadcasting correctly
    console.log('\n🧪 Testing server broadcast capability...');
    
    // Simulate a voucher being sent to Audit by checking if the broadcast function works
    // We'll check the server logs to see if broadcasts are happening
    
    // 5. Analysis and recommendations
    console.log('\n' + '=' .repeat(60));
    console.log('📋 REAL-TIME NOTIFICATION ANALYSIS');
    console.log('=' .repeat(60));
    
    const pendingVouchers = vouchers.filter(v => v.sent_to_audit && !v.received_by_audit);
    const pendingBatches = batches.filter(b => !b.received && !b.from_audit);
    
    console.log(`🎫 Vouchers pending Audit receipt: ${pendingVouchers.length}`);
    console.log(`📦 Batches pending Audit receipt: ${pendingBatches.length}`);
    console.log(`🔔 WebSocket connection status: ${auditSocket?.connected ? 'CONNECTED' : 'DISCONNECTED'}`);
    console.log(`📡 Notification received: ${notificationReceived ? 'YES' : 'NO'}`);
    console.log(`📦 Batch update received: ${batchUpdateReceived ? 'YES' : 'NO'}`);
    console.log(`🎫 Voucher update received: ${voucherUpdateReceived ? 'YES' : 'NO'}`);
    
    if (pendingVouchers.length > 0 && !notificationReceived) {
      console.log('\n⚠️ ISSUE DETECTED: Vouchers pending but no real-time notifications received');
      console.log('💡 Possible causes:');
      console.log('   1. WebSocket not properly connected to AUDIT department room');
      console.log('   2. Server not broadcasting new_batch_notification events');
      console.log('   3. Client not listening for the correct event names');
      console.log('   4. Session/authentication issues preventing WebSocket connection');
    }
    
    if (!auditSocket?.connected) {
      console.log('\n⚠️ WEBSOCKET CONNECTION FAILED');
      console.log('💡 This explains why real-time notifications are not working');
      console.log('🔧 Check server WebSocket configuration and port accessibility');
    }
    
    console.log('\n✅ REAL-TIME NOTIFICATION TEST COMPLETE');
    
  } catch (error) {
    console.error('❌ Error testing real-time notifications:', error);
  } finally {
    if (auditSocket) {
      auditSocket.disconnect();
      console.log('🔌 WebSocket disconnected');
    }
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the test
testRealtimeAuditNotifications().catch(console.error);
