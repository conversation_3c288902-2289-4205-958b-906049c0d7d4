/**
 * 🔔 FINAL REAL-TIME NOTIFICATION TEST
 * Tests the complete voucher notification workflow
 */

const axios = require('axios');
const { io } = require('socket.io-client');

const BASE_URL = 'http://10.25.41.232:8080';

async function testRealTimeNotifications() {
  console.log('🔔 TESTING REAL-TIME VOUCHER NOTIFICATIONS');
  console.log('=' .repeat(60));
  
  let auditSocket = null;
  let financeSocket = null;
  
  try {
    // Step 1: Get test users
    console.log('\n1️⃣  Getting test users...');
    const usersResponse = await axios.get(`${BASE_URL}/api/auth/users-by-department`);
    const users = usersResponse.data;
    
    const financeUser = users.find(u => u.department === 'FINANCE');
    const auditUser = users.find(u => u.department === 'AUDIT');
    
    if (!financeUser || !auditUser) {
      throw new Error('Could not find Finance and Audit users');
    }
    
    console.log(`   ✅ Finance User: ${financeUser.name}`);
    console.log(`   ✅ Audit User: ${auditUser.name}`);
    
    // Step 2: Connect Audit user via WebSocket
    console.log('\n2️⃣  Connecting Audit user to WebSocket...');
    auditSocket = io(BASE_URL, {
      reconnection: false,
      timeout: 10000,
      forceNew: true
    });
    
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => reject(new Error('Connection timeout')), 10000);
      
      auditSocket.on('connect', () => {
        clearTimeout(timeout);
        console.log(`   ✅ Audit WebSocket connected - Socket ID: ${auditSocket.id}`);
        
        // Join Audit department room
        auditSocket.emit('join_department', { 
          department: 'AUDIT',
          userId: auditUser.id,
          userName: auditUser.name
        });
        
        resolve();
      });
      
      auditSocket.on('connect_error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
    
    // Step 3: Set up notification listener
    console.log('\n3️⃣  Setting up notification listener...');
    let notificationReceived = false;
    let notificationData = null;
    let roomJoined = false;
    
    auditSocket.on('new_batch_notification', (data) => {
      console.log('🔔 REAL-TIME NOTIFICATION RECEIVED!');
      console.log('📄 Data:', JSON.stringify(data, null, 2));
      notificationReceived = true;
      notificationData = data;
    });
    
    auditSocket.on('joined_department', (data) => {
      console.log('   ✅ Joined AUDIT department room:', data);
      roomJoined = true;
    });
    
    // Wait for room join confirmation
    await new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (roomJoined) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
      
      setTimeout(() => {
        clearInterval(checkInterval);
        resolve(); // Continue even if no confirmation
      }, 2000);
    });
    
    // Step 4: Get existing voucher from Finance
    console.log('\n4️⃣  Getting existing Finance voucher...');

    // Login as Finance user
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      department: financeUser.department,
      username: financeUser.name,
      password: '123123'
    });

    const sessionId = loginResponse.data.sessionId;
    const authHeaders = {
      'x-session-id': sessionId,
      'Content-Type': 'application/json'
    };

    // Create a test voucher
    const voucherData = {
      claimant: 'TEST CLAIMANT FOR NOTIFICATION',
      amount: 1000,
      description: 'Test voucher for real-time notification',
      currency: 'GHS',
      department: 'FINANCE'
    };

    const voucherResponse = await axios.post(`${BASE_URL}/api/vouchers`, voucherData, {
      headers: authHeaders
    });

    const voucher = voucherResponse.data;
    console.log(`   ✅ Created voucher: ${voucher.voucher_number}`);
    
    // Step 5: Send voucher to Audit via batch
    console.log('\n5️⃣  Sending voucher to Audit...');
    
    const batchData = {
      department: 'FINANCE',
      voucherIds: [voucher.id],
      dispatchedBy: 'TEST DISPATCHER'
    };
    
    const batchResponse = await axios.post(`${BASE_URL}/api/batches`, batchData, {
      headers: authHeaders
    });
    
    console.log(`   ✅ Batch sent: ${batchResponse.data.batch_id}`);
    
    // Step 6: Wait for notification
    console.log('\n6️⃣  Waiting for real-time notification...');
    
    await new Promise((resolve) => {
      setTimeout(resolve, 5000); // Wait 5 seconds
    });
    
    // Step 7: Check results
    console.log('\n7️⃣  RESULTS:');
    console.log('=' .repeat(40));
    
    if (notificationReceived) {
      console.log('✅ REAL-TIME NOTIFICATION: SUCCESS');
      console.log('📄 Notification Data:', JSON.stringify(notificationData, null, 2));
    } else {
      console.log('❌ REAL-TIME NOTIFICATION: FAILED');
      console.log('   No notification received within 5 seconds');
      console.log(`   Room joined: ${roomJoined}`);
      console.log(`   Socket connected: ${auditSocket.connected}`);
    }
    
    // Step 8: Verify notification in database
    console.log('\n8️⃣  Verifying database notification...');
    
    const notificationsResponse = await axios.get(`${BASE_URL}/api/notifications`, {
      headers: authHeaders
    });
    
    const notifications = notificationsResponse.data;
    const recentNotification = notifications.find(n => 
      n.batch_id === batchResponse.data.batch_id && 
      n.department === 'AUDIT'
    );
    
    if (recentNotification) {
      console.log('✅ DATABASE NOTIFICATION: SUCCESS');
      console.log(`   Notification ID: ${recentNotification.id}`);
      console.log(`   Message: ${recentNotification.message}`);
    } else {
      console.log('❌ DATABASE NOTIFICATION: FAILED');
    }
    
    console.log('\n🎉 TEST COMPLETE');
    
  } catch (error) {
    console.error('❌ TEST FAILED:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Cleanup
    if (auditSocket) {
      auditSocket.disconnect();
    }
    if (financeSocket) {
      financeSocket.disconnect();
    }
  }
}

// Run the test
testRealTimeNotifications();
