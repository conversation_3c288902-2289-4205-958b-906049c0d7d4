/**
 * 🧪 TEST SEND VOUCHER REAL-TIME
 * Create a voucher and send it to Audit to test real-time notifications
 */

const mysql = require('mysql2/promise');
const axios = require('axios');
const io = require('socket.io-client');

async function testSendVoucherRealtime() {
  console.log('🧪 TESTING SEND VOUCHER TO AUDIT - REAL-TIME NOTIFICATIONS');
  console.log('=' .repeat(70));
  
  let connection;
  let auditSocket;
  
  try {
    // 1. Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 2. Get users
    const [financeUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'FINANCE' LIMIT 1
    `);
    
    const [auditUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'AUDIT' LIMIT 1
    `);
    
    if (financeUsers.length === 0 || auditUsers.length === 0) {
      console.log('❌ Need both Finance and Audit users');
      return;
    }
    
    const financeUser = financeUsers[0];
    const auditUser = auditUsers[0];
    
    console.log(`👤 Finance user: ${financeUser.name} (ID: ${financeUser.id})`);
    console.log(`👤 Audit user: ${auditUser.name} (ID: ${auditUser.id})`);
    
    // 3. Set up Audit WebSocket BEFORE creating voucher
    console.log('\n🔌 Setting up Audit WebSocket listener...');
    
    auditSocket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 5000
    });
    
    let notificationReceived = false;
    let notificationData = null;
    let roomJoined = false;
    
    auditSocket.on('connect', () => {
      console.log('✅ Audit WebSocket connected');
      
      // Join Audit department room
      auditSocket.emit('join_department', { 
        department: 'AUDIT',
        userId: auditUser.id,
        userName: auditUser.name
      });
    });
    
    auditSocket.on('joined_department', (data) => {
      console.log('✅ Successfully joined Audit department room:', data);
      roomJoined = true;
    });
    
    auditSocket.on('new_batch_notification', (data) => {
      console.log('🔔 REAL-TIME: new_batch_notification received!');
      console.log('📄 Data:', JSON.stringify(data, null, 2));
      notificationReceived = true;
      notificationData = data;
    });
    
    auditSocket.on('batch_update', (data) => {
      console.log('📦 REAL-TIME: batch_update received:', data);
    });
    
    auditSocket.on('voucher_update', (data) => {
      console.log('🎫 REAL-TIME: voucher_update received:', data);
    });
    
    // Wait for WebSocket setup
    console.log('⏳ Waiting for WebSocket setup...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    if (!auditSocket.connected) {
      console.log('❌ WebSocket connection failed');
      return;
    }
    
    if (!roomJoined) {
      console.log('⚠️ Room join not confirmed, but proceeding...');
    }
    
    // 4. Create a test voucher
    console.log('\n📝 Creating test voucher...');
    
    const voucherId = `TEST${Date.now()}`;
    
    // Get voucher table structure first
    const [columns] = await connection.execute('DESCRIBE vouchers');
    const hasPayeeName = columns.some(col => col.Field === 'payee_name');
    const hasDescription = columns.some(col => col.Field === 'description');
    
    let insertQuery, insertValues;
    
    if (hasPayeeName && hasDescription) {
      insertQuery = `
        INSERT INTO vouchers (
          voucher_id, amount, currency, department, description, 
          created_by, payee_name, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'FINANCE_PENDING', NOW())
      `;
      insertValues = [
        voucherId, 5000.00, 'NGN', 'FINANCE', 
        'Test voucher for real-time notification',
        financeUser.name, 'Test Payee'
      ];
    } else {
      // Minimal insert
      insertQuery = `
        INSERT INTO vouchers (
          voucher_id, amount, currency, department, 
          created_by, status, created_at
        ) VALUES (?, ?, ?, ?, ?, 'FINANCE_PENDING', NOW())
      `;
      insertValues = [voucherId, 5000.00, 'NGN', 'FINANCE', financeUser.name];
    }
    
    const [voucherResult] = await connection.execute(insertQuery, insertValues);
    const dbVoucherId = voucherResult.insertId;
    
    console.log(`✅ Created voucher: ${voucherId} (DB ID: ${dbVoucherId})`);
    
    // 5. Login as Finance user and send voucher to Audit
    console.log('\n🔐 Logging in as Finance user...');
    
    try {
      const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
        userId: financeUser.id
      });
      
      const sessionCookie = loginResponse.headers['set-cookie']?.[0];
      console.log('✅ Logged in as Finance user');
      
      // 6. Send voucher to Audit
      console.log('\n📤 Sending voucher to Audit...');
      
      const sendResponse = await axios.post(
        'http://localhost:8080/api/audit/send-to-audit',
        {
          department: 'FINANCE',
          voucherIds: [dbVoucherId.toString()],
          dispatchedBy: financeUser.name
        },
        {
          headers: {
            'Cookie': sessionCookie,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('✅ Voucher sent to Audit successfully!');
      console.log('📊 API Response:', sendResponse.data);
      
    } catch (apiError) {
      console.log('❌ API Error:', apiError.response?.data || apiError.message);
      console.log('🔄 This might be expected if the API endpoint has changed');
    }
    
    // 7. Wait for real-time notification
    console.log('\n⏳ Waiting for real-time notification (15 seconds)...');
    
    const startTime = Date.now();
    while (Date.now() - startTime < 15000) {
      if (notificationReceived) {
        console.log('🎉 NOTIFICATION RECEIVED!');
        break;
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
      process.stdout.write('.');
    }
    
    console.log('\n');
    
    // 8. Check final results
    console.log('\n' + '=' .repeat(70));
    console.log('📋 REAL-TIME NOTIFICATION TEST RESULTS');
    console.log('=' .repeat(70));
    
    console.log(`🔌 WebSocket connected: ${auditSocket.connected ? 'YES' : 'NO'}`);
    console.log(`📡 Joined department room: ${roomJoined ? 'YES' : 'NO'}`);
    console.log(`🔔 Notification received: ${notificationReceived ? 'YES' : 'NO'}`);
    
    if (notificationData) {
      console.log('📄 Notification data:');
      console.log(JSON.stringify(notificationData, null, 2));
    }
    
    // Check database state
    const [updatedVoucher] = await connection.execute(`
      SELECT voucher_id, status, sent_to_audit, received_by_audit 
      FROM vouchers WHERE id = ?
    `, [dbVoucherId]);
    
    if (updatedVoucher.length > 0) {
      const voucher = updatedVoucher[0];
      console.log(`🎫 Final voucher status: ${voucher.status}`);
      console.log(`📤 Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`📥 Received by Audit: ${voucher.received_by_audit ? 'YES' : 'NO'}`);
    }
    
    if (notificationReceived) {
      console.log('\n🎉 SUCCESS: Real-time notifications are working!');
      console.log('✅ The issue has been resolved');
    } else {
      console.log('\n⚠️ ISSUE PERSISTS: No real-time notification received');
      console.log('🔧 Need to check server broadcast logs');
    }
    
    console.log('\n✅ SEND VOUCHER REAL-TIME TEST COMPLETE');
    
  } catch (error) {
    console.error('❌ Error in send voucher test:', error);
  } finally {
    if (auditSocket) {
      auditSocket.disconnect();
    }
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
testSendVoucherRealtime().catch(console.error);
