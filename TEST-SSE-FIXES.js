/**
 * 🔧 SSE FIXES VERIFICATION TEST
 * Test the enhanced SSE error handling
 */

const http = require('http');

async function testSSEFixes() {
  console.log('🔧 SSE FIXES VERIFICATION TEST');
  console.log('=' .repeat(50));
  
  return new Promise((resolve) => {
    // Test SSE endpoint
    const req = http.request({
      hostname: '************',
      port: 8080,
      path: '/api/login-updates/user-updates',
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      }
    }, (res) => {
      console.log('✅ SSE Connection Status:', res.statusCode);
      console.log('📡 Response Headers:');
      Object.entries(res.headers).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
      
      let messageCount = 0;
      
      res.on('data', (chunk) => {
        const data = chunk.toString();
        if (data.trim()) {
          messageCount++;
          console.log(`📨 SSE Message ${messageCount}:`, data.trim());
          
          // Parse the message if it's JSON
          try {
            const lines = data.split('\n');
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const jsonData = line.substring(6);
                const parsed = JSON.parse(jsonData);
                console.log(`   Type: ${parsed.type}`);
                console.log(`   Message: ${parsed.message || 'N/A'}`);
                console.log(`   Timestamp: ${new Date(parsed.timestamp).toLocaleTimeString()}`);
              }
            }
          } catch (error) {
            console.log('   Raw data:', data);
          }
        }
      });
      
      res.on('error', (error) => {
        console.log('❌ SSE Response Error:', error.message);
      });
      
      // Test connection for 5 seconds then close
      setTimeout(() => {
        console.log('\n🔌 Closing SSE connection after 5 seconds...');
        req.destroy();
        
        setTimeout(() => {
          console.log(`✅ Test completed. Received ${messageCount} messages.`);
          console.log('🔧 Enhanced error handling should prevent ECONNRESET errors in logs');
          resolve();
        }, 1000);
      }, 5000);
    });
    
    req.on('error', (error) => {
      console.log('❌ SSE Request Error:', error.message);
      resolve();
    });
    
    req.end();
  });
}

// Run test
testSSEFixes().then(() => {
  console.log('\n' + '=' .repeat(50));
  console.log('🔧 SSE FIXES TEST COMPLETE');
  console.log('=' .repeat(50));
}).catch(console.error);
