/**
 * 🧪 TEST VOUCHER TO AUDIT REAL-TIME
 * Create a voucher and send it to Audit to test real-time notifications
 */

const mysql = require('mysql2/promise');
const axios = require('axios');
const io = require('socket.io-client');

async function testVoucherToAuditRealtime() {
  console.log('🧪 TESTING VOUCHER TO AUDIT REAL-TIME NOTIFICATIONS');
  console.log('=' .repeat(60));
  
  let connection;
  let auditSocket;
  
  try {
    // 1. Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 2. Get Finance and Audit users
    const [financeUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'FINANCE' LIMIT 1
    `);
    
    const [auditUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'AUDIT' LIMIT 1
    `);
    
    if (financeUsers.length === 0 || auditUsers.length === 0) {
      console.log('❌ Need both Finance and Audit users for test');
      return;
    }
    
    const financeUser = financeUsers[0];
    const auditUser = auditUsers[0];
    
    console.log(`👤 Finance user: ${financeUser.name}`);
    console.log(`👤 Audit user: ${auditUser.name}`);
    
    // 3. Set up WebSocket as Audit user BEFORE creating voucher
    console.log('\n🔌 Setting up WebSocket as Audit user...');
    
    auditSocket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 5000
    });
    
    let notificationReceived = false;
    let notificationData = null;
    
    auditSocket.on('connect', () => {
      console.log('✅ Audit WebSocket connected');
      
      // Join Audit department room
      auditSocket.emit('join_department', { 
        department: 'AUDIT',
        userId: auditUser.id,
        userName: auditUser.name
      });
      
      console.log('📡 Joined AUDIT department room');
    });
    
    auditSocket.on('new_batch_notification', (data) => {
      console.log('🔔 REAL-TIME: Received new_batch_notification:', data);
      notificationReceived = true;
      notificationData = data;
    });
    
    auditSocket.on('batch_update', (data) => {
      console.log('📦 REAL-TIME: Received batch_update:', data);
    });
    
    auditSocket.on('voucher_update', (data) => {
      console.log('🎫 REAL-TIME: Received voucher_update:', data);
    });
    
    // Wait for WebSocket connection
    await new Promise(resolve => {
      auditSocket.on('connect', resolve);
      setTimeout(resolve, 2000);
    });
    
    if (!auditSocket.connected) {
      console.log('❌ WebSocket connection failed');
      return;
    }
    
    // 4. Create a test voucher
    console.log('\n📝 Creating test voucher...');
    
    const testVoucher = {
      voucher_id: `TEST${Date.now()}`,
      amount: 5000.00,
      currency: 'NGN',
      department: 'FINANCE',
      description: 'Test voucher for real-time notification',
      created_by: financeUser.name,
      beneficiary: 'Test Beneficiary'
    };
    
    // Insert voucher directly into database
    const [voucherResult] = await connection.execute(`
      INSERT INTO vouchers (
        voucher_id, amount, currency, department, description, 
        created_by, beneficiary, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'FINANCE_PENDING', NOW())
    `, [
      testVoucher.voucher_id,
      testVoucher.amount,
      testVoucher.currency,
      testVoucher.department,
      testVoucher.description,
      testVoucher.created_by,
      testVoucher.beneficiary
    ]);
    
    const voucherId = voucherResult.insertId;
    console.log(`✅ Created voucher: ${testVoucher.voucher_id} (ID: ${voucherId})`);
    
    // 5. Send voucher to Audit via API
    console.log('\n📤 Sending voucher to Audit...');
    
    try {
      // First, we need to login as Finance user to get session
      const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
        userId: financeUser.id
      });
      
      const sessionCookie = loginResponse.headers['set-cookie']?.[0];
      console.log('🔐 Logged in as Finance user');
      
      // Send voucher to Audit
      const sendResponse = await axios.post(
        'http://localhost:8080/api/audit/send-to-audit',
        {
          department: 'FINANCE',
          voucherIds: [voucherId.toString()],
          dispatchedBy: financeUser.name
        },
        {
          headers: {
            'Cookie': sessionCookie,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('✅ Voucher sent to Audit via API');
      console.log('📊 Response:', sendResponse.data);
      
    } catch (apiError) {
      console.log('❌ API Error:', apiError.response?.data || apiError.message);
      
      // Fallback: Update database directly to simulate sending to Audit
      console.log('\n🔄 Fallback: Updating database directly...');
      
      await connection.execute(`
        UPDATE vouchers 
        SET sent_to_audit = TRUE, 
            status = 'PENDING_RECEIPT',
            dispatch_to_audit_by = ?,
            dispatch_time = NOW()
        WHERE id = ?
      `, [financeUser.name, voucherId]);
      
      // Create batch manually
      const batchId = `BATCH${Date.now()}`;
      await connection.execute(`
        INSERT INTO voucher_batches (id, department, voucher_count, received, from_audit)
        VALUES (?, 'FINANCE', 1, FALSE, FALSE)
      `, [batchId]);
      
      console.log(`✅ Created batch: ${batchId}`);
      
      // Manually trigger notification (this should test if the broadcast works)
      console.log('\n📢 Testing manual broadcast...');
      
      // We can't directly call the server function, but we can check if the notification appears
    }
    
    // 6. Wait for real-time notification
    console.log('\n⏳ Waiting for real-time notification (10 seconds)...');
    
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // 7. Check results
    console.log('\n' + '=' .repeat(60));
    console.log('📋 REAL-TIME TEST RESULTS');
    console.log('=' .repeat(60));
    
    console.log(`🔔 Notification received: ${notificationReceived ? 'YES' : 'NO'}`);
    if (notificationData) {
      console.log('📄 Notification data:', notificationData);
    }
    
    // Check database state
    const [updatedVoucher] = await connection.execute(`
      SELECT voucher_id, status, sent_to_audit, received_by_audit 
      FROM vouchers WHERE id = ?
    `, [voucherId]);
    
    if (updatedVoucher.length > 0) {
      const voucher = updatedVoucher[0];
      console.log(`🎫 Voucher status: ${voucher.status}`);
      console.log(`📤 Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`📥 Received by Audit: ${voucher.received_by_audit ? 'YES' : 'NO'}`);
    }
    
    const [batches] = await connection.execute(`
      SELECT id, department, received FROM voucher_batches 
      ORDER BY id DESC LIMIT 1
    `);
    
    if (batches.length > 0) {
      const batch = batches[0];
      console.log(`📦 Latest batch: ${batch.id} (${batch.department})`);
      console.log(`📥 Batch received: ${batch.received ? 'YES' : 'NO'}`);
    }
    
    if (!notificationReceived) {
      console.log('\n⚠️ REAL-TIME NOTIFICATION FAILED');
      console.log('💡 This confirms the issue: vouchers are sent but notifications don\'t appear in real-time');
      console.log('🔧 Need to fix the WebSocket broadcast system');
    } else {
      console.log('\n✅ REAL-TIME NOTIFICATION SUCCESS');
      console.log('🎉 The notification system is working correctly');
    }
    
    console.log('\n✅ REAL-TIME TEST COMPLETE');
    
  } catch (error) {
    console.error('❌ Error in real-time test:', error);
  } finally {
    if (auditSocket) {
      auditSocket.disconnect();
    }
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
testVoucherToAuditRealtime().catch(console.error);
