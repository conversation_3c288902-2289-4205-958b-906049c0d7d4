@echo off
REM VMS 5.0 System Status Checker

title VMS 5.0 - System Status

echo.
echo ========================================
echo 📊 VMS 5.0 SYSTEM STATUS
echo ========================================
echo.

echo [CHECKING] 🔍 Node.js processes...
tasklist /fi "imagename eq node.exe" 2>nul | find "node.exe" >nul
if %errorLevel% == 0 (
    echo [STATUS] ✅ Node.js processes are running
    tasklist /fi "imagename eq node.exe"
) else (
    echo [STATUS] ❌ No Node.js processes found
)

echo.
echo [CHECKING] 🌐 Port 8080 (VMS Server)...
netstat -an | find ":8080" >nul
if %errorLevel% == 0 (
    echo [STATUS] ✅ Port 8080 is active (Server running)
) else (
    echo [STATUS] ❌ Port 8080 is not active (Server not running)
)

echo.
echo [CHECKING] 🌐 Port 3000 (VMS Client)...
netstat -an | find ":3000" >nul
if %errorLevel% == 0 (
    echo [STATUS] ✅ Port 3000 is active (Client running)
) else (
    echo [STATUS] ❌ Port 3000 is not active (Client not running)
)

echo.
echo [CHECKING] 🗄️  Database connection...
mysql -u root -pvms@2025@1989 -e "SELECT 1;" >nul 2>&1
if %errorLevel% == 0 (
    echo [STATUS] ✅ Database connection successful
) else (
    echo [STATUS] ❌ Database connection failed
)

echo.
echo [CHECKING] 💚 VMS Server health...
curl http://localhost:8080/health >nul 2>&1
if %errorLevel% == 0 (
    echo [STATUS] ✅ VMS Server is healthy
    echo [INFO] Getting detailed health information...
    curl -s http://localhost:8080/health
) else (
    echo [STATUS] ❌ VMS Server health check failed
)

echo.
echo ========================================
echo 📊 STATUS CHECK COMPLETE
echo ========================================
echo.
pause
