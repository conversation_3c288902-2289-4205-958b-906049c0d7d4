@echo off
REM VMS 5.0 System Stop

title VMS 5.0 - Stop System

echo.
echo ========================================
echo 🛑 VMS 5.0 SYSTEM STOP
echo ========================================
echo.

echo [INFO] 🔄 Stopping all VMS processes...

REM Stop Node.js processes
taskkill /f /im node.exe >nul 2>&1
if %errorLevel% == 0 (
    echo [SUCCESS] ✅ Node.js processes stopped
) else (
    echo [INFO] ℹ️  No Node.js processes were running
)

REM Clean up ports
echo [INFO] 🧹 Cleaning up ports...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do taskkill /f /pid %%a >nul 2>&1

timeout /t 2 >nul

echo [INFO] 🔍 Verifying shutdown...
netstat -an | find ":8080" >nul
if %errorLevel% neq 0 (
    echo [SUCCESS] ✅ Port 8080 is free
) else (
    echo [WARNING] ⚠️  Port 8080 may still be in use
)

netstat -an | find ":3000" >nul
if %errorLevel% neq 0 (
    echo [SUCCESS] ✅ Port 3000 is free
) else (
    echo [WARNING] ⚠️  Port 3000 may still be in use
)

echo.
echo ========================================
echo ✅ VMS 5.0 STOPPED SUCCESSFULLY
echo ========================================
echo.
echo All VMS processes have been terminated.
echo You can now safely close this window.
echo.
pause
