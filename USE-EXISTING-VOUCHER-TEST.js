/**
 * 🧪 USE EXISTING VOUCHER TEST
 * Test real-time notifications using an existing voucher
 */

const mysql = require('mysql2/promise');
const axios = require('axios');
const io = require('socket.io-client');

async function useExistingVoucherTest() {
  console.log('🧪 USE EXISTING VOUCHER REAL-TIME TEST');
  console.log('=' .repeat(60));
  
  let connection;
  let auditSocket;
  
  try {
    // 1. Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 2. Get users
    const [auditUsers] = await connection.execute(`
      SELECT id, name FROM users WHERE department = 'AUDIT' LIMIT 1
    `);
    
    if (auditUsers.length === 0) {
      console.log('❌ No Audit users found');
      return;
    }
    
    const auditUser = auditUsers[0];
    console.log(`👤 Audit user: ${auditUser.name} (ID: ${auditUser.id})`);
    
    // 3. Check existing vouchers
    console.log('\n📊 Checking existing vouchers...');
    
    const [allVouchers] = await connection.execute(`
      SELECT id, voucher_id, department, status, sent_to_audit, received_by_audit 
      FROM vouchers 
      ORDER BY id DESC 
      LIMIT 5
    `);
    
    console.log(`📋 Found ${allVouchers.length} vouchers:`);
    allVouchers.forEach((v, index) => {
      console.log(`   ${index + 1}. ${v.voucher_id} (${v.department}) - Status: ${v.status}`);
      console.log(`      Sent to Audit: ${v.sent_to_audit ? 'YES' : 'NO'}, Received: ${v.received_by_audit ? 'YES' : 'NO'}`);
    });
    
    // 4. Set up Audit WebSocket
    console.log('\n🔌 Setting up Audit WebSocket listener...');
    
    auditSocket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 5000
    });
    
    let connected = false;
    let roomJoined = false;
    let notificationReceived = false;
    let notificationData = null;
    
    auditSocket.on('connect', () => {
      console.log('✅ Audit WebSocket connected');
      connected = true;
      
      // Join Audit department room
      auditSocket.emit('join_department', { 
        department: 'AUDIT',
        userId: auditUser.id,
        userName: auditUser.name
      });
    });
    
    auditSocket.on('joined_department', (data) => {
      console.log('✅ Joined department room:', data);
      roomJoined = true;
    });
    
    auditSocket.on('new_batch_notification', (data) => {
      console.log('🔔 REAL-TIME: new_batch_notification received!');
      console.log('📄 Data:', JSON.stringify(data, null, 2));
      notificationReceived = true;
      notificationData = data;
    });
    
    auditSocket.on('batch_update', (data) => {
      console.log('📦 REAL-TIME: batch_update received:', data);
    });
    
    auditSocket.on('voucher_update', (data) => {
      console.log('🎫 REAL-TIME: voucher_update received:', data);
    });
    
    // Wait for WebSocket setup
    console.log('⏳ Waiting for WebSocket setup...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log(`🔌 Connected: ${connected}`);
    console.log(`📡 Room joined: ${roomJoined}`);
    
    if (!connected || !roomJoined) {
      console.log('❌ WebSocket setup failed');
      return;
    }
    
    // 5. Check if there are pending vouchers that should show notifications
    const pendingVouchers = allVouchers.filter(v => v.sent_to_audit && !v.received_by_audit);
    
    console.log(`\n🎫 Vouchers pending Audit receipt: ${pendingVouchers.length}`);
    
    if (pendingVouchers.length > 0) {
      console.log('📋 Pending vouchers:');
      pendingVouchers.forEach(v => {
        console.log(`   - ${v.voucher_id} (${v.department}) - Status: ${v.status}`);
      });
      
      console.log('\n💡 These vouchers should trigger notifications when Audit user connects');
      console.log('⏳ Waiting to see if notifications appear (10 seconds)...');
      
      // Wait for any automatic notifications
      const startTime = Date.now();
      while (Date.now() - startTime < 10000) {
        if (notificationReceived) {
          console.log('🎉 NOTIFICATION RECEIVED!');
          break;
        }
        await new Promise(resolve => setTimeout(resolve, 500));
        process.stdout.write('.');
      }
      console.log('\n');
    }
    
    // 6. Check batches
    console.log('\n📦 Checking recent batches...');
    
    const [recentBatches] = await connection.execute(`
      SELECT id, department, received, from_audit, created_at 
      FROM voucher_batches 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    console.log(`📋 Found ${recentBatches.length} recent batches:`);
    recentBatches.forEach((b, index) => {
      console.log(`   ${index + 1}. ${b.id} (${b.department}) - Received: ${b.received ? 'YES' : 'NO'}`);
    });
    
    const pendingBatches = recentBatches.filter(b => !b.received && !b.from_audit);
    console.log(`📦 Batches pending receipt: ${pendingBatches.length}`);
    
    // 7. Final results
    console.log('\n' + '=' .repeat(60));
    console.log('📋 EXISTING VOUCHER TEST RESULTS');
    console.log('=' .repeat(60));
    
    console.log(`🔌 WebSocket connected: ${connected ? 'YES' : 'NO'}`);
    console.log(`📡 Joined department room: ${roomJoined ? 'YES' : 'NO'}`);
    console.log(`🔔 Notification received: ${notificationReceived ? 'YES' : 'NO'}`);
    console.log(`🎫 Pending vouchers: ${pendingVouchers.length}`);
    console.log(`📦 Pending batches: ${pendingBatches.length}`);
    
    if (notificationData) {
      console.log('📄 Notification data:');
      console.log(JSON.stringify(notificationData, null, 2));
    }
    
    if (pendingVouchers.length > 0 && !notificationReceived) {
      console.log('\n⚠️ ISSUE CONFIRMED: Pending vouchers exist but no notifications received');
      console.log('💡 The real-time notification system needs investigation');
      console.log('🔧 Possible issues:');
      console.log('   1. Notifications only sent when vouchers are NEWLY sent to Audit');
      console.log('   2. No automatic notification for existing pending vouchers');
      console.log('   3. Need to test with fresh voucher sending');
    } else if (notificationReceived) {
      console.log('\n🎉 SUCCESS: Real-time notifications working!');
    } else {
      console.log('\n📝 NO PENDING ITEMS: Need to send a voucher to test notifications');
    }
    
    console.log('\n💡 RECOMMENDATION: Test by actually sending a voucher through the web interface');
    console.log('   1. Login to Finance department');
    console.log('   2. Create a new voucher');
    console.log('   3. Send it to Audit');
    console.log('   4. Check if Audit user receives real-time notification');
    
    console.log('\n✅ EXISTING VOUCHER TEST COMPLETE');
    
  } catch (error) {
    console.error('❌ Error in existing voucher test:', error);
  } finally {
    if (auditSocket) {
      auditSocket.disconnect();
    }
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
useExistingVoucherTest().catch(console.error);
