/**
 * 🔍 VERIFY SSE ENDPOINT
 * Simple verification that the Server-Sent Events endpoint is working
 */

const axios = require('axios');

const BASE_URL = 'http://10.25.41.232:8080';

async function verifySSEEndpoint() {
  console.log('🔍 VERIFYING SSE ENDPOINT FOR LOGIN UPDATES');
  console.log('=' .repeat(50));
  
  try {
    console.log('1️⃣  Testing SSE endpoint accessibility...');
    
    // Test SSE endpoint with a timeout
    const response = await axios.get(`${BASE_URL}/api/login-updates/user-updates`, {
      timeout: 3000,
      responseType: 'stream'
    });
    
    if (response.status === 200) {
      console.log('   ✅ SSE endpoint is accessible');
      console.log('   ✅ Content-Type:', response.headers['content-type']);
      console.log('   ✅ Cache-Control:', response.headers['cache-control']);
      
      // Listen for initial data
      let dataReceived = false;
      
      response.data.on('data', (chunk) => {
        const data = chunk.toString();
        if (data.includes('connected')) {
          console.log('   ✅ Received initial connection message');
          dataReceived = true;
        }
      });
      
      // Wait a moment for initial data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Close the stream
      response.data.destroy();
      
      if (dataReceived) {
        console.log('   ✅ SSE stream is working correctly');
      } else {
        console.log('   ⚠️  SSE stream connected but no initial data received');
      }
      
    } else {
      throw new Error(`Unexpected status: ${response.status}`);
    }
    
  } catch (error) {
    console.log('   ❌ SSE endpoint test failed:', error.message);
    return false;
  }
  
  try {
    console.log('2️⃣  Testing user endpoint for comparison...');
    
    const response = await axios.get(`${BASE_URL}/api/auth/users-by-department`);
    
    if (response.status === 200) {
      console.log(`   ✅ User endpoint working - ${response.data.length} users found`);
      console.log('   ✅ Cache headers:', {
        'cache-control': response.headers['cache-control'],
        'pragma': response.headers['pragma'],
        'expires': response.headers['expires']
      });
    } else {
      throw new Error(`User endpoint failed: ${response.status}`);
    }
    
  } catch (error) {
    console.log('   ❌ User endpoint test failed:', error.message);
    return false;
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('🎉 SSE ENDPOINT VERIFICATION COMPLETE');
  console.log('=' .repeat(50));
  console.log('✅ Server-Sent Events endpoint is functional');
  console.log('✅ User data endpoint is working');
  console.log('✅ Cache prevention headers are configured');
  console.log('\n🚀 READY FOR REAL-TIME LOGIN UPDATES!');
  
  return true;
}

// Run verification
verifySSEEndpoint().catch(console.error);
