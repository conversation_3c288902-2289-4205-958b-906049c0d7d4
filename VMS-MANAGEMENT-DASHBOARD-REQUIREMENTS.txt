================================================================================
                    SIMPLIFIED VMS SYSTEM MANAGEMENT DASHBOARD
                              REQUIREMENTS SPECIFICATION
================================================================================

PROJECT OVERVIEW:
-----------------
A separate non-technical admin UI for VMS system management with PM2 integration,
featuring essential server controls, basic monitoring, and simple management tools
designed specifically for non-technical users.

CORE PRINCIPLES:
----------------
- SIMPLE: Easy to understand for non-technical users
- ESSENTIAL: Only critical features, no overkill
- LAN-OPTIMIZED: No internet-facing features (no email alerts)
- AUTOMATED: Leverage existing VMS automated backup system
- PM2 INTEGRATED: Full process management capabilities

================================================================================
                              FEATURE REQUIREMENTS
================================================================================

1. SIMPLE SERVER CONTROL PANEL
-------------------------------
✅ START SERVER BUTTON
   - One-click server startup with PM2
   - Visual confirmation of startup success/failure
   - Display server PID and startup time

✅ STOP SERVER BUTTON  
   - Graceful server shutdown with PM2
   - Confirmation dialog before stopping
   - Visual confirmation of shutdown

✅ RESTART SERVER BUTTON
   - Zero-downtime restart with PM2
   - Progress indicator during restart
   - Automatic health check after restart

✅ SERVER STATUS INDICATOR
   - Green: Running (with uptime)
   - Red: Stopped
   - Yellow: Starting/Stopping
   - Display current server PID

2. BASIC SYSTEM MONITORING
--------------------------
✅ SIMPLE STATUS CARDS
   - Server Health: Running/Stopped with uptime
   - Active Users: Current user count with department breakdown
   - Database Connection: Connected/Disconnected with connection count
   - Memory Usage: Current server memory consumption

✅ PERFORMANCE ANALYTICS
   - CPU Usage: Real-time percentage with simple gauge
   - Memory Usage: RAM consumption with simple bar chart
   - Response Time: Average API response time
   - Database Load: Active connections and query performance
   - Simple 24-hour trend charts (no complex analytics)

✅ USER ACTIVITY MONITORING
   - Currently logged in users by department
   - Recent login/logout activity (last 10 events)
   - Active WebSocket connections count

3. SIMPLE DATABASE TOOLS
-------------------------
✅ DATABASE STATUS
   - Connection status indicator
   - Total vouchers count by department
   - Database size information

✅ CLEAN OLD DATA BUTTON
   - Remove old session data (older than 7 days)
   - Clean temporary files
   - Confirmation dialog with data preview

✅ DATABASE HEALTH CHECK
   - Test database connectivity
   - Verify critical tables exist
   - Display last successful backup time

4. BASIC AUDIT TRAIL
--------------------
✅ SIMPLE ACTIVITY LOG
   - Recent system events (last 50 events)
   - Server start/stop/restart events
   - User login/logout events
   - Critical system errors
   - Simple timestamp and description format

✅ LOG FILTERING
   - Filter by event type (Server, Users, Errors)
   - Filter by date range (Today, Yesterday, Last 7 days)
   - Simple search by keyword

5. SIMPLE SETTINGS MANAGEMENT
-----------------------------
✅ ADMIN PASSWORD CHANGE
   - Change management dashboard password
   - Secure password requirements
   - Confirmation dialog

✅ SYSTEM CONFIGURATION
   - View current server configuration
   - Display database connection settings (masked passwords)
   - Show PM2 process configuration

✅ MAINTENANCE MODE
   - Enable/disable maintenance mode
   - Display maintenance message to users
   - Graceful user session handling

================================================================================
                              TECHNICAL SPECIFICATIONS
================================================================================

TECHNOLOGY STACK:
-----------------
- Frontend: React.js with TypeScript (separate from main VMS)
- Backend: Node.js/Express API (separate lightweight server)
- Process Management: PM2 integration
- Database: MySQL (same as VMS)
- Authentication: Simple admin login (separate from VMS users)
- Port: 8081 (separate from VMS on 8080)

ARCHITECTURE:
-------------
- Standalone application (separate from main VMS)
- Connects to VMS database for monitoring
- Uses PM2 API for process management
- Real-time updates via WebSocket
- Responsive design for desktop/tablet use

SECURITY:
---------
- Simple admin authentication (username/password)
- LAN-only access (no internet exposure)
- Session-based authentication
- Basic input validation
- No complex security features (LAN-optimized)

DEPLOYMENT:
-----------
- Separate directory: /VMS-ADMIN-DASHBOARD/
- Independent startup script
- Can run on same server as VMS
- Minimal resource footprint
- Auto-start with PM2

================================================================================
                                USER INTERFACE
================================================================================

LAYOUT:
-------
- Clean, simple dashboard layout
- Large, clearly labeled buttons
- Color-coded status indicators
- Minimal text, maximum visual clarity
- Mobile-friendly responsive design

NAVIGATION:
-----------
- Simple sidebar navigation
- 5 main sections:
  1. Server Control
  2. System Monitor  
  3. Database Tools
  4. Activity Log
  5. Settings

VISUAL DESIGN:
--------------
- Clean, professional appearance
- Green/Red/Yellow status colors
- Large fonts for readability
- Minimal animations
- Clear error/success messages

================================================================================
                              EXCLUDED FEATURES
================================================================================

❌ EMAIL NOTIFICATIONS (System is not internet-facing)
❌ BACKUP MANAGEMENT (VMS has automated backup system at admin account)
❌ COMPLEX ANALYTICS (Keep it simple)
❌ USER MANAGEMENT (VMS handles this)
❌ VOUCHER OPERATIONS (This is for system management only)
❌ ADVANCED SECURITY (LAN-optimized, keep simple)
❌ MULTI-SERVER SUPPORT (Single server deployment)
❌ COMPLEX REPORTING (Basic monitoring only)

================================================================================
                                DELIVERABLES
================================================================================

1. Complete standalone VMS Management Dashboard application
2. PM2 integration for VMS server management
3. Real-time monitoring with simple analytics
4. Basic audit trail and activity logging
5. Simple admin authentication system
6. Installation and setup documentation
7. User manual for non-technical administrators

================================================================================
                                  TIMELINE
================================================================================

PHASE 1: Core Server Control (Week 1)
- Basic PM2 integration
- Start/Stop/Restart functionality
- Simple status monitoring

PHASE 2: Monitoring & Analytics (Week 2)  
- Performance monitoring
- Database status
- User activity tracking

PHASE 3: Management Tools (Week 3)
- Audit trail
- Settings management
- Database tools

PHASE 4: Polish & Documentation (Week 4)
- UI/UX improvements
- Documentation
- Testing and deployment

================================================================================
                                   NOTES
================================================================================

- This dashboard is specifically designed for NON-TECHNICAL users
- Focus on SIMPLICITY over advanced features
- Leverage existing VMS infrastructure where possible
- Ensure ZERO impact on main VMS system performance
- Design for LAN-only deployment (no internet features needed)

================================================================================
                                END OF DOCUMENT
================================================================================
