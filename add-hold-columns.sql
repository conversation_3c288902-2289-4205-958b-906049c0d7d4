-- Add hold functionality columns to vouchers table
-- Run this manually in your MySQL client

USE vms_production;

-- Add hold-related columns
ALTER TABLE vouchers 
ADD COLUMN IF NOT EXISTS is_on_hold BOOLEAN DEFAULT FALSE COMMENT 'Whether the voucher is currently on hold';

ALTER TABLE vouchers 
ADD COLUMN IF NOT EXISTS hold_comment TEXT NULL COMMENT 'Reason why the voucher is on hold';

ALTER TABLE vouchers 
ADD COLUMN IF NOT EXISTS hold_by VARCHAR(255) NULL COMMENT 'User who put the voucher on hold';

ALTER TABLE vouchers 
ADD COLUMN IF NOT EXISTS hold_time TIMESTAMP NULL COMMENT 'When the voucher was put on hold';

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_vouchers_hold_status ON vouchers(is_on_hold, department);

-- Update existing vouchers to have default hold status
UPDATE vouchers SET is_on_hold = FALSE WHERE is_on_hold IS NULL;

-- Show the new columns
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'vms_production' 
AND TABLE_NAME = 'vouchers' 
AND COLUMN_NAME IN ('is_on_hold', 'hold_comment', 'hold_by', 'hold_time')
ORDER BY COLUMN_NAME;
