const mysql = require('mysql2/promise');

async function checkCurrentVouchers() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 CURRENT VOUCHER STATUS:');
  console.log('==========================');
  
  const [vouchers] = await connection.execute(`
    SELECT 
      voucher_id, 
      id,
      status, 
      workflow_state,
      department, 
      original_department,
      work_started, 
      is_on_hold, 
      hold_comment,
      sent_to_audit
    FROM vouchers 
    WHERE department = 'AUDIT' 
    ORDER BY created_at DESC 
    LIMIT 10
  `);
  
  vouchers.forEach((v, i) => {
    console.log(`${i+1}. ${v.voucher_id} (ID: ${v.id}):`);
    console.log(`   original_department: ${v.original_department}`);
    console.log(`   workflow_state: ${v.workflow_state}`);
    console.log(`   work_started: ${v.work_started}`);
    console.log(`   is_on_hold: ${v.is_on_hold}`);
    console.log(`   hold_comment: ${v.hold_comment || 'null'}`);
    console.log(`   sent_to_audit: ${v.sent_to_audit}`);
    
    // Check if should be in NEW VOUCHER tab
    const shouldBeInNewVoucher = (
      v.original_department !== 'AUDIT' &&
      v.department === 'AUDIT' &&
      v.status === 'AUDIT: PROCESSING' &&
      v.sent_to_audit === 1 &&
      (v.work_started !== 1 || v.is_on_hold === 1)
    );
    
    console.log(`   → Should be in NEW VOUCHER: ${shouldBeInNewVoucher ? '✅ YES' : '❌ NO'}`);
    console.log('   ────────────────────────────────────');
  });
  
  await connection.end();
}

checkCurrentVouchers().catch(console.error);
