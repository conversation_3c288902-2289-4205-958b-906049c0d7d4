const mysql = require('mysql2/promise');

async function checkUserPasswords() {
  console.log('🔍 Checking user passwords in database...');
  
  try {
    // Create connection to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // Query all users
    const [users] = await connection.execute('SELECT id, name, department, password FROM users WHERE is_active = 1');
    
    console.log('\nUsers in database:');
    users.forEach(user => {
      console.log(`  - ${user.name} (${user.department}): password = "${user.password}"`);
    });
    
    await connection.end();
    
  } catch (error) {
    console.log('❌ Database error:', error.message);
  }
}

// Run the check
checkUserPasswords();
