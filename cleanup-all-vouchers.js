/**
 * 🧹 CLEANUP ALL VOUCHERS
 * Clean all vouchers, batches, and notifications from the system for fresh testing
 */

const mysql = require('mysql2/promise');

async function cleanupAllVouchers() {
  console.log('🧹 CLEANING ALL VOUCHERS FROM SYSTEM');
  console.log('=' .repeat(50));
  
  let connection;
  
  try {
    // Connect to database
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // 1. Check current counts
    console.log('\n📊 Current system state:');
    
    const [voucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
    const [batchCount] = await connection.execute('SELECT COUNT(*) as count FROM voucher_batches');
    const [notificationCount] = await connection.execute('SELECT COUNT(*) as count FROM notifications');
    
    console.log(`🎫 Vouchers: ${voucherCount[0].count}`);
    console.log(`📦 Batches: ${batchCount[0].count}`);
    console.log(`🔔 Notifications: ${notificationCount[0].count}`);
    
    if (voucherCount[0].count === 0 && batchCount[0].count === 0 && notificationCount[0].count === 0) {
      console.log('\n✅ System is already clean - no vouchers to remove');
      return;
    }
    
    // 2. Start cleanup
    console.log('\n🧹 Starting cleanup...');
    
    // Disable foreign key checks temporarily
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    console.log('🔓 Disabled foreign key checks');
    
    // 3. Clean notifications first
    console.log('\n🔔 Cleaning notifications...');
    const [notificationResult] = await connection.execute('DELETE FROM notifications');
    console.log(`✅ Deleted ${notificationResult.affectedRows} notifications`);
    
    // 4. Clean voucher batches
    console.log('\n📦 Cleaning voucher batches...');
    const [batchResult] = await connection.execute('DELETE FROM voucher_batches');
    console.log(`✅ Deleted ${batchResult.affectedRows} batches`);
    
    // 5. Clean vouchers
    console.log('\n🎫 Cleaning vouchers...');
    const [voucherResult] = await connection.execute('DELETE FROM vouchers');
    console.log(`✅ Deleted ${voucherResult.affectedRows} vouchers`);
    
    // 6. Reset auto-increment counters
    console.log('\n🔄 Resetting auto-increment counters...');
    
    try {
      await connection.execute('ALTER TABLE vouchers AUTO_INCREMENT = 1');
      console.log('✅ Reset vouchers auto-increment');
    } catch (error) {
      console.log('ℹ️ Vouchers table might not have auto-increment');
    }
    
    try {
      await connection.execute('ALTER TABLE notifications AUTO_INCREMENT = 1');
      console.log('✅ Reset notifications auto-increment');
    } catch (error) {
      console.log('ℹ️ Notifications table might not have auto-increment');
    }
    
    // 7. Re-enable foreign key checks
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    console.log('🔒 Re-enabled foreign key checks');
    
    // 8. Verify cleanup
    console.log('\n🔍 Verifying cleanup...');
    
    const [finalVoucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
    const [finalBatchCount] = await connection.execute('SELECT COUNT(*) as count FROM voucher_batches');
    const [finalNotificationCount] = await connection.execute('SELECT COUNT(*) as count FROM notifications');
    
    console.log(`🎫 Vouchers remaining: ${finalVoucherCount[0].count}`);
    console.log(`📦 Batches remaining: ${finalBatchCount[0].count}`);
    console.log(`🔔 Notifications remaining: ${finalNotificationCount[0].count}`);
    
    if (finalVoucherCount[0].count === 0 && finalBatchCount[0].count === 0 && finalNotificationCount[0].count === 0) {
      console.log('\n🎉 CLEANUP SUCCESSFUL!');
      console.log('✅ System is now clean and ready for fresh testing');
      console.log('\n💡 You can now:');
      console.log('   1. Login to Finance department');
      console.log('   2. Create a new voucher');
      console.log('   3. Send it to Audit');
      console.log('   4. Test real-time notifications');
    } else {
      console.log('\n⚠️ CLEANUP INCOMPLETE - Some records remain');
    }
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    
    // Try to re-enable foreign key checks if there was an error
    try {
      if (connection) {
        await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
        console.log('🔒 Re-enabled foreign key checks after error');
      }
    } catch (fkError) {
      console.error('❌ Could not re-enable foreign key checks:', fkError.message);
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the cleanup
cleanupAllVouchers().catch(console.error);
