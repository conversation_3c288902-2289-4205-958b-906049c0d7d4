const mysql = require('mysql2/promise');

async function clearUserSessions() {
  console.log('🧹 Clearing all user sessions to fix department misclassification...');
  
  try {
    // Create connection to database
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'vms_user',
      password: 'vms_password',
      database: 'vms_production'
    });
    
    console.log('✅ Connected to database');
    
    // Get current active sessions
    const [sessions] = await connection.execute('SELECT * FROM active_sessions WHERE is_active = TRUE');
    console.log(`\nFound ${sessions.length} active sessions:`);
    sessions.forEach(session => {
      console.log(`  - ${session.user_name} (${session.department}) - Session: ${session.id}`);
    });
    
    // Clear all active sessions
    const [result] = await connection.execute('UPDATE active_sessions SET is_active = FALSE, session_end = NOW() WHERE is_active = TRUE');
    
    console.log(`\n✅ Cleared ${result.affectedRows} active sessions`);
    console.log('🔄 All users will need to login again');
    
    await connection.end();
    
    console.log('\n📋 Next steps:');
    console.log('1. Close all VMS browser tabs/windows');
    console.log('2. Clear browser cookies for localhost:3000');
    console.log('3. Login fresh as the correct user');
    console.log('4. Try sending vouchers to audit again');
    
  } catch (error) {
    console.log('❌ Database error:', error.message);
    
    // Try alternative database credentials
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n🔄 Trying alternative database credentials...');
      
      try {
        const connection = await mysql.createConnection({
          host: 'localhost',
          user: 'root',
          password: '',
          database: 'vms_production'
        });
        
        console.log('✅ Connected with alternative credentials');
        
        // Clear sessions with alternative connection
        const [result] = await connection.execute('UPDATE active_sessions SET is_active = FALSE, session_end = NOW() WHERE is_active = TRUE');
        console.log(`✅ Cleared ${result.affectedRows} active sessions`);
        
        await connection.end();
        
      } catch (altError) {
        console.log('❌ Alternative connection also failed:', altError.message);
        console.log('\n🔧 Manual fix required:');
        console.log('1. Access your MySQL database directly');
        console.log('2. Run: UPDATE active_sessions SET is_active = FALSE, session_end = NOW() WHERE is_active = TRUE;');
        console.log('3. Restart the VMS server');
      }
    }
  }
}

// Run the session cleanup
clearUserSessions();
