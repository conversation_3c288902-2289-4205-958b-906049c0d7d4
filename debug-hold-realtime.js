const mysql = require('mysql2/promise');

async function debugHoldRealtime() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 REAL-TIME HOLD DEBUGGING');
  console.log('==========================================');
  console.log('');
  
  // Function to get voucher details
  async function getVoucherDetails(voucherId) {
    const [vouchers] = await connection.execute(`
      SELECT 
        voucher_id, 
        status, 
        workflow_state,
        department, 
        original_department,
        work_started, 
        is_on_hold, 
        hold_comment,
        sent_to_audit,
        received_by_audit,
        audit_dispatched_by,
        dispatched,
        created_at
      FROM vouchers 
      WHERE voucher_id = ?
    `, [voucherId]);
    
    return vouchers[0] || null;
  }
  
  // Function to check tab placement
  function checkTabPlacement(voucher, department) {
    if (!voucher) return { newVoucher: false, pendingDispatch: false, reason: 'Voucher not found' };
    
    const newVoucherLogic = (
      voucher.original_department === department &&
      voucher.department === 'AUDIT' &&
      voucher.status === 'AUDIT: PROCESSING' &&
      voucher.sent_to_audit === 1 &&
      (voucher.work_started !== 1 || voucher.is_on_hold === 1)
    );
    
    const pendingDispatchLogic = (
      voucher.original_department === department &&
      voucher.department === 'AUDIT' &&
      voucher.status === 'AUDIT: PROCESSING' &&
      voucher.sent_to_audit === 1 &&
      voucher.work_started === 1 &&
      voucher.is_on_hold !== 1 &&
      !voucher.audit_dispatched_by
    );
    
    let reason = '';
    if (!newVoucherLogic && !pendingDispatchLogic) {
      reason = `Failed conditions: `;
      if (voucher.original_department !== department) reason += `orig_dept(${voucher.original_department}!=${department}) `;
      if (voucher.department !== 'AUDIT') reason += `dept(${voucher.department}!=AUDIT) `;
      if (voucher.status !== 'AUDIT: PROCESSING') reason += `status(${voucher.status}!=AUDIT: PROCESSING) `;
      if (voucher.sent_to_audit !== 1) reason += `sent_to_audit(${voucher.sent_to_audit}!=1) `;
      if (voucher.work_started === 1 && voucher.is_on_hold !== 1) reason += `work_started(${voucher.work_started}) && not_on_hold(${voucher.is_on_hold}) `;
    }
    
    return {
      newVoucher: newVoucherLogic,
      pendingDispatch: pendingDispatchLogic,
      reason: reason || 'Conditions met'
    };
  }
  
  // Get all current AUDIT vouchers
  const [allVouchers] = await connection.execute(`
    SELECT voucher_id, original_department, is_on_hold 
    FROM vouchers 
    WHERE department = 'AUDIT' 
    ORDER BY created_at DESC 
    LIMIT 10
  `);
  
  console.log('📋 CURRENT AUDIT VOUCHERS:');
  allVouchers.forEach((v, i) => {
    console.log(`${i+1}. ${v.voucher_id} (from ${v.original_department}) - Hold: ${v.is_on_hold ? 'YES' : 'NO'}`);
  });
  console.log('');
  
  if (allVouchers.length === 0) {
    console.log('❌ No vouchers found in AUDIT department');
    await connection.end();
    return;
  }
  
  // Focus on the first voucher for detailed analysis
  const testVoucher = allVouchers[0];
  console.log(`🎯 ANALYZING VOUCHER: ${testVoucher.voucher_id}`);
  console.log('==========================================');
  
  const voucher = await getVoucherDetails(testVoucher.voucher_id);
  if (!voucher) {
    console.log('❌ Voucher not found');
    await connection.end();
    return;
  }
  
  console.log('📊 VOUCHER DETAILS:');
  console.log(`   voucher_id: ${voucher.voucher_id}`);
  console.log(`   status: ${voucher.status}`);
  console.log(`   workflow_state: ${voucher.workflow_state}`);
  console.log(`   department: ${voucher.department}`);
  console.log(`   original_department: ${voucher.original_department}`);
  console.log(`   work_started: ${voucher.work_started}`);
  console.log(`   is_on_hold: ${voucher.is_on_hold}`);
  console.log(`   hold_comment: ${voucher.hold_comment || 'null'}`);
  console.log(`   sent_to_audit: ${voucher.sent_to_audit}`);
  console.log(`   received_by_audit: ${voucher.received_by_audit}`);
  console.log(`   audit_dispatched_by: ${voucher.audit_dispatched_by || 'null'}`);
  console.log(`   dispatched: ${voucher.dispatched}`);
  console.log('');
  
  // Check tab placement for all departments
  const departments = ['FINANCE', 'MISSIONS', 'PENSIONS', 'MINISTRIES', 'PENTMEDIA', 'PENTSOS'];
  
  console.log('🏷️  TAB PLACEMENT ANALYSIS:');
  departments.forEach(dept => {
    const placement = checkTabPlacement(voucher, dept);
    console.log(`   ${dept} Department:`);
    console.log(`     → NEW VOUCHER: ${placement.newVoucher ? '✅ YES' : '❌ NO'}`);
    console.log(`     → PENDING DISPATCH: ${placement.pendingDispatch ? '✅ YES' : '❌ NO'}`);
    if (!placement.newVoucher && !placement.pendingDispatch) {
      console.log(`     → REASON: ${placement.reason}`);
    }
    console.log('');
  });
  
  await connection.end();
}

debugHoldRealtime().catch(console.error);
