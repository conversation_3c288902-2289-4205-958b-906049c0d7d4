import express from 'express';
import http from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
// Load environment variables
dotenv.config();
// Simple logger for development
const logger = {
    info: (message) => console.log(`[INFO] ${new Date().toISOString()} - ${message}`),
    error: (message, error) => console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error || ''),
    warn: (message) => console.warn(`[WARN] ${new Date().toISOString()} - ${message}`),
    debug: (message) => console.log(`[DEBUG] ${new Date().toISOString()} - ${message}`)
};
// Create Express app
const app = express();
const PORT = process.env.PORT || 8080;
// Middleware
app.use(cors({
    origin: process.env.CLIENT_URL || 'http://localhost:3000',
    credentials: true
}));
app.use(helmet());
app.use(morgan('combined', {
    stream: {
        write: (message) => logger.info(message.trim())
    }
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
// API Routes
app.get('/api', (req, res) => {
    res.json({
        name: 'VMS 10.0 Development Server',
        version: '1.0.0',
        status: 'active',
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString()
    });
});
app.get('/api/status', (req, res) => {
    res.json({
        server: 'running',
        database: 'not connected (development mode)',
        websocket: 'active',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
    });
});
// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development'
    });
});
// Development info endpoint
app.get('/dev', (req, res) => {
    res.json({
        message: 'VMS 10.0 Development Environment',
        features: [
            'Hot reload enabled',
            'Debug logging active',
            'TypeScript compilation',
            'WebSocket support',
            'CORS configured for localhost'
        ],
        endpoints: {
            api: '/api',
            health: '/health',
            status: '/api/status',
            dev: '/dev'
        },
        environment: {
            NODE_ENV: process.env.NODE_ENV || 'development',
            PORT: PORT,
            CLIENT_URL: process.env.CLIENT_URL || 'http://localhost:3000'
        }
    });
});
// Error handling middleware
app.use((err, req, res, next) => {
    logger.error(`Error: ${err.message}`, err);
    res.status(err.status || 500).json({
        error: {
            message: err.message || 'Internal Server Error',
            ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
        },
    });
});
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`,
        availableRoutes: ['/api', '/health', '/api/status', '/dev']
    });
});
// Create HTTP server
const server = http.createServer(app);
// Create WebSocket server
const io = new SocketIOServer(server, {
    cors: {
        origin: process.env.CLIENT_URL || 'http://localhost:3000',
        methods: ['GET', 'POST'],
        credentials: true
    },
    pingTimeout: 60000,
    pingInterval: 25000
});
// WebSocket connection handling
io.on('connection', (socket) => {
    logger.info(`WebSocket client connected: ${socket.id}`);
    // Send welcome message
    socket.emit('welcome', {
        message: 'Connected to VMS 10.0 Development Server',
        timestamp: new Date().toISOString(),
        socketId: socket.id
    });
    // Handle ping
    socket.on('ping', () => {
        socket.emit('pong', { timestamp: new Date().toISOString() });
    });
    // Handle disconnect
    socket.on('disconnect', (reason) => {
        logger.info(`WebSocket client disconnected: ${socket.id}, reason: ${reason}`);
    });
});
// Graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    server.close(() => {
        logger.info('Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    server.close(() => {
        logger.info('Server closed');
        process.exit(0);
    });
});
// Start server
async function startServer() {
    try {
        logger.info('🚀 Starting VMS 10.0 Development Server...');
        server.listen(PORT, () => {
            logger.info('✅ Development server started successfully!');
            logger.info(`✅ Server running on: http://localhost:${PORT}`);
            logger.info(`✅ Environment: ${process.env.NODE_ENV || 'development'}`);
            logger.info(`✅ Health check: http://localhost:${PORT}/health`);
            logger.info(`✅ API endpoint: http://localhost:${PORT}/api`);
            logger.info(`✅ Dev info: http://localhost:${PORT}/dev`);
            logger.info(`✅ WebSocket server active`);
            logger.info('📝 Ready for development!');
        });
    }
    catch (error) {
        logger.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}
// Start the server
startServer();
//# sourceMappingURL=index.js.map