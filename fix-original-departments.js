const mysql = require('mysql2/promise');

async function fixOriginalDepartments() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔧 FIXING ORIGINAL DEPARTMENTS...');
  
  // Fix vouchers based on their voucher_id prefix
  const fixes = [
    { prefix: 'FIN', department: 'FINANCE' },
    { prefix: 'MIS', department: 'MISSIONS' },
    { prefix: 'PEN', department: 'PENSIONS' },
    { prefix: 'MIN', department: 'MINISTRIES' },
    { prefix: 'PMD', department: 'PENTMEDIA' },
    { prefix: 'PSO', department: 'PENTSOS' }
  ];
  
  for (const fix of fixes) {
    const [result] = await connection.execute(`
      UPDATE vouchers 
      SET original_department = ? 
      WHERE voucher_id LIKE ? AND original_department = 'AUDIT'
    `, [fix.department, fix.prefix + '%']);
    
    console.log(`✅ Fixed ${result.affectedRows} vouchers for ${fix.department}`);
  }
  
  console.log('');
  console.log('📋 UPDATED VOUCHER DEPARTMENTS:');
  
  const [vouchers] = await connection.execute(`
    SELECT voucher_id, original_department, department, is_on_hold 
    FROM vouchers 
    WHERE department = 'AUDIT' 
    ORDER BY created_at DESC 
    LIMIT 5
  `);
  
  vouchers.forEach((v, i) => {
    console.log(`${i+1}. ${v.voucher_id}: orig=${v.original_department}, current=${v.department}, hold=${v.is_on_hold}`);
  });
  
  await connection.end();
}

fixOriginalDepartments().catch(console.error);
