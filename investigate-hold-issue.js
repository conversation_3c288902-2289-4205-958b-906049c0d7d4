const mysql = require('mysql2/promise');

async function investigateVoucherDisappearance() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 INVESTIGATING VOUCHER DISAPPEARANCE...');
  console.log('');
  
  // Get all vouchers in AUDIT department
  const [vouchers] = await connection.execute(`
    SELECT 
      voucher_id, 
      status, 
      workflow_state,
      department, 
      original_department,
      work_started, 
      is_on_hold, 
      hold_comment,
      sent_to_audit,
      received_by_audit,
      audit_dispatched_by,
      dispatched
    FROM vouchers 
    WHERE department = 'AUDIT' 
    ORDER BY created_at DESC 
    LIMIT 5
  `);
  
  console.log('📋 ALL AUDIT VOUCHERS:');
  vouchers.forEach((v, i) => {
    console.log(`${i+1}. ${v.voucher_id}:`);
    console.log(`   status: ${v.status}`);
    console.log(`   workflow_state: ${v.workflow_state}`);
    console.log(`   work_started: ${v.work_started}`);
    console.log(`   is_on_hold: ${v.is_on_hold}`);
    console.log(`   sent_to_audit: ${v.sent_to_audit}`);
    console.log(`   received_by_audit: ${v.received_by_audit}`);
    console.log(`   audit_dispatched_by: ${v.audit_dispatched_by}`);
    console.log(`   dispatched: ${v.dispatched}`);
    console.log('');
    
    // Apply NEW VOUCHER tab logic (using actual original_department)
    const shouldBeInNewVoucher = (
      v.original_department === v.original_department && // Any department
      v.department === 'AUDIT' &&
      v.status === 'AUDIT: PROCESSING' &&
      v.sent_to_audit === 1 &&
      (v.work_started !== 1 || v.is_on_hold === 1)
    );

    // Apply PENDING DISPATCH tab logic (using actual original_department)
    const shouldBeInPendingDispatch = (
      v.original_department === v.original_department && // Any department
      v.department === 'AUDIT' &&
      v.status === 'AUDIT: PROCESSING' &&
      v.sent_to_audit === 1 &&
      v.work_started === 1 &&
      v.is_on_hold !== 1 &&
      !v.audit_dispatched_by
    );
    
    console.log(`   → NEW VOUCHER tab: ${shouldBeInNewVoucher ? '✅ YES' : '❌ NO'}`);
    console.log(`   → PENDING DISPATCH tab: ${shouldBeInPendingDispatch ? '✅ YES' : '❌ NO'}`);
    console.log('   ────────────────────────────────────');
  });
  
  await connection.end();
}

investigateVoucherDisappearance().catch(console.error);
