{"level":"info","message":"🚀 Starting VMS Server - Production Grade Architecture...","service":"vms-server","timestamp":"2025-07-30 08:48:59"}
{"level":"info","message":"🎯 Using forced port: 8080","service":"vms-server","timestamp":"2025-07-30 08:48:59"}
{"level":"info","message":"🔧 Setting up Socket.IO middleware and handlers...","service":"vms-server","timestamp":"2025-07-30 08:48:59"}
{"level":"info","message":"✅ Single WebSocket system initialized - No duplicates","service":"vms-server","timestamp":"2025-07-30 08:48:59"}
{"level":"info","message":"🎯 Duplicate broadcast prevention: Only socketHandlers active","service":"vms-server","timestamp":"2025-07-30 08:48:59"}
{"level":"info","message":"🔄 Initializing database connection with enterprise retry logic...","service":"vms-server","timestamp":"2025-07-30 08:48:59"}
{"level":"info","message":"Connected to MySQL database","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Database connection pool initialized","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Database 'vms_production' created or already exists","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Starting workflow state migration...","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"No vouchers need workflow state migration","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Database tables created or already exist","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Users already exist (7 users found)","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"✅ Database connected successfully - Production ready","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🔍 Testing database connectivity...","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🔄 Initializing workflow service...","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Initializing workflow service...","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"✅ Workflow service initialized successfully","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🔄 Initializing file storage...","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Created base uploads directory","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Created audit attachments directory for 2025","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"File storage initialized successfully","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"✅ File storage initialized successfully","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"📊 Database health monitoring DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🎉 VMS Server - Production Grade Startup Complete!","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🚀 Server Status: RUNNING (Port 8080)","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🔌 WebSocket Status: ACTIVE (Enterprise Configuration)","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"💾 Database Status: CONNECTED","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🌐 Environment: DEVELOPMENT","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"📊 Process ID: 22584","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"New database connection established: 22","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Database connection test successful","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Database 'vms_production' verified","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Essential database tables verified","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Database setup verification complete","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Health checks DISABLED to prevent infinite loops","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"Database Manager initialized successfully","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"acquiring":0,"connections":"0/50 (0%)","free":0,"level":"info","message":"📊 DB Pool Health:","queued":"0/100 (0%)","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"📡 Service Endpoints:","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"   🌐 WebSocket: http://localhost:8080/socket.io/","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"   📊 Health Check: http://localhost:8080/health","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"   📈 Detailed Health: http://localhost:8080/health/detailed","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"   🔧 API Base: http://localhost:8080/api","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"✅ Session cleanup scheduled (every 10 minutes)","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🛑 Automated backup scheduler stopped","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🕒 Starting automated backup scheduler (every 24 hours)","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"✅ Automated backup scheduler started (daily backups)","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🌍 Enterprise Network Access:","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"   🖥️  VMS System: http://************:8080","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"   🔌 WebSocket: http://************:8080/socket.io/","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"   🖥️  VMS System: http://10.25.41.232:8080","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"   🔌 WebSocket: http://10.25.41.232:8080/socket.io/","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"📊 System Resources:","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"   💾 Memory: 63MB RSS","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"   🔄 Uptime: 1s","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🎯 VMS Service Discovery started - Broadcasting on all network interfaces","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"📊 Service Info: {\n  \"serviceName\": \"VMS-Server\",\n  \"host\": \"************\",\n  \"port\": 8080,\n  \"version\": \"3.0.0\",\n  \"timestamp\": 1753865340271,\n  \"capabilities\": [\n    \"voucher-management\",\n    \"real-time-updates\",\n    \"multi-user\"\n  ]\n}","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"📡 Service Discovery: ACTIVE - Clients can auto-discover this server","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🎯 VMS Server - Ready for Production Operations!","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"============================================================","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"📡 Service discovery broadcast started on port 45454","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🔍 Service discovery listener started on port 45455","service":"vms-server","timestamp":"2025-07-30 08:49:00"}
{"level":"info","message":"🔄 Starting session deduplication process...","service":"vms-server","timestamp":"2025-07-30 08:49:30"}
{"level":"info","message":"✅ No duplicate sessions found","service":"vms-server","timestamp":"2025-07-30 08:49:30"}
{"level":"info","message":"✅ All users have unique sessions (0 unique active users)","service":"vms-server","timestamp":"2025-07-30 08:49:30"}
{"level":"info","message":"✅ Initial session cleanup completed","service":"vms-server","timestamp":"2025-07-30 08:49:30"}
