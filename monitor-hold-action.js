const mysql = require('mysql2/promise');

async function monitorHoldAction() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 REAL-TIME HOLD ACTION MONITOR');
  console.log('=====================================');
  console.log('');
  
  // Function to get all voucher states
  async function getVoucherSnapshot() {
    const [vouchers] = await connection.execute(`
      SELECT 
        voucher_id, 
        status, 
        workflow_state,
        department, 
        original_department,
        work_started, 
        is_on_hold, 
        hold_comment,
        sent_to_audit,
        received_by_audit,
        audit_dispatched_by,
        dispatched,
        created_at
      FROM vouchers
      WHERE department = 'AUDIT'
      ORDER BY created_at DESC
      LIMIT 10
    `);
    
    return vouchers;
  }
  
  console.log('📸 INITIAL SNAPSHOT:');
  let previousSnapshot = await getVoucherSnapshot();
  previousSnapshot.forEach((v, i) => {
    console.log(`${i+1}. ${v.voucher_id} (${v.original_department}): hold=${v.is_on_hold}, work_started=${v.work_started}, workflow=${v.workflow_state}`);
  });
  console.log('');
  console.log('🎯 NOW PERFORM THE HOLD ACTION ON A VOUCHER...');
  console.log('   (Monitoring for changes every 2 seconds)');
  console.log('');
  
  // Monitor for changes
  let changeCount = 0;
  const maxChecks = 30; // Monitor for 1 minute
  
  for (let i = 0; i < maxChecks; i++) {
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    
    const currentSnapshot = await getVoucherSnapshot();
    
    // Compare snapshots
    let hasChanges = false;
    
    // Check for changes in existing vouchers
    for (const currentVoucher of currentSnapshot) {
      const previousVoucher = previousSnapshot.find(v => v.voucher_id === currentVoucher.voucher_id);
      
      if (previousVoucher) {
        // Check if any field changed
        const fieldsToCheck = ['status', 'workflow_state', 'work_started', 'is_on_hold', 'hold_comment', 'audit_dispatched_by', 'dispatched'];
        
        for (const field of fieldsToCheck) {
          if (previousVoucher[field] !== currentVoucher[field]) {
            hasChanges = true;
            changeCount++;
            console.log(`🔄 CHANGE DETECTED #${changeCount}:`);
            console.log(`   Voucher: ${currentVoucher.voucher_id}`);
            console.log(`   Field: ${field}`);
            console.log(`   Before: ${previousVoucher[field]}`);
            console.log(`   After: ${currentVoucher[field]}`);
            console.log(`   Time: ${new Date().toLocaleTimeString()}`);
            
            // Show tab placement after change
            const shouldBeInNewVoucher = (
              currentVoucher.original_department !== 'AUDIT' &&
              currentVoucher.department === 'AUDIT' &&
              currentVoucher.status === 'AUDIT: PROCESSING' &&
              currentVoucher.sent_to_audit === 1 &&
              (currentVoucher.work_started !== 1 || currentVoucher.is_on_hold === 1)
            );
            
            const shouldBeInPendingDispatch = (
              currentVoucher.original_department !== 'AUDIT' &&
              currentVoucher.department === 'AUDIT' &&
              currentVoucher.status === 'AUDIT: PROCESSING' &&
              currentVoucher.sent_to_audit === 1 &&
              currentVoucher.work_started === 1 &&
              currentVoucher.is_on_hold !== 1 &&
              !currentVoucher.audit_dispatched_by
            );
            
            console.log(`   → Should be in NEW VOUCHER: ${shouldBeInNewVoucher ? '✅ YES' : '❌ NO'}`);
            console.log(`   → Should be in PENDING DISPATCH: ${shouldBeInPendingDispatch ? '✅ YES' : '❌ NO'}`);
            console.log('   ────────────────────────────────────');
            break;
          }
        }
      }
    }
    
    // Check for new vouchers
    for (const currentVoucher of currentSnapshot) {
      const existedBefore = previousSnapshot.find(v => v.voucher_id === currentVoucher.voucher_id);
      if (!existedBefore) {
        hasChanges = true;
        changeCount++;
        console.log(`🆕 NEW VOUCHER DETECTED #${changeCount}:`);
        console.log(`   Voucher: ${currentVoucher.voucher_id}`);
        console.log(`   Time: ${new Date().toLocaleTimeString()}`);
        console.log('   ────────────────────────────────────');
      }
    }
    
    // Check for deleted vouchers
    for (const previousVoucher of previousSnapshot) {
      const stillExists = currentSnapshot.find(v => v.voucher_id === previousVoucher.voucher_id);
      if (!stillExists) {
        hasChanges = true;
        changeCount++;
        console.log(`🗑️  VOUCHER DELETED #${changeCount}:`);
        console.log(`   Voucher: ${previousVoucher.voucher_id}`);
        console.log(`   Time: ${new Date().toLocaleTimeString()}`);
        console.log('   ────────────────────────────────────');
      }
    }
    
    if (hasChanges) {
      previousSnapshot = currentSnapshot;
    }
    
    // Show progress
    if (i % 5 === 0 && i > 0) {
      console.log(`⏱️  Still monitoring... (${i * 2}s elapsed, ${changeCount} changes detected)`);
    }
  }
  
  console.log('');
  console.log(`🏁 MONITORING COMPLETE: ${changeCount} changes detected in ${maxChecks * 2} seconds`);
  
  await connection.end();
}

monitorHoldAction().catch(console.error);
