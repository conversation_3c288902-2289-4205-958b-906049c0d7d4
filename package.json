{"name": "vms-production", "version": "5.0.0", "description": "VMS Production System - Clean Architecture", "main": "server/dist/index.js", "scripts": {"start": "Tools\\START-VMS-SINGLE.bat", "dev": "Tools\\START-VMS-SINGLE.bat", "stop": "Tools\\STOP.bat", "restart": "Tools\\RESTART.bat", "status": "Tools\\STATUS.bat", "health": "curl http://localhost:8080/health", "health:detailed": "curl http://localhost:8080/health/detailed", "test": "echo \"VMS Production System - Clean Architecture\""}, "keywords": ["vms", "voucher-management", "production-ready", "enterprise", "lan-deployment", "clean-architecture"], "author": "VMS Development Team", "license": "MIT", "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "local"}, "dependencies": {"axios": "^1.10.0", "bcrypt": "^5.1.1", "dotenv": "^16.5.0", "mysql2": "^3.14.2", "node-fetch": "^3.3.2", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"pm2": "^5.3.0"}}