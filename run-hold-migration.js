/**
 * Run Hold Feature Migration
 * Adds the necessary database columns for the ON-HOLD feature
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });

    console.log('🔗 Connected to database');

    // Read migration file
    const migrationPath = path.join(__dirname, 'Server', 'migrations', 'add_voucher_hold_columns.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('📄 Raw migration SQL:');
    console.log(migrationSQL);
    console.log('---');

    // Split SQL statements (handle multiple statements)
    const statements = migrationSQL
      .split(';')
      .map(stmt => {
        // Remove comment lines but keep SQL statements
        return stmt
          .split('\n')
          .filter(line => !line.trim().startsWith('--'))
          .join('\n')
          .trim();
      })
      .filter(stmt => stmt.length > 0 && (stmt.toUpperCase().includes('ALTER') || stmt.toUpperCase().includes('CREATE') || stmt.toUpperCase().includes('UPDATE')));

    console.log('📝 Parsed statements:');
    statements.forEach((stmt, i) => {
      console.log(`${i + 1}: ${stmt.substring(0, 100)}...`);
    });
    console.log(`📝 Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        await connection.execute(statement);
        console.log(`✅ Statement ${i + 1} executed successfully`);
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log(`⚠️  Column already exists, skipping statement ${i + 1}`);
        } else {
          throw error;
        }
      }
    }

    // Verify the columns were added
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'vms_production' 
      AND TABLE_NAME = 'vouchers' 
      AND COLUMN_NAME IN ('is_on_hold', 'hold_comment', 'hold_by', 'hold_time')
      ORDER BY COLUMN_NAME
    `);

    console.log('\n📊 HOLD COLUMNS VERIFICATION:');
    columns.forEach(col => {
      console.log(`  ✅ ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.COLUMN_COMMENT || 'No comment'}`);
    });

    console.log('\n🎉 HOLD FEATURE MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('✅ Database is ready for ON-HOLD functionality');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the migration
runMigration();
