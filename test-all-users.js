const axios = require('axios');

async function testAllUsers() {
  console.log('🔍 TESTING ALL USERS WITH PASSWORD CHANGES');
  console.log('==========================================');

  const users = [
    { name: "JAM<PERSON>", dept: "MINISTRIES", pwd: "123456" },
    { name: "MR. FELIX AYISI", dept: "FINANCE", pwd: "123123" },
    { name: "JERRY JOHN", dept: "PENSIONS", pwd: "123123" },
    { name: "SAMMY MAWUKO", dept: "MINISTRIES", pwd: "123123" }
  ];

  for (const user of users) {
    try {
      const response = await axios.post("http://localhost:8080/api/auth/login", {
        username: user.name,
        department: user.dept,
        password: user.pwd
      });
      
      console.log(`✅ ${user.name} (${user.dept}): LOGIN SUCCESS`);
      
    } catch (error) {
      console.log(`❌ ${user.name} (${user.dept}): LOGIN FAILED`);
      if (error.response?.data?.error) {
        console.log(`   Error: ${error.response.data.error}`);
      }
    }
  }
  
  console.log('\n🏁 All login tests completed');
}

testAllUsers().catch(console.error);
