// Use built-in fetch or http module
const http = require('http');

async function testApiResponse() {
  try {
    console.log('🧪 TESTING API RESPONSE FOR VOUCHERS...');
    console.log('=====================================');
    
    // Test the vouchers API endpoint
    const response = await fetch('http://localhost:8080/api/vouchers', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Mock session for testing
        'Cookie': 'session=test-session'
      }
    });
    
    if (!response.ok) {
      console.log(`❌ API Error: ${response.status} ${response.statusText}`);
      return;
    }
    
    const vouchers = await response.json();
    console.log(`📋 API returned ${vouchers.length} vouchers`);
    console.log('');
    
    // Check AUDIT vouchers specifically
    const auditVouchers = vouchers.filter(v => v.department === 'AUDIT');
    console.log(`🎯 Found ${auditVouchers.length} AUDIT vouchers:`);
    console.log('');
    
    auditVouchers.forEach((v, i) => {
      console.log(`${i+1}. ${v.voucherId || v.voucher_id}:`);
      console.log(`   originalDepartment: ${v.originalDepartment || v.original_department}`);
      console.log(`   department: ${v.department}`);
      console.log(`   workStarted: ${v.workStarted}`);
      console.log(`   work_started: ${v.work_started}`);
      console.log(`   isOnHold: ${v.isOnHold}`);
      console.log(`   is_on_hold: ${v.is_on_hold}`);
      console.log(`   holdComment: ${v.holdComment || 'undefined'}`);
      console.log(`   hold_comment: ${v.hold_comment || 'undefined'}`);
      console.log(`   status: ${v.status}`);
      console.log(`   workflow_state: ${v.workflow_state}`);
      console.log(`   sentToAudit: ${v.sentToAudit}`);
      console.log(`   sent_to_audit: ${v.sent_to_audit}`);
      console.log('   ────────────────────────────────────');
    });
    
    // Check if any vouchers have hold status
    const heldVouchers = auditVouchers.filter(v => v.isOnHold === true || v.is_on_hold === true || v.is_on_hold === 1);
    console.log(`🔒 Vouchers with hold status: ${heldVouchers.length}`);
    
    if (heldVouchers.length > 0) {
      console.log('✅ HOLD FIELDS ARE BEING RETURNED BY API');
    } else {
      console.log('❌ HOLD FIELDS ARE NOT BEING RETURNED BY API');
    }
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

testApiResponse();
