const axios = require('axios');

async function testAuditUsersDropdown() {
  console.log('🔧 Testing audit users dropdown population...');
  
  try {
    // Step 1: Login as an AUDIT user to get session
    console.log('\n1️⃣ Logging in as AUDIT user...');
    
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'AUDIT',
      username: 'EMMANUEL AMOAKOH',
      password: 'enter123'
    });
    
    if (!loginResponse.data.success) {
      console.log('❌ AUDIT login failed:', loginResponse.data.error);
      return;
    }
    
    const sessionCookie = loginResponse.headers['set-cookie']?.[0];
    console.log('✅ AUDIT user login successful');
    
    // Step 2: Fetch all users from the API (what the dropdown uses)
    console.log('\n2️⃣ Fetching all users from API...');
    
    const usersResponse = await axios.get('http://localhost:8080/api/users', {
      headers: { 'Cookie': sessionCookie }
    });
    
    const allUsers = usersResponse.data;
    console.log(`✅ Fetched ${allUsers.length} total users from API`);
    
    // Step 3: Filter for AUDIT users (what the dropdown filtering does)
    const auditUsers = allUsers.filter(user => user.department === 'AUDIT' && user.isActive);
    console.log(`✅ Found ${auditUsers.length} active AUDIT users`);
    
    // Step 4: Display all AUDIT users
    console.log('\n📋 AUDIT USERS AVAILABLE FOR DROPDOWNS:');
    console.log('='.repeat(60));
    
    if (auditUsers.length === 0) {
      console.log('❌ NO AUDIT USERS FOUND!');
      console.log('This explains why dropdowns are empty.');
    } else {
      auditUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name}`);
        console.log(`   - ID: ${user.id}`);
        console.log(`   - Department: ${user.department}`);
        console.log(`   - Active: ${user.isActive}`);
        console.log(`   - Role: ${user.role}`);
        console.log('');
      });
    }
    
    // Step 5: Check for inactive AUDIT users
    const inactiveAuditUsers = allUsers.filter(user => user.department === 'AUDIT' && !user.isActive);
    if (inactiveAuditUsers.length > 0) {
      console.log('\n⚠️ INACTIVE AUDIT USERS (NOT SHOWN IN DROPDOWNS):');
      console.log('='.repeat(60));
      inactiveAuditUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name} (INACTIVE)`);
      });
    }
    
    // Step 6: Check what the login endpoint returns for comparison
    console.log('\n3️⃣ Checking users from login endpoint...');
    
    const loginUsersResponse = await axios.get('http://localhost:8080/api/auth/users-by-department');
    const loginUsers = loginUsersResponse.data;
    const loginAuditUsers = loginUsers.filter(user => user.department === 'AUDIT');
    
    console.log(`✅ Login endpoint shows ${loginAuditUsers.length} AUDIT users`);
    
    // Step 7: Compare the two sources
    console.log('\n📊 COMPARISON ANALYSIS:');
    console.log('='.repeat(60));
    console.log(`API /users endpoint: ${auditUsers.length} active AUDIT users`);
    console.log(`Login endpoint: ${loginAuditUsers.length} AUDIT users`);
    
    if (auditUsers.length !== loginAuditUsers.length) {
      console.log('⚠️ MISMATCH DETECTED! Different endpoints return different user counts.');
      
      // Find missing users
      const apiUserNames = auditUsers.map(u => u.name);
      const loginUserNames = loginAuditUsers.map(u => u.name);
      
      const missingFromApi = loginUserNames.filter(name => !apiUserNames.includes(name));
      const missingFromLogin = apiUserNames.filter(name => !loginUserNames.includes(name));
      
      if (missingFromApi.length > 0) {
        console.log('\n❌ Users missing from API endpoint (but present in login):');
        missingFromApi.forEach(name => console.log(`   - ${name}`));
      }
      
      if (missingFromLogin.length > 0) {
        console.log('\n❌ Users missing from login endpoint (but present in API):');
        missingFromLogin.forEach(name => console.log(`   - ${name}`));
      }
    } else {
      console.log('✅ Both endpoints return the same number of AUDIT users');
    }
    
    // Step 8: Diagnosis and recommendations
    console.log('\n🔍 DIAGNOSIS:');
    console.log('='.repeat(60));
    
    if (auditUsers.length === 0) {
      console.log('🚨 CRITICAL ISSUE: No active AUDIT users found!');
      console.log('📋 POSSIBLE CAUSES:');
      console.log('   1. All AUDIT users are marked as inactive (is_active = 0)');
      console.log('   2. AUDIT users have incorrect department names in database');
      console.log('   3. Database connection or query issues');
      console.log('   4. Authentication middleware blocking the request');
    } else if (auditUsers.length < 3) {
      console.log('⚠️ LIMITED AUDIT USERS: Only a few AUDIT users available');
      console.log('📋 This might explain why "not all audit users" appear in dropdowns');
      console.log('📋 Check if more AUDIT users should be active');
    } else {
      console.log('✅ AUDIT users are available - issue might be elsewhere');
      console.log('📋 Check if the dropdown components are properly using the fetched users');
    }
    
    return {
      totalUsers: allUsers.length,
      auditUsers: auditUsers.length,
      inactiveAuditUsers: inactiveAuditUsers.length,
      auditUsersList: auditUsers.map(u => u.name)
    };
    
  } catch (error) {
    console.log('❌ Test error:', error.message);
    if (error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
  }
}

// Run the test
testAuditUsersDropdown();
