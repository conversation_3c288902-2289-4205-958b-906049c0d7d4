const axios = require('axios');
const io = require('socket.io-client');

async function testDepartmentExclusiveAccess(department) {
  console.log(`\n🔧 Testing exclusive access for ${department} department...`);
  
  try {
    // Get users for this department
    const usersResponse = await axios.get('http://localhost:8080/api/auth/users-by-department');
    const departmentUsers = usersResponse.data.filter(u => u.department === department);
    
    if (departmentUsers.length < 2) {
      console.log(`❌ ${department}: Need at least 2 users to test exclusive access. Found: ${departmentUsers.length}`);
      return { department, status: 'INSUFFICIENT_USERS', users: departmentUsers.length };
    }
    
    console.log(`✅ ${department}: Found ${departmentUsers.length} users - ${departmentUsers[0].name} and ${departmentUsers[1].name}`);
    
    // Step 1: Login first user
    const loginResponse1 = await axios.post('http://localhost:8080/api/auth/login', {
      department: department,
      username: departmentUsers[0].name,
      password: 'enter123'
    });
    
    if (!loginResponse1.data.success) {
      console.log(`❌ ${department}: First user login failed`);
      return { department, status: 'LOGIN_FAILED', error: 'First user login failed' };
    }
    
    const sessionCookie1 = loginResponse1.headers['set-cookie']?.[0];
    console.log(`✅ ${department}: First user (${departmentUsers[0].name}) logged in`);
    
    // Step 2: Connect first user WebSocket
    const socket1 = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true,
      extraHeaders: { 'Cookie': sessionCookie1 }
    });
    
    let user1Results = { connected: false, accessGranted: false, lockAcquired: false };
    
    await new Promise((resolve) => {
      socket1.on('connect', () => {
        user1Results.connected = true;
        console.log(`✅ ${department}: First user WebSocket connected`);
        
        // Check access
        socket1.emit('check_department_access', { targetDepartment: department }, (response) => {
          user1Results.accessGranted = response.canAccess;
          console.log(`${department}: First user access check: ${response.canAccess ? '✅ GRANTED' : '❌ DENIED'}`);
          
          if (response.canAccess) {
            // Try to acquire lock
            socket1.emit('department_lock_request', { targetDepartment: department }, (lockResponse) => {
              user1Results.lockAcquired = lockResponse.success;
              console.log(`${department}: First user lock: ${lockResponse.success ? '✅ ACQUIRED' : '❌ FAILED'}`);
              if (!lockResponse.success) console.log(`${department}: Lock error: ${lockResponse.message}`);
              resolve();
            });
          } else {
            resolve();
          }
        });
      });
      
      socket1.on('connect_error', (error) => {
        console.log(`❌ ${department}: First user WebSocket connection failed: ${error.message}`);
        resolve();
      });
    });
    
    // Wait for first user to establish lock
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Step 3: Login second user
    const loginResponse2 = await axios.post('http://localhost:8080/api/auth/login', {
      department: department,
      username: departmentUsers[1].name,
      password: 'enter123'
    });
    
    if (!loginResponse2.data.success) {
      console.log(`❌ ${department}: Second user login failed`);
      socket1.disconnect();
      return { department, status: 'LOGIN_FAILED', error: 'Second user login failed' };
    }
    
    const sessionCookie2 = loginResponse2.headers['set-cookie']?.[0];
    console.log(`✅ ${department}: Second user (${departmentUsers[1].name}) logged in`);
    
    // Step 4: Connect second user WebSocket
    const socket2 = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true,
      extraHeaders: { 'Cookie': sessionCookie2 }
    });
    
    let user2Results = { connected: false, accessGranted: false, lockAcquired: false };
    
    await new Promise((resolve) => {
      socket2.on('connect', () => {
        user2Results.connected = true;
        console.log(`✅ ${department}: Second user WebSocket connected`);
        
        // Check access (should be DENIED if first user has lock)
        socket2.emit('check_department_access', { targetDepartment: department }, (response) => {
          user2Results.accessGranted = response.canAccess;
          console.log(`${department}: Second user access check: ${response.canAccess ? '✅ GRANTED' : '❌ DENIED'}`);
          
          if (response.canAccess) {
            // Try to acquire lock (should FAIL if first user has lock)
            socket2.emit('department_lock_request', { targetDepartment: department }, (lockResponse) => {
              user2Results.lockAcquired = lockResponse.success;
              console.log(`${department}: Second user lock: ${lockResponse.success ? '✅ ACQUIRED' : '❌ FAILED'}`);
              if (!lockResponse.success) console.log(`${department}: Lock error: ${lockResponse.message}`);
              resolve();
            });
          } else {
            resolve();
          }
        });
      });
      
      socket2.on('connect_error', (error) => {
        console.log(`❌ ${department}: Second user WebSocket connection failed: ${error.message}`);
        resolve();
      });
    });
    
    // Cleanup
    socket1.disconnect();
    socket2.disconnect();
    
    // Analyze results
    const testResult = {
      department,
      user1: user1Results,
      user2: user2Results,
      status: 'TESTED'
    };
    
    // Determine if exclusive access is working
    if (user1Results.lockAcquired && user2Results.lockAcquired) {
      testResult.exclusiveAccess = 'BROKEN';
      testResult.issue = 'Both users acquired locks simultaneously';
      console.log(`🚨 ${department}: EXCLUSIVE ACCESS BROKEN - Both users have locks!`);
    } else if (user1Results.lockAcquired && !user2Results.lockAcquired) {
      testResult.exclusiveAccess = 'WORKING';
      console.log(`✅ ${department}: Exclusive access working correctly`);
    } else if (!user1Results.lockAcquired) {
      testResult.exclusiveAccess = 'UNKNOWN';
      testResult.issue = 'First user could not acquire lock';
      console.log(`⚠️ ${department}: First user could not acquire lock - inconclusive test`);
    } else {
      testResult.exclusiveAccess = 'UNKNOWN';
      testResult.issue = 'Unexpected test result';
      console.log(`⚠️ ${department}: Unexpected test result`);
    }
    
    return testResult;
    
  } catch (error) {
    console.log(`❌ ${department}: Test error: ${error.message}`);
    return { department, status: 'ERROR', error: error.message };
  }
}

async function runAllDepartmentTests() {
  console.log('🔧 Testing exclusive access for all departments...');
  
  const departmentsToTest = ['FINANCE', 'MINISTRIES', 'PENSIONS', 'PENTMEDIA', 'MISSIONS', 'PENTSOS'];
  const results = [];
  
  for (const dept of departmentsToTest) {
    const result = await testDepartmentExclusiveAccess(dept);
    results.push(result);
    
    // Wait between tests to avoid conflicts
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Summary Report
  console.log('\n📊 EXCLUSIVE ACCESS TEST SUMMARY:');
  console.log('='.repeat(60));
  
  const working = results.filter(r => r.exclusiveAccess === 'WORKING');
  const broken = results.filter(r => r.exclusiveAccess === 'BROKEN');
  const unknown = results.filter(r => r.exclusiveAccess === 'UNKNOWN');
  const errors = results.filter(r => r.status === 'ERROR' || r.status === 'LOGIN_FAILED' || r.status === 'INSUFFICIENT_USERS');
  
  console.log(`✅ Working correctly: ${working.length} departments`);
  working.forEach(r => console.log(`   - ${r.department}`));
  
  console.log(`🚨 Broken exclusive access: ${broken.length} departments`);
  broken.forEach(r => console.log(`   - ${r.department}: ${r.issue}`));
  
  console.log(`⚠️ Inconclusive tests: ${unknown.length} departments`);
  unknown.forEach(r => console.log(`   - ${r.department}: ${r.issue || 'Unknown issue'}`));
  
  console.log(`❌ Test errors: ${errors.length} departments`);
  errors.forEach(r => console.log(`   - ${r.department}: ${r.error || r.status}`));
  
  if (broken.length > 0) {
    console.log('\n🚨 CRITICAL ISSUE CONFIRMED:');
    console.log(`${broken.length} department(s) have broken exclusive access!`);
    console.log('Multiple users can access the same department simultaneously.');
  } else if (working.length === departmentsToTest.length) {
    console.log('\n✅ ALL DEPARTMENTS WORKING CORRECTLY');
  }
  
  return results;
}

// Run the comprehensive test
runAllDepartmentTests();
