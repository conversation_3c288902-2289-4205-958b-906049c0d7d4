const axios = require('axios');
const io = require('socket.io-client');

async function testExclusiveAccessBug() {
  console.log('🔧 Testing exclusive access bug for department users...');
  
  try {
    // Step 1: Login as first PENSIONS user
    console.log('\n1️⃣ Logging in as first PENSIONS user...');
    
    let loginResponse1 = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'PENSIONS',
      username: 'FIRST_PENSIONS_USER',
      password: 'enter123'
    });
    
    if (!loginResponse1.data.success) {
      console.log('❌ First PENSIONS login failed - using test credentials');
      // Use any available PENSIONS user for testing
      const usersResponse = await axios.get('http://localhost:8080/api/auth/users-by-department');
      const pensionsUsers = usersResponse.data.filter(u => u.department === 'PENSIONS');
      
      if (pensionsUsers.length < 2) {
        console.log('❌ Need at least 2 PENSIONS users to test this bug');
        return;
      }
      
      console.log(`Using PENSIONS users: ${pensionsUsers[0].name} and ${pensionsUsers[1].name}`);
      
      // Login as first user
      loginResponse1 = await axios.post('http://localhost:8080/api/auth/login', {
        department: 'PENSIONS',
        username: pensionsUsers[0].name,
        password: 'enter123'
      });
    }
    
    let sessionCookie1 = loginResponse1.headers['set-cookie']?.[0];
    console.log('✅ First PENSIONS user login successful');
    
    // Step 2: Connect WebSocket as first PENSIONS user
    console.log('\n2️⃣ Connecting WebSocket as first PENSIONS user...');
    
    const socket1 = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true,
      extraHeaders: {
        'Cookie': sessionCookie1
      }
    });
    
    let user1Connected = false;
    let user1AccessCheck = null;
    let user1LockResult = null;
    
    socket1.on('connect', () => {
      console.log('✅ First PENSIONS user WebSocket connected');
      user1Connected = true;
      
      // Check department access
      socket1.emit('check_department_access', {
        targetDepartment: 'PENSIONS'
      }, (response) => {
        user1AccessCheck = response;
        console.log('First user access check:', response.canAccess ? '✅ GRANTED' : '❌ DENIED');
        
        if (response.canAccess) {
          // Try to acquire lock
          socket1.emit('department_lock_request', {
            targetDepartment: 'PENSIONS'
          }, (lockResponse) => {
            user1LockResult = lockResponse;
            console.log('First user lock result:', lockResponse.success ? '✅ SUCCESS' : '❌ FAILED');
            if (!lockResponse.success) console.log('Error:', lockResponse.message);
          });
        }
      });
    });
    
    // Wait for first user to get established
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Step 3: Login as second PENSIONS user (different session)
    console.log('\n3️⃣ Logging in as second PENSIONS user...');
    
    let loginResponse2 = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'PENSIONS',
      username: 'SECOND_PENSIONS_USER',
      password: 'enter123'
    });
    
    if (!loginResponse2.data.success) {
      // Use second available PENSIONS user
      const usersResponse = await axios.get('http://localhost:8080/api/auth/users-by-department');
      const pensionsUsers = usersResponse.data.filter(u => u.department === 'PENSIONS');
      
      loginResponse2 = await axios.post('http://localhost:8080/api/auth/login', {
        department: 'PENSIONS',
        username: pensionsUsers[1].name,
        password: 'enter123'
      });
    }
    
    let sessionCookie2 = loginResponse2.headers['set-cookie']?.[0];
    console.log('✅ Second PENSIONS user login successful');
    
    // Step 4: Connect WebSocket as second PENSIONS user
    console.log('\n4️⃣ Connecting WebSocket as second PENSIONS user...');
    
    const socket2 = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true,
      extraHeaders: {
        'Cookie': sessionCookie2
      }
    });
    
    let user2Connected = false;
    let user2AccessCheck = null;
    let user2LockResult = null;
    
    socket2.on('connect', () => {
      console.log('✅ Second PENSIONS user WebSocket connected');
      user2Connected = true;
      
      // Check department access (this should be DENIED if first user has lock)
      socket2.emit('check_department_access', {
        targetDepartment: 'PENSIONS'
      }, (response) => {
        user2AccessCheck = response;
        console.log('Second user access check:', response.canAccess ? '✅ GRANTED' : '❌ DENIED');
        
        if (response.canAccess) {
          // Try to acquire lock (this should FAIL if first user has lock)
          socket2.emit('department_lock_request', {
            targetDepartment: 'PENSIONS'
          }, (lockResponse) => {
            user2LockResult = lockResponse;
            console.log('Second user lock result:', lockResponse.success ? '✅ SUCCESS' : '❌ FAILED');
            if (!lockResponse.success) console.log('Error:', lockResponse.message);
          });
        }
      });
    });
    
    // Wait for second user test
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Results Analysis
    console.log('\n📊 EXCLUSIVE ACCESS BUG TEST RESULTS:');
    console.log(`First user connected: ${user1Connected ? '✅' : '❌'}`);
    console.log(`First user access check: ${user1AccessCheck?.canAccess ? '✅ GRANTED' : '❌ DENIED'}`);
    console.log(`First user lock acquired: ${user1LockResult?.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`Second user connected: ${user2Connected ? '✅' : '❌'}`);
    console.log(`Second user access check: ${user2AccessCheck?.canAccess ? '✅ GRANTED' : '❌ DENIED'}`);
    console.log(`Second user lock acquired: ${user2LockResult?.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    // Bug Detection
    if (user1LockResult?.success && user2LockResult?.success) {
      console.log('\n🚨 BUG CONFIRMED: Both users acquired exclusive access simultaneously!');
      console.log('❌ This violates the exclusive access principle');
      console.log('❌ Only one user should be able to access a department hub at a time');
    } else if (user1LockResult?.success && !user2LockResult?.success) {
      console.log('\n✅ EXCLUSIVE ACCESS WORKING CORRECTLY');
      console.log('✅ First user has lock, second user properly blocked');
    } else {
      console.log('\n⚠️ INCONCLUSIVE TEST - Check server logs for details');
    }
    
    // Cleanup
    socket1.disconnect();
    socket2.disconnect();
    
  } catch (error) {
    console.log('❌ Test error:', error.message);
  }
}

// Run the test
testExclusiveAccessBug();
