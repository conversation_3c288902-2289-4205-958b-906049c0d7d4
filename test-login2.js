const axios = require("axios"); async function testLogin() { console.log("Testing JAMES with trailing space"); try { const response = await axios.post("http://localhost:8080/api/auth/login", { username: "<PERSON><PERSON><PERSON> ", department: "MINISTRIES", password: "123456" }); console.log("SUCCESS:", response.data.user?.name); } catch (error) { console.log("FAILED:", error.response?.data?.error); } } testLogin();
