const axios = require('axios');

async function testMissionsAuditUsersDropdown() {
  console.log('🔧 Testing MISSIONS voucher hub - checking if all audit users appear in dropdowns...');
  
  try {
    // Step 1: Login as MISSIONS user to test audit users dropdown
    console.log('\n1️⃣ Logging in as MISSIONS user to test audit users dropdown...');

    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'MISSIONS',
      username: 'CHARIS',
      password: '123123'
    });
    
    if (!loginResponse.data.success) {
      console.log('❌ MISSIONS login failed:', loginResponse.data.error);
      return;
    }

    const sessionCookie = loginResponse.headers['set-cookie']?.[0];
    console.log('✅ MISSIONS user login successful');
    
    // Step 2: Fetch all users from the API (what the dropdowns use)
    console.log('\n2️⃣ Fetching all users from API (what dropdowns use)...');
    
    const usersResponse = await axios.get('http://localhost:8080/api/users', {
      headers: { 'Cookie': sessionCookie }
    });
    
    const allUsers = usersResponse.data;
    console.log(`✅ Fetched ${allUsers.length} total users from API`);
    
    // Step 3: Filter for AUDIT users (what the dropdown filtering does)
    const auditUsers = allUsers.filter(user => user.department === 'AUDIT' && user.isActive);
    console.log(`✅ Found ${auditUsers.length} active AUDIT users for dropdowns`);
    
    // Step 4: Display all AUDIT users that should appear in dropdowns
    console.log('\n📋 AUDIT USERS AVAILABLE FOR MISSIONS VOUCHER HUB DROPDOWNS:');
    console.log('='.repeat(70));
    console.log('These users should appear in:');
    console.log('  - Dispatched By dropdown');
    console.log('  - Certified By dropdown');
    console.log('  - Pre-Audited By dropdown');
    console.log('  - etc.');
    console.log('');
    
    if (auditUsers.length === 0) {
      console.log('❌ NO AUDIT USERS FOUND!');
      console.log('🚨 This explains why dropdowns are empty or incomplete.');
      console.log('');
      console.log('📋 POSSIBLE CAUSES:');
      console.log('   1. All AUDIT users are marked as inactive (is_active = 0)');
      console.log('   2. AUDIT users have incorrect department names in database');
      console.log('   3. Database connection or query issues');
      console.log('   4. Authentication middleware blocking the request');
    } else {
      auditUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name}`);
        console.log(`   - ID: ${user.id}`);
        console.log(`   - Department: ${user.department}`);
        console.log(`   - Active: ${user.isActive}`);
        console.log(`   - Role: ${user.role}`);
        console.log(`   - Date Created: ${user.dateCreated}`);
        console.log('');
      });
    }
    
    // Step 5: Check for inactive AUDIT users that won't appear
    const inactiveAuditUsers = allUsers.filter(user => user.department === 'AUDIT' && !user.isActive);
    if (inactiveAuditUsers.length > 0) {
      console.log('\n⚠️ INACTIVE AUDIT USERS (WILL NOT APPEAR IN DROPDOWNS):');
      console.log('='.repeat(70));
      inactiveAuditUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name} (INACTIVE)`);
        console.log(`   - ID: ${user.id}`);
        console.log(`   - Reason: isActive = ${user.isActive}`);
        console.log('');
      });
    }
    
    // Step 6: Check what the login endpoint returns for comparison
    console.log('\n3️⃣ Checking users from login endpoint for comparison...');
    
    const loginUsersResponse = await axios.get('http://localhost:8080/api/auth/users-by-department');
    const loginUsers = loginUsersResponse.data;
    const loginAuditUsers = loginUsers.filter(user => user.department === 'AUDIT');
    
    console.log(`✅ Login endpoint shows ${loginAuditUsers.length} AUDIT users`);
    
    // Step 7: Compare the two sources
    console.log('\n📊 COMPARISON ANALYSIS:');
    console.log('='.repeat(70));
    console.log(`API /users endpoint: ${auditUsers.length} active AUDIT users`);
    console.log(`Login endpoint: ${loginAuditUsers.length} AUDIT users`);
    
    if (auditUsers.length !== loginAuditUsers.length) {
      console.log('⚠️ MISMATCH DETECTED! Different endpoints return different user counts.');
      
      // Find missing users
      const apiUserNames = auditUsers.map(u => u.name);
      const loginUserNames = loginAuditUsers.map(u => u.name);
      
      const missingFromApi = loginUserNames.filter(name => !apiUserNames.includes(name));
      const missingFromLogin = apiUserNames.filter(name => !loginUserNames.includes(name));
      
      if (missingFromApi.length > 0) {
        console.log('\n❌ Users missing from API endpoint (but present in login):');
        missingFromApi.forEach(name => console.log(`   - ${name}`));
        console.log('   → These users will NOT appear in MISSIONS voucher hub dropdowns!');
      }
      
      if (missingFromLogin.length > 0) {
        console.log('\n❌ Users missing from login endpoint (but present in API):');
        missingFromLogin.forEach(name => console.log(`   - ${name}`));
      }
    } else {
      console.log('✅ Both endpoints return the same number of AUDIT users');
    }
    
    // Step 8: Specific diagnosis for MISSIONS voucher hub
    console.log('\n🔍 MISSIONS VOUCHER HUB DIAGNOSIS:');
    console.log('='.repeat(70));
    
    if (auditUsers.length === 0) {
      console.log('🚨 CRITICAL ISSUE: No active AUDIT users found!');
      console.log('📋 IMPACT ON MISSIONS VOUCHER HUB:');
      console.log('   - "Dispatched By" dropdown will be empty');
      console.log('   - "Certified By" dropdown will be empty');
      console.log('   - "Pre-Audited By" dropdown will be empty');
      console.log('   - Cannot properly process vouchers in MISSIONS hub');
    } else if (auditUsers.length < 3) {
      console.log('⚠️ LIMITED AUDIT USERS: Only a few AUDIT users available');
      console.log('📋 This explains why "not all audit users" appear in dropdowns');
      console.log('📋 Expected vs Actual:');
      console.log(`   - Available in dropdowns: ${auditUsers.length} users`);
      console.log(`   - You might expect more users to be available`);
      console.log('');
      console.log('📋 RECOMMENDATIONS:');
      console.log('   1. Check if more AUDIT users should be marked as active');
      console.log('   2. Verify AUDIT user accounts in the database');
      console.log('   3. Ensure all intended AUDIT users have been created');
    } else {
      console.log('✅ Multiple AUDIT users are available for dropdowns');
      console.log('📋 If you\'re still not seeing all expected users, check:');
      console.log('   1. Frontend dropdown component implementation');
      console.log('   2. User filtering logic in components');
      console.log('   3. State management and data flow');
    }
    
    // Step 9: Show expected dropdown content
    console.log('\n📋 EXPECTED DROPDOWN CONTENT FOR MISSIONS VOUCHER HUB:');
    console.log('='.repeat(70));
    console.log('Dropdowns should contain:');
    console.log('  1. "SELECT PERSON" (default option)');
    auditUsers.forEach((user, index) => {
      console.log(`  ${index + 2}. ${user.name}`);
    });
    console.log(`  ${auditUsers.length + 2}. "GUEST" (custom option)`);
    
    return {
      totalUsers: allUsers.length,
      activeAuditUsers: auditUsers.length,
      inactiveAuditUsers: inactiveAuditUsers.length,
      auditUsersList: auditUsers.map(u => u.name),
      loginAuditUsers: loginAuditUsers.length,
      mismatch: auditUsers.length !== loginAuditUsers.length
    };
    
  } catch (error) {
    console.log('❌ Test error:', error.message);
    if (error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
  }
}

// Run the test
testMissionsAuditUsersDropdown();
