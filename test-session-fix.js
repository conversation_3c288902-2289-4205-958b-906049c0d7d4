const axios = require('axios');
const io = require('socket.io-client');

async function testSessionFix() {
  console.log('🔧 Testing session management fix...');
  
  try {
    // Step 1: Get available users first
    console.log('\n1️⃣ Getting available users...');
    const usersResponse = await axios.get('http://localhost:8080/api/auth/users-by-department');
    const users = usersResponse.data;

    const financeUsers = users.filter(u => u.department === 'FINANCE');
    console.log('Available FINANCE users:', financeUsers.map(u => u.name));

    if (financeUsers.length === 0) {
      console.log('❌ No FINANCE users found');
      return;
    }

    const financeUser = financeUsers[0];
    console.log(`\n2️⃣ Trying to login as: ${financeUser.name}`);

    // Try common passwords
    const passwords = ['enter123', 'password123', 'FINANCE123', 'admin123'];
    let loginResponse = null;

    for (const pwd of passwords) {
      try {
        console.log(`  Trying password: ${pwd}`);
        loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
          department: 'FINANCE',
          username: financeUser.name,
          password: pwd
        });

        if (loginResponse.data.success) {
          console.log(`  ✅ Login successful with password: ${pwd}`);
          break;
        }
      } catch (err) {
        console.log(`  ❌ Failed with password: ${pwd}`);
      }
    }
    
    if (!loginResponse || !loginResponse.data.success) {
      console.log('❌ Login failed with all passwords');
      return;
    }
    
    const sessionCookie = loginResponse.headers['set-cookie']?.[0];
    console.log('✅ Login successful');
    console.log('Session cookie:', sessionCookie);

    // Step 2: Connect WebSocket with the session cookie
    console.log('\n3️⃣ Connecting WebSocket with session cookie...');
    
    const socket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true,
      extraHeaders: {
        'Cookie': sessionCookie
      }
    });
    
    let wsConnected = false;
    let wsAuthenticated = false;
    let lockTestResult = null;
    
    socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      wsConnected = true;
      
      // Join department room
      socket.emit('join_department', {
        department: 'FINANCE',
        userId: financeUser.id,
        userName: financeUser.name
      });
    });
    
    socket.on('department_joined', (data) => {
      console.log('✅ Department joined:', data);
      wsAuthenticated = true;
      
      // Test lock acquisition for FINANCE department
      console.log('\n4️⃣ Testing lock acquisition for FINANCE department...');
      socket.emit('lock_request', {
        lockType: 'batch-dispatch',
        department: 'FINANCE',
        voucherId: 'test-voucher-id'
      }, (response) => {
        lockTestResult = response;
        console.log('Lock request response:', response);
        
        if (response.success) {
          console.log('✅ Lock acquired successfully - Session fix working!');
        } else {
          console.log('❌ Lock acquisition failed:', response.message);
        }
      });
    });
    
    socket.on('connect_error', (error) => {
      console.log('❌ WebSocket connection error:', error.message);
    });
    
    // Wait for test completion
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Results
    console.log('\n📊 TEST RESULTS:');
    console.log(`WebSocket Connected: ${wsConnected ? '✅' : '❌'}`);
    console.log(`Department Authenticated: ${wsAuthenticated ? '✅' : '❌'}`);
    console.log(`Lock Test: ${lockTestResult?.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    if (lockTestResult?.success) {
      console.log('\n🎉 SESSION FIX SUCCESSFUL!');
      console.log('The WebSocket is now correctly using the current session.');
      console.log('Users should no longer experience department misclassification.');
    } else {
      console.log('\n⚠️ Session fix may need additional work.');
      console.log('Lock test failed:', lockTestResult?.message);
    }
    
    socket.disconnect();
    
  } catch (error) {
    console.log('❌ Test error:', error.message);
  }
}

// Run the test
testSessionFix();
