const axios = require('axios');
const io = require('socket.io-client');

async function testSessionManagementFix() {
  console.log('🔧 Testing comprehensive session management fix...');
  
  try {
    // Step 1: Login as MINISTRIES user first
    console.log('\n1️⃣ Logging in as MINISTRIES user...');
    
    let loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'MINISTRIES',
      username: 'SAMMY MAWUKO',
      password: 'enter123'
    });
    
    if (!loginResponse.data.success) {
      console.log('❌ MINISTRIES login failed:', loginResponse.data.error);
      return;
    }
    
    let sessionCookie = loginResponse.headers['set-cookie']?.[0];
    console.log('✅ MINISTRIES login successful');
    
    // Step 2: Connect WebSocket as MINISTRIES user
    console.log('\n2️⃣ Connecting WebSocket as MINISTRIES user...');
    
    const socket = io('http://localhost:8080', {
      transports: ['websocket'],
      timeout: 10000,
      forceNew: true,
      extraHeaders: {
        'Cookie': sessionCookie
      }
    });
    
    let wsConnected = false;
    let ministriesLockResult = null;
    let financeLockResult = null;
    
    socket.on('connect', () => {
      console.log('✅ WebSocket connected as MINISTRIES');
      wsConnected = true;
      
      // Join MINISTRIES department room
      socket.emit('join_department', {
        department: 'MINISTRIES',
        userId: 'abe28dd4-00f4-4d23-80f6-246235a79c51',
        userName: 'SAMMY MAWUKO'
      });
    });
    
    socket.on('department_joined', (data) => {
      console.log('✅ MINISTRIES department joined');
      
      // Test lock acquisition for MINISTRIES (should work)
      console.log('\n3️⃣ Testing MINISTRIES lock acquisition...');
      socket.emit('department_lock_request', {
        targetDepartment: 'MINISTRIES'
      }, (response) => {
        ministriesLockResult = response;
        console.log('MINISTRIES lock result:', response.success ? '✅ SUCCESS' : '❌ FAILED');
        if (!response.success) console.log('Error:', response.message);
      });
    });
    
    // Wait for MINISTRIES test
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Step 3: Logout from MINISTRIES
    console.log('\n4️⃣ Logging out from MINISTRIES...');
    try {
      await axios.post('http://localhost:8080/api/auth/logout', {}, {
        headers: { 'Cookie': sessionCookie }
      });
      console.log('✅ MINISTRIES logout successful');
    } catch (error) {
      console.log('⚠️ MINISTRIES logout API failed (non-critical)');
    }
    
    // Step 4: Login as FINANCE user (same WebSocket connection)
    console.log('\n5️⃣ Logging in as FINANCE user...');
    
    loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      department: 'FINANCE',
      username: 'MR. FELIX AYISI',
      password: 'enter123'
    });
    
    if (!loginResponse.data.success) {
      console.log('❌ FINANCE login failed:', loginResponse.data.error);
      return;
    }
    
    sessionCookie = loginResponse.headers['set-cookie']?.[0];
    console.log('✅ FINANCE login successful');
    
    // Step 5: Update WebSocket with new session cookie
    console.log('\n6️⃣ Testing lock acquisition as FINANCE user (same WebSocket)...');
    
    // The WebSocket connection is still the same, but we'll test if it re-authenticates
    socket.emit('department_lock_request', {
      targetDepartment: 'FINANCE'
    }, (response) => {
      financeLockResult = response;
      console.log('FINANCE lock result:', response.success ? '✅ SUCCESS' : '❌ FAILED');
      if (!response.success) console.log('Error:', response.message);
      
      // This is the critical test - if the fix works, this should succeed
      // If the fix doesn't work, it will fail with "You can only acquire locks on your own department (MINISTRIES)"
    });
    
    // Wait for FINANCE test
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Results
    console.log('\n📊 TEST RESULTS:');
    console.log(`WebSocket Connected: ${wsConnected ? '✅' : '❌'}`);
    console.log(`MINISTRIES Lock Test: ${ministriesLockResult?.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`FINANCE Lock Test: ${financeLockResult?.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    if (financeLockResult?.success) {
      console.log('\n🎉 SESSION MANAGEMENT FIX SUCCESSFUL!');
      console.log('✅ WebSocket correctly re-authenticated with new session');
      console.log('✅ Department misclassification issue is RESOLVED');
      console.log('✅ Users can now logout and login to different departments without issues');
    } else {
      console.log('\n⚠️ Session management fix needs more work');
      console.log('❌ WebSocket still using stale session data');
      console.log('Error message:', financeLockResult?.message);
      
      if (financeLockResult?.message?.includes('MINISTRIES')) {
        console.log('🔍 The WebSocket is still seeing the user as MINISTRIES department');
        console.log('🔧 This indicates the re-authentication logic needs adjustment');
      }
    }
    
    socket.disconnect();
    
  } catch (error) {
    console.log('❌ Test error:', error.message);
  }
}

// Run the comprehensive test
testSessionManagementFix();
