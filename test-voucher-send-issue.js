const axios = require('axios');

async function testVoucherSendIssue() {
  console.log('🔍 Testing voucher send to audit issue...');
  
  try {
    // First, let's check what users exist in the system
    console.log('\n1. Checking available users...');
    const usersResponse = await axios.get('http://localhost:8080/api/auth/users-by-department');
    const users = usersResponse.data;
    
    console.log('Available users:');
    users.forEach(user => {
      console.log(`  - ${user.name} (${user.department})`);
    });
    
    // Find a Finance user to test with
    const financeUser = users.find(u => u.department === 'FINANCE');
    if (!financeUser) {
      console.log('❌ No Finance user found');
      return;
    }
    
    console.log(`\n2. Testing login as Finance user: ${financeUser.name}`);

    // Try common default passwords
    const defaultPasswords = ['password123', 'enter123', 'admin123', 'FINANCE123', 'department123'];
    let loginResponse = null;
    let sessionCookie = null;

    for (const pwd of defaultPasswords) {
      try {
        console.log(`  Trying password: ${pwd}`);
        loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
          department: 'FINANCE',
          username: financeUser.name,
          password: pwd
        });

        if (loginResponse.data.success) {
          console.log(`  ✅ Login successful with password: ${pwd}`);
          sessionCookie = loginResponse.headers['set-cookie']?.[0];
          break;
        }
      } catch (err) {
        console.log(`  ❌ Failed with password: ${pwd}`);
      }
    }
    
    if (!loginResponse || !loginResponse.data.success) {
      console.log('❌ Login failed with all passwords');
      return;
    }

    console.log('Session cookie:', sessionCookie);
    
    // Check current user session
    console.log('\n3. Verifying session...');
    const meResponse = await axios.get('http://localhost:8080/api/auth/me', {
      headers: {
        'Cookie': sessionCookie
      }
    });
    
    console.log('Current user from session:', meResponse.data);
    
    // Try to get vouchers
    console.log('\n4. Fetching vouchers...');
    const vouchersResponse = await axios.get('http://localhost:8080/api/vouchers', {
      headers: {
        'Cookie': sessionCookie
      }
    });
    
    const vouchers = vouchersResponse.data;
    console.log(`Found ${vouchers.length} vouchers`);
    
    // Find a voucher that can be sent to audit
    const eligibleVoucher = vouchers.find(v => 
      v.department === 'FINANCE' && 
      !v.sentToAudit && 
      v.status !== 'PENDING'
    );
    
    if (!eligibleVoucher) {
      console.log('❌ No eligible voucher found to send to audit');
      console.log('Available vouchers:');
      vouchers.forEach(v => {
        console.log(`  - ID: ${v.id}, Dept: ${v.department}, Status: ${v.status}, SentToAudit: ${v.sentToAudit}`);
      });
      return;
    }
    
    console.log(`\n5. Attempting to send voucher ${eligibleVoucher.id} to audit...`);
    console.log(`Voucher details: Dept=${eligibleVoucher.department}, Status=${eligibleVoucher.status}`);
    
    // Try to send voucher to audit
    const sendResponse = await axios.post(`http://localhost:8080/api/vouchers/${eligibleVoucher.id}/send-to-audit`, {}, {
      headers: {
        'Cookie': sessionCookie,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Voucher sent to audit successfully:', sendResponse.data);
    
  } catch (error) {
    console.log('❌ Error occurred:');
    console.log('Status:', error.response?.status);
    console.log('Error message:', error.response?.data?.error || error.message);
    console.log('Error details:', error.response?.data?.details);
    
    if (error.response?.status === 403) {
      console.log('\n🔍 This appears to be the department access issue you mentioned!');
      console.log('The system is denying access based on department classification.');
    }
  }
}

// Run the test
testVoucherSendIssue();
