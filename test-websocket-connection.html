<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Connection Test</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Testing...</div>
    <div id="logs"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const logsDiv = document.getElementById('logs');
        
        function log(message) {
            console.log(message);
            logsDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }
        
        log('🔌 Starting WebSocket connection test...');
        
        // Test Socket.IO connection to the same server
        const socket = io('/', {
            transports: ['polling', 'websocket'],
            timeout: 5000,
            reconnection: true,
            reconnectionAttempts: 3,
            reconnectionDelay: 1000
        });
        
        socket.on('connect', () => {
            log('✅ WebSocket CONNECTED! Socket ID: ' + socket.id);
            statusDiv.innerHTML = '✅ CONNECTED';
            statusDiv.style.color = 'green';
            
            // Test joining a department room
            socket.emit('join_department', {
                department: 'AUDIT',
                userId: 'test-user',
                userName: 'Test User'
            });
        });
        
        socket.on('joined_department', (data) => {
            log('✅ Joined department room: ' + JSON.stringify(data));
        });
        
        socket.on('connect_error', (error) => {
            log('❌ Connection ERROR: ' + error.message);
            statusDiv.innerHTML = '❌ CONNECTION ERROR';
            statusDiv.style.color = 'red';
        });
        
        socket.on('disconnect', (reason) => {
            log('🔌 Disconnected: ' + reason);
            statusDiv.innerHTML = '🔌 DISCONNECTED';
            statusDiv.style.color = 'orange';
        });
        
        socket.on('error', (error) => {
            log('❌ Socket ERROR: ' + error);
        });
        
        // Test timeout
        setTimeout(() => {
            if (!socket.connected) {
                log('⏰ Connection timeout - WebSocket failed to connect within 10 seconds');
                statusDiv.innerHTML = '⏰ TIMEOUT';
                statusDiv.style.color = 'red';
            }
        }, 10000);
    </script>
</body>
</html>
